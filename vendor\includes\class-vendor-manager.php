<?php
/**
 * Vendor Manager
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Vendor manager class
 */
class Vendor_Manager {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Initialize
     */
    public function init() {
        // Hook into WooCommerce product save
        add_action('woocommerce_process_product_meta', array($this, 'assign_product_to_vendor'));
        
        // Hook into order completion
        add_action('woocommerce_order_status_completed', array($this, 'process_order_commission'));
        
        // Add vendor meta to products
        add_action('add_meta_boxes', array($this, 'add_vendor_meta_box'));
    }
    
    /**
     * Create a new vendor
     */
    public function create_vendor($user_id, $vendor_data) {
        global $wpdb;
        
        $table = Vendor_Database::get_vendors_table();
        
        $data = array(
            'user_id' => $user_id,
            'store_name' => sanitize_text_field($vendor_data['store_name']),
            'store_slug' => sanitize_title($vendor_data['store_slug']),
            'store_description' => wp_kses_post($vendor_data['store_description']),
            'store_logo' => sanitize_url($vendor_data['store_logo']),
            'store_banner' => sanitize_url($vendor_data['store_banner']),
            'store_address' => wp_kses_post($vendor_data['store_address']),
            'store_phone' => sanitize_text_field($vendor_data['store_phone']),
            'store_email' => sanitize_email($vendor_data['store_email']),
            'commission_rate' => floatval($vendor_data['commission_rate']),
            'status' => sanitize_text_field($vendor_data['status'])
        );
        
        $result = $wpdb->insert($table, $data);
        
        if ($result) {
            $vendor_id = $wpdb->insert_id;
            
            // Update user role to vendor
            $user = new WP_User($user_id);
            $user->set_role('vendor');
            
            do_action('vendor_created', $vendor_id, $user_id, $vendor_data);
            
            return $vendor_id;
        }
        
        return false;
    }
    
    /**
     * Get vendor by user ID
     */
    public function get_vendor_by_user($user_id) {
        global $wpdb;
        
        $table = Vendor_Database::get_vendors_table();
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table WHERE user_id = %d",
            $user_id
        ));
    }
    
    /**
     * Get vendor by ID
     */
    public function get_vendor($vendor_id) {
        global $wpdb;
        
        $table = Vendor_Database::get_vendors_table();
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table WHERE id = %d",
            $vendor_id
        ));
    }
    
    /**
     * Update vendor
     */
    public function update_vendor($vendor_id, $vendor_data) {
        global $wpdb;
        
        $table = Vendor_Database::get_vendors_table();
        
        $data = array();
        
        if (isset($vendor_data['store_name'])) {
            $data['store_name'] = sanitize_text_field($vendor_data['store_name']);
        }
        
        if (isset($vendor_data['store_slug'])) {
            $data['store_slug'] = sanitize_title($vendor_data['store_slug']);
        }
        
        if (isset($vendor_data['store_description'])) {
            $data['store_description'] = wp_kses_post($vendor_data['store_description']);
        }
        
        if (isset($vendor_data['store_logo'])) {
            $data['store_logo'] = sanitize_url($vendor_data['store_logo']);
        }
        
        if (isset($vendor_data['store_banner'])) {
            $data['store_banner'] = sanitize_url($vendor_data['store_banner']);
        }
        
        if (isset($vendor_data['store_address'])) {
            $data['store_address'] = wp_kses_post($vendor_data['store_address']);
        }
        
        if (isset($vendor_data['store_phone'])) {
            $data['store_phone'] = sanitize_text_field($vendor_data['store_phone']);
        }
        
        if (isset($vendor_data['store_email'])) {
            $data['store_email'] = sanitize_email($vendor_data['store_email']);
        }
        
        if (isset($vendor_data['commission_rate'])) {
            $data['commission_rate'] = floatval($vendor_data['commission_rate']);
        }
        
        if (isset($vendor_data['status'])) {
            $data['status'] = sanitize_text_field($vendor_data['status']);
        }
        
        if (!empty($data)) {
            $result = $wpdb->update($table, $data, array('id' => $vendor_id));
            
            if ($result !== false) {
                do_action('vendor_updated', $vendor_id, $vendor_data);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Delete vendor
     */
    public function delete_vendor($vendor_id) {
        global $wpdb;
        
        $vendor = $this->get_vendor($vendor_id);
        if (!$vendor) {
            return false;
        }
        
        $table = Vendor_Database::get_vendors_table();
        
        $result = $wpdb->delete($table, array('id' => $vendor_id));
        
        if ($result) {
            // Remove vendor role from user
            $user = new WP_User($vendor->user_id);
            $user->set_role('customer');
            
            do_action('vendor_deleted', $vendor_id, $vendor->user_id);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Get all vendors
     */
    public function get_vendors($args = array()) {
        global $wpdb;
        
        $table = Vendor_Database::get_vendors_table();
        
        $defaults = array(
            'status' => '',
            'limit' => 20,
            'offset' => 0,
            'orderby' => 'created_at',
            'order' => 'DESC'
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $where = array('1=1');
        $values = array();
        
        if (!empty($args['status'])) {
            $where[] = 'status = %s';
            $values[] = $args['status'];
        }
        
        $where_clause = implode(' AND ', $where);
        
        $query = $wpdb->prepare(
            "SELECT * FROM $table WHERE $where_clause ORDER BY {$args['orderby']} {$args['order']} LIMIT %d OFFSET %d",
            array_merge($values, array($args['limit'], $args['offset']))
        );
        
        return $wpdb->get_results($query);
    }
    
    /**
     * Get vendor count
     */
    public function get_vendor_count($status = '') {
        global $wpdb;
        
        $table = Vendor_Database::get_vendors_table();
        
        if (!empty($status)) {
            return $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table WHERE status = %s",
                $status
            ));
        }
        
        return $wpdb->get_var("SELECT COUNT(*) FROM $table");
    }
    
    /**
     * Check if user is vendor
     */
    public function is_vendor($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        $user = get_user_by('id', $user_id);
        
        return $user && in_array('vendor', $user->roles);
    }
    
    /**
     * Get current vendor
     */
    public function get_current_vendor() {
        if (!$this->is_vendor()) {
            return false;
        }
        
        return $this->get_vendor_by_user(get_current_user_id());
    }
    
    /**
     * Assign product to vendor
     */
    public function assign_product_to_vendor($product_id) {
        if (!$this->is_vendor()) {
            return;
        }
        
        $vendor = $this->get_current_vendor();
        if ($vendor) {
            update_post_meta($product_id, '_vendor_id', $vendor->id);
        }
    }
    
    /**
     * Process order commission
     */
    public function process_order_commission($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }
        
        foreach ($order->get_items() as $item_id => $item) {
            $product_id = $item->get_product_id();
            $vendor_id = get_post_meta($product_id, '_vendor_id', true);
            
            if ($vendor_id) {
                $vendor = $this->get_vendor($vendor_id);
                if ($vendor) {
                    vendor()->commission->create_commission($vendor_id, $order_id, $product_id, $item_id, $item);
                }
            }
        }
    }
    
    /**
     * Add vendor meta box
     */
    public function add_vendor_meta_box() {
        add_meta_box(
            'vendor-product-meta',
            __('Vendor Information', 'vendor'),
            array($this, 'vendor_meta_box_callback'),
            'product',
            'side',
            'default'
        );
    }
    
    /**
     * Vendor meta box callback
     */
    public function vendor_meta_box_callback($post) {
        $vendor_id = get_post_meta($post->ID, '_vendor_id', true);
        $vendor = $vendor_id ? $this->get_vendor($vendor_id) : null;
        
        echo '<p>';
        if ($vendor) {
            printf(__('Vendor: %s', 'vendor'), esc_html($vendor->store_name));
        } else {
            _e('No vendor assigned', 'vendor');
        }
        echo '</p>';
    }
}
