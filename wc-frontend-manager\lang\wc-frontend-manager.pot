# /*! DO NOT EDIT THIS FILE. This file is a auto generated on 2024-09-21 */
# This file is distributed under the same license as the WCFM - WooCommerce Multivendor Marketplace plugin.
# 
msgid ""
msgstr ""
"Project-Id-Version: WCFM - WooCommerce Frontend Manager ********\n"
"Report-Msgid-Bugs-To: "
"https://wordpress.org/support/plugin/wc-frontend-manager\n"
"POT-Creation-Date: 2024-09-21 16:41:33+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2024-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: en\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-Country: United States\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: "
"__;_e;_x:1,2c;_ex:1,2c;_n:1,2;_nx:1,2,4c;_n_noop:1,2;_nx_noop:1,2,3c;esc_"
"attr__;esc_html__;esc_attr_e;esc_html_e;esc_attr_x:1,2c;esc_html_x:1,2c;\n"
"X-Poedit-Basepath: ../\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-Bookmarks: \n"
"X-Textdomain-Support: yes\n"
"X-Generator: grunt-wp-i18n 1.0.3\n"

#: controllers/articles/wcfm-controller-articles-manage.php:32
#: controllers/coupons/wcfm-controller-coupons-manage.php:32
#: controllers/customers/wcfm-controller-customers-manage.php:33
#: controllers/enquiry/wcfm-controller-enquiry-form.php:54
#: controllers/products-manager/wcfm-controller-products-manage.php:39
#: controllers/profile/wcfm-controller-profile.php:69
#: controllers/settings/wcfm-controller-settings.php:29
#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:31
#: core/class-wcfm-ajax.php:129 core/class-wcfm-ajax.php:487
#: core/class-wcfm-ajax.php:560 core/class-wcfm-ajax.php:635
#: core/class-wcfm-ajax.php:680 core/class-wcfm-ajax.php:713
#: core/class-wcfm-ajax.php:747 core/class-wcfm-ajax.php:780
#: core/class-wcfm-ajax.php:811 core/class-wcfm-ajax.php:871
#: core/class-wcfm-ajax.php:947 core/class-wcfm-ajax.php:974
#: core/class-wcfm-ajax.php:1001 core/class-wcfm-ajax.php:1028
#: core/class-wcfm-ajax.php:1055 core/class-wcfm-ajax.php:1082
#: core/class-wcfm-ajax.php:1111 core/class-wcfm-ajax.php:1142
#: core/class-wcfm-ajax.php:1166 core/class-wcfm-ajax.php:1185
#: core/class-wcfm-ajax.php:1204 core/class-wcfm-ajax.php:1265
#: core/class-wcfm-ajax.php:1320 core/class-wcfm-ajax.php:1393
#: core/class-wcfm-ajax.php:1453 core/class-wcfm-ajax.php:1478
#: core/class-wcfm-ajax.php:1541 core/class-wcfm-ajax.php:1620
#: core/class-wcfm-ajax.php:1707 core/class-wcfm-ajax.php:1738
#: core/class-wcfm-ajax.php:1758 core/class-wcfm-ajax.php:1773
#: core/class-wcfm-ajax.php:1851 core/class-wcfm-article.php:221
#: core/class-wcfm-article.php:262 core/class-wcfm-customer.php:290
#: core/class-wcfm-customer.php:390 core/class-wcfm-customer.php:424
#: core/class-wcfm-enquiry.php:269 core/class-wcfm-enquiry.php:399
#: core/class-wcfm-enquiry.php:426 core/class-wcfm-enquiry.php:454
#: core/class-wcfm-enquiry.php:662 core/class-wcfm-integrations.php:1624
#: core/class-wcfm-notification.php:391 core/class-wcfm-notification.php:915
#: core/class-wcfm-notification.php:980 core/class-wcfm-notification.php:1019
#: core/class-wcfm-notification.php:1065 core/class-wcfm-notification.php:1099
#: core/class-wcfm-wcbookings.php:296 core/class-wcfm-wcmarketplace.php:857
#: core/class-wcfm-wcmarketplace.php:886 core/class-wcfm-withdrawal.php:438
msgid "Invalid nonce! Refresh your page and try again."
msgstr ""

#: controllers/articles/wcfm-controller-articles-manage.php:52
#: controllers/coupons/wcfm-controller-coupons-manage.php:52
#: controllers/customers/wcfm-controller-customers-manage.php:47
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:40
#: controllers/knowledgebase/wcfm-controller-knowledgebase-manage.php:37
#: controllers/products-manager/wcfm-controller-products-manage.php:53
#: controllers/profile/wcfm-controller-profile.php:78
#: controllers/settings/wcfm-controller-dokan-settings.php:33
#: controllers/settings/wcfm-controller-settings.php:40
#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:49
#: controllers/settings/wcfm-controller-wcmarketplace-settings.php:33
#: controllers/settings/wcfm-controller-wcpvendors-settings.php:29
#: controllers/settings/wcfm-controller-wcvendors-settings.php:31
#: controllers/vendors/wcfm-controller-vendors-manage.php:29
#: controllers/vendors/wcfm-controller-vendors-new.php:48
#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:29
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:37
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:34
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:140
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:33
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:37
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:140
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:202
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse-actions.php:34
#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal-request.php:33
msgid "There has some error in submitted data."
msgstr ""

#: controllers/articles/wcfm-controller-articles.php:138
#: controllers/listings/wcfm-controller-listings.php:24
#: controllers/products/wcfm-controller-products.php:25
#: controllers/products/wcfm-controller-products.php:276
#: views/articles/wcfm-view-articles-manage.php:134
#: views/articles/wcfm-view-articles.php:11
#: views/listings/wcfm-view-listings.php:37
#: views/products/wcfm-view-products.php:11
#: views/products-manager/wcfm-view-products-manage.php:471
msgid "Published"
msgstr ""

#: controllers/articles/wcfm-controller-articles.php:166
#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:119
#: controllers/listings/wcfm-controller-applications.php:143
#: controllers/listings/wcfm-controller-listings.php:164
#: controllers/notice/wcfm-controller-notices.php:93
#: controllers/products/wcfm-controller-products.php:431
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:105
#: views/articles/wcfm-view-articles-manage.php:444
#: views/enquiry/wcfm-view-my-account-enquiry.php:80
#: views/notice/wcfm-view-notice-manage.php:66
#: views/products-manager/wcfm-view-products-manage.php:960
#: views/products-popup/wcfm-view-product-popup.php:332
msgid "View"
msgstr ""

#: controllers/articles/wcfm-controller-articles.php:169
#: controllers/articles/wcfm-controller-articles.php:172
#: controllers/coupons/wcfm-controller-coupons.php:119
#: controllers/coupons/wcfm-controller-coupons.php:121
#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:121
#: controllers/listings/wcfm-controller-applications.php:146
#: controllers/listings/wcfm-controller-listings.php:169
#: controllers/notice/wcfm-controller-notices.php:96
#: controllers/products/wcfm-controller-products.php:465
#: controllers/products/wcfm-controller-products.php:474
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:107
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:110
#: core/class-wcfm-frontend.php:354 views/notice/wcfm-view-notice-view.php:66
#: views/vendors/wcfm-view-vendors-new.php:69
msgid "Edit"
msgstr ""

#: controllers/articles/wcfm-controller-articles.php:170
#: controllers/articles/wcfm-controller-articles.php:173
#: controllers/customers/wcfm-controller-customers.php:239
#: controllers/enquiry/wcfm-controller-enquiry.php:218
#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:127
#: controllers/listings/wcfm-controller-applications.php:186
#: controllers/listings/wcfm-controller-listings.php:215
#: controllers/messages/wcfm-controller-messages.php:239
#: controllers/notice/wcfm-controller-notices.php:102
#: controllers/products/wcfm-controller-products.php:472
#: controllers/products/wcfm-controller-products.php:475
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:108
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:111
#: core/class-wcfm-frontend.php:358
#: views/enquiry/wcfm-view-enquiry-manage.php:248
#: views/messages/wcfm-view-messages.php:54
msgid "Delete"
msgstr ""

#: controllers/capability/wcfm-controller-capability.php:39
msgid "Capability saved successfully"
msgstr ""

#: controllers/coupons/wcfm-controller-coupons-manage.php:113
msgid ""
"Coupon code already exists - customers will use the latest coupon with this "
"code."
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:135
#: controllers/customers/wcfm-controller-customers-details.php:141
#: controllers/orders/wcfm-controller-dokan-orders.php:184
#: controllers/orders/wcfm-controller-dokan-orders.php:190
#: controllers/orders/wcfm-controller-orders.php:223
#: controllers/orders/wcfm-controller-orders.php:229
#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:232
#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:238
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:283
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:289
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:208
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:214
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:177
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:183
#: controllers/orders/wcfm-controller-wcvendors-orders.php:205
#: controllers/orders/wcfm-controller-wcvendors-orders.php:211
msgid "Guest"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:145
#: controllers/customers/wcfm-controller-customers-details.php:147
#: controllers/orders/wcfm-controller-dokan-orders.php:194
#: controllers/orders/wcfm-controller-dokan-orders.php:196
#: controllers/orders/wcfm-controller-orders.php:235
#: controllers/orders/wcfm-controller-orders.php:237
#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:242
#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:244
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:295
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:297
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:218
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:220
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:187
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:189
#: controllers/orders/wcfm-controller-wcvendors-orders.php:215
#: controllers/orders/wcfm-controller-wcvendors-orders.php:217
#: views/enquiry/wcfm-view-enquiry-tab.php:65
msgid "by"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:175
#: controllers/orders/wcfm-controller-dokan-orders.php:223
#: controllers/orders/wcfm-controller-orders.php:267
#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:281
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:343
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:250
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:245
#: controllers/orders/wcfm-controller-wcvendors-orders.php:234
msgid "%d item"
msgid_plural "%d items"
msgstr[0] ""
msgstr[1] ""

#: controllers/customers/wcfm-controller-customers-details.php:186
#: controllers/orders/wcfm-controller-dokan-orders.php:252
#: controllers/orders/wcfm-controller-orders.php:296
#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:317
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:410
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:104
#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:163
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:174
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse.php:145
msgid "Via"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:198
#: controllers/orders/wcfm-controller-dokan-orders.php:280
#: controllers/orders/wcfm-controller-orders.php:344
#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:372
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:476
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:327
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:309
#: controllers/orders/wcfm-controller-wcvendors-orders.php:360
msgid "Mark as Complete"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:202
#: controllers/customers/wcfm-controller-customers-details.php:399
#: controllers/customers/wcfm-controller-customers-details.php:572
#: controllers/orders/wcfm-controller-dokan-orders.php:284
#: controllers/orders/wcfm-controller-orders.php:348
#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:376
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:480
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:331
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:313
#: controllers/orders/wcfm-controller-wcvendors-orders.php:364
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:217
#: core/class-wcfm-admin.php:173 views/capability/wcfm-view-capability.php:588
msgid "View Details"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:207
#: controllers/customers/wcfm-controller-customers-details.php:214
#: controllers/orders/wcfm-controller-orders.php:353
#: views/capability/wcfm-view-capability.php:600
#: views/orders/wcfm-view-orders-details.php:152
#: views/orders/wcfm-view-orders-details.php:154
msgid "PDF Invoice"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:210
#: views/capability/wcfm-view-capability.php:606
msgid "PDF Packing Slip"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:249
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:25
#: views/wc_bookings/wcfm-view-wcbookings.php:21
msgid "Paid & Confirmed"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:249
#: controllers/customers/wcfm-controller-customers-details.php:436
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:25
#: views/wc_bookings/wcfm-view-wcbookings.php:23
msgid "Pending Confirmation"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:249
#: controllers/customers/wcfm-controller-customers-details.php:436
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:25
msgid "Un-paid"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:249
#: controllers/customers/wcfm-controller-customers-details.php:436
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:25
#: core/class-wcfm-wcfmmarketplace.php:218
#: views/wc_bookings/wcfm-view-wcbookings.php:24
#: views/withdrawal/dokan/wcfm-view-payments.php:54
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:40
#: views/withdrawal/wcfm/wcfm-view-payments.php:63
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:48
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:61
msgid "Cancelled"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:249
#: controllers/customers/wcfm-controller-customers-details.php:436
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:25
#: views/wc_bookings/wcfm-view-wcbookings.php:20
msgid "Complete"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:249
#: controllers/customers/wcfm-controller-customers-details.php:436
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:25
#: views/wc_bookings/wcfm-view-wcbookings.php:22
msgid "Confirmed"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:314
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:117
msgid "#"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:396
#: controllers/customers/wcfm-controller-customers-details.php:570
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:214
msgid "Mark as Confirmed"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:436
#: core/class-wcfm-wcfmmarketplace.php:217
msgid "Paid"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:439
msgid "Partial Paid"
msgstr ""

#: controllers/customers/wcfm-controller-customers-manage.php:56
#: controllers/vendors/wcfm-controller-vendors-new.php:36
msgid "Please provide a valid email address."
msgstr ""

#: controllers/customers/wcfm-controller-customers-manage.php:61
#: controllers/vendors/wcfm-controller-vendors-new.php:41
msgid "Please enter a valid account username."
msgstr ""

#: controllers/customers/wcfm-controller-customers-manage.php:198
#: controllers/vendors/wcfm-controller-vendors-new.php:231
msgid "Dear"
msgstr ""

#: controllers/customers/wcfm-controller-customers-manage.php:200
msgid ""
"Your account has been created as {user_role}. Follow the bellow details to "
"log into the system"
msgstr ""

#: controllers/customers/wcfm-controller-customers-manage.php:202
msgid "Site"
msgstr ""

#: controllers/customers/wcfm-controller-customers-manage.php:204
#: views/login-popup/wcfm-login-popup-form.php:6
#: views/login-popup/wcfm-login-popup-form.php:32
msgid "Login"
msgstr ""

#: controllers/customers/wcfm-controller-customers-manage.php:206
#: controllers/vendors/wcfm-controller-vendors-new.php:241
#: views/login-popup/wcfm-login-popup-form.php:22
#: views/profile/wcfm-view-profile.php:245
msgid "Password"
msgstr ""

#: controllers/customers/wcfm-controller-customers-manage.php:218
#: controllers/vendors/wcfm-controller-vendors-new.php:255
msgid "New Account"
msgstr ""

#: controllers/customers/wcfm-controller-customers-manage.php:234
msgid "A new customer <b>%s</b> added to the store by <b>%s</b>"
msgstr ""

#: controllers/customers/wcfm-controller-customers.php:234
#: views/customers/wcfm-view-customers-manage.php:123
msgid "Manage Customer"
msgstr ""

#: controllers/customers/wcfm-controller-customers.php:236
#: views/capability/wcfm-view-capability.php:618
#: views/customers/wcfm-view-customers-details.php:118
#: views/customers/wcfm-view-customers-manage.php:130
msgid "Edit Customer"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-form.php:46
msgid "Captcha failed, please try again."
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-form.php:144
#: views/enquiry/wcfm-view-enquiry-manage.php:150
#: views/enquiry/wcfm-view-enquiry.php:87
#: views/enquiry/wcfm-view-enquiry.php:102
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:127
#: views/enquiry/wcfm-view-my-account-enquiry.php:46
#: views/enquiry/wcfm-view-my-account-enquiry.php:64
#: views/orders/wcfm-view-orders.php:120 views/orders/wcfm-view-orders.php:151
#: views/vendors/wcfm-view-vendors.php:90
#: views/vendors/wcfm-view-vendors.php:111
msgid "Additional Info"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-form.php:146
#: controllers/enquiry/wcfm-controller-enquiry-form.php:147
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:133
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:134
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:137
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:186
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:324
#: controllers/messages/wcfm-controller-messages.php:169
#: controllers/messages/wcfm-controller-messages.php:193
#: core/class-wcfm-policy.php:68 core/class-wcfm-policy.php:72
#: core/class-wcfm-vendor-support.php:206
#: core/class-wcfm-vendor-support.php:317
#: core/class-wcfm-vendor-support.php:319
#: core/class-wcfm-vendor-support.php:462
#: core/class-wcfm-vendor-support.php:467 helpers/wcfm-core-functions.php:1341
#: includes/emails/class-wcfm-email-new-enquiry.php:92
#: includes/emails/class-wcfm-email-new-enquiry.php:93
#: includes/emails/class-wcfm-email-new-enquiry.php:96
#: includes/emails/class-wcfm-email-new-enquiry.php:97
#: views/articles/wcfm-view-articles-manage.php:267
#: views/articles/wcfm-view-articles.php:114
#: views/articles/wcfm-view-articles.php:125
#: views/capability/wcfm-view-capability.php:198
#: views/coupons/wcfm-view-coupons-manage.php:140
#: views/coupons/wcfm-view-coupons.php:58
#: views/coupons/wcfm-view-coupons.php:69
#: views/customers/wcfm-view-customers-manage.php:181
#: views/customers/wcfm-view-customers.php:80
#: views/customers/wcfm-view-customers.php:96
#: views/enquiry/wcfm-view-enquiry-manage.php:140
#: views/enquiry/wcfm-view-enquiry.php:86
#: views/enquiry/wcfm-view-enquiry.php:101
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:116
#: views/enquiry/wcfm-view-my-account-enquiry.php:44
#: views/enquiry/wcfm-view-my-account-enquiry.php:60
#: views/listings/wcfm-view-listings.php:119
#: views/listings/wcfm-view-listings.php:134
#: views/orders/wcfm-view-orders-details.php:417
#: views/products/wcfm-view-products.php:241
#: views/products/wcfm-view-products.php:265
#: views/reports/wcfm-view-reports-menu.php:6
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:91
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:93
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:145
#: views/settings/wcfm-view-dokan-settings.php:159
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:278
#: views/settings/wcfm-view-wcmarketplace-settings.php:166
#: views/settings/wcfm-view-wcvendors-settings.php:150
#: views/vendors/wcfm-view-vendors-manage.php:164
#: views/vendors/wcfm-view-vendors-manage.php:205
#: views/vendors/wcfm-view-vendors-manage.php:208
#: views/vendors/wcfm-view-vendors-manage.php:291
#: views/vendors/wcfm-view-vendors-new.php:62
#: views/vendors/wcfm-view-vendors-new.php:69
#: views/vendors/wcfm-view-vendors-new.php:72
#: views/vendors/wcfm-view-vendors-new.php:75
#: views/vendors/wcfm-view-vendors.php:17
#: views/vendors/wcfm-view-vendors.php:25
#: views/vendors/wcfm-view-vendors.php:28
#: views/vendors/wcfm-view-vendors.php:29
#: views/vendors/wcfm-view-vendors.php:78
#: views/vendors/wcfm-view-vendors.php:99
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:54
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:64
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:73
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:90
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:85
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:98
msgid "Store"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-form.php:200
#: controllers/enquiry/wcfm-controller-enquiry-form.php:224
msgid "New Inquiry <b>%s</b> received for <b>%s</b>"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:154
msgid "Reply for your Inquiry"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:155
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:351
#: core/class-wcfm-ajax.php:1508 core/class-wcfm-notification.php:806
#: core/class-wcfm-notification.php:817 views/emails/plain/new-enquiry.php:18
msgid "Hi"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:157
msgid ""
"We recently have a enquiry from you regarding \"%s\". Please see our "
"response below: "
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:163
msgid "See details %shere%s."
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:165
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:358
#: controllers/vendors/wcfm-controller-vendors-new.php:243
#: core/class-wcfm-ajax.php:1511 core/class-wcfm-notification.php:813
#: core/class-wcfm-notification.php:822 views/emails/new-enquiry.php:43
#: views/emails/plain/new-enquiry.php:26
msgid "Thank You"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:175
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:350
msgid "Inquiry Reply"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:212
msgid "New reply posted for Inquiry <b>%s</b>"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:350
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:357
#: core/class-wcfm-enquiry.php:374 core/class-wcfm-enquiry.php:568
#: core/class-wcfm-enquiry.php:572 core/class-wcfm-enquiry.php:591
#: core/class-wcfm-enquiry.php:595 helpers/class-wcfm-install.php:394
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:68
msgid "Inquiry"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:353
msgid ""
"You have received reply for your \"{enquiry_for}\" inquiry. Please see our "
"response below: "
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:357
msgid "See details here"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:373
msgid "Reply to Inquiry"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:385
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:405
msgid "New reply received for Inquiry <b>%s</b>"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry.php:207
msgid "Last Reply By"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry.php:215
msgid "Reply"
msgstr ""

#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:90
#: controllers/notice/wcfm-controller-notices.php:81
msgid "OFF Line"
msgstr ""

#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:92
#: controllers/notice/wcfm-controller-notices.php:83
msgid "ON Line"
msgstr ""

#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:123
#: controllers/notice/wcfm-controller-notices.php:98
msgid "Publish - on line this now"
msgstr ""

#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:125
#: controllers/notice/wcfm-controller-notices.php:100
msgid "Archive - off line for the timing"
msgstr ""

#: controllers/listings/wcfm-controller-applications.php:153
#: controllers/listings/wcfm-controller-listings.php:175
msgid "Mark not filled"
msgstr ""

#: controllers/listings/wcfm-controller-applications.php:155
#: controllers/listings/wcfm-controller-listings.php:177
msgid "Mark filled"
msgstr ""

#: controllers/listings/wcfm-controller-applications.php:162
#: controllers/listings/wcfm-controller-listings.php:184
#: controllers/products/wcfm-controller-products.php:439
#: views/products-manager/wcfm-view-products-manage.php:518
msgid "No Featured"
msgstr ""

#: controllers/listings/wcfm-controller-applications.php:165
#: controllers/listings/wcfm-controller-listings.php:187
#: controllers/products/wcfm-controller-products.php:442
#: views/products-manager/wcfm-view-products-manage.php:521
msgid "Mark Featured"
msgstr ""

#: controllers/listings/wcfm-controller-applications.php:173
#: controllers/listings/wcfm-controller-listings.php:195
#: controllers/products/wcfm-controller-products.php:456
#: views/products-manager/wcfm-view-products-manage.php:510
msgid "Duplicate"
msgstr ""

#: controllers/listings/wcfm-controller-applications.php:180
#: controllers/listings/wcfm-controller-listings.php:208
msgid "Relist"
msgstr ""

#: controllers/listings/wcfm-controller-listings.php:25
#: controllers/products/wcfm-controller-products.php:27
#: core/class-wcfm-non-ajax.php:56 views/articles/wcfm-view-articles.php:13
#: views/listings/wcfm-view-listings.php:38
#: views/products/wcfm-view-products.php:13
#: views/vendors/wcfm-view-vendors.php:29
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:60
msgid "Pending"
msgstr ""

#: controllers/listings/wcfm-controller-listings.php:26
#: views/listings/wcfm-view-listings.php:39
msgid "Expired"
msgstr ""

#: controllers/listings/wcfm-controller-listings.php:27
#: views/articles/wcfm-view-articles-manage.php:145
#: views/articles/wcfm-view-articles-manage.php:438
#: views/listings/wcfm-view-listings.php:40
#: views/products-manager/wcfm-view-products-manage.php:483
#: views/products-manager/wcfm-view-products-manage.php:954
#: views/products-popup/wcfm-view-product-popup.php:326
msgid "Preview"
msgstr ""

#: controllers/listings/wcfm-controller-listings.php:28
msgid "Pending Payment"
msgstr ""

#: controllers/listings/wcfm-controller-listings.php:161
#: views/products-manager/wcfm-view-products-manage.php:934
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:88
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:119
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:125
msgid "Approve"
msgstr ""

#: controllers/listings/wcfm-controller-listings.php:201
msgid "Continue Submission"
msgstr ""

#: controllers/messages/wcfm-controller-message-sent.php:51
msgid "Message sent successfully"
msgstr ""

#: controllers/messages/wcfm-controller-messages.php:167
#: controllers/messages/wcfm-controller-messages.php:182
msgid "System"
msgstr ""

#: controllers/messages/wcfm-controller-messages.php:180
msgid "Affiliate"
msgstr ""

#: controllers/messages/wcfm-controller-messages.php:185
#: controllers/messages/wcfm-controller-messages.php:195
#: controllers/messages/wcfm-controller-messages.php:197
#: controllers/messages/wcfm-controller-messages.php:210
msgid "You"
msgstr ""

#: controllers/messages/wcfm-controller-messages.php:191
#: core/class-wcfm-vendor-support.php:1038
#: views/articles/wcfm-view-articles.php:10
#: views/listings/wcfm-view-listings.php:36
#: views/messages/wcfm-view-messages.php:61
#: views/products/wcfm-view-products.php:10
#: views/wc_bookings/wcfm-view-wcbookings.php:19
msgid "All"
msgstr ""

#: controllers/messages/wcfm-controller-messages.php:221
#: views/messages/wcfm-view-messages-send-reply.php:11
msgid "Send Reply"
msgstr ""

#: controllers/messages/wcfm-controller-messages.php:225
#: controllers/messages/wcfm-controller-messages.php:227
#: controllers/messages/wcfm-controller-messages.php:229
msgid "Approve / Reject"
msgstr ""

#: controllers/messages/wcfm-controller-messages.php:231
#: views/messages/wcfm-view-messages.php:55
msgid "Mark Read"
msgstr ""

#: controllers/notice/wcfm-controller-notice-manage.php:74
msgid ""
"A new announcement submitted. <a target=\"_blank\" "
"class=\"wcfm_dashboard_item_title\" href=\"%s\">%s</a>"
msgstr ""

#: controllers/orders/wcfm-controller-orders.php:261
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:318
#: views/orders/wcfm-view-orders-details.php:506
msgid "SKU:"
msgstr ""

#: controllers/orders/wcfm-controller-orders.php:315
#: controllers/orders/wcfm-controller-orders.php:319
#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:325
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:424
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:280
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:277
#: controllers/orders/wcfm-controller-wcvendors-orders.php:311
#: core/class-wcfm-wcmarketplace.php:545 core/class-wcfm-wcmarketplace.php:638
#: core/class-wcfm-wcmarketplace.php:688 core/class-wcfm-wcmarketplace.php:702
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:291
#: views/orders/wcfm-view-orders-details.php:957
#: views/orders/wcfm-view-orders-details.php:977
#: views/wc_bookings/wcfm-view-wcbookings-details.php:420
msgid "N/A"
msgstr ""

#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:328
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:427
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:284
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:280
msgid "UNPAID"
msgstr ""

#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:332
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:431
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:288
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:284
#: controllers/orders/wcfm-controller-wcvendors-orders.php:318
msgid "PAID"
msgstr ""

#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:336
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:435
msgid "REQUESTED"
msgstr ""

#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:340
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:439
msgid "CANCELLED"
msgstr ""

#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:381
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:485
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:336
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:317
#: controllers/orders/wcfm-controller-wcvendors-orders.php:368
msgid "Mark Shipped"
msgstr ""

#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:292
#: controllers/orders/wcfm-controller-wcvendors-orders.php:322
msgid "REVERSED"
msgstr ""

#: controllers/orders/wcfm-controller-wcpvendors-orders.php:223
msgid "<br /><small>( %1$s: %2$s )</small>"
msgstr ""

#: controllers/orders/wcfm-controller-wcpvendors-orders.php:231
msgid "%1$s %2$s: %3$s"
msgstr ""

#: controllers/orders/wcfm-controller-wcpvendors-orders.php:241
msgid "Product Not Found"
msgstr ""

#: controllers/orders/wcfm-controller-wcpvendors-orders.php:288
msgid "VOID"
msgstr ""

#: controllers/orders/wcfm-controller-wcvendors-orders.php:314
msgid "DUE"
msgstr ""

#: controllers/products/wcfm-controller-products.php:26
#: core/class-wcfm-non-ajax.php:56
#: views/articles/wcfm-view-articles-manage.php:431
#: views/articles/wcfm-view-articles.php:12
#: views/coupons/wcfm-view-coupons-manage.php:167
#: views/products/wcfm-view-products.php:12
#: views/products-manager/wcfm-view-products-manage.php:945
#: views/products-popup/wcfm-view-product-popup.php:319
msgid "Draft"
msgstr ""

#: controllers/products/wcfm-controller-products.php:28
#: core/class-wcfm-admin.php:549 views/products/wcfm-view-products.php:33
msgid "Archived"
msgstr ""

#: controllers/products/wcfm-controller-products.php:287
#: controllers/products/wcfm-controller-products.php:291
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:98
#: views/products-manager/wcfm-view-products-manage-tabs.php:33
#: views/products-manager/wcfm-view-products-manage-tabs.php:294
msgid "In stock"
msgstr ""

#: controllers/products/wcfm-controller-products.php:287
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:100
#: views/products-manager/wcfm-view-products-manage-tabs.php:33
#: views/products-manager/wcfm-view-products-manage-tabs.php:294
#: views/reports/wcfm-view-reports-menu.php:7
#: views/reports/wcfm-view-reports-menu.php:11
msgid "Out of stock"
msgstr ""

#: controllers/products/wcfm-controller-products.php:287
#: views/products-manager/wcfm-view-products-manage-tabs.php:33
#: views/products-manager/wcfm-view-products-manage-tabs.php:294
msgid "On backorder"
msgstr ""

#: controllers/products/wcfm-controller-products.php:307
#: views/articles/wcfm-view-articles-manage.php:184
#: views/articles/wcfm-view-articles-manage.php:287
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:89
#: views/products-manager/wcfm-view-products-manage.php:571
#: views/products-manager/wcfm-view-products-manage.php:715
#: views/products-popup/wcfm-view-product-popup.php:159
#: views/settings/wcfm-view-settings.php:579
msgid "Categories"
msgstr ""

#: controllers/products/wcfm-controller-products.php:348
#: views/capability/wcfm-view-capability.php:228
msgid "Grouped"
msgstr ""

#: controllers/products/wcfm-controller-products.php:350
msgid "Group By"
msgstr ""

#: controllers/products/wcfm-controller-products.php:352
msgid "External/Affiliate"
msgstr ""

#: controllers/products/wcfm-controller-products.php:356
#: views/products/wcfm-view-products.php:194
#: views/products-manager/wcfm-view-products-manage-tabs.php:282
#: views/products-manager/wcfm-view-products-manage.php:547
#: views/products-popup/wcfm-view-product-popup.php:127
msgid "Virtual"
msgstr ""

#: controllers/products/wcfm-controller-products.php:358
#: views/products/wcfm-view-products.php:190
#: views/products-manager/wcfm-view-products-manage-tabs.php:52
#: views/products-manager/wcfm-view-products-manage.php:548
#: views/products-popup/wcfm-view-product-popup.php:128
msgid "Downloadable"
msgstr ""

#: controllers/products/wcfm-controller-products.php:360
#: helpers/class-wcfm-setup-bak.php:413 helpers/class-wcfm-setup.php:413
#: views/capability/wcfm-view-capability.php:226
msgid "Simple"
msgstr ""

#: controllers/products/wcfm-controller-products.php:364
#: views/capability/wcfm-view-capability.php:227
msgid "Variable"
msgstr ""

#: controllers/products/wcfm-controller-products.php:366
msgid "Subscription"
msgstr ""

#: controllers/products/wcfm-controller-products.php:368
msgid "Variable Subscription"
msgstr ""

#: controllers/products/wcfm-controller-products.php:370
msgid "Listings Package"
msgstr ""

#: controllers/products/wcfm-controller-products.php:372
msgid "Resume Package"
msgstr ""

#: controllers/products/wcfm-controller-products.php:374
#: core/class-wcfm-integrations.php:327 core/class-wcfm-integrations.php:355
#: core/class-wcfm-integrations.php:543
msgid "Auction"
msgstr ""

#: controllers/products/wcfm-controller-products.php:376
#: core/class-wcfm-integrations.php:349 core/class-wcfm-integrations.php:493
msgid "Rental"
msgstr ""

#: controllers/products/wcfm-controller-products.php:378
#: views/customers/wcfm-view-customers-details.php:209
#: views/customers/wcfm-view-customers-details.php:220
#: views/wc_bookings/wcfm-view-wcbookings-details.php:176
#: views/wc_bookings/wcfm-view-wcbookings.php:119
#: views/wc_bookings/wcfm-view-wcbookings.php:131
msgid "Booking"
msgstr ""

#: controllers/products/wcfm-controller-products.php:380
msgid "Accommodation"
msgstr ""

#: controllers/products/wcfm-controller-products.php:382
#: views/customers/wcfm-view-customers-details.php:247
#: views/customers/wcfm-view-customers-details.php:258
#: views/customers/wcfm-view-customers.php:84
#: views/customers/wcfm-view-customers.php:100
msgid "Appointment"
msgstr ""

#: controllers/products/wcfm-controller-products.php:384
msgid "Bundle"
msgstr ""

#: controllers/products/wcfm-controller-products.php:386
msgid "Composite"
msgstr ""

#: controllers/products/wcfm-controller-products.php:388
#: views/capability/wcfm-view-capability.php:385
msgid "Lottery"
msgstr ""

#: controllers/products/wcfm-controller-products.php:421
msgid "Mark Approve / Publish"
msgstr ""

#: controllers/products/wcfm-controller-products.php:425
msgid "Mark Rejected"
msgstr ""

#: controllers/products/wcfm-controller-products.php:447
msgid "Featured Product: Upgrade your WCFM to WCFM Ultimate to avail this feature."
msgstr ""

#: controllers/products/wcfm-controller-products.php:459
msgid "Duplicate Product: Upgrade your WCFM to WCFM Ultimate to avail this feature."
msgstr ""

#: controllers/products/wcfm-controller-products.php:469
msgid "Archive Product"
msgstr ""

#: controllers/products-manager/wcfm-controller-products-manage.php:428
#. translators: %s: attachment id
msgid "#%s is an invalid image ID."
msgstr ""

#: controllers/products-manager/wcfm-controller-products-manage.php:509
msgid "Variation #%s of %s"
msgstr ""

#: controllers/profile/wcfm-controller-profile.php:277
msgid "Email verification code invalid."
msgstr ""

#: controllers/profile/wcfm-controller-profile.php:287
#: controllers/vendors/wcfm-controller-vendors-manage.php:49
msgid "Profile saved successfully"
msgstr ""

#: controllers/settings/wcfm-controller-dokan-settings.php:93
#: controllers/settings/wcfm-controller-settings.php:275
#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:250
#: controllers/settings/wcfm-controller-wcmarketplace-settings.php:290
#: controllers/settings/wcfm-controller-wcpvendors-settings.php:80
#: controllers/settings/wcfm-controller-wcvendors-settings.php:122
msgid "Settings saved successfully"
msgstr ""

#: controllers/settings/wcfm-controller-settings.php:209
#: core/class-wcfm-enquiry.php:532
#: includes/shortcodes/class-wcfm-shortcode-enquiry.php:33
#: views/enquiry/wcfm-view-enquiry-form.php:35
#: views/enquiry/wcfm-view-enquiry-tab.php:24
#: views/settings/wcfm-view-settings.php:53
msgid "Ask a Question"
msgstr ""

#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:135
#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:139
#: controllers/settings/wcfm-controller-wcmarketplace-settings.php:94
#: controllers/vendors/wcfm-controller-vendors-new.php:72
#: controllers/vendors/wcfm-controller-vendors-new.php:75
msgid "Shop Slug already exists."
msgstr ""

#: controllers/settings/wcfm-controller-wcpvendors-settings.php:82
msgid "Settings failed to save"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors-manage.php:81
msgid "Badges saved successfully"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors-new.php:230
msgid "New Account Created"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors-new.php:233
msgid ""
"Your account has been created and your role is: {user_role}. Use the below "
"details to log into the system."
msgstr ""

#: controllers/vendors/wcfm-controller-vendors-new.php:235
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:290
msgid "Store Name"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors-new.php:237
#: helpers/class-wcfm-install.php:102
msgid "Store Manager"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors-new.php:237
msgid "Click here ..."
msgstr ""

#: controllers/vendors/wcfm-controller-vendors-new.php:239
#: views/customers/wcfm-view-customers-manage.php:160
#: views/customers/wcfm-view-customers-manage.php:162
#: views/customers/wcfm-view-customers.php:78
#: views/customers/wcfm-view-customers.php:94
#: views/vendors/wcfm-view-vendors-new.php:90
msgid "Username"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors-new.php:269
msgid "A new vendor <b>%s</b> added ."
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:99
msgid "Store Off-line"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:102 core/class-wcfm.php:533
#: core/class-wcfm.php:589
msgid "Disable Vendor"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:104
msgid "Active Vendor"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:118
#: views/vendors/wcfm-view-vendors-manage.php:179
msgid "Email Verified"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:120
#: views/vendors/wcfm-view-vendors-manage.php:181
msgid "Email Verification Pending"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:157
msgid "Next payment on"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:176
#: controllers/vendors/wcfm-controller-vendors.php:181
#: controllers/vendors/wcfm-controller-vendors.php:187
#: controllers/vendors/wcfm-controller-vendors.php:190
#: controllers/vendors/wcfm-controller-vendors.php:198
msgid "Expiry on"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:190
#: controllers/vendors/wcfm-controller-vendors.php:198
msgid "Never Expire"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:238
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:82
msgid "Details"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:240 core/class-wcfm.php:727
#: views/capability/wcfm-view-capability.php:582
#: views/customers/wcfm-view-customers-details.php:278
#: views/customers/wcfm-view-customers.php:82
#: views/customers/wcfm-view-customers.php:98
#: views/orders/wcfm-view-orders.php:32
msgid "Orders"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:241
#: views/vendors/wcfm-view-vendors-manage.php:331
#: views/vendors/wcfm-view-vendors-manage.php:449
msgid "Sales Report"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:245
msgid "Disable Vendor Account"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:249
msgid "Enable Vendor Account"
msgstr ""

#: controllers/vendors/wcfm-controller-wcfmmarketplace-shipping-settings.php:33
msgid "Shipping Settings saved successfully"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:63
msgid "Booking schedule updated"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:64
msgid "Booking schedule updated."
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:70
msgid "Booking schedule updated successfully"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:72
msgid "Booking schedule updated failed!"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:78
#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:57
msgid "Completed"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:80
#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:59
#: views/withdrawal/dokan/wcfm-view-payments.php:53
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:39
#: views/withdrawal/wcfm/wcfm-view-payments.php:62
msgid "Processing"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:82
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:87
msgid "Cancel"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:90
#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:80
msgid "PayPal"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:92
#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:82
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:700
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:786
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:811
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:831
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:907
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:971
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:993
#: views/settings/wcfm-view-wcmarketplace-settings.php:388
#: views/settings/wcfm-view-wcmarketplace-settings.php:415
#: views/settings/wcfm-view-wcmarketplace-settings.php:436
#: views/settings/wcfm-view-wcmarketplace-settings.php:517
#: views/settings/wcfm-view-wcmarketplace-settings.php:564
#: views/settings/wcfm-view-wcmarketplace-settings.php:586
#: views/settings/wcfm-view-wcpvendors-settings.php:247
#: views/settings/wcfm-view-wcpvendors-settings.php:274
#: views/settings/wcfm-view-wcpvendors-settings.php:295
#: views/settings/wcfm-view-wcpvendors-settings.php:369
#: views/settings/wcfm-view-wcpvendors-settings.php:415
#: views/settings/wcfm-view-wcpvendors-settings.php:437
msgid "Stripe"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:94
msgid "Bank Transfer"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:61
#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal-request.php:46
msgid "Request successfully sent"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:63
#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal-request.php:54
msgid "Something went wrong please try again later"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:66
msgid "Withdraw amount must be greater than %d"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:69
msgid "You don't have enough balance for this request"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:72
msgid "Withdraw method required"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:75
msgid "Withdraw amount required "
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:56
msgid "Seller account balance not enough for this withdrawal."
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:109
msgid "Withdrawal Requests successfully approved."
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:111
msgid "No withdrawals selected for approve."
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:175
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:220
msgid "Withdrawal Requests successfully cancelled."
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:177
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:222
msgid "No withdrawals selected for cancel."
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:92
msgid "Withdrawal Approved"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:94
#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:118
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:132
msgid "Withdrawal Cancelled"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:96
msgid "Withdrawal Pending"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:116
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:130
msgid "Withdrawal Completed"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:120
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:134
msgid "Withdrawal Processing"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:132
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:143
#: helpers/class-wcfm-install.php:396
#: views/orders/wcfm-view-orders-details.php:421
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:122
msgid "Invoice"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:177
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:167
msgid "Auto Withdrawal"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:180
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:170
msgid "By Payment Type"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:182
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:172
msgid "By Request"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:184
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:174
msgid "By Auto Request"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:186
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:176
msgid "By Schedule Request"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:188
msgid "Split Pay"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:190
msgid "Wirecard Pay"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:108
msgid "Withdrawal Request successfully processed."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:110
msgid "Withdrawal Request processing failed, please contact Store Admin."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:115
msgid "Vendor <b>%s</b> has placed a Withdrawal Request #%s."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:132
msgid "Your withdrawal request successfully sent."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:137
msgid "Your withdrawal request failed, please try after sometime."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:140
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:143
#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal-request.php:60
msgid "No payment method selected for withdrawal commission"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:146
#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal-request.php:63
msgid "No commission selected for withdrawal"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:48
msgid "Manual Payment"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:102
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:167
msgid "Withdrawal Requests successfully processed."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:106
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:169
msgid "Withdrawal Requests partially processed, check log for more details."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:111
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:172
msgid "No withdrawals selected for approval."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse-actions.php:46
msgid "Reverse Withdrawal Requests successfully approveed."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse-actions.php:48
msgid "No reverse withdrawals selected for approve."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse.php:126
msgid "Reverse Pay Completed"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse.php:128
msgid "Reverse Pay Cancelled"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse.php:131
msgid "Reverse Pay Processing"
msgstr ""

#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:84
msgid "Direct Bank Transfer"
msgstr ""

#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal-request.php:57
msgid "Invalid payment method"
msgstr ""

#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal.php:73
msgid "Already Requested"
msgstr ""

#: core/class-wcfm-admin.php:111
msgid ""
"<h2>\r\n"
"\t\t\t\t\t\t\t\t\t\t Notice something missing in your marketplace?\r\n"
"\t\t\t\t\t\t\t\t\t\t </h2>"
msgstr ""

#: core/class-wcfm-admin.php:119
msgid ""
"<p>WooCommerce Frontend Manage - Ultimate is there to fill up all those for "
"you. Live Chat, Store Invoice, Support Ticket, Shipment Tracking, Direct "
"Messaging, Followers, Badges, Verificaton, Product Importer, Bulk Edit and "
"many more, almost a never ending features list for you.</p>"
msgstr ""

#: core/class-wcfm-admin.php:126
msgid "GET Ultimate"
msgstr ""

#: core/class-wcfm-admin.php:164
msgid ""
"<h2>\r\n"
"\t\t\t\t\t\t\t\t\t\t Now setup your vendor membership subscription in "
"minutes & it's FREE !!!\r\n"
"\t\t\t\t\t\t\t\t\t\t </h2>"
msgstr ""

#: core/class-wcfm-admin.php:167
msgid ""
"<p>A simple membership plugin for offering FREE AND PREMIUM SUBSCRIPTION "
"for your multi-vendor marketplace. You may set up unlimited membership "
"levels (example: free, silver, gold etc) with different pricing plan, "
"capabilities and commission. Also you will have Pay for Product option.</p>"
msgstr ""

#: core/class-wcfm-admin.php:211
msgid ""
"<h2>\r\n"
"\t\t\t\t\t\t\t\t\t\t Want to offer personalized experience for each of your "
"vendors?\r\n"
"\t\t\t\t\t\t\t\t\t\t </h2>"
msgstr ""

#: core/class-wcfm-admin.php:219
msgid ""
"<p>Get total control over how each of your vendors interacts with your "
"marketplace with WCFM - Groups & Staffs. Configure everything the way you "
"like - be it menu items, features, color scheme or commission - in groups "
"or individually.</p>"
msgstr ""

#: core/class-wcfm-admin.php:247 core/class-wcfm-admin.php:287
#: core/class-wcfm-admin.php:288 core/class-wcfm-admin.php:291
msgid "WCFM View"
msgstr ""

#: core/class-wcfm-admin.php:308 core/class-wcfm-admin.php:318
#: core/class-wcfm-admin.php:320 core/class-wcfm-admin.php:322
#: core/class-wcfm-wcvendors.php:196
msgid "WCFM Home"
msgstr ""

#: core/class-wcfm-admin.php:337
msgid "WCFM Page Settings"
msgstr ""

#: core/class-wcfm-admin.php:345
msgid "WCFM Page"
msgstr ""

#: core/class-wcfm-admin.php:367
msgid "WCFM totally works from front-end ... check dashboard settings %shere >>%s"
msgstr ""

#: core/class-wcfm-admin.php:370
msgid "This page should contain \"[wc_frontend_manager]\" short code"
msgstr ""

#: core/class-wcfm-admin.php:406 views/settings/wcfm-view-settings.php:298
msgid ""
"DO NOT USE WCFM DASHBOARD PAGE FOR OTHER PAGE SETTINGS, you will break your "
"site if you do."
msgstr ""

#: core/class-wcfm-admin.php:418
msgid "WC Frontend Manager"
msgstr ""

#: core/class-wcfm-admin.php:419
msgid "WCFM Options"
msgstr ""

#: core/class-wcfm-admin.php:441
msgid "Settings Saved"
msgstr ""

#: core/class-wcfm-admin.php:524
msgid "Vendor Dashboard Page"
msgstr ""

#: core/class-wcfm-admin.php:528
msgid "Vendor Membership Page"
msgstr ""

#: core/class-wcfm-admin.php:532
msgid "Vendor Registration Page"
msgstr ""

#: core/class-wcfm-admin.php:536
msgid "Affiliate Registration Page"
msgstr ""

#: core/class-wcfm-admin.php:585
msgid "WCfM Enquiry Form"
msgstr ""

#: core/class-wcfm-admin.php:586
msgid "WCfM Support Form"
msgstr ""

#: core/class-wcfm-admin.php:587
msgid "WCfM Registration Form"
msgstr ""

#: core/class-wcfm-admin.php:588
msgid "WCfM Refund Request Form"
msgstr ""

#: core/class-wcfm-ajax.php:142 core/class-wcfm-ajax.php:152
#: core/class-wcfm-ajax.php:180 core/class-wcfm-ajax.php:190
#: core/class-wcfm-ajax.php:200 core/class-wcfm-ajax.php:235
#: core/class-wcfm-ajax.php:247 core/class-wcfm-ajax.php:257
#: core/class-wcfm-ajax.php:267 core/class-wcfm-ajax.php:277
#: core/class-wcfm-ajax.php:287 core/class-wcfm-ajax.php:306
#: core/class-wcfm-ajax.php:316 core/class-wcfm-ajax.php:326
#: core/class-wcfm-ajax.php:336 core/class-wcfm-ajax.php:346
#: core/class-wcfm-ajax.php:356 core/class-wcfm-ajax.php:366
#: core/class-wcfm-ajax.php:381 core/class-wcfm-ajax.php:396
#: core/class-wcfm-ajax.php:406 core/class-wcfm-ajax.php:416
#: core/class-wcfm-ajax.php:426 core/class-wcfm-ajax.php:436
#: core/class-wcfm-ajax.php:446 core/class-wcfm-ajax.php:456
#: core/class-wcfm-ajax.php:466 core/class-wcfm-ajax.php:492
#: core/class-wcfm-ajax.php:565 core/class-wcfm-ajax.php:640
#: core/class-wcfm-ajax.php:685 core/class-wcfm-ajax.php:718
#: core/class-wcfm-ajax.php:752 core/class-wcfm-ajax.php:785
#: core/class-wcfm-ajax.php:816 core/class-wcfm-ajax.php:876
#: core/class-wcfm-ajax.php:952 core/class-wcfm-ajax.php:979
#: core/class-wcfm-ajax.php:1006 core/class-wcfm-ajax.php:1033
#: core/class-wcfm-ajax.php:1060 core/class-wcfm-ajax.php:1087
#: core/class-wcfm-ajax.php:1115 core/class-wcfm-ajax.php:1147
#: core/class-wcfm-ajax.php:1209 core/class-wcfm-ajax.php:1270
#: core/class-wcfm-ajax.php:1325 core/class-wcfm-ajax.php:1398
#: core/class-wcfm-ajax.php:1458 core/class-wcfm-ajax.php:1483
#: core/class-wcfm-ajax.php:1546 core/class-wcfm-ajax.php:1625
#: core/class-wcfm-ajax.php:1712 core/class-wcfm-ajax.php:1743
#: core/class-wcfm-article.php:234 core/class-wcfm-article.php:244
#: core/class-wcfm-article.php:267 core/class-wcfm-customer.php:303
#: core/class-wcfm-customer.php:313 core/class-wcfm-customer.php:328
#: core/class-wcfm-customer.php:338 core/class-wcfm-customer.php:348
#: core/class-wcfm-customer.php:395 core/class-wcfm-customer.php:429
#: core/class-wcfm-enquiry.php:282 core/class-wcfm-enquiry.php:297
#: core/class-wcfm-enquiry.php:404 core/class-wcfm-enquiry.php:431
#: core/class-wcfm-enquiry.php:458 core/class-wcfm-integrations.php:1629
#: core/class-wcfm-notification.php:985 core/class-wcfm-notification.php:1024
#: core/class-wcfm-notification.php:1070 core/class-wcfm-notification.php:1104
#: core/class-wcfm-wcbookings.php:309 core/class-wcfm-wcbookings.php:324
#: core/class-wcfm-wcmarketplace.php:862 core/class-wcfm-wcmarketplace.php:891
#: core/class-wcfm-withdrawal.php:450 core/class-wcfm-withdrawal.php:468
#: core/class-wcfm-withdrawal.php:486 core/class-wcfm-withdrawal.php:504
#: core/class-wcfm-withdrawal.php:522 core/class-wcfm-withdrawal.php:534
#: core/class-wcfm-withdrawal.php:552 core/class-wcfm-withdrawal.php:570
#: core/class-wcfm-withdrawal.php:582
msgid "You don&#8217;t have permission to do this."
msgstr ""

#: core/class-wcfm-ajax.php:520
msgid "A new %s <b>%s</b> added to the store by <b>%s</b>"
msgstr ""

#: core/class-wcfm-ajax.php:834 core/class-wcfm-ajax.php:908
msgid "<b>%s</b> order status updated to <b>%s</b> by <b>%s</b>"
msgstr ""

#: core/class-wcfm-ajax.php:886 core/class-wcfm-ajax.php:931
#: core/class-wcfm-ajax.php:934
msgid "Order status updated."
msgstr ""

#: core/class-wcfm-ajax.php:900
msgid "Order status updated to <b>%s</b> by <b>%s</b>"
msgstr ""

#: core/class-wcfm-ajax.php:1507
msgid "Email Verification Code"
msgstr ""

#: core/class-wcfm-ajax.php:1510
msgid "Here is your email verification code - <b>%s</b>"
msgstr ""

#: core/class-wcfm-ajax.php:1518
msgid "Email Verification"
msgstr ""

#: core/class-wcfm-ajax.php:1527
msgid "Email verification code send to your email."
msgstr ""

#: core/class-wcfm-ajax.php:1529
msgid "Email verification not working right now, please try after sometime."
msgstr ""

#: core/class-wcfm-ajax.php:1568
msgid "<b>%s</b> (Store: <b>%s</b>) has been disabled."
msgstr ""

#: core/class-wcfm-ajax.php:1587
msgid "Your Store: <b>%s</b> has been disabled."
msgstr ""

#: core/class-wcfm-ajax.php:1606
msgid "Vendor successfully disabled."
msgstr ""

#: core/class-wcfm-ajax.php:1609
msgid "Vendor can not be disabled right now, please try after sometime."
msgstr ""

#: core/class-wcfm-ajax.php:1655
msgid "<b>%s</b> (Store: <b>%s</b>) has been enabled."
msgstr ""

#: core/class-wcfm-ajax.php:1674
msgid "Your Store: <b>%s</b> has been enabled."
msgstr ""

#: core/class-wcfm-ajax.php:1693
msgid "Vendor successfully enabled."
msgstr ""

#: core/class-wcfm-ajax.php:1696
msgid "Vendor can not be enabled right now, please try after sometime."
msgstr ""

#: core/class-wcfm-ajax.php:1781 core/class-wcfm-library.php:1073
msgid "Please insert username before submit."
msgstr ""

#: core/class-wcfm-ajax.php:1786 core/class-wcfm-library.php:1073
msgid "Please insert password before submit."
msgstr ""

#: core/class-wcfm-ajax.php:1799 core/class-wcfm-ajax.php:1820
msgid "Please insert a valid username / e-mail address."
msgstr ""

#: core/class-wcfm-ajax.php:1810 core/class-wcfm-ajax.php:1815
#: core/class-wcfm-ajax.php:1831 core/class-wcfm-ajax.php:1836
msgid "Please try again!"
msgstr ""

#: core/class-wcfm-ajax.php:1812 core/class-wcfm-ajax.php:1833
msgid "Login successfully ..."
msgstr ""

#: core/class-wcfm-article.php:71
msgid "Articles Dashboard"
msgstr ""

#: core/class-wcfm-article.php:74
msgid "Articles Manager"
msgstr ""

#: core/class-wcfm-article.php:118 views/articles/wcfm-view-articles.php:32
#: views/capability/wcfm-view-capability.php:502
msgid "Articles"
msgstr ""

#: core/class-wcfm-capability.php:327
msgid "Products Limit: "
msgstr ""

#: core/class-wcfm-capability.php:335 core/class-wcfm-capability.php:340
#: core/class-wcfm-capability.php:343 core/class-wcfm-capability.php:581
#: core/class-wcfm-capability.php:586 core/class-wcfm-capability.php:590
#: core/class-wcfm-capability.php:948 core/class-wcfm-capability.php:953
#: core/class-wcfm-capability.php:969
msgid "remaining"
msgstr ""

#: core/class-wcfm-capability.php:346 core/class-wcfm-capability.php:593
#: core/class-wcfm-capability.php:972 core/class-wcfm-integrations.php:461
#: views/capability/wcfm-view-capability.php:628
#: views/products-manager/wcfm-view-products-manage-tabs.php:64
msgid "Unlimited"
msgstr ""

#: core/class-wcfm-capability.php:577
msgid "Articles Limit: "
msgstr ""

#: core/class-wcfm-capability.php:944
msgid "Customers Limit: "
msgstr ""

#: core/class-wcfm-catalog.php:54 views/capability/wcfm-view-capability.php:244
msgid "Catalog"
msgstr ""

#: core/class-wcfm-catalog.php:76 core/class-wcfm.php:844
msgid "Catalog Mode"
msgstr ""

#: core/class-wcfm-catalog.php:81
msgid "Disable Add to Cart?"
msgstr ""

#: core/class-wcfm-catalog.php:82
msgid "Hide Price?"
msgstr ""

#: core/class-wcfm-customer.php:91
msgid "Customers Dashboard"
msgstr ""

#: core/class-wcfm-customer.php:94
msgid "Customers Manager"
msgstr ""

#: core/class-wcfm-customer.php:97
msgid "Customers Details"
msgstr ""

#: core/class-wcfm-customer.php:143
#: views/capability/wcfm-view-capability.php:612
#: views/customers/wcfm-view-customers.php:25
msgid "Customers"
msgstr ""

#: core/class-wcfm-customer.php:218 core/class-wcfm-library.php:372
#: core/class-wcfm-library.php:501 core/class-wcfm-library.php:607
#: core/class-wcfm-library.php:656 helpers/class-wcfm-setup-bak.php:153
#: helpers/class-wcfm-setup.php:153
msgid "Clear"
msgstr ""

#: core/class-wcfm-customer.php:218 core/class-wcfm-library.php:372
#: core/class-wcfm-library.php:501 core/class-wcfm-library.php:607
#: core/class-wcfm-library.php:656 helpers/class-wcfm-setup-bak.php:153
#: helpers/class-wcfm-setup.php:153
#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:75
msgid "Default"
msgstr ""

#: core/class-wcfm-customer.php:218 core/class-wcfm-library.php:372
#: core/class-wcfm-library.php:501 core/class-wcfm-library.php:607
#: core/class-wcfm-library.php:656 helpers/class-wcfm-setup-bak.php:153
#: helpers/class-wcfm-setup.php:153
msgid "Select Color"
msgstr ""

#: core/class-wcfm-customer.php:412
msgid "New Customer"
msgstr ""

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:178
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:422
#: views/products-manager/wcfm-view-products-manage.php:429
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/products-popup/wcfm-view-product-popup.php:99
#: views/settings/wcfm-view-settings.php:573
msgid "Simple Product"
msgstr ""

#: core/class-wcfm-customfield-support.php:30
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
msgid "Virtual Product"
msgstr ""

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:178
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:422
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/settings/wcfm-view-settings.php:573
msgid "Variable Product"
msgstr ""

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:178
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:422
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/settings/wcfm-view-settings.php:573
msgid "Grouped Product"
msgstr ""

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:178
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:422
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/settings/wcfm-view-settings.php:573
msgid "External/Affiliate Product"
msgstr ""

#: core/class-wcfm-customfield-support.php:32
msgid "Do not show"
msgstr ""

#: core/class-wcfm-customfield-support.php:38
#: core/class-wcfm-customfield-support.php:42
msgid "Product Custom Field"
msgstr ""

#: core/class-wcfm-customfield-support.php:47
msgid "Custom Fields"
msgstr ""

#: core/class-wcfm-customfield-support.php:47
msgid ""
"You can integrate any Third Party plugin using Custom Fields, but you "
"should use the same fields name as used by Third Party plugins."
msgstr ""

#: core/class-wcfm-customfield-support.php:48
#: core/class-wcfm-integrations.php:906 core/class-wcfm-integrations.php:916
#: helpers/class-wcfm-setup.php:774
#: views/products-manager/wcfm-view-products-manage-tabs.php:281
#: views/settings/wcfm-view-settings.php:482
#: views/settings/wcfm-view-settings.php:548
msgid "Enable"
msgstr ""

#: core/class-wcfm-customfield-support.php:49
msgid "Block Name"
msgstr ""

#: core/class-wcfm-customfield-support.php:50
msgid "Exlude Product Types"
msgstr ""

#: core/class-wcfm-customfield-support.php:50
msgid "Choose product types for which you want to disable this field block."
msgstr ""

#: core/class-wcfm-customfield-support.php:51
msgid "Visibility"
msgstr ""

#: core/class-wcfm-customfield-support.php:51
msgid ""
"Set where and how you want to visible this custom field block in single "
"product page."
msgstr ""

#: core/class-wcfm-customfield-support.php:52
msgid "Fields as Group?"
msgstr ""

#: core/class-wcfm-customfield-support.php:53
msgid "Group name"
msgstr ""

#: core/class-wcfm-customfield-support.php:54
msgid "Fields"
msgstr ""

#: core/class-wcfm-customfield-support.php:55
#: views/settings/wcfm-view-settings.php:549
msgid "Field Type"
msgstr ""

#: core/class-wcfm-customfield-support.php:56
#: views/settings/wcfm-view-settings.php:483
#: views/settings/wcfm-view-settings.php:550
#: views/settings/wcfm-view-wcmarketplace-settings.php:681
msgid "Label"
msgstr ""

#: core/class-wcfm-customfield-support.php:57 core/class-wcfm-frontend.php:1077
#: views/articles/wcfm-view-articles.php:110
#: views/articles/wcfm-view-articles.php:121
#: views/customers/wcfm-view-customers.php:77
#: views/customers/wcfm-view-customers.php:93
#: views/enquiry/wcfm-view-enquiry-form.php:52
#: views/products/wcfm-view-products.php:232
#: views/products/wcfm-view-products.php:256
#: views/products-manager/wcfm-view-products-manage-tabs.php:59
#: views/products-manager/wcfm-view-products-manage-tabs.php:174
#: views/products-manager/wcfm-view-products-manage.php:798
msgid "Name"
msgstr ""

#: core/class-wcfm-customfield-support.php:57
msgid ""
"This is will going to use as `meta_key` for storing this field value in "
"database."
msgstr ""

#: core/class-wcfm-customfield-support.php:58
#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:78
#: views/settings/wcfm-view-settings.php:551
msgid "Options"
msgstr ""

#: core/class-wcfm-customfield-support.php:58
msgid ""
"Insert option values | separated, leave first element empty to show as "
"'-Select-'"
msgstr ""

#: core/class-wcfm-customfield-support.php:59
#: views/integrations/wcfm-view-integrations-products-manage.php:223
#: views/notice/wcfm-view-notice-manage.php:88
#: views/products-popup/wcfm-view-product-popup.php:277
msgid "Content"
msgstr ""

#: core/class-wcfm-customfield-support.php:60
#: views/settings/wcfm-view-settings.php:552
msgid "Help Content"
msgstr ""

#: core/class-wcfm-customfield-support.php:61
#: views/settings/wcfm-view-settings.php:553
msgid "Required?"
msgstr ""

#: core/class-wcfm-dokan.php:124 core/class-wcfm-wcmarketplace.php:136
#: core/class-wcfm-wcmarketplace.php:137 core/class-wcfm-wcpvendors.php:122
#: core/class-wcfm-wcvendors.php:151 views/settings/wcfm-view-settings.php:37
#: views/wcfm-view-header-panels.php:35 views/wcfm-view-menu.php:79
msgid "My Store"
msgstr ""

#: core/class-wcfm-dokan.php:128 core/class-wcfm-vendor-support.php:1270
#: core/class-wcfm-vendor-support.php:1278
#: core/class-wcfm-vendor-support.php:1286
#: core/class-wcfm-vendor-support.php:1297
#: core/class-wcfm-vendor-support.php:1306
#: core/class-wcfm-wcfmmarketplace.php:128
#: core/class-wcfm-wcmarketplace.php:140 core/class-wcfm-wcpvendors.php:126
#: core/class-wcfm-wcvendors.php:155
msgid "Shop"
msgstr ""

#: core/class-wcfm-dokan.php:453 core/class-wcfm-wcfmmarketplace.php:1127
#: core/class-wcfm-wcmarketplace.php:697 core/class-wcfm-wcpvendors.php:561
#: core/class-wcfm-wcvendors.php:870
msgid "Total Earning"
msgstr ""

#: core/class-wcfm-enquiry.php:143
msgid "Enquiry Dashboard"
msgstr ""

#: core/class-wcfm-enquiry.php:146
msgid "Enquiry Manager"
msgstr ""

#: core/class-wcfm-enquiry.php:352 core/class-wcfm-enquiry.php:366
#: core/class-wcfm-enquiry.php:630
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:71
msgid "Inquiries"
msgstr ""

#: core/class-wcfm-enquiry.php:472
msgid "Unable to delete"
msgstr ""

#: core/class-wcfm-enquiry.php:479
msgid "Successfully deleted"
msgstr ""

#: core/class-wcfm-enquiry.php:490 helpers/wcfm-core-functions.php:1520
#: views/enquiry/wcfm-view-enquiry-manage.php:93
#: views/enquiry/wcfm-view-enquiry-tab.php:53
#: views/enquiry/wcfm-view-enquiry.php:44
msgid "Enquiries"
msgstr ""

#: core/class-wcfm-enquiry.php:642 core/class-wcfm-notification.php:368
msgid "Show All"
msgstr ""

#: core/class-wcfm-enquiry.php:645
msgid "There is no enquiry yet!!"
msgstr ""

#: core/class-wcfm-enquiry.php:693
#: views/enquiry/wcfm-view-enquiry-manage.php:282
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:232
#: views/listings/wcfm-view-applications.php:62
#: views/listings/wcfm-view-applications.php:75
msgid "Attachment(s)"
msgstr ""

#: core/class-wcfm-enquiry.php:719 helpers/wcfm-core-functions.php:1131
msgid "New Enquiry"
msgstr ""

#: core/class-wcfm-frontend.php:319 views/dashboard/wcfm-view-dashboard.php:99
#: views/dashboard/wcfm-view-dokan-dashboard.php:123
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:128
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:144
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:152
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:130
#: views/settings/wcfm-view-settings.php:142
#: views/settings/wcfm-view-settings.php:298
msgid "Dashboard"
msgstr ""

#: core/class-wcfm-frontend.php:1010
msgid "First choose product category to get associated attributes."
msgstr ""

#: core/class-wcfm-frontend.php:1076
#: views/products-manager/wcfm-view-products-manage-tabs.php:173
msgid "Active?"
msgstr ""

#: core/class-wcfm-frontend.php:1076
msgid "Check to associate this attribute with the product"
msgstr ""

#: core/class-wcfm-frontend.php:1079
#: views/products-manager/wcfm-view-products-manage-tabs.php:176
msgid "Visible on the product page"
msgstr ""

#: core/class-wcfm-frontend.php:1080
#: views/products-manager/wcfm-view-products-manage-tabs.php:177
msgid "Use as Variation"
msgstr ""

#: core/class-wcfm-integrations.php:218
msgid "Listings Dashboard"
msgstr ""

#: core/class-wcfm-integrations.php:222
msgid "Applications Dashboard"
msgstr ""

#: core/class-wcfm-integrations.php:280 core/class-wcfm-integrations.php:394
#: helpers/class-wcfm-setup-bak.php:950 helpers/class-wcfm-setup.php:1042
#: views/listings/wcfm-view-listings.php:27
msgid "Listings"
msgstr ""

#: core/class-wcfm-integrations.php:313 core/class-wcfm-integrations.php:343
msgid "Listing Package"
msgstr ""

#: core/class-wcfm-integrations.php:320
msgid "Rental Product"
msgstr ""

#: core/class-wcfm-integrations.php:375 core/class-wcfm-integrations.php:391
#: core/class-wcfm-integrations.php:394
msgid "Manage Listings"
msgstr ""

#: core/class-wcfm-integrations.php:381
msgid "Edit Listing"
msgstr ""

#: core/class-wcfm-integrations.php:383
msgid "Manage Listing"
msgstr ""

#: core/class-wcfm-integrations.php:385
msgid "Add Listing"
msgstr ""

#: core/class-wcfm-integrations.php:388
#: views/articles/wcfm-view-articles-manage.php:153
#: views/articles/wcfm-view-articles.php:60
#: views/coupons/wcfm-view-coupons-manage.php:106
#: views/coupons/wcfm-view-coupons.php:39
#: views/customers/wcfm-view-customers-details.php:111
#: views/customers/wcfm-view-customers-manage.php:135
#: views/customers/wcfm-view-customers.php:37
#: views/listings/wcfm-view-listings.php:86
#: views/orders/wcfm-view-orders-details.php:133
#: views/orders/wcfm-view-orders.php:80
#: views/products/wcfm-view-products-export.php:45
#: views/products/wcfm-view-products.php:83
#: views/products-manager/wcfm-view-products-manage.php:495
#: views/products-manager/wcfm-view-products-manage.php:499
#: views/reports/wcfm-view-reports-out-of-stock.php:37
#: views/reports/wcfm-view-reports-sales-by-date.php:78
#: views/wc_bookings/wcfm-view-wcbookings-details.php:75
#: views/wc_bookings/wcfm-view-wcbookings.php:64
msgid "WP Admin View"
msgstr ""

#: core/class-wcfm-integrations.php:392
#: views/listings/wcfm-view-listings.php:91
msgid "Add New Listing"
msgstr ""

#: core/class-wcfm-integrations.php:392 core/class-wcfm-non-ajax.php:301
#: helpers/wcfm-core-functions.php:1320
#: views/articles/wcfm-view-articles-manage.php:158
#: views/articles/wcfm-view-articles.php:65
#: views/coupons/wcfm-view-coupons-manage.php:111
#: views/coupons/wcfm-view-coupons.php:43
#: views/customers/wcfm-view-customers-details.php:122
#: views/customers/wcfm-view-customers-manage.php:142
#: views/customers/wcfm-view-customers.php:42
#: views/knowledgebase/wcfm-view-knowledgebase.php:38
#: views/listings/wcfm-view-listings.php:91
#: views/notice/wcfm-view-notices.php:37
#: views/products/wcfm-view-products-export.php:64
#: views/products/wcfm-view-products.php:124
#: views/products-manager/wcfm-view-products-manage.php:505
#: views/vendors/wcfm-view-vendors-manage.php:208
#: views/vendors/wcfm-view-vendors-new.php:75
#: views/vendors/wcfm-view-vendors.php:28 views/wcfm-view-menu.php:152
msgid "Add New"
msgstr ""

#: core/class-wcfm-integrations.php:460
msgid "Subscription Type"
msgstr ""

#: core/class-wcfm-integrations.php:460
msgid ""
"Link the subscription to the package (renew listing limit every "
"subscription term)"
msgstr ""

#: core/class-wcfm-integrations.php:460
msgid ""
"Link the subscription to posted listings (renew posted listings every "
"subscription term)"
msgstr ""

#: core/class-wcfm-integrations.php:460
msgid "Choose how subscriptions affect this package"
msgstr ""

#: core/class-wcfm-integrations.php:461
msgid "Job listing limit"
msgstr ""

#: core/class-wcfm-integrations.php:461
msgid "The number of job listings a user can post with this package."
msgstr ""

#: core/class-wcfm-integrations.php:462
msgid "Job listing duration"
msgstr ""

#: core/class-wcfm-integrations.php:462
msgid "The number of days that the job listing will be active."
msgstr ""

#: core/class-wcfm-integrations.php:463
msgid "Feature Listings?"
msgstr ""

#: core/class-wcfm-integrations.php:463
msgid "Feature this job listing - it will be styled differently and sticky."
msgstr ""

#: core/class-wcfm-integrations.php:498
msgid "Set Price Type"
msgstr ""

#: core/class-wcfm-integrations.php:498
msgid "General Pricing"
msgstr ""

#: core/class-wcfm-integrations.php:498
msgid "Choose a price type - this controls the schema."
msgstr ""

#: core/class-wcfm-integrations.php:499
msgid "Hourly Price"
msgstr ""

#: core/class-wcfm-integrations.php:499
msgid "Hourly price will be applicabe if booking or rental days min 1day"
msgstr ""

#: core/class-wcfm-integrations.php:499 core/class-wcfm-integrations.php:500
msgid "Enter price here"
msgstr ""

#: core/class-wcfm-integrations.php:500
msgid "General Price"
msgstr ""

#: core/class-wcfm-integrations.php:506
#: views/products-manager/wcfm-view-wcbookings-products-manage.php:57
msgid "Availability"
msgstr ""

#: core/class-wcfm-integrations.php:511
msgid "Product Availabilities"
msgstr ""

#: core/class-wcfm-integrations.php:511
msgid "Please select the date range to be disabled for the product."
msgstr ""

#: core/class-wcfm-integrations.php:512 views/coupons/wcfm-view-coupons.php:56
#: views/coupons/wcfm-view-coupons.php:67
#: views/messages/wcfm-view-messages.php:77
#: views/messages/wcfm-view-messages.php:90
#: views/products/wcfm-view-products.php:238
#: views/products/wcfm-view-products.php:262
msgid "Type"
msgstr ""

#: core/class-wcfm-integrations.php:512
msgid "Custom Date"
msgstr ""

#: core/class-wcfm-integrations.php:513 core/class-wcfm-integrations.php:908
#: views/messages/wcfm-view-messages.php:79
#: views/messages/wcfm-view-messages.php:92
#: views/products-manager/wcfm-view-products-manage-tabs.php:289
#: views/products-manager/wcfm-view-products-manage.php:558
#: views/products-popup/wcfm-view-product-popup.php:138
#: views/settings/wcfm-view-dokan-settings.php:532
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1160
#: views/settings/wcfm-view-wcmarketplace-settings.php:760
#: views/settings/wcfm-view-wcpvendors-settings.php:154
#: views/settings/wcfm-view-wcvendors-settings.php:455
msgid "From"
msgstr ""

#: core/class-wcfm-integrations.php:514
#: views/messages/wcfm-view-messages.php:80
#: views/messages/wcfm-view-messages.php:93
#: views/products-manager/wcfm-view-products-manage-tabs.php:290
#: views/products-manager/wcfm-view-products-manage.php:559
#: views/products-popup/wcfm-view-product-popup.php:139
#: views/settings/wcfm-view-dokan-settings.php:533
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1161
#: views/settings/wcfm-view-wcmarketplace-settings.php:761
#: views/settings/wcfm-view-wcpvendors-settings.php:155
#: views/settings/wcfm-view-wcvendors-settings.php:456
msgid "To"
msgstr ""

#: core/class-wcfm-integrations.php:515 core/class-wcfm-wcbookings.php:190
msgid "Bookable"
msgstr ""

#: core/class-wcfm-integrations.php:515
msgid "NO"
msgstr ""

#: core/class-wcfm-integrations.php:548
msgid "Auction Date From"
msgstr ""

#: core/class-wcfm-integrations.php:549
msgid "Auction Date To"
msgstr ""

#: core/class-wcfm-integrations.php:581
msgid "Has Voucher"
msgstr ""

#: core/class-wcfm-integrations.php:609
msgid "-- Choose Template --"
msgstr ""

#: core/class-wcfm-integrations.php:615
msgid "Voucher Template"
msgstr ""

#: core/class-wcfm-integrations.php:615
msgid "Select a voucher template to make this into a voucher product."
msgstr ""

#: core/class-wcfm-integrations.php:636 core/class-wcfm-integrations.php:782
msgid "Service"
msgstr ""

#: core/class-wcfm-integrations.php:637
msgid "Diff. Taxation"
msgstr ""

#: core/class-wcfm-integrations.php:659 core/class-wcfm-integrations.php:783
msgid "Optional Mini Description"
msgstr ""

#: core/class-wcfm-integrations.php:659 core/class-wcfm-integrations.php:783
msgid ""
"This content will be shown as short product description within checkout and "
"emails."
msgstr ""

#: core/class-wcfm-integrations.php:703 core/class-wcfm-integrations.php:773
msgid "Sale Label"
msgstr ""

#: core/class-wcfm-integrations.php:703 core/class-wcfm-integrations.php:704
#: core/class-wcfm-integrations.php:773 core/class-wcfm-integrations.php:774
msgid "Select Price Label"
msgstr ""

#: core/class-wcfm-integrations.php:703 core/class-wcfm-integrations.php:773
msgid ""
"If the product is on sale you may want to show a price label right before "
"outputting the old price to inform the customer."
msgstr ""

#: core/class-wcfm-integrations.php:704 core/class-wcfm-integrations.php:774
msgid "Sale Regular Label"
msgstr ""

#: core/class-wcfm-integrations.php:704 core/class-wcfm-integrations.php:774
msgid ""
"If the product is on sale you may want to show a price label right before "
"outputting the new price to inform the customer."
msgstr ""

#: core/class-wcfm-integrations.php:705
#: views/integrations/wcfm-view-integrations-products-manage.php:285
msgid "Unit"
msgstr ""

#: core/class-wcfm-integrations.php:705
msgid "Select unit"
msgstr ""

#: core/class-wcfm-integrations.php:705
msgid "Needed if selling on a per unit basis"
msgstr ""

#: core/class-wcfm-integrations.php:706 core/class-wcfm-integrations.php:775
msgid "Product Units"
msgstr ""

#: core/class-wcfm-integrations.php:706 core/class-wcfm-integrations.php:775
msgid "Number of units included per default product price. Example: 1000 ml."
msgstr ""

#: core/class-wcfm-integrations.php:707
msgid "Base Price Units"
msgstr ""

#: core/class-wcfm-integrations.php:707
msgid ""
"Base price units. Example base price: 0,99 € / 100 ml. Insert 100 as base "
"price unit amount."
msgstr ""

#: core/class-wcfm-integrations.php:708
msgid "Calculation"
msgstr ""

#: core/class-wcfm-integrations.php:708
msgid "Calculate base prices automatically."
msgstr ""

#: core/class-wcfm-integrations.php:709 core/class-wcfm-integrations.php:776
msgid "Regular Base Price"
msgstr ""

#: core/class-wcfm-integrations.php:710 core/class-wcfm-integrations.php:777
msgid "Sale Base Price"
msgstr ""

#: core/class-wcfm-integrations.php:711 core/class-wcfm-integrations.php:778
msgid "Minimum Age"
msgstr ""

#: core/class-wcfm-integrations.php:711 core/class-wcfm-integrations.php:778
msgid "Adds an age verification checkbox while purchasing this product."
msgstr ""

#: core/class-wcfm-integrations.php:740 core/class-wcfm-integrations.php:763
msgid "Select Delivery Time"
msgstr ""

#: core/class-wcfm-integrations.php:748 core/class-wcfm-integrations.php:779
msgid "Delivery Time"
msgstr ""

#: core/class-wcfm-integrations.php:749
msgid "Free shipping?"
msgstr ""

#: core/class-wcfm-integrations.php:749
msgid "This option disables the \"plus shipping costs\" notice on product page"
msgstr ""

#: core/class-wcfm-integrations.php:839
msgid "Scheduler Config"
msgstr ""

#: core/class-wcfm-integrations.php:900
msgid "Availability Scheduler"
msgstr ""

#: core/class-wcfm-integrations.php:906
#: views/articles/wcfm-view-articles.php:111
#: views/articles/wcfm-view-articles.php:122
#: views/customers/wcfm-view-customers-details.php:208
#: views/customers/wcfm-view-customers-details.php:219
#: views/customers/wcfm-view-customers-details.php:246
#: views/customers/wcfm-view-customers-details.php:257
#: views/customers/wcfm-view-customers-details.php:284
#: views/customers/wcfm-view-customers-details.php:294
#: views/knowledgebase/wcfm-view-knowledgebase.php:74
#: views/knowledgebase/wcfm-view-knowledgebase.php:86
#: views/listings/wcfm-view-applications.php:57
#: views/listings/wcfm-view-applications.php:70
#: views/listings/wcfm-view-listings.php:120
#: views/listings/wcfm-view-listings.php:135
#: views/notice/wcfm-view-notices.php:54 views/notice/wcfm-view-notices.php:65
#: views/orders/wcfm-view-orders.php:97 views/orders/wcfm-view-orders.php:128
#: views/products/wcfm-view-products.php:234
#: views/products/wcfm-view-products.php:258
#: views/products-manager/wcfm-view-products-manage-tabs.php:227
#: views/vendors/wcfm-view-vendors.php:75
#: views/vendors/wcfm-view-vendors.php:96
#: views/wc_bookings/wcfm-view-wcbookings.php:118
#: views/wc_bookings/wcfm-view-wcbookings.php:130
#: views/withdrawal/dokan/wcfm-view-payments.php:67
#: views/withdrawal/dokan/wcfm-view-payments.php:76
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:53
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:63
#: views/withdrawal/wcfm/wcfm-view-payments.php:75
#: views/withdrawal/wcfm/wcfm-view-payments.php:90
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:144
#: views/withdrawal/wcmp/wcfm-view-payments.php:63
#: views/withdrawal/wcmp/wcfm-view-payments.php:74
msgid "Status"
msgstr ""

#: core/class-wcfm-integrations.php:906 core/class-wcfm-integrations.php:916
msgid "Disable"
msgstr ""

#: core/class-wcfm-integrations.php:908
msgid "Start Time"
msgstr ""

#: core/class-wcfm-integrations.php:912
msgid "End Time"
msgstr ""

#: core/class-wcfm-integrations.php:912
#: views/products-manager/wcfm-view-products-manage-tabs.php:290
#: views/products-manager/wcfm-view-products-manage.php:559
#: views/products-popup/wcfm-view-product-popup.php:139
#: views/settings/wcfm-view-dokan-settings.php:533
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1161
#: views/settings/wcfm-view-wcmarketplace-settings.php:761
#: views/settings/wcfm-view-wcpvendors-settings.php:155
#: views/settings/wcfm-view-wcvendors-settings.php:456
msgid "Upto"
msgstr ""

#: core/class-wcfm-integrations.php:916
msgid "CountDown"
msgstr ""

#: core/class-wcfm-integrations.php:922
msgid "Note: Start time and End time will be on GMT, Current GMT time is"
msgstr ""

#: core/class-wcfm-integrations.php:948
msgid "Country restrictions"
msgstr ""

#: core/class-wcfm-integrations.php:954
msgid "Restriction rule"
msgstr ""

#: core/class-wcfm-integrations.php:954
msgid "Product Available for all countries"
msgstr ""

#: core/class-wcfm-integrations.php:954
msgid "Product Available for selected countries"
msgstr ""

#: core/class-wcfm-integrations.php:954
msgid "Product not Available for selected countries"
msgstr ""

#: core/class-wcfm-integrations.php:956
msgid "Select countries"
msgstr ""

#: core/class-wcfm-integrations.php:981 core/class-wcfm-integrations.php:983
#: helpers/class-wcfm-setup-bak.php:837 helpers/class-wcfm-setup.php:917
#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:52
msgid "Fixed"
msgstr ""

#: core/class-wcfm-integrations.php:983
#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:54
msgid "Percentage"
msgstr ""

#: core/class-wcfm-integrations.php:988
#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:64
msgid "Minimum quantity"
msgstr ""

#: core/class-wcfm-integrations.php:988
#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:69
msgid "Set if you are selling the product from qty more than 1"
msgstr ""

#: core/class-wcfm-integrations.php:990
#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:88
msgid "Tiered pricing type"
msgstr ""

#: core/class-wcfm-integrations.php:992 core/class-wcfm-integrations.php:998
#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:96
#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:117
msgid "Tiered price"
msgstr ""

#: core/class-wcfm-integrations.php:993 core/class-wcfm-integrations.php:999
#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:103
#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:124
#: views/orders/wcfm-view-orders.php:101 views/orders/wcfm-view-orders.php:132
msgid "Quantity"
msgstr ""

#: core/class-wcfm-integrations.php:994
#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:109
#: views/products/wcfm-view-products.php:236
#: views/products/wcfm-view-products.php:260
#: views/products-manager/wcfm-view-products-manage.php:556
#: views/products-popup/wcfm-view-product-popup.php:136
#: views/settings/wcfm-view-wcmarketplace-settings.php:680
msgid "Price"
msgstr ""

#: core/class-wcfm-integrations.php:1000
#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:130
msgid "Percent discount"
msgstr ""

#: core/class-wcfm-integrations.php:1080 core/class-wcfm-integrations.php:1086
#: core/class-wcfm-integrations.php:1129
msgid "Select"
msgstr ""

#: core/class-wcfm-integrations.php:1086
msgid "Use the default"
msgstr ""

#: core/class-wcfm-integrations.php:1092 core/class-wcfm-integrations.php:1135
msgid "Delivery Time:"
msgstr ""

#: core/class-wcfm-integrations.php:1093
msgid "Alternative Shipping Information"
msgstr ""

#: core/class-wcfm-integrations.php:1093
msgid ""
"Instead of the general shipping information you can enter a special "
"information just for this product."
msgstr ""

#: core/class-wcfm-integrations.php:1094
msgid "Disable Shipping Information"
msgstr ""

#: core/class-wcfm-integrations.php:1094
msgid ""
"Don’t display shipping information for this product (e.g. if it is "
"virtual/digital)."
msgstr ""

#: core/class-wcfm-integrations.php:1095 core/class-wcfm-integrations.php:1138
msgid "Sale Label:"
msgstr ""

#: core/class-wcfm-integrations.php:1099 core/class-wcfm-integrations.php:1142
msgid "GTIN"
msgstr ""

#: core/class-wcfm-integrations.php:1123 core/class-wcfm-integrations.php:1129
#: core/class-wcfm-integrations.php:1136 core/class-wcfm-integrations.php:1137
#: views/products-manager/wcfm-view-products-manage.php:370
#: views/products-manager/wcfm-view-products-manage.php:382
#: views/products-popup/wcfm-view-product-popup.php:55
#: views/products-popup/wcfm-view-product-popup.php:67
msgid "Same as parent"
msgstr ""

#: core/class-wcfm-integrations.php:1136
#: views/integrations/wcfm-view-wc-german-market-products-manage.php:55
msgid "Price per Unit"
msgstr ""

#: core/class-wcfm-integrations.php:1137
msgid "Shipping Information"
msgstr ""

#: core/class-wcfm-integrations.php:1200
#: core/class-wcfm-wcfmmarketplace.php:872
#: core/class-wcfm-wcmarketplace.php:490 core/class-wcfm-wcmarketplace.php:645
#: core/class-wcfm-wcpvendors.php:428 core/class-wcfm-wcpvendors.php:533
#: core/class-wcfm-wcvendors.php:640 core/class-wcfm-wcvendors.php:653
#: core/class-wcfm-wcvendors.php:824 core/class-wcfm-wcvendors.php:847
#: helpers/class-wcfm-install.php:385
#: includes/reports/class-dokan-report-sales-by-date.php:845
#: includes/reports/class-wcfm-report-sales-by-date.php:721
#: views/capability/wcfm-view-capability.php:239
#: views/customers/wcfm-view-customers-manage.php:223
#: views/orders/wcfm-view-orders-details.php:653
#: views/orders/wcfm-view-orders-details.php:888
#: views/products-manager/wcfm-view-products-manage-tabs.php:100
#: views/products-manager/wcfm-view-products-manage-tabs.php:259
#: views/profile/wcfm-view-profile.php:306
#: views/settings/wcfm-view-dokan-settings.php:383
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1036
#: views/settings/wcfm-view-wcmarketplace-settings.php:620
#: views/settings/wcfm-view-wcvendors-settings.php:355
#: views/vendors/wcfm-view-vendors-new.php:131
msgid "Shipping"
msgstr ""

#: core/class-wcfm-integrations.php:1205
msgid "Shipment Origin Information"
msgstr ""

#: core/class-wcfm-integrations.php:1326
msgid "Type the size chart name"
msgstr ""

#: core/class-wcfm-integrations.php:1362
msgid "Search/Select Size Chart"
msgstr ""

#: core/class-wcfm-integrations.php:1448
msgid "Post Expirator"
msgstr ""

#: core/class-wcfm-integrations.php:1450
msgid "Enable Post Expiration"
msgstr ""

#: core/class-wcfm-integrations.php:1453
msgid "The published date/time will be used as the expiration value"
msgstr ""

#: core/class-wcfm-integrations.php:1456 core/class-wcfm-library.php:1325
#: core/class-wcfm-library.php:1329
#: includes/reports/class-dokan-report-sales-by-date.php:654
#: includes/reports/class-wcfm-report-analytics.php:141
#: includes/reports/class-wcfm-report-sales-by-date.php:577
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:306
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:259
#: includes/reports/class-wcpvendors-report-sales-by-date.php:253
#: includes/reports/class-wcvendors-report-sales-by-date.php:283
#: views/enquiry/wcfm-view-enquiry.php:28
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:29
#: views/reports/wcfm-view-reports-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:56
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:29
msgid "Year"
msgstr ""

#: core/class-wcfm-integrations.php:1457 core/class-wcfm-library.php:1322
#: core/class-wcfm-library.php:1328
msgid "Month"
msgstr ""

#: core/class-wcfm-integrations.php:1458 core/class-wcfm-library.php:1291
msgid "Day"
msgstr ""

#: core/class-wcfm-integrations.php:1490 core/class-wcfm-library.php:1338
msgid "Hour"
msgstr ""

#: core/class-wcfm-integrations.php:1491 core/class-wcfm-library.php:1339
msgid "Minute"
msgstr ""

#: core/class-wcfm-integrations.php:1511
msgid "How to expire"
msgstr ""

#: core/class-wcfm-library.php:381
msgid "Too short"
msgstr ""

#: core/class-wcfm-library.php:381
msgid "Weak"
msgstr ""

#: core/class-wcfm-library.php:381
msgid "Good"
msgstr ""

#: core/class-wcfm-library.php:381
msgid "Strong"
msgstr ""

#: core/class-wcfm-library.php:381
msgid "Password strength should be atleast \"Good\"."
msgstr ""

#: core/class-wcfm-library.php:482 core/class-wcfm-library.php:642
#: core/class-wcfm-library.php:690
msgid "Insert your address .."
msgstr ""

#: core/class-wcfm-library.php:489
msgid "Select an option&hellip;"
msgstr ""

#: core/class-wcfm-library.php:589 core/class-wcfm.php:838
#: helpers/class-wcfm-install.php:387 views/settings/wcfm-view-settings.php:124
#: views/vendors/wcfm-view-vendors-manage.php:395
#: views/vendors/wcfm-view-vendors.php:79
#: views/vendors/wcfm-view-vendors.php:100
msgid "Membership"
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "Processing..."
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "Search:"
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "Show _MENU_ entries"
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "Showing _START_ to _END_ of _TOTAL_ entries"
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "Showing 0 to 0 of 0 entries"
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "(filtered _MAX_ entries of total)"
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "Loading..."
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "No matching records found"
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "No data in the table"
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "First"
msgstr ""

#: core/class-wcfm-library.php:1086 core/class-wcfm-library.php:1320
#: helpers/wcfm-core-functions.php:1348
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "Previous"
msgstr ""

#: core/class-wcfm-library.php:1086 core/class-wcfm-library.php:1326
#: helpers/wcfm-core-functions.php:1347
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "Next"
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "Last"
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
#: views/reports/wcfm-html-report-sales-by-date.php:37
msgid "Print"
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "PDF"
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "Excel"
msgstr ""

#: core/class-wcfm-library.php:1086
#: includes/shortcodes/class-wcfm-shortcode-wc-frontend-manager.php:57
msgid "CSV"
msgstr ""

#: core/class-wcfm-library.php:1200
msgid "Choose Media"
msgstr ""

#: core/class-wcfm-library.php:1200
msgid "Choose Image"
msgstr ""

#: core/class-wcfm-library.php:1200
msgid "Add to Gallery"
msgstr ""

#: core/class-wcfm-library.php:1204
msgid "Could not load the preview image. Please reload the page and try again."
msgstr ""

#: core/class-wcfm-library.php:1247
msgid "Done"
msgstr ""

#: core/class-wcfm-library.php:1248
msgid "Today"
msgstr ""

#: core/class-wcfm-library.php:1251
msgid "Show a different month"
msgstr ""

#: core/class-wcfm-library.php:1290
msgid "Selected:"
msgstr ""

#: core/class-wcfm-library.php:1292
msgid "Days"
msgstr ""

#: core/class-wcfm-library.php:1293
msgid "Close"
msgstr ""

#: core/class-wcfm-library.php:1294
msgid "mo"
msgstr ""

#: core/class-wcfm-library.php:1295
msgid "tu"
msgstr ""

#: core/class-wcfm-library.php:1296
msgid "we"
msgstr ""

#: core/class-wcfm-library.php:1297
msgid "th"
msgstr ""

#: core/class-wcfm-library.php:1298
msgid "fr"
msgstr ""

#: core/class-wcfm-library.php:1299
msgid "sa"
msgstr ""

#: core/class-wcfm-library.php:1300
msgid "su"
msgstr ""

#: core/class-wcfm-library.php:1301
msgid "W"
msgstr ""

#: core/class-wcfm-library.php:1303
msgid "january"
msgstr ""

#: core/class-wcfm-library.php:1304
msgid "february"
msgstr ""

#: core/class-wcfm-library.php:1305
msgid "march"
msgstr ""

#: core/class-wcfm-library.php:1306
msgid "april"
msgstr ""

#: core/class-wcfm-library.php:1307
msgid "may"
msgstr ""

#: core/class-wcfm-library.php:1308
msgid "june"
msgstr ""

#: core/class-wcfm-library.php:1309
msgid "july"
msgstr ""

#: core/class-wcfm-library.php:1310
msgid "august"
msgstr ""

#: core/class-wcfm-library.php:1311
msgid "september"
msgstr ""

#: core/class-wcfm-library.php:1312
msgid "october"
msgstr ""

#: core/class-wcfm-library.php:1313
msgid "november"
msgstr ""

#: core/class-wcfm-library.php:1314
msgid "december"
msgstr ""

#: core/class-wcfm-library.php:1316
msgid "Shortcuts"
msgstr ""

#: core/class-wcfm-library.php:1317
msgid "Custom Values"
msgstr ""

#: core/class-wcfm-library.php:1318
msgid "Past"
msgstr ""

#: core/class-wcfm-library.php:1319
msgid "Following"
msgstr ""

#: core/class-wcfm-library.php:1321 core/class-wcfm-library.php:1327
msgid "Week"
msgstr ""

#: core/class-wcfm-library.php:1323
msgid "This Week"
msgstr ""

#: core/class-wcfm-library.php:1324
#: includes/reports/class-wcfm-report-analytics.php:143
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:308
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:261
#: includes/reports/class-wcpvendors-report-sales-by-date.php:255
#: includes/reports/class-wcvendors-report-sales-by-date.php:285
#: views/enquiry/wcfm-view-enquiry.php:26
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:31
#: views/reports/wcfm-view-reports-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:58
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:31
msgid "This Month"
msgstr ""

#: core/class-wcfm-library.php:1330
msgid "Date range should not be more than %d days"
msgstr ""

#: core/class-wcfm-library.php:1331
msgid "Date range should not be less than %d days"
msgstr ""

#: core/class-wcfm-library.php:1332
msgid "Please select a date range longer than %d days"
msgstr ""

#: core/class-wcfm-library.php:1333
msgid "Please select a date"
msgstr ""

#: core/class-wcfm-library.php:1334
msgid "Please select a date range less than %d days"
msgstr ""

#: core/class-wcfm-library.php:1335
msgid "Please select a date range between %d and %d days"
msgstr ""

#: core/class-wcfm-library.php:1336
msgid "Please select a date range"
msgstr ""

#: core/class-wcfm-library.php:1337
msgid "Time"
msgstr ""

#: core/class-wcfm-library.php:1350
msgid "to"
msgstr ""

#: core/class-wcfm-library.php:1353
msgid "Choose Date Range"
msgstr ""

#: core/class-wcfm-library.php:1510 core/class-wcfm-library.php:1573
#: views/customers/wcfm-view-customers-details.php:176
#: views/customers/wcfm-view-customers-manage.php:167
#: views/customers/wcfm-view-customers-manage.php:204
#: views/customers/wcfm-view-customers-manage.php:229
#: views/profile/wcfm-view-profile.php:206
#: views/profile/wcfm-view-profile.php:294
#: views/profile/wcfm-view-profile.php:310
#: views/vendors/wcfm-view-vendors-manage.php:358
#: views/vendors/wcfm-view-vendors-new.php:93
#: views/vendors/wcfm-view-vendors-new.php:116
#: views/vendors/wcfm-view-vendors-new.php:134
msgid "First Name"
msgstr ""

#: core/class-wcfm-library.php:1514 core/class-wcfm-library.php:1577
#: views/customers/wcfm-view-customers-details.php:177
#: views/customers/wcfm-view-customers-manage.php:168
#: views/customers/wcfm-view-customers-manage.php:205
#: views/customers/wcfm-view-customers-manage.php:230
#: views/profile/wcfm-view-profile.php:207
#: views/profile/wcfm-view-profile.php:295
#: views/profile/wcfm-view-profile.php:311
#: views/vendors/wcfm-view-vendors-manage.php:359
#: views/vendors/wcfm-view-vendors-new.php:94
#: views/vendors/wcfm-view-vendors-new.php:117
#: views/vendors/wcfm-view-vendors-new.php:135
msgid "Last Name"
msgstr ""

#: core/class-wcfm-library.php:1518 core/class-wcfm-library.php:1581
msgid "Company"
msgstr ""

#: core/class-wcfm-library.php:1522 core/class-wcfm-library.php:1585
#: views/customers/wcfm-view-customers-manage.php:208
#: views/customers/wcfm-view-customers-manage.php:232
#: views/profile/wcfm-view-profile.php:296
#: views/profile/wcfm-view-profile.php:312
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1128
#: views/settings/wcfm-view-wcmarketplace-settings.php:229
#: views/settings/wcfm-view-wcmarketplace-settings.php:729
#: views/settings/wcfm-view-wcvendors-settings.php:219
#: views/settings/wcfm-view-wcvendors-settings.php:417
#: views/vendors/wcfm-view-vendors-new.php:119
#: views/vendors/wcfm-view-vendors-new.php:136
msgid "Address 1"
msgstr ""

#: core/class-wcfm-library.php:1526 core/class-wcfm-library.php:1589
#: views/customers/wcfm-view-customers-manage.php:209
#: views/customers/wcfm-view-customers-manage.php:233
#: views/profile/wcfm-view-profile.php:297
#: views/profile/wcfm-view-profile.php:313
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1129
#: views/settings/wcfm-view-wcmarketplace-settings.php:230
#: views/settings/wcfm-view-wcmarketplace-settings.php:730
#: views/settings/wcfm-view-wcvendors-settings.php:220
#: views/settings/wcfm-view-wcvendors-settings.php:418
#: views/vendors/wcfm-view-vendors-new.php:120
#: views/vendors/wcfm-view-vendors-new.php:137
msgid "Address 2"
msgstr ""

#: core/class-wcfm-library.php:1530 core/class-wcfm-library.php:1593
msgid "City"
msgstr ""

#: core/class-wcfm-library.php:1534 core/class-wcfm-library.php:1597
#: views/settings/wcfm-view-wcvendors-settings.php:393
msgid "Postcode"
msgstr ""

#: core/class-wcfm-library.php:1538 core/class-wcfm-library.php:1601
#: views/customers/wcfm-view-customers-manage.php:210
#: views/customers/wcfm-view-customers-manage.php:234
#: views/profile/wcfm-view-profile.php:298
#: views/profile/wcfm-view-profile.php:314
#: views/settings/wcfm-view-dokan-settings.php:204
#: views/settings/wcfm-view-dokan-settings.php:458
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:529
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1130
#: views/settings/wcfm-view-wcmarketplace-settings.php:231
#: views/settings/wcfm-view-wcmarketplace-settings.php:731
#: views/settings/wcfm-view-wcvendors-settings.php:221
#: views/settings/wcfm-view-wcvendors-settings.php:327
#: views/settings/wcfm-view-wcvendors-settings.php:391
#: views/settings/wcfm-view-wcvendors-settings.php:419
#: views/vendors/wcfm-view-vendors-new.php:121
#: views/vendors/wcfm-view-vendors-new.php:138
msgid "Country"
msgstr ""

#: core/class-wcfm-library.php:1542 core/class-wcfm-library.php:1605
msgid "Select a country&hellip;"
msgstr ""

#: core/class-wcfm-library.php:1545 core/class-wcfm-library.php:1608
#: views/customers/wcfm-view-customers-manage.php:212
#: views/customers/wcfm-view-customers-manage.php:236
#: views/profile/wcfm-view-profile.php:300
#: views/profile/wcfm-view-profile.php:316
#: views/settings/wcfm-view-dokan-settings.php:205
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:530
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1132
#: views/settings/wcfm-view-wcmarketplace-settings.php:232
#: views/settings/wcfm-view-wcmarketplace-settings.php:733
#: views/settings/wcfm-view-wcvendors-settings.php:223
#: views/settings/wcfm-view-wcvendors-settings.php:421
#: views/vendors/wcfm-view-vendors-new.php:123
#: views/vendors/wcfm-view-vendors-new.php:140
msgid "State/County"
msgstr ""

#: core/class-wcfm-library.php:1550
#: views/customers/wcfm-view-customers-details.php:175
#: views/customers/wcfm-view-customers-manage.php:166
#: views/customers/wcfm-view-customers.php:79
#: views/customers/wcfm-view-customers.php:95
#: views/enquiry/wcfm-view-enquiry-form.php:57
#: views/profile/wcfm-view-profile.php:208
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1127
#: views/settings/wcfm-view-wcmarketplace-settings.php:728
#: views/vendors/wcfm-view-vendors-manage.php:303
#: views/vendors/wcfm-view-vendors-new.php:92
msgid "Email"
msgstr ""

#: core/class-wcfm-library.php:1553
#: views/customers/wcfm-view-customers-manage.php:207
#: views/profile/wcfm-view-profile.php:240
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1126
#: views/settings/wcfm-view-wcmarketplace-settings.php:727
#: views/vendors/wcfm-view-vendors-manage.php:304
#: views/vendors/wcfm-view-vendors-new.php:118
msgid "Phone"
msgstr ""

#: core/class-wcfm-non-ajax.php:56
msgid "Online"
msgstr ""

#: core/class-wcfm-non-ajax.php:160
msgid "No sales yet ..!!!"
msgstr ""

#: core/class-wcfm-non-ajax.php:180
msgid "Top Selling Products"
msgstr ""

#: core/class-wcfm-non-ajax.php:210
msgid "View WCFM settings"
msgstr ""

#: core/class-wcfm-non-ajax.php:210 core/class-wcfm-query.php:184
#: core/class-wcfm.php:739 helpers/class-wcfm-install.php:386
#: views/capability/wcfm-view-capability.php:182
#: views/settings/wcfm-view-dokan-settings.php:129
#: views/settings/wcfm-view-settings.php:87
#: views/settings/wcfm-view-settings.php:96
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:248
#: views/settings/wcfm-view-wcmarketplace-settings.php:136
#: views/settings/wcfm-view-wcpvendors-settings.php:73
#: views/settings/wcfm-view-wcvendors-settings.php:120
msgid "Settings"
msgstr ""

#: core/class-wcfm-non-ajax.php:217 core/class-wcfm-non-ajax.php:244
msgid "Add more power to your WCFM"
msgstr ""

#: core/class-wcfm-non-ajax.php:217 core/class-wcfm-non-ajax.php:244
msgid "WCFM Ultimate"
msgstr ""

#: core/class-wcfm-non-ajax.php:235
msgid "View WCFM documentation"
msgstr ""

#: core/class-wcfm-non-ajax.php:235 views/settings/wcfm-view-settings.php:101
msgid "Documentation"
msgstr ""

#: core/class-wcfm-non-ajax.php:237
msgid "View WCFM Video Tutorial"
msgstr ""

#: core/class-wcfm-non-ajax.php:237 views/settings/wcfm-view-settings.php:100
msgid "Video Tutorial"
msgstr ""

#: core/class-wcfm-non-ajax.php:238
msgid "Visit premium customer support"
msgstr ""

#: core/class-wcfm-non-ajax.php:238
msgid "Support"
msgstr ""

#: core/class-wcfm-non-ajax.php:239
msgid "Any WC help feel free to contact us"
msgstr ""

#: core/class-wcfm-non-ajax.php:239
msgid "Customization Help"
msgstr ""

#: core/class-wcfm-non-ajax.php:263 core/class-wcfm-non-ajax.php:265
msgid "WCFM"
msgstr ""

#: core/class-wcfm-notification.php:71
msgid "A new product <b>%s</b> added by <b>%s</b>"
msgstr ""

#: core/class-wcfm-notification.php:109
msgid "Product <b>%s</b> has been approved."
msgstr ""

#: core/class-wcfm-notification.php:139
msgid "Product <b>%s</b> has been rejected.<br/>Reason: %s"
msgstr ""

#: core/class-wcfm-notification.php:172
msgid "Product <b>%s</b> awaiting for review"
msgstr ""

#: core/class-wcfm-notification.php:212 core/class-wcfm-notification.php:271
msgid "You have received an Order <b>#%s</b>"
msgstr ""

#: core/class-wcfm-notification.php:248
msgid "You have received an Order <b>#%s</b> for <b>%s</b>"
msgstr ""

#: core/class-wcfm-notification.php:347
#: views/messages/wcfm-view-messages.php:48
msgid "Notifications"
msgstr ""

#: core/class-wcfm-notification.php:371
msgid "There is no notification yet!!"
msgstr ""

#: core/class-wcfm-notification.php:780 core/class-wcfm-notification.php:834
#: core/class-wcfm.php:847 helpers/class-wcfm-install.php:403
msgid "Notification"
msgstr ""

#: core/class-wcfm-notification.php:808 core/class-wcfm-notification.php:819
msgid "You have received a new notification:"
msgstr ""

#: core/class-wcfm-notification.php:812
msgid "Check more details %shere%s."
msgstr ""

#: core/class-wcfm-policy.php:68 core/class-wcfm.php:837
msgid "Policies"
msgstr ""

#: core/class-wcfm-policy.php:72
msgid "Store Policies Setting"
msgstr ""

#: core/class-wcfm-policy.php:84 core/class-wcfm-policy.php:199
#: core/class-wcfm-policy.php:363
msgid "Policy Tab Label"
msgstr ""

#: core/class-wcfm-policy.php:85 core/class-wcfm-policy.php:200
#: core/class-wcfm-policy.php:370 core/class-wcfm-policy.php:647
#: views/settings/wcfm-view-wcvendors-settings.php:407
msgid "Shipping Policy"
msgstr ""

#: core/class-wcfm-policy.php:86 core/class-wcfm-policy.php:201
#: core/class-wcfm-policy.php:377 core/class-wcfm-policy.php:652
#: views/settings/wcfm-view-wcvendors-settings.php:408
msgid "Refund Policy"
msgstr ""

#: core/class-wcfm-policy.php:87 core/class-wcfm-policy.php:202
#: core/class-wcfm-policy.php:384
msgid "Cancellation/Return/Exchange Policy"
msgstr ""

#: core/class-wcfm-policy.php:181 core/class-wcfm-policy.php:474
msgid "Store Policies"
msgstr ""

#: core/class-wcfm-policy.php:185
msgid "Policies Setting"
msgstr ""

#: core/class-wcfm-policy.php:333
msgid "Product Policies"
msgstr ""

#: core/class-wcfm-policy.php:352
msgid "Override Policy Fields"
msgstr ""

#: core/class-wcfm-policy.php:357
msgid "Enable this checkbox to set product-based policies."
msgstr ""

#: core/class-wcfm-policy.php:657
msgid "Cancellation / Return / Exchange Policy"
msgstr ""

#: core/class-wcfm-query.php:127
msgid "Products Dashboard"
msgstr ""

#: core/class-wcfm-query.php:132
msgid "Product Manager -%s"
msgstr ""

#: core/class-wcfm-query.php:132
msgid "Product Manager"
msgstr ""

#: core/class-wcfm-query.php:135
msgid "Products Stock Manager"
msgstr ""

#: core/class-wcfm-query.php:138
#: views/products/wcfm-view-products-export.php:58
#: views/products/wcfm-view-products.php:98
#: views/products/wcfm-view-products.php:103
msgid "Products Import"
msgstr ""

#: core/class-wcfm-query.php:141
#: views/products/wcfm-view-products-export.php:33
#: views/products/wcfm-view-products.php:89
msgid "Products Export"
msgstr ""

#: core/class-wcfm-query.php:144
msgid "Coupons Dashboard"
msgstr ""

#: core/class-wcfm-query.php:149
msgid "Coupon Manager -%s"
msgstr ""

#: core/class-wcfm-query.php:149
msgid "Coupon Manager"
msgstr ""

#: core/class-wcfm-query.php:153
msgid "Orders Dashboard"
msgstr ""

#: core/class-wcfm-query.php:157
msgid "Order Details #%s"
msgstr ""

#: core/class-wcfm-query.php:157 views/orders/wcfm-view-orders-details.php:120
msgid "Order Details"
msgstr ""

#: core/class-wcfm-query.php:160
msgid "Reports - Sales by Date"
msgstr ""

#: core/class-wcfm-query.php:163
msgid "Reports - Sales by Vendor"
msgstr ""

#: core/class-wcfm-query.php:166
msgid "Reports - Sales by Product"
msgstr ""

#: core/class-wcfm-query.php:169
msgid "Reports - Coupons by Date"
msgstr ""

#: core/class-wcfm-query.php:172
msgid "Reports - Out of Stock"
msgstr ""

#: core/class-wcfm-query.php:175
msgid "Reports - Low in Stock"
msgstr ""

#: core/class-wcfm-query.php:178 helpers/class-wcfm-install.php:404
#: views/settings/wcfm-view-settings.php:211
#: views/settings/wcfm-view-settings.php:223
msgid "Analytics"
msgstr ""

#: core/class-wcfm-query.php:181 core/class-wcfm.php:839
#: helpers/class-wcfm-install.php:388 views/profile/wcfm-view-profile.php:175
#: views/settings/wcfm-view-wcpvendors-settings.php:112
#: views/vendors/wcfm-view-vendors-manage.php:351
#: views/vendors/wcfm-view-vendors.php:77
#: views/vendors/wcfm-view-vendors.php:98 views/wcfm-view-header-panels.php:60
msgid "Profile"
msgstr ""

#: core/class-wcfm-query.php:187
msgid "Capability Manager"
msgstr ""

#: core/class-wcfm-query.php:190 core/class-wcfm.php:849
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:66
#: views/knowledgebase/wcfm-view-knowledgebase.php:26
#: views/wcfm-view-header-panels.php:76
msgid "Knowledgebase"
msgstr ""

#: core/class-wcfm-query.php:193
msgid "Knowledgebase Manager"
msgstr ""

#: core/class-wcfm-query.php:196
msgid "Notice Dashboard"
msgstr ""

#: core/class-wcfm-query.php:199
msgid "Notice Manager"
msgstr ""

#: core/class-wcfm-query.php:202 helpers/class-wcfm-install.php:402
msgid "Notice"
msgstr ""

#: core/class-wcfm-query.php:205
msgid "Message Dashboard"
msgstr ""

#: core/class-wcfm-vendor-support.php:149
msgid "Vendors Dashboard"
msgstr ""

#: core/class-wcfm-vendor-support.php:152
msgid "New Vendor"
msgstr ""

#: core/class-wcfm-vendor-support.php:155
msgid "Vendors Manager"
msgstr ""

#: core/class-wcfm-vendor-support.php:158
msgid "Vendors Commission"
msgstr ""

#: core/class-wcfm-vendor-support.php:206
#: core/class-wcfm-vendor-support.php:219 core/class-wcfm.php:779
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:171
#: views/vendors/wcfm-view-vendors-new.php:72
#: views/vendors/wcfm-view-vendors.php:17
#: views/vendors/wcfm-view-vendors.php:25
#: views/vendors/wcfm-view-vendors.php:29
msgid "Vendors"
msgstr ""

#: core/class-wcfm-vendor-support.php:323
msgid "Store Order ID"
msgstr ""

#: core/class-wcfm-vendor-support.php:326
msgid "Package Qty"
msgstr ""

#: core/class-wcfm-vendor-support.php:329
msgid "Shipping Method"
msgstr ""

#: core/class-wcfm-vendor-support.php:332
#: views/settings/wcfm-view-dokan-settings.php:434
msgid "Processing Time"
msgstr ""

#: core/class-wcfm-vendor-support.php:335
#: views/articles/wcfm-view-articles-manage-tabs.php:79
#: views/articles/wcfm-view-articles-manage.php:257
#: views/articles/wcfm-view-articles-manage.php:401
#: views/coupons/wcfm-view-coupons-manage.php:127
#: views/integrations/wcfm-view-integrations-products-manage.php:185
#: views/products-manager/wcfm-view-products-manage.php:673
#: views/products-manager/wcfm-view-products-manage.php:885
#: views/products-popup/wcfm-view-product-popup.php:291
msgid "Description"
msgstr ""

#: core/class-wcfm-vendor-support.php:338
msgid "Pickup Address"
msgstr ""

#: core/class-wcfm-vendor-support.php:341
msgid "Delivery Boy"
msgstr ""

#: core/class-wcfm-vendor-support.php:356
msgid "Store Shipping by Country"
msgstr ""

#: core/class-wcfm-vendor-support.php:358
msgid "Store Shipping by Weight"
msgstr ""

#: core/class-wcfm-vendor-support.php:360
msgid "Store Shipping by Distance"
msgstr ""

#: core/class-wcfm-vendor-support.php:424
#: core/class-wcfm-vendor-support.php:593
#: core/class-wcfm-vendor-support.php:632
#: core/class-wcfm-vendor-support.php:648
#: core/class-wcfm-vendor-support.php:677 core/class-wcfm-wcmarketplace.php:483
#: core/class-wcfm-wcpvendors.php:426 core/class-wcfm-wcvendors.php:634
#: views/settings/wcfm-view-wcpvendors-settings.php:181
msgid "Commission"
msgstr ""

#: core/class-wcfm-vendor-support.php:598
#: core/class-wcfm-vendor-support.php:638
#: core/class-wcfm-vendor-support.php:643
#: core/class-wcfm-vendor-support.php:667
#: core/class-wcfm-vendor-support.php:672
msgid "Commission(%)"
msgstr ""

#: core/class-wcfm-vendor-support.php:639
#: core/class-wcfm-vendor-support.php:668
msgid "Fixed (per transaction)"
msgstr ""

#: core/class-wcfm-vendor-support.php:644
#: core/class-wcfm-vendor-support.php:673
msgid "Fixed (per unit)"
msgstr ""

#: core/class-wcfm-vendor-support.php:1040
msgid "Choose Vendor ..."
msgstr ""

#: core/class-wcfm-vendor-support.php:2725
msgid "Review Product"
msgstr ""

#: core/class-wcfm-vendor-support.php:2726 helpers/wcfm-core-functions.php:1380
msgid "New Product"
msgstr ""

#: core/class-wcfm-vendor-support.php:2727 helpers/wcfm-core-functions.php:1381
msgid "New Category"
msgstr ""

#: core/class-wcfm-vendor-support.php:2734
msgid "You may manage this using WCfM Capability Controller."
msgstr ""

#: core/class-wcfm-vendor-support.php:2735
msgid ""
"Manage vendor backend access from <a href=\"%s\">WCfM Capability "
"Controller</a>."
msgstr ""

#: core/class-wcfm-wcbookings.php:96
msgid "Bookings Dashboard"
msgstr ""

#: core/class-wcfm-wcbookings.php:99
#: views/capability/wcfm-view-capability.php:541
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:99
#: views/wc_bookings/wcfm-view-wcbookings-details.php:85
#: views/wc_bookings/wcfm-view-wcbookings.php:36
msgid "Bookings List"
msgstr ""

#: core/class-wcfm-wcbookings.php:102
msgid "Bookings Resources"
msgstr ""

#: core/class-wcfm-wcbookings.php:105
msgid "Bookings Resources Manage"
msgstr ""

#: core/class-wcfm-wcbookings.php:108
msgid "Create Bookings"
msgstr ""

#: core/class-wcfm-wcbookings.php:111
#: views/capability/wcfm-view-capability.php:542
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:114
msgid "Bookings Calendar"
msgstr ""

#: core/class-wcfm-wcbookings.php:114
msgid "Booking Details #%s"
msgstr ""

#: core/class-wcfm-wcbookings.php:117
msgid "Bookings settings"
msgstr ""

#: core/class-wcfm-wcbookings.php:150 core/class-wcfm-wcbookings.php:158
#: views/capability/wcfm-view-capability.php:535
#: views/customers/wcfm-view-customers-details.php:202
#: views/customers/wcfm-view-customers.php:83
#: views/customers/wcfm-view-customers.php:99
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:24
msgid "Bookings"
msgstr ""

#: core/class-wcfm-wcbookings.php:176
msgid "Bookable product"
msgstr ""

#: core/class-wcfm-wcbookings.php:361
msgid "Booking Options"
msgstr ""

#: core/class-wcfm-wcbookings.php:367
msgid "Booking duration"
msgstr ""

#: core/class-wcfm-wcbookings.php:367
msgid "Fixed blocks of"
msgstr ""

#: core/class-wcfm-wcbookings.php:367
msgid "Customer defined blocks of"
msgstr ""

#: core/class-wcfm-wcbookings.php:369 core/class-wcfm-wcbookings.php:377
#: views/products-manager/wcfm-view-wcbookings-products-manage.php:65
#: views/products-manager/wcfm-view-wcbookings-products-manage.php:67
msgid "Month(s)"
msgstr ""

#: core/class-wcfm-wcbookings.php:369 core/class-wcfm-wcbookings.php:377
#: views/products-manager/wcfm-view-wcbookings-products-manage.php:65
#: views/products-manager/wcfm-view-wcbookings-products-manage.php:67
msgid "Day(s)"
msgstr ""

#: core/class-wcfm-wcbookings.php:369 core/class-wcfm-wcbookings.php:377
#: views/products-manager/wcfm-view-wcbookings-products-manage.php:65
#: views/products-manager/wcfm-view-wcbookings-products-manage.php:67
msgid "Hour(s)"
msgstr ""

#: core/class-wcfm-wcbookings.php:369 core/class-wcfm-wcbookings.php:377
#: views/products-manager/wcfm-view-wcbookings-products-manage.php:65
#: views/products-manager/wcfm-view-wcbookings-products-manage.php:67
msgid "Minute(s)"
msgstr ""

#: core/class-wcfm-wcbookings.php:370
msgid "Minimum duration"
msgstr ""

#: core/class-wcfm-wcbookings.php:370
msgid "The minimum allowed duration the user can input."
msgstr ""

#: core/class-wcfm-wcbookings.php:371
msgid "Maximum duration"
msgstr ""

#: core/class-wcfm-wcbookings.php:371
msgid "The maximum allowed duration the user can input."
msgstr ""

#: core/class-wcfm-wcbookings.php:372
msgid "Enable Calendar Range Picker?"
msgstr ""

#: core/class-wcfm-wcbookings.php:372
msgid ""
"Lets the user select a start and end date on the calendar - duration will "
"be calculated automatically."
msgstr ""

#: core/class-wcfm-wcbookings.php:373
msgid "Calendar display mode"
msgstr ""

#: core/class-wcfm-wcbookings.php:373
msgid "Display calendar on click"
msgstr ""

#: core/class-wcfm-wcbookings.php:373
msgid "Calendar always visible"
msgstr ""

#: core/class-wcfm-wcbookings.php:374
msgid "Requires confirmation?"
msgstr ""

#: core/class-wcfm-wcbookings.php:374
msgid ""
"Check this box if the booking requires admin approval/confirmation. Payment "
"will not be taken during checkout."
msgstr ""

#: core/class-wcfm-wcbookings.php:375
msgid "Can be cancelled?"
msgstr ""

#: core/class-wcfm-wcbookings.php:375
msgid ""
"Check this box if the booking can be cancelled by the customer after it has "
"been purchased. A refund will not be sent automatically."
msgstr ""

#: core/class-wcfm-wcbookings.php:376
msgid "Booking can be cancelled until"
msgstr ""

#: core/class-wcfm-wcbookings.php:377
msgid "before the start date."
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:214
msgid "Show all"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:215
msgid "Unpaid"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:216
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:47
msgid "Requested"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:519
#: core/class-wcfm-wcmarketplace.php:481 views/orders/wcfm-view-orders.php:138
msgid "Fees"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:521
#: includes/reports/class-dokan-report-sales-by-date.php:825
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:597
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:537
#: includes/reports/class-wcpvendors-report-sales-by-date.php:440
#: includes/reports/class-wcvendors-report-sales-by-date.php:553
#: views/orders/wcfm-view-orders.php:109 views/orders/wcfm-view-orders.php:116
#: views/orders/wcfm-view-orders.php:140 views/orders/wcfm-view-orders.php:147
msgid "Earning"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:770
msgid "Subtotal"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:794
#: core/class-wcfm-wcfmmarketplace.php:849
#: core/class-wcfm-wcfmmarketplace.php:895
#: core/class-wcfm-wcmarketplace.php:496 core/class-wcfm-wcmarketplace.php:656
#: core/class-wcfm-wcpvendors.php:429 core/class-wcfm-wcpvendors.php:542
#: core/class-wcfm-wcvendors.php:646 core/class-wcfm-wcvendors.php:659
#: core/class-wcfm-wcvendors.php:835 core/class-wcfm-wcvendors.php:858
#: helpers/class-wcfm-setup.php:775
#: views/orders/wcfm-view-orders-details.php:461
#: views/orders/wcfm-view-orders-details.php:462
#: views/products-manager/wcfm-view-products-manage-tabs.php:136
msgid "Tax"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:848
#: core/class-wcfm-wcfmmarketplace.php:942
#: core/class-wcfm-wcmarketplace.php:502 core/class-wcfm-wcpvendors.php:431
#: core/class-wcfm-wcvendors.php:666
#: views/orders/wcfm-view-orders-details.php:454
msgid "Total"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:922
#: core/class-wcfm-wcfmmarketplace.php:943
#: core/class-wcfm-wcmarketplace.php:497 core/class-wcfm-wcmarketplace.php:665
#: core/class-wcfm-wcpvendors.php:430 core/class-wcfm-wcpvendors.php:551
msgid "Shipping Tax"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:966
#: views/orders/wcfm-view-orders-details.php:939
msgid "Refunded"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:975
#: views/orders/wcfm-view-orders-details.php:876
msgid "Discount"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:984
#: core/class-wcfm-wcmarketplace.php:711 core/class-wcfm-wcpvendors.php:571
#: core/class-wcfm-wcvendors.php:881
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:86
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:99
msgid "Gross Total"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:1009
#: core/class-wcfm-wcfmmarketplace.php:1058
#: core/class-wcfm-wcfmmarketplace.php:1095
msgid "Gross Earning"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:1025
#: core/class-wcfm-wcfmmarketplace.php:1071
msgid "Affiliate Commission"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:1110
msgid "Transaction Charge"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:1147
#: views/orders/wcfm-view-orders-details.php:964
#: views/orders/wcfm-view-orders.php:107 views/orders/wcfm-view-orders.php:114
#: views/orders/wcfm-view-orders.php:145
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:88
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:101
msgid "Admin Fee"
msgstr ""

#: core/class-wcfm-wcmarketplace.php:118
msgid "Dashboard - manage your account here"
msgstr ""

#: core/class-wcfm-wcmarketplace.php:633 core/class-wcfm-wcpvendors.php:523
#: core/class-wcfm-wcvendors.php:812
msgid "Line Commission"
msgstr ""

#: core/class-wcfm-wcmarketplace.php:679 views/vendors/wcfm-view-vendors.php:84
#: views/vendors/wcfm-view-vendors.php:105
msgid "Total Fees"
msgstr ""

#: core/class-wcfm-wcmarketplace.php:901
msgid "No product to duplicate has been supplied!"
msgstr ""

#: core/class-wcfm-wcmarketplace.php:912
#. translators: %s: product id
msgid "Product creation failed, could not find original product: %s"
msgstr ""

#: core/class-wcfm-wcpvendors.php:139
msgid "Vendor Dashboard"
msgstr ""

#: core/class-wcfm-wcsubscriptions.php:44
#: core/class-wcfm-xasubscriptions.php:41
msgid "Simple subscription"
msgstr ""

#: core/class-wcfm-wcsubscriptions.php:58
#: core/class-wcfm-xasubscriptions.php:55
#: views/capability/wcfm-view-capability.php:569
msgid "Subscriptions"
msgstr ""

#: core/class-wcfm-wcsubscriptions.php:59
msgid "Variable Subscriptions"
msgstr ""

#: core/class-wcfm-wcsubscriptions.php:83
#: core/class-wcfm-xasubscriptions.php:80
msgid "Subscription price (%s)"
msgstr ""

#: core/class-wcfm-wcsubscriptions.php:83
#: core/class-wcfm-xasubscriptions.php:80
msgid "Choose the subscription price, billing interval and period."
msgstr ""

#: core/class-wcfm-wcsubscriptions.php:86
#: core/class-wcfm-wcsubscriptions.php:87
#: core/class-wcfm-wcsubscriptions.php:88
#: core/class-wcfm-wcsubscriptions.php:89
msgid "Expire after"
msgstr ""

#: core/class-wcfm-wcsubscriptions.php:86
#: core/class-wcfm-wcsubscriptions.php:87
#: core/class-wcfm-wcsubscriptions.php:88
#: core/class-wcfm-wcsubscriptions.php:89
#: core/class-wcfm-xasubscriptions.php:83
#: core/class-wcfm-xasubscriptions.php:84
#: core/class-wcfm-xasubscriptions.php:85
#: core/class-wcfm-xasubscriptions.php:86
msgid ""
"Automatically expire the subscription after this length of time. This "
"length is in addition to any free trial or amount of time provided before a "
"synchronised first renewal date."
msgstr ""

#: core/class-wcfm-wcsubscriptions.php:150
msgid "Renewal Order"
msgstr ""

#: core/class-wcfm-wcsubscriptions.php:152
msgid "Resubscribe Order"
msgstr ""

#: core/class-wcfm-wcsubscriptions.php:154
msgid "Parent Order"
msgstr ""

#: core/class-wcfm-withdrawal.php:66
#: views/withdrawal/dokan/wcfm-view-payments.php:26
#: views/withdrawal/wcfm/wcfm-view-payments.php:32
#: views/withdrawal/wcmp/wcfm-view-payments.php:26
msgid "Payments History"
msgstr ""

#: core/class-wcfm-withdrawal.php:71
#: views/capability/wcfm-view-capability.php:331
#: views/withdrawal/dokan/wcfm-view-payments.php:42
#: views/withdrawal/dokan/wcfm-view-withdrawal.php:53
#: views/withdrawal/wcfm/wcfm-view-payments.php:48
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:110
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:117
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:43
#: views/withdrawal/wcmp/wcfm-view-payments.php:43
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:49
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:28
msgid "Withdrawal Request"
msgstr ""

#: core/class-wcfm-withdrawal.php:75
msgid "Withdrawal Reverse"
msgstr ""

#: core/class-wcfm-withdrawal.php:79
#: views/capability/wcfm-view-capability.php:333
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:97
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:36
msgid "Transaction Details"
msgstr ""

#: core/class-wcfm-withdrawal.php:130 views/settings/wcfm-view-settings.php:392
msgid "Payments"
msgstr ""

#: core/class-wcfm-withdrawal.php:139 core/class-wcfm-withdrawal.php:150
#: core/class-wcfm.php:840 helpers/class-wcfm-install.php:390
#: includes/reports/class-dokan-report-sales-by-date.php:835
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:608
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:547
#: includes/reports/class-wcpvendors-report-sales-by-date.php:450
#: includes/reports/class-wcvendors-report-sales-by-date.php:563
#: views/capability/wcfm-view-capability.php:327
#: views/settings/wcfm-view-settings.php:398
#: views/vendors/wcfm-view-vendors.php:88
#: views/vendors/wcfm-view-vendors.php:109
#: views/withdrawal/dokan/wcfm-view-payments.php:42
#: views/withdrawal/wcfm/wcfm-view-payments.php:48
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:110
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:117
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:47
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:49
#: views/withdrawal/wcmp/wcfm-view-payments.php:43
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:49
msgid "Withdrawal"
msgstr ""

#: core/class-wcfm-xasubscriptions.php:83
#: core/class-wcfm-xasubscriptions.php:84
#: core/class-wcfm-xasubscriptions.php:85
#: core/class-wcfm-xasubscriptions.php:86
msgid "Subscription length"
msgstr ""

#: core/class-wcfm.php:152
msgid "Archived <span class=\"count\">(%s)</span>"
msgid_plural "Archived <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: core/class-wcfm.php:705 views/capability/wcfm-view-capability.php:208
#: views/listings/wcfm-view-listings.php:121
#: views/listings/wcfm-view-listings.php:136
#: views/products/wcfm-view-products.php:44
msgid "Products"
msgstr ""

#: core/class-wcfm.php:716 views/capability/wcfm-view-capability.php:518
#: views/coupons/wcfm-view-coupons.php:16
msgid "Coupons"
msgstr ""

#: core/class-wcfm.php:733 views/capability/wcfm-view-capability.php:634
msgid "Reports"
msgstr ""

#: core/class-wcfm.php:745 helpers/class-wcfm-setup-bak.php:87
#: helpers/class-wcfm-setup-bak.php:925 helpers/class-wcfm-setup.php:87
#: helpers/class-wcfm-setup.php:1017 views/settings/wcfm-view-settings.php:121
#: views/settings/wcfm-view-settings.php:487
msgid "Capability"
msgstr ""

#: core/class-wcfm.php:834 helpers/class-wcfm-install.php:384
msgid "Article"
msgstr ""

#: core/class-wcfm.php:835 helpers/class-wcfm-install.php:400
#: views/enquiry/wcfm-view-enquiry.php:85
#: views/enquiry/wcfm-view-enquiry.php:100
#: views/wc_bookings/wcfm-view-wcbookings-details.php:343
msgid "Customer"
msgstr ""

#: core/class-wcfm.php:836 helpers/class-wcfm-install.php:383
msgid "Coupon"
msgstr ""

#: core/class-wcfm.php:841 helpers/class-wcfm-install.php:391
msgid "Refund"
msgstr ""

#: core/class-wcfm.php:842
msgid "Enquiry"
msgstr ""

#: core/class-wcfm.php:843
msgid "Enquiry Tab"
msgstr ""

#: core/class-wcfm.php:843
msgid ""
"If you just want to hide Single Product page `Enquiry Tab`, but keep enable "
"`Enquiry Module` for `Catalog Mode`."
msgstr ""

#: core/class-wcfm.php:844
msgid ""
"If you disable `Enquiry Module` then `Catalog Module` will stop working "
"automatically."
msgstr ""

#: core/class-wcfm.php:845
msgid "Popup Add Product"
msgstr ""

#: core/class-wcfm.php:846
msgid "Custom Field"
msgstr ""

#: core/class-wcfm.php:848 helpers/wcfm-core-functions.php:1372
msgid "Direct Message"
msgstr ""

#: core/class-wcfm.php:850
msgid "Annoncement"
msgstr ""

#: core/class-wcfm.php:856
msgid "BuddyPress Integration"
msgstr ""

#: core/class-wcfm.php:880
msgid "Base Highlighter Color"
msgstr ""

#: core/class-wcfm.php:881
msgid "Top Bar Background Color"
msgstr ""

#: core/class-wcfm.php:882
msgid "Top Bar Text Color"
msgstr ""

#: core/class-wcfm.php:883
msgid "Dashboard Background Color"
msgstr ""

#: core/class-wcfm.php:884
msgid "Container Background Color"
msgstr ""

#: core/class-wcfm.php:885
msgid "Container Head Color"
msgstr ""

#: core/class-wcfm.php:886
msgid "Container Head Text Color"
msgstr ""

#: core/class-wcfm.php:887
msgid "Container Head Active Color"
msgstr ""

#: core/class-wcfm.php:888
msgid "Container Head Active Text Color"
msgstr ""

#: core/class-wcfm.php:889
msgid "Menu Background Color"
msgstr ""

#: core/class-wcfm.php:890
msgid "Menu Item Text Color"
msgstr ""

#: core/class-wcfm.php:891
msgid "Menu Active Item Background"
msgstr ""

#: core/class-wcfm.php:892
msgid "Menu Active Item Text Color"
msgstr ""

#: core/class-wcfm.php:893
msgid "Button Background Color"
msgstr ""

#: core/class-wcfm.php:894
msgid "Button Text Color"
msgstr ""

#: helpers/class-wcfm-install.php:380
msgid "General"
msgstr ""

#: helpers/class-wcfm-install.php:381
#: views/customers/wcfm-view-customers-details.php:210
#: views/customers/wcfm-view-customers-details.php:221
#: views/customers/wcfm-view-customers-details.php:248
#: views/customers/wcfm-view-customers-details.php:259
#: views/enquiry/wcfm-view-enquiry.php:84
#: views/enquiry/wcfm-view-enquiry.php:99
#: views/enquiry/wcfm-view-my-account-enquiry.php:43
#: views/enquiry/wcfm-view-my-account-enquiry.php:57
#: views/reports/wcfm-view-reports-out-of-stock.php:50
#: views/reports/wcfm-view-reports-out-of-stock.php:59
#: views/wc_bookings/wcfm-view-wcbookings.php:120
#: views/wc_bookings/wcfm-view-wcbookings.php:132
msgid "Product"
msgstr ""

#: helpers/class-wcfm-install.php:382
#: views/customers/wcfm-view-customers-details.php:211
#: views/customers/wcfm-view-customers-details.php:222
#: views/customers/wcfm-view-customers-details.php:249
#: views/customers/wcfm-view-customers-details.php:260
#: views/customers/wcfm-view-customers-details.php:285
#: views/customers/wcfm-view-customers-details.php:295
#: views/orders/wcfm-view-orders.php:99 views/orders/wcfm-view-orders.php:130
#: views/wc_bookings/wcfm-view-wcbookings.php:121
#: views/wc_bookings/wcfm-view-wcbookings.php:133
msgid "Order"
msgstr ""

#: helpers/class-wcfm-install.php:389
#: views/settings/wcfm-view-dokan-settings.php:252
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:577
#: views/settings/wcfm-view-wcmarketplace-settings.php:303
#: views/settings/wcfm-view-wcpvendors-settings.php:174
#: views/settings/wcfm-view-wcvendors-settings.php:246
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:55
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:65
#: views/withdrawal/wcfm/wcfm-view-payments.php:81
#: views/withdrawal/wcfm/wcfm-view-payments.php:96
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:76
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:93
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:95
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:109
msgid "Payment"
msgstr ""

#: helpers/class-wcfm-install.php:392
msgid "Review"
msgstr ""

#: helpers/class-wcfm-install.php:393
msgid "Policy"
msgstr ""

#: helpers/class-wcfm-install.php:395
msgid "Support Ticket"
msgstr ""

#: helpers/class-wcfm-install.php:397
msgid "Vacation"
msgstr ""

#: helpers/class-wcfm-install.php:398 views/vendors/wcfm-view-vendors.php:76
#: views/vendors/wcfm-view-vendors.php:97
msgid "Verification"
msgstr ""

#: helpers/class-wcfm-install.php:399
msgid "Report"
msgstr ""

#: helpers/class-wcfm-install.php:401 views/settings/wcfm-view-settings.php:408
msgid "Followers"
msgstr ""

#: helpers/class-wcfm-install.php:405
msgid "Marketing"
msgstr ""

#: helpers/class-wcfm-install.php:406
#: views/articles/wcfm-view-articles-manage-tabs.php:73
#: views/articles/wcfm-view-articles-manage-tabs.php:92
#: views/integrations/wcfm-view-integrations-products-manage.php:160
#: views/integrations/wcfm-view-integrations-products-manage.php:179
#: views/integrations/wcfm-view-integrations-products-manage.php:198
#: views/settings/wcfm-view-dokan-settings.php:485
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1054
msgid "SEO"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:52 helpers/class-wcfm-setup.php:52
msgid "Introduction"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:57 helpers/class-wcfm-setup.php:57
msgid "Dashboard Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:62 helpers/class-wcfm-setup.php:62
msgid "Marketplace Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:67 helpers/class-wcfm-setup.php:67
msgid "Commission Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:72 helpers/class-wcfm-setup.php:72
msgid "Withdrawal Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:77 helpers/class-wcfm-setup.php:77
msgid "Registration Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:82 helpers/class-wcfm-setup.php:82
msgid "Style"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:92 helpers/class-wcfm-setup.php:92
msgid "Ready!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:218 helpers/class-wcfm-setup.php:220
msgid "WCFM &rsaquo; Setup Wizard"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:275 helpers/class-wcfm-setup-bak.php:386
#: helpers/class-wcfm-setup.php:278
msgid "Welcome to WooCommerce Multi-vendor Marketplace!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:276 helpers/class-wcfm-setup.php:279
msgid ""
"Thank you for choosing WCFM Marketplace! This quick setup wizard will help "
"you to configure the basic settings and you will have your marketplace "
"ready in no time."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:278 helpers/class-wcfm-setup.php:281
msgid "Let's experience the best ever WC Frontend Dashboard!!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:279 helpers/class-wcfm-setup.php:282
msgid ""
"Thank you for choosing WCFM! This quick setup wizard will help you to "
"configure the basic settings and you will have your dashboard ready in no "
"time. <strong>It’s completely optional as WCFM already auto-setup.</strong>"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:281 helpers/class-wcfm-setup.php:284
msgid ""
"If you don't want to go through the wizard right now, you can skip and "
"return to the WordPress dashboard. Come back anytime if you change your "
"mind!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:283 helpers/class-wcfm-setup-bak.php:390
#: helpers/class-wcfm-setup.php:286
msgid "Let's go!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:284 helpers/class-wcfm-setup.php:287
msgid "Not right now"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:305 helpers/class-wcfm-setup.php:308
msgid "Dashboard setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:310 helpers/class-wcfm-setup.php:313
msgid "WCFM Full View"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:311 helpers/class-wcfm-setup.php:314
msgid "Theme Header"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:312 helpers/class-wcfm-setup.php:315
msgid "WCFM Slick Menu"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:313 helpers/class-wcfm-setup.php:316
msgid "WCFM Header Panel"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:314 helpers/class-wcfm-setup.php:317
msgid "Welcome Box"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:315 helpers/class-wcfm-setup.php:318
msgid "Category Checklist View"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:315 helpers/class-wcfm-setup.php:318
msgid ""
"Disable this to have Product Manager Category/Custom Taxonomy Selector - "
"Flat View."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:316 helpers/class-wcfm-setup.php:319
msgid "Quick Access"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:317
msgid "Disable Responsive Float Menu"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:318 helpers/class-wcfm-setup.php:321
msgid "Float Button"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:323 helpers/class-wcfm-setup-bak.php:426
#: helpers/class-wcfm-setup-bak.php:747 helpers/class-wcfm-setup-bak.php:863
#: helpers/class-wcfm-setup-bak.php:894 helpers/class-wcfm-setup-bak.php:963
#: helpers/class-wcfm-setup.php:326 helpers/class-wcfm-setup.php:432
#: helpers/class-wcfm-setup.php:781 helpers/class-wcfm-setup.php:955
#: helpers/class-wcfm-setup.php:986 helpers/class-wcfm-setup.php:1055
msgid "Continue"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:324 helpers/class-wcfm-setup-bak.php:383
#: helpers/class-wcfm-setup-bak.php:427 helpers/class-wcfm-setup-bak.php:748
#: helpers/class-wcfm-setup-bak.php:864 helpers/class-wcfm-setup-bak.php:895
#: helpers/class-wcfm-setup-bak.php:964 helpers/class-wcfm-setup.php:327
#: helpers/class-wcfm-setup.php:433 helpers/class-wcfm-setup.php:782
#: helpers/class-wcfm-setup.php:956 helpers/class-wcfm-setup.php:987
#: helpers/class-wcfm-setup.php:1056
msgid "Skip this step"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:376
msgid "Do you want to setup a multi-vendor marketplace!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:378
msgid "Install WCFM Marketplace"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:387
msgid ""
"You have installed <b>%s</b> as your multi-vendor marketplace. Setup "
"multi-vendor setting from plugin setup panel."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:388
msgid ""
"You may switch your multi-vendor to %s for having more features and "
"flexibilities."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:393 helpers/class-wcfm-setup.php:387
msgid "Marketplace setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:411
msgid "Vendor Store URL"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:411 helpers/class-wcfm-setup.php:411
msgid "Define the seller store URL  (%s/[this-text]/[seller-name])"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:412 helpers/class-wcfm-setup.php:412
msgid "Visible Sold By"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:412 helpers/class-wcfm-setup.php:412
msgid "Uncheck this to disable Sold By display for products."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:413 helpers/class-wcfm-setup.php:413
msgid "Sold By Template"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:413 helpers/class-wcfm-setup.php:413
#: views/capability/wcfm-view-capability.php:243
msgid "Advanced"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:413 helpers/class-wcfm-setup.php:413
msgid "Single product page Sold By template."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:416 helpers/class-wcfm-setup.php:417
msgid "Sold By Position"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:416
msgid "Bellow Price"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:416 helpers/class-wcfm-setup.php:417
#: views/settings/wcfm-view-settings.php:546
msgid "Below Short Description"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:416 helpers/class-wcfm-setup.php:417
#: views/settings/wcfm-view-settings.php:546
msgid "Below Add to Cart"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:416 helpers/class-wcfm-setup.php:417
msgid "Sold by display position at Single Product Page."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:417 helpers/class-wcfm-setup.php:418
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:453
msgid "Store Name Position"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:417 helpers/class-wcfm-setup.php:418
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:453
msgid "On Banner"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:417 helpers/class-wcfm-setup.php:418
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:453
msgid "At Header"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:417 helpers/class-wcfm-setup.php:418
msgid "Store name position at Vendor Store Page."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:418 helpers/class-wcfm-setup.php:419
msgid "Store Sidebar"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:418 helpers/class-wcfm-setup.php:419
msgid "Uncheck this to disable vendor store sidebar."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:419
msgid "Product Mulivendor"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:419
msgid ""
"Enable this to allow vendors to sell other vendor products, single product "
"multiple seller."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:420 helpers/class-wcfm-setup.php:425
msgid "Marketplace Shipping"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:420 helpers/class-wcfm-setup.php:425
msgid "Enable this to allow your vendors to setup their own shipping by country."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:421 helpers/class-wcfm-setup.php:427
msgid "Google Map API Key"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:421 helpers/class-wcfm-setup.php:427
msgid "%sAPI Key%s is needed to display map on store page"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:520 helpers/class-wcfm-setup-bak.php:684
#: helpers/class-wcfm-setup.php:532 helpers/class-wcfm-setup.php:704
msgid ""
"%1$s could not be installed (%2$s). <a href=\"%3$s\">Please install it "
"manually by clicking here.</a>"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:540 helpers/class-wcfm-setup-bak.php:704
#: helpers/class-wcfm-setup.php:555 helpers/class-wcfm-setup.php:724
msgid ""
"%1$s was installed but could not be activated. <a href=\"%2$s\">Please "
"activate it manually by clicking here.</a>"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:590 helpers/class-wcfm-setup.php:610
msgid "Setup WCFM Maketplace vendor registration:"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:592 helpers/class-wcfm-setup.php:612
msgid "Setup Registration"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:731 helpers/class-wcfm-setup.php:757
msgid "Commission setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:736 helpers/class-wcfm-setup.php:762
msgid "Commission For"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:736 helpers/class-wcfm-setup.php:762
#: views/vendors/wcfm-view-vendors-manage.php:164
#: views/vendors/wcfm-view-vendors-manage.php:205
#: views/vendors/wcfm-view-vendors-manage.php:208
#: views/vendors/wcfm-view-vendors-new.php:62
#: views/vendors/wcfm-view-vendors-new.php:69
#: views/vendors/wcfm-view-vendors-new.php:75
#: views/vendors/wcfm-view-vendors.php:28
#: views/vendors/wcfm-view-vendors.php:29
msgid "Vendor"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:736 helpers/class-wcfm-setup.php:762
msgid "Admin"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:737 helpers/class-wcfm-setup.php:763
msgid "Commission Mode"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:737 helpers/class-wcfm-setup.php:763
msgid ""
"You may setup more commission rules (By Sales Total and Product Price) from "
"setting panel."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:738 helpers/class-wcfm-setup.php:764
msgid "Commission Percent(%)"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:739 helpers/class-wcfm-setup.php:765
msgid "Commission Fixed"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:740 helpers/class-wcfm-setup.php:766
msgid "Shipping cost goes to vendor?"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:741 helpers/class-wcfm-setup.php:767
msgid "Tax goes to vendor?"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:742
msgid "Commission after deduct discounts?"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:742
msgid "Generate vendor commission after deduct coupon or other discounts."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:796 helpers/class-wcfm-setup.php:847
msgid "Withdrawal setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:801 helpers/class-wcfm-setup.php:852
msgid "Request auto-approve?"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:801 helpers/class-wcfm-setup.php:852
msgid ""
"Check this to automatically disburse payments to vendors on request, no "
"admin approval required. Auto disbursement only works for auto-payment "
"gateways, e.g. PayPal, Stripe etc. Bank Transfer or other non-autopay mode "
"always requires approval, as these are manual transactions."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:802 helpers/class-wcfm-setup.php:887
msgid "Withdraw Payment Methods"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:803 helpers/class-wcfm-setup.php:888
msgid "Enable Test Mode"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:810 helpers/class-wcfm-setup-bak.php:816
#: helpers/class-wcfm-setup.php:897 helpers/class-wcfm-setup.php:905
msgid "PayPal Client ID"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:811 helpers/class-wcfm-setup-bak.php:817
#: helpers/class-wcfm-setup.php:898 helpers/class-wcfm-setup.php:906
msgid "PayPal Secret Key"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:812 helpers/class-wcfm-setup-bak.php:818
#: helpers/class-wcfm-setup.php:899 helpers/class-wcfm-setup.php:907
msgid "Stripe Client ID"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:813 helpers/class-wcfm-setup-bak.php:819
#: helpers/class-wcfm-setup.php:900 helpers/class-wcfm-setup.php:908
msgid "Stripe Publish Key"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:814 helpers/class-wcfm-setup-bak.php:820
#: helpers/class-wcfm-setup.php:901 helpers/class-wcfm-setup.php:909
msgid "Stripe Secret Key"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:827 helpers/class-wcfm-setup.php:870
msgid "Order Status for Withdraw"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:828
msgid "Disallow Order Payment Mothods for Withdraw"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:828
msgid ""
"Order Payment Mothods which are not applicable for vendor withdrawal "
"request. e.g Order payment method COD and vendor receiving that amount "
"directly from customers."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:835 helpers/class-wcfm-setup.php:879
msgid "Minimum Withdraw Limit"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:835 helpers/class-wcfm-setup.php:879
msgid ""
"Minimum balance required to make a withdraw request. Leave blank to set no "
"minimum limits."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:836 helpers/class-wcfm-setup.php:880
msgid "Withdraw Threshold"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:836 helpers/class-wcfm-setup.php:880
msgid ""
"Withdraw Threshold Days, (Make order matured to make a withdraw request). "
"Leave empty to inactive this option."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:837 helpers/class-wcfm-setup.php:917
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:190
msgid "Withdrawal Charges"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:837 helpers/class-wcfm-setup.php:917
msgid "No Charge"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:837 helpers/class-wcfm-setup.php:917
msgid "Percent"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:837 helpers/class-wcfm-setup.php:917
msgid "Percent + Fixed"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:837 helpers/class-wcfm-setup.php:917
msgid "Charges applicable for each withdarwal."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:839 helpers/class-wcfm-setup.php:919
msgid "PayPal Charge"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:840 helpers/class-wcfm-setup-bak.php:845
#: helpers/class-wcfm-setup-bak.php:850 helpers/class-wcfm-setup-bak.php:855
#: helpers/class-wcfm-setup.php:920 helpers/class-wcfm-setup.php:925
#: helpers/class-wcfm-setup.php:930 helpers/class-wcfm-setup.php:935
msgid "Percent Charge(%)"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:841 helpers/class-wcfm-setup-bak.php:846
#: helpers/class-wcfm-setup-bak.php:851 helpers/class-wcfm-setup-bak.php:856
#: helpers/class-wcfm-setup.php:921 helpers/class-wcfm-setup.php:926
#: helpers/class-wcfm-setup.php:931 helpers/class-wcfm-setup.php:936
msgid "Fixed Charge"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:842 helpers/class-wcfm-setup-bak.php:847
#: helpers/class-wcfm-setup-bak.php:852 helpers/class-wcfm-setup-bak.php:857
#: helpers/class-wcfm-setup.php:922 helpers/class-wcfm-setup.php:927
#: helpers/class-wcfm-setup.php:932 helpers/class-wcfm-setup.php:937
msgid "Charge Tax"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:842 helpers/class-wcfm-setup-bak.php:847
#: helpers/class-wcfm-setup-bak.php:852 helpers/class-wcfm-setup-bak.php:857
#: helpers/class-wcfm-setup.php:922 helpers/class-wcfm-setup.php:927
#: helpers/class-wcfm-setup.php:932 helpers/class-wcfm-setup.php:937
msgid "Tax for withdrawal charge, calculate in percent."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:844 helpers/class-wcfm-setup.php:924
msgid "Stripe Charge"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:849 helpers/class-wcfm-setup.php:929
msgid "Skrill Charge"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:854 helpers/class-wcfm-setup.php:934
msgid "Bank Transfer Charge"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:882 helpers/class-wcfm-setup.php:974
#: views/settings/wcfm-view-settings.php:237
msgid "Dashboard Style"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:930 helpers/class-wcfm-setup.php:1022
#: views/capability/wcfm-view-capability.php:259
msgid "Backend Access"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:933 helpers/class-wcfm-setup.php:1025
msgid "Submit Products"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:934 helpers/class-wcfm-setup.php:1026
#: views/capability/wcfm-view-capability.php:214
msgid "Publish Products"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:935 helpers/class-wcfm-setup.php:1027
#: views/capability/wcfm-view-capability.php:215
msgid "Edit Live Products"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:936 helpers/class-wcfm-setup.php:1028
#: views/capability/wcfm-view-capability.php:217
msgid "Delete Products"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:940 helpers/class-wcfm-setup.php:1032
#: views/capability/wcfm-view-capability.php:538
msgid "Manage Bookings"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:945 helpers/class-wcfm-setup.php:1037
#: views/capability/wcfm-view-capability.php:572
msgid "Manage Subscriptions"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:950 helpers/class-wcfm-setup.php:1042
#: views/capability/wcfm-view-capability.php:364
msgid "by WP Job Manager."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:954 helpers/class-wcfm-setup.php:1046
#: views/capability/wcfm-view-capability.php:586
msgid "View Orders"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:955 helpers/class-wcfm-setup.php:1047
#: views/capability/wcfm-view-capability.php:587
msgid "Status Update"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:958 helpers/class-wcfm-setup.php:1050
#: views/capability/wcfm-view-capability.php:637
msgid "View Reports"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:980 helpers/class-wcfm-setup.php:1072
msgid "We are done!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:983 helpers/class-wcfm-setup.php:1075
msgid ""
"Your marketplace is ready. It's time to experience the things more Easily "
"and Peacefully. Also you will be a bit more relax than ever before, have "
"fun!!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:985 helpers/class-wcfm-setup.php:1077
msgid ""
"Your front-end dashboard is ready. It's time to experience the things more "
"Easily and Peacefully. Also you will be a bit more relax than ever before, "
"have fun!!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:990 helpers/class-wcfm-setup.php:1082
msgid "Next steps"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:992
msgid "Let's go to Dashboard"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:996 helpers/class-wcfm-setup.php:1088
msgid "Learn more"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:998 helpers/class-wcfm-setup.php:1090
msgid "Watch the tutorial videos"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:999 helpers/class-wcfm-setup.php:1091
msgid "WCFM - What & Why?"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:1000 helpers/class-wcfm-setup.php:1092
msgid "Choose your multi-vendor plugin"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:1281 helpers/class-wcfm-setup.php:1432
msgid "Return to the WordPress Dashboard"
msgstr ""

#: helpers/class-wcfm-setup.php:380
msgid "Setup your multi-vendor marketplace in minutes!"
msgstr ""

#: helpers/class-wcfm-setup.php:382
msgid "Install & Setup Multi-vendor Module"
msgstr ""

#: helpers/class-wcfm-setup.php:411
msgid "Store URL Base"
msgstr ""

#: helpers/class-wcfm-setup.php:413
msgid "As Tab"
msgstr ""

#: helpers/class-wcfm-setup.php:417 views/settings/wcfm-view-settings.php:546
msgid "Below Price"
msgstr ""

#: helpers/class-wcfm-setup.php:420
msgid "Store Sidebar Position"
msgstr ""

#: helpers/class-wcfm-setup.php:420
msgid "At Left"
msgstr ""

#: helpers/class-wcfm-setup.php:420
msgid "At Right"
msgstr ""

#: helpers/class-wcfm-setup.php:421
msgid "Store Related Products"
msgstr ""

#: helpers/class-wcfm-setup.php:421
msgid "As per WC Default Rule"
msgstr ""

#: helpers/class-wcfm-setup.php:421
msgid "Only same Store Products"
msgstr ""

#: helpers/class-wcfm-setup.php:421
msgid "Single product page related products rule."
msgstr ""

#: helpers/class-wcfm-setup.php:422
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:454
msgid "Products per page"
msgstr ""

#: helpers/class-wcfm-setup.php:422
msgid "No of products at Store per Page."
msgstr ""

#: helpers/class-wcfm-setup.php:426
msgid "Marketplace Shipping by Weight"
msgstr ""

#: helpers/class-wcfm-setup.php:426
msgid "Enable this to allow your vendors to setup their own shipping by weight."
msgstr ""

#: helpers/class-wcfm-setup.php:768
msgid "Commission after consider Vendor Coupon?"
msgstr ""

#: helpers/class-wcfm-setup.php:768
msgid "Generate vendor commission after deduct Vendor Coupon discounts."
msgstr ""

#: helpers/class-wcfm-setup.php:769
msgid "Commission after consider Admin Coupon?"
msgstr ""

#: helpers/class-wcfm-setup.php:769
msgid "Generate vendor commission after deduct Admin Coupon discounts."
msgstr ""

#: helpers/class-wcfm-setup.php:773
msgid "Commission Tax Settings"
msgstr ""

#: helpers/class-wcfm-setup.php:774
msgid "Enable this to deduct tax from vendor's commission."
msgstr ""

#: helpers/class-wcfm-setup.php:775
msgid "Tax Label"
msgstr ""

#: helpers/class-wcfm-setup.php:776
msgid "Tax Percent (%)"
msgstr ""

#: helpers/class-wcfm-setup.php:859
msgid "Withdrawal Mode"
msgstr ""

#: helpers/class-wcfm-setup.php:859
msgid "Manual Withdrawal"
msgstr ""

#: helpers/class-wcfm-setup.php:859
msgid "Periodic Withdrawal"
msgstr ""

#: helpers/class-wcfm-setup.php:859
msgid "By Order Status"
msgstr ""

#: helpers/class-wcfm-setup.php:861
msgid "Order Status"
msgstr ""

#: helpers/class-wcfm-setup.php:861
msgid "Order status for generate withdrawal request automatically."
msgstr ""

#: helpers/class-wcfm-setup.php:863
msgid "Schedule"
msgstr ""

#: helpers/class-wcfm-setup.php:863
msgid "Every 7 Days"
msgstr ""

#: helpers/class-wcfm-setup.php:863
msgid "Every 15 Days"
msgstr ""

#: helpers/class-wcfm-setup.php:863
msgid "Every 30 Days"
msgstr ""

#: helpers/class-wcfm-setup.php:863
msgid "Every 60 Days"
msgstr ""

#: helpers/class-wcfm-setup.php:863
msgid "Every 90 Days"
msgstr ""

#: helpers/class-wcfm-setup.php:895
msgid "Stripe 3D Secure and SCA?"
msgstr ""

#: helpers/class-wcfm-setup.php:895
msgid ""
"3D Secure and SCA ready transaction is only supported when both your "
"platform and the connected account (Vendor) are in the same region: both in "
"Europe or both in the U.S."
msgstr ""

#: helpers/class-wcfm-setup.php:896
msgid "Stripe Split Pay Mode"
msgstr ""

#: helpers/class-wcfm-setup.php:896
msgid "Direct Charges"
msgstr ""

#: helpers/class-wcfm-setup.php:896
msgid "Destination Charges"
msgstr ""

#: helpers/class-wcfm-setup.php:896
msgid "Transfer Charges"
msgstr ""

#: helpers/class-wcfm-setup.php:896
msgid "Set your preferred Stripe Split pay mode."
msgstr ""

#: helpers/class-wcfm-setup.php:943
msgid "Reverse Withdrawal setup"
msgstr ""

#: helpers/class-wcfm-setup.php:947
#: views/withdrawal/wcfm/wcfm-view-payments.php:51
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:36
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:69
msgid "Reverse Withdrawal"
msgstr ""

#: helpers/class-wcfm-setup.php:947
msgid ""
"Enable this to keep track reverse withdrawals. In case vendor receive full "
"payment (e.g. COD) from customer then they have to reverse-pay admin "
"commission. This is only applicable for auto-withdrawal payment methods."
msgstr ""

#: helpers/class-wcfm-setup.php:948
msgid "Reverse or No Withdrawal Payment Methods"
msgstr ""

#: helpers/class-wcfm-setup.php:948
msgid ""
"Order Payment Methods which are not applicable for vendor withdrawal "
"request. e.g Order payment method COD and vendor receiving that amount "
"directly from customers. So, no more require withdrawal request. You may "
"also enable Reverse Withdrawal to track reverse pending payments for such "
"payment options."
msgstr ""

#: helpers/class-wcfm-setup.php:950
msgid "Reverse Withdraw Limit"
msgstr ""

#: helpers/class-wcfm-setup.php:950
msgid ""
"Set reverse withdrawal threshold limit, if reverse-pay balance reach this "
"limit then vendor will not allow to withdrawal anymore. Leave empty to "
"inactive this option."
msgstr ""

#: helpers/class-wcfm-setup.php:1084
msgid "Let's go to the Dashboard"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:382
msgid "Albania"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:383
msgid "Algeria"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:384
msgid "Angola"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:385
msgid "Antigua and Barbuda"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:386
msgid "Argentina"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:387
msgid "Armenia"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:388
msgid "Azerbaijan"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:389
msgid "Bahamas"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:390
msgid "Bahrain"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:391
msgid "Bangladesh"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:392
msgid "Benin"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:393
msgid "Bhutan"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:394
msgid "Bolivia"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:395
msgid "Bosnia and Herzegovina"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:396
msgid "Botswana"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:397
msgid "Brunei"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:398
msgid "Cambodia"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:399
msgid "Chile"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:400
msgid "Colombia"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:401
msgid "Costa Rica"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:402
msgid "Dominican Republic"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:403
msgid "Ecuador"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:404
msgid "Egypt"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:405
msgid "El Salvador"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:406
msgid "Ethiopia"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:407
msgid "Gabon"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:408
msgid "Gambia"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:409
msgid "Ghana"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:410
msgid "Guatemala"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:411
msgid "Guyana"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:412
msgid "Iceland"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:413
msgid "India"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:414
msgid "Indonesia"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:415
msgid "Israel"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:416
msgid "Jamaica"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:417
msgid "Jordan"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:418
msgid "Kazakhstan"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:419
msgid "Kenya"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:420
msgid "Kuwait"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:421
msgid "Laos"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:422
msgid "Macao"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:423
msgid "Madagascar"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:424
msgid "Malaysia"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:425
msgid "Mauritius"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:426
msgid "Moldova"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:427
msgid "Monaco"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:428
msgid "Mongolia"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:429
msgid "Morocco"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:430
msgid "Mozambique"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:431
msgid "Namibia"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:432
msgid "Niger"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:433
msgid "Nigeria"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:434
msgid "Oman"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:435
msgid "Pakistan"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:436
msgid "Panama"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:437
msgid "Paraguay"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:438
msgid "Peru"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:439
msgid "Philippines"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:440
msgid "Qatar"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:441
msgid "Rwanda"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:442
msgid "San Marino"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:443
msgid "Saudi Arabia"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:444
msgid "Senegal"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:445
msgid "Serbia"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:446
msgid "South Africa"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:447
msgid "South Korea"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:448
msgid "Sri Lanka"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:449
msgid "Taiwan"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:450
msgid "Tanzania"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:451
msgid "Trinidad and Tobago"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:452
msgid "Tunisia"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:453
msgid "Turkey"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:454
msgid "Uruguay"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:455
msgid "Uzbekistan"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:456
msgid "Vietnam"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:476
msgid "Could not retrieve platform data: %s"
msgstr ""

#: helpers/class-wcfm-stripe-connect-client.php:535
msgid "Could not retrieve countryspec: %s"
msgstr ""

#: helpers/wcfm-core-functions.php:6
msgid ""
"%sWooCommerce Frontend Manager is inactive.%s The %sWooCommerce plugin%s "
"must be active for the WooCommerce Frontend Manager to work. Please "
"%sinstall & activate WooCommerce%s"
msgstr ""

#: helpers/wcfm-core-functions.php:16
msgid ""
"%sOops ..!!!%s You are using %sWC %s. WCFM works only with %sWC 3.0+%s. "
"PLease upgrade your WooCommerce version now to make your life easier and "
"peaceful by using WCFM."
msgstr ""

#: helpers/wcfm-core-functions.php:62
#. translators: 1) open <strong> tag 2) close </strong> tag 3) feature 4)
#. membership title
msgid ""
"%1$s%3$s%2$s: Your %4$s membership level doesn't give you permission to "
"access this page. Please upgrade your membership, or contact the "
"%1$sWebsite Manager%2$s for assistance."
msgstr ""

#: helpers/wcfm-core-functions.php:65 helpers/wcfm-core-functions.php:69
#. translators: 1) open <strong> tag 2) close </strong> tag 3) feature
msgid ""
"%1$s%3$s%2$s: You don't have permission to access this page. Please contact "
"your %1$sStore Admin%2$s for assistance."
msgstr ""

#: helpers/wcfm-core-functions.php:90 helpers/wcfm-core-functions.php:127
#. translators: 1) feature
msgid ""
"%s: Please ask your Store Admin to upgrade your dashboard to access this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:96
#. translators: 1) open <strong> tag 2) close </strong> tag 3) feature
msgid ""
"%1$s%2$s%3$s: Please ask your %1$sStore Admin%2$s to upgrade your dashboard "
"to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:105
#. translators: 1) feature
msgid ""
"%s: Upgrade your WCFM to WCFM - Ultimate to avail this feature. Disable "
"this notice from settings panel using \"Disable Ultimate Notice\" option."
msgstr ""

#: helpers/wcfm-core-functions.php:111
#. translators: 1) open <strong> tag 2) feature 3) close </strong> tag 4) link
#. start 5) link end
msgid ""
"%s%s%s: Upgrade your WCFM to %sWCFM - Ultimate%s to access this feature. "
"Disable this notice from settings panel using \"Disable Ultimate Notice\" "
"option."
msgstr ""

#: helpers/wcfm-core-functions.php:133
#. translators: 1) open <strong> tag 2) close </strong> tag 3) feature
msgid ""
"%1$s%3$s%2$s: Please ask your %1$sStore Admin%2$s to upgrade your dashboard "
"to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:142
#. translators: 1) feature
msgid "%s: Associate your WCFM with WCFM - Groups & Staffs to avail this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:148
#. translators: 1) open <strong> tag 2) feature 3) close </strong> tag 4) link
#. start 5) link end
msgid ""
"%s%s%s: Associate your WCFM with %sWCFM - Groups & Staffs%s to access this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:164
#. translators: 1) feature
msgid "%s: Please contact your Store Admin to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:170
#. translators: 1) open <strong> tag 2) close </strong> tag 3) feature
msgid ""
"%1$s%3$s%2$s: Please contact your %1$sStore Admin%2$s to access this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:179
#. translators: 1) feature
msgid "%s: Associate your WCFM with WCFM - Analytics to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:185
#. translators: 1) open <strong> tag 2) feature 3) close </strong> tag 4) link
#. start 5) link end
msgid ""
"%s%s%s: Associate your WCFM with %sWCFM - Analytics%s to access this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:1142
msgid "Please insert Article Title before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1143
msgid "Article Successfully Saved."
msgstr ""

#: helpers/wcfm-core-functions.php:1144
msgid "Article Successfully submitted for moderation."
msgstr ""

#: helpers/wcfm-core-functions.php:1145
msgid "Article Successfully Published."
msgstr ""

#: helpers/wcfm-core-functions.php:1157
msgid "Please insert Product Title before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1158
msgid "Please insert Product Short Description before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1159
msgid "Please insert Product Description before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1160
msgid "Product SKU must be unique."
msgstr ""

#: helpers/wcfm-core-functions.php:1161
msgid "Variation SKU must be unique."
msgstr ""

#: helpers/wcfm-core-functions.php:1162
msgid "Product Successfully Saved."
msgstr ""

#: helpers/wcfm-core-functions.php:1163
msgid "Product Successfully submitted for moderation."
msgstr ""

#: helpers/wcfm-core-functions.php:1164
msgid "Product Successfully Published."
msgstr ""

#: helpers/wcfm-core-functions.php:1165
msgid "Set Stock"
msgstr ""

#: helpers/wcfm-core-functions.php:1166
#: views/products-manager/wcfm-view-products-manage-tabs.php:252
msgid "Increase Stock"
msgstr ""

#: helpers/wcfm-core-functions.php:1167
#: views/integrations/wcfm-view-integrations-products-manage.php:352
#: views/products-manager/wcfm-view-products-manage-tabs.php:287
msgid "Regular Price"
msgstr ""

#: helpers/wcfm-core-functions.php:1168
msgid "Regular price increase by"
msgstr ""

#: helpers/wcfm-core-functions.php:1169
msgid "Regular price decrease by"
msgstr ""

#: helpers/wcfm-core-functions.php:1170
#: views/products-manager/wcfm-view-products-manage-tabs.php:288
#: views/products-manager/wcfm-view-products-manage.php:557
#: views/products-popup/wcfm-view-product-popup.php:137
msgid "Sale Price"
msgstr ""

#: helpers/wcfm-core-functions.php:1171
msgid "Sale price increase by"
msgstr ""

#: helpers/wcfm-core-functions.php:1172
msgid "Sale price decrease by"
msgstr ""

#: helpers/wcfm-core-functions.php:1173
#: views/products-manager/wcfm-view-products-manage-tabs.php:110
#: views/products-manager/wcfm-view-products-manage-tabs.php:260
msgid "Length"
msgstr ""

#: helpers/wcfm-core-functions.php:1174
#: views/products-manager/wcfm-view-products-manage-tabs.php:111
#: views/products-manager/wcfm-view-products-manage-tabs.php:261
msgid "Width"
msgstr ""

#: helpers/wcfm-core-functions.php:1175
#: views/products-manager/wcfm-view-products-manage-tabs.php:112
#: views/products-manager/wcfm-view-products-manage-tabs.php:262
msgid "Height"
msgstr ""

#: helpers/wcfm-core-functions.php:1176
#: views/products-manager/wcfm-view-products-manage-tabs.php:109
#: views/products-manager/wcfm-view-products-manage-tabs.php:263
#: views/settings/wcfm-view-wcmarketplace-settings.php:680
msgid "Weight"
msgstr ""

#: helpers/wcfm-core-functions.php:1177
#: views/products-manager/wcfm-view-products-manage-tabs.php:64
msgid "Download Limit"
msgstr ""

#: helpers/wcfm-core-functions.php:1178
#: views/products-manager/wcfm-view-products-manage-tabs.php:65
msgid "Download Expiry"
msgstr ""

#: helpers/wcfm-core-functions.php:1192
msgid "Please insert atleast Coupon Title before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1193
msgid "Coupon Successfully Saved."
msgstr ""

#: helpers/wcfm-core-functions.php:1194
msgid "Coupon Successfully Published."
msgstr ""

#: helpers/wcfm-core-functions.php:1206
msgid "Please insert atleast Knowledgebase Title before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1207
msgid "Knowledgebase Successfully Saved."
msgstr ""

#: helpers/wcfm-core-functions.php:1208
msgid "Knowledgebase Successfully Published."
msgstr ""

#: helpers/wcfm-core-functions.php:1220
msgid "Please insert atleast Topic Title before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1221
msgid "Topic Successfully Saved."
msgstr ""

#: helpers/wcfm-core-functions.php:1222
msgid "Topic Successfully Published."
msgstr ""

#: helpers/wcfm-core-functions.php:1234
msgid "Please write something before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1235
msgid "Reply send failed, try again."
msgstr ""

#: helpers/wcfm-core-functions.php:1236
msgid "Reply Successfully Send."
msgstr ""

#: helpers/wcfm-core-functions.php:1248
msgid "Name is required."
msgstr ""

#: helpers/wcfm-core-functions.php:1249
msgid "Email is required."
msgstr ""

#: helpers/wcfm-core-functions.php:1250
msgid "Please insert your enquiry before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1251
msgid "Please insert your reply before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1252
msgid "Your enquiry successfully sent."
msgstr ""

#: helpers/wcfm-core-functions.php:1253
msgid "Enquiry reply successfully published."
msgstr ""

#: helpers/wcfm-core-functions.php:1254
msgid "Your reply successfully sent."
msgstr ""

#: helpers/wcfm-core-functions.php:1266
msgid "Please insert Username before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1267
msgid "Please insert Email before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1268
msgid "Please insert Store Name before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1269 helpers/wcfm-core-functions.php:1286
msgid "This Username already exists."
msgstr ""

#: helpers/wcfm-core-functions.php:1270 helpers/wcfm-core-functions.php:1287
msgid "This Email already exists."
msgstr ""

#: helpers/wcfm-core-functions.php:1271
msgid "Vendor Saving Failed."
msgstr ""

#: helpers/wcfm-core-functions.php:1272
msgid "Vendor Successfully Saved."
msgstr ""

#: helpers/wcfm-core-functions.php:1284
msgid "Please insert Customer Username before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1285
msgid "Please insert Customer Email before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:1288
msgid "Customer Saving Failed."
msgstr ""

#: helpers/wcfm-core-functions.php:1289
msgid "Customer Successfully Saved."
msgstr ""

#: helpers/wcfm-core-functions.php:1290
msgid "Invalid Customer."
msgstr ""

#: helpers/wcfm-core-functions.php:1302
msgid "Are you sure and want to approve / publish this 'Product'?"
msgstr ""

#: helpers/wcfm-core-functions.php:1303
msgid ""
"Are you sure and want to reject this 'Product'?\n"
"Reason:"
msgstr ""

#: helpers/wcfm-core-functions.php:1304
msgid "Are you sure and want to archive this 'Product'?"
msgstr ""

#: helpers/wcfm-core-functions.php:1305
msgid ""
"Are you sure and want to delete this 'Block'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1306
msgid ""
"Are you sure and want to delete this 'Article'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1307
msgid ""
"Are you sure and want to delete this 'Product'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1308
msgid ""
"Are you sure and want to delete this 'Message'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1309
msgid ""
"Are you sure and want to delete this 'Order'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1310
msgid ""
"Are you sure and want to delete this 'Enquiry'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1311
msgid ""
"Are you sure and want to delete this 'Support Ticket'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1312
msgid ""
"Are you sure and want to delete this 'Follower'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1313
msgid ""
"Are you sure and want to delete this 'Following'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1314
msgid ""
"Are you sure and want to delete this 'Resource'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1315
msgid ""
"Are you sure and want to delete this 'Bid'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1316
msgid "Are you sure and want to 'Mark as Complete' this Order?"
msgstr ""

#: helpers/wcfm-core-functions.php:1317
msgid "Are you sure and want to 'Mark as Confirmed' this Booking?"
msgstr ""

#: helpers/wcfm-core-functions.php:1318
msgid "Are you sure and want to 'Mark as Declined' this Booking?"
msgstr ""

#: helpers/wcfm-core-functions.php:1319
msgid "Are you sure and want to 'Mark as Complete' this Appointment?"
msgstr ""

#: helpers/wcfm-core-functions.php:1321
msgid "Select all"
msgstr ""

#: helpers/wcfm-core-functions.php:1322
msgid "Select none"
msgstr ""

#: helpers/wcfm-core-functions.php:1323
msgid "Any"
msgstr ""

#: helpers/wcfm-core-functions.php:1324
msgid "Enter a name for the new attribute term:"
msgstr ""

#: helpers/wcfm-core-functions.php:1325
msgid ""
"Please upgrade your WC Frontend Manager to Ultimate version and avail this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:1326
msgid ""
"Install WC Frontend Manager Ultimate and WooCommerce PDF Invoices & Packing "
"Slips to avail this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:1327
msgid "Please select some element first!!"
msgstr ""

#: helpers/wcfm-core-functions.php:1328
msgid ""
"Are you sure and want to do this?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1329
msgid "Are you sure and want to do this?"
msgstr ""

#: helpers/wcfm-core-functions.php:1330
#: includes/libs/php/class-wcfm-fields.php:660
msgid "Everywhere Else"
msgstr ""

#: helpers/wcfm-core-functions.php:1331
#: includes/libs/php/class-wcfm-fields.php:83
#: includes/libs/php/class-wcfm-fields.php:85
#: includes/libs/php/class-wcfm-fields.php:88
#: includes/libs/php/class-wcfm-fields.php:149
#: includes/libs/php/class-wcfm-fields.php:222
#: includes/libs/php/class-wcfm-fields.php:264
#: includes/libs/php/class-wcfm-fields.php:330
#: includes/libs/php/class-wcfm-fields.php:392
#: includes/libs/php/class-wcfm-fields.php:452
#: includes/libs/php/class-wcfm-fields.php:557
#: includes/libs/php/class-wcfm-fields.php:635
#: includes/libs/php/class-wcfm-fields.php:714
#: includes/libs/php/class-wcfm-fields.php:790
#: includes/libs/php/class-wcfm-fields.php:793
#: includes/libs/php/class-wcfm-fields.php:865
#: includes/libs/php/class-wcfm-fields.php:867
#: includes/libs/php/class-wcfm-fields.php:870
#: includes/libs/php/class-wcfm-fields.php:928
#: includes/libs/php/class-wcfm-fields.php:977
#: includes/libs/php/class-wcfm-fields.php:1071
#: views/profile/wcfm-view-profile.php:230
msgid "This field is required."
msgstr ""

#: helpers/wcfm-core-functions.php:1332
msgid "Choose "
msgstr ""

#: helpers/wcfm-core-functions.php:1333
msgid "All Attributes"
msgstr ""

#: helpers/wcfm-core-functions.php:1334
msgid "Search for a page ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1335
msgid "Search for an attribute ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1336
msgid "Filter by product ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1337
msgid "Filter by category ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1338
msgid "Choose Categories ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1339
msgid "Choose Listings ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1340
msgid "Choose Tags ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1341
msgid "Choose"
msgstr ""

#: helpers/wcfm-core-functions.php:1342
msgid "No categories"
msgstr ""

#: helpers/wcfm-core-functions.php:1343
msgid "Searching ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1344
msgid "No matching result found."
msgstr ""

#: helpers/wcfm-core-functions.php:1345
msgid "Loading ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1346
msgid "Minimum input character "
msgstr ""

#: helpers/wcfm-core-functions.php:1349
msgid "Add New Block"
msgstr ""

#: helpers/wcfm-core-functions.php:1350
msgid "Remove Block"
msgstr ""

#: helpers/wcfm-core-functions.php:1351
msgid "Toggle Block"
msgstr ""

#: helpers/wcfm-core-functions.php:1352
msgid "Drag to re-arrange blocks"
msgstr ""

#: helpers/wcfm-core-functions.php:1353
msgid "Do you want to add this item(s) to your store?"
msgstr ""

#: helpers/wcfm-core-functions.php:1354
msgid "Please select some product first!"
msgstr ""

#: helpers/wcfm-core-functions.php:1355
msgid "Please login to the site first!"
msgstr ""

#: helpers/wcfm-core-functions.php:1356
msgid "Please select a shipping method"
msgstr ""

#: helpers/wcfm-core-functions.php:1357
msgid "Shipping method not found"
msgstr ""

#: helpers/wcfm-core-functions.php:1358
msgid "Shipping zone not found"
msgstr ""

#: helpers/wcfm-core-functions.php:1359
msgid ""
"Are you sure you want to delete this 'Shipping Method'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:1360
msgid ""
"Are you sure you want to link all variations? This will create a new "
"variation for each and every possible combination of variation attributes "
"(max 50 per run)."
msgstr ""

#: helpers/wcfm-core-functions.php:1373 views/notice/wcfm-view-notices.php:26
#: views/wcfm-view-header-panels.php:72
msgid "Announcement"
msgstr ""

#: helpers/wcfm-core-functions.php:1374
msgid "Product Review"
msgstr ""

#: helpers/wcfm-core-functions.php:1375
msgid "Low Stock Product"
msgstr ""

#: helpers/wcfm-core-functions.php:1377
msgid "Status Updated"
msgstr ""

#: helpers/wcfm-core-functions.php:1378
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:23
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:30
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:26
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:33
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:47
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:49
msgid "Withdrawal Requests"
msgstr ""

#: helpers/wcfm-core-functions.php:1379
#: views/capability/wcfm-view-capability.php:280
msgid "Refund Requests"
msgstr ""

#: helpers/wcfm-core-functions.php:1382
msgid "New Order"
msgstr ""

#: helpers/wcfm-core-functions.php:1966
#. translators: %1$s is the file field label; %2$s is the file type; %3$s is
#. the list of allowed file types.
msgid "\"%1$s\" (filetype %2$s) needs to be one of the following file types: %3$s"
msgstr ""

#: helpers/wcfm-core-functions.php:1969
#. translators: %s is the list of allowed file types.
msgid "Uploaded files need to be one of the following file types: %s"
msgstr ""

#: helpers/wcfm-core-functions.php:2033
msgid "Tutorial"
msgstr ""

#: includes/emails/class-wcfm-email-new-enquiry.php:24
msgid "New Inquiry notification emails are sent when new inquiry raised by users."
msgstr ""

#: includes/emails/class-wcfm-email-new-enquiry.php:27
msgid "New enquiry for"
msgstr ""

#: includes/emails/class-wcfm-email-new-enquiry.php:202
msgid "Enable/Disable"
msgstr ""

#: includes/emails/class-wcfm-email-new-enquiry.php:204
msgid "Enable this email notification."
msgstr ""

#: includes/emails/class-wcfm-email-new-enquiry.php:208
msgid "Subject"
msgstr ""

#: includes/emails/class-wcfm-email-new-enquiry.php:210
msgid ""
"This controls the email subject line. Leave it blank to use the default "
"subject: <code>%s</code>."
msgstr ""

#: includes/emails/class-wcfm-email-new-enquiry.php:215
msgid "Email Heading"
msgstr ""

#: includes/emails/class-wcfm-email-new-enquiry.php:217
msgid ""
"This controls the main heading contained within the email notification. "
"Leave it blank to use the default heading: <code>%s</code>."
msgstr ""

#: includes/emails/class-wcfm-email-new-enquiry.php:222
msgid "Email Type"
msgstr ""

#: includes/emails/class-wcfm-email-new-enquiry.php:224
msgid "Choose which format of email to be sent."
msgstr ""

#: includes/emails/class-wcfm-email-new-enquiry.php:228
msgid "Plain Text"
msgstr ""

#: includes/emails/class-wcfm-email-new-enquiry.php:229
msgid "HTML"
msgstr ""

#: includes/emails/class-wcfm-email-new-enquiry.php:230
msgid "Multipart"
msgstr ""

#: includes/libs/php/class-wcfm-fields.php:657
#: includes/libs/php/class-wcfm-fields.php:665
msgid "-Select a location-"
msgstr ""

#: includes/libs/php/class-wcfm-fields.php:825
msgid "Upload"
msgstr ""

#: includes/libs/php/class-wcfm-fields.php:829
msgid "Remove"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:533
#: includes/reports/class-wcfm-report-sales-by-date.php:460
#. translators: %s: average total sales
msgid "%s average gross daily sales"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:538
#: includes/reports/class-wcfm-report-sales-by-date.php:465
#. translators: %s: average sales
msgid "%s average net daily sales"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:546
#: includes/reports/class-wcfm-report-sales-by-date.php:473
#. translators: %s: average total sales
msgid "%s average gross monthly sales"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:551
#: includes/reports/class-wcfm-report-sales-by-date.php:478
#. translators: %s: average sales
msgid "%s average net monthly sales"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:561
#: includes/reports/class-wcfm-report-sales-by-date.php:487
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:204
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:174
#: includes/reports/class-wcpvendors-report-sales-by-date.php:181
#: includes/reports/class-wcvendors-report-sales-by-date.php:211
#. translators: %s: total sales
msgid "%s gross sales in this period"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:564
#: includes/reports/class-wcfm-report-sales-by-date.php:490
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:205
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:175
#: includes/reports/class-wcpvendors-report-sales-by-date.php:182
#: includes/reports/class-wcvendors-report-sales-by-date.php:212
msgid ""
"This is the sum of the order totals after any refunds and including "
"shipping and taxes."
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:580
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:235
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:205
#: includes/reports/class-wcpvendors-report-sales-by-date.php:199
#: includes/reports/class-wcvendors-report-sales-by-date.php:220
msgid "%s total earnings"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:581
#: includes/reports/class-wcfm-report-analytics.php:127
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:236
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:206
#: includes/reports/class-wcpvendors-report-sales-by-date.php:200
#: includes/reports/class-wcvendors-report-sales-by-date.php:221
msgid ""
"This is the sum of the earned commission including shipping and taxes if "
"applicable."
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:589
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:244
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:214
#: includes/reports/class-wcpvendors-report-sales-by-date.php:208
#: includes/reports/class-wcvendors-report-sales-by-date.php:229
msgid "%s total withdrawal"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:590
msgid ""
"This is the sum of the commission withdraw including shipping and taxes if "
"applicable."
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:599
#: includes/reports/class-wcfm-report-sales-by-date.php:523
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:270
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:231
#: includes/reports/class-wcpvendors-report-sales-by-date.php:226
#: includes/reports/class-wcvendors-report-sales-by-date.php:245
#. translators: %s: total orders
msgid "%s orders placed"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:609
#: includes/reports/class-wcfm-report-sales-by-date.php:533
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:276
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:237
#: includes/reports/class-wcpvendors-report-sales-by-date.php:232
#: includes/reports/class-wcvendors-report-sales-by-date.php:251
#. translators: %s: total items
msgid "%s items purchased"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:629
#: includes/reports/class-wcfm-report-sales-by-date.php:553
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:291
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:244
#: includes/reports/class-wcpvendors-report-sales-by-date.php:239
#: includes/reports/class-wcvendors-report-sales-by-date.php:259
#: includes/reports/class-wcvendors-report-sales-by-date.php:267
#. translators: %s: total shipping
msgid "%s charged for shipping"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:639
#: includes/reports/class-wcfm-report-sales-by-date.php:562
#. translators: %s: total coupons
msgid "%s worth of coupons used"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:655
#: includes/reports/class-wcfm-report-sales-by-date.php:578
msgid "Last month"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:656
#: includes/reports/class-wcfm-report-sales-by-date.php:579
msgid "This month"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:657
#: includes/reports/class-wcfm-report-sales-by-date.php:580
msgid "Last 7 days"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:690
#: includes/reports/class-dokan-report-sales-by-date.php:907
#: includes/reports/class-wcfm-report-analytics.php:170
#: includes/reports/class-wcfm-report-sales-by-date.php:613
#: includes/reports/class-wcfm-report-sales-by-date.php:797
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:343
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:689
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:294
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:603
#: includes/reports/class-wcpvendors-report-sales-by-date.php:288
#: includes/reports/class-wcpvendors-report-sales-by-date.php:506
#: includes/reports/class-wcvendors-report-sales-by-date.php:318
#: includes/reports/class-wcvendors-report-sales-by-date.php:632
#: views/articles/wcfm-view-articles.php:113
#: views/articles/wcfm-view-articles.php:124
#: views/customers/wcfm-view-customers-details.php:288
#: views/customers/wcfm-view-customers-details.php:298
#: views/enquiry/wcfm-view-enquiry.php:89
#: views/enquiry/wcfm-view-enquiry.php:104
#: views/messages/wcfm-view-messages.php:81
#: views/messages/wcfm-view-messages.php:94
#: views/orders/wcfm-view-orders.php:122 views/orders/wcfm-view-orders.php:153
#: views/products/wcfm-view-products.php:240
#: views/products/wcfm-view-products.php:264
#: views/withdrawal/dokan/wcfm-view-payments.php:71
#: views/withdrawal/dokan/wcfm-view-payments.php:80
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:57
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:67
#: views/withdrawal/wcfm/wcfm-view-payments.php:85
#: views/withdrawal/wcfm/wcfm-view-payments.php:100
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:80
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:97
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:91
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:104
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:97
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:111
#: views/withdrawal/wcmp/wcfm-view-payments.php:69
#: views/withdrawal/wcmp/wcfm-view-payments.php:80
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:61
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:70
msgid "Date"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:694
#: includes/reports/class-wcfm-report-analytics.php:176
#: includes/reports/class-wcfm-report-sales-by-date.php:617
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:349
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:300
#: includes/reports/class-wcpvendors-report-sales-by-date.php:294
#: includes/reports/class-wcvendors-report-sales-by-date.php:324
#: views/capability/wcfm-view-capability.php:593
msgid "Export CSV"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:815
#: includes/reports/class-wcfm-report-sales-by-date.php:712
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:586
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:527
#: includes/reports/class-wcpvendors-report-sales-by-date.php:430
#: includes/reports/class-wcvendors-report-sales-by-date.php:543
#: views/customers/wcfm-view-customers-details.php:287
#: views/customers/wcfm-view-customers-details.php:297
#: views/orders/wcfm-view-orders.php:104 views/orders/wcfm-view-orders.php:135
#: views/vendors/wcfm-view-vendors.php:82
#: views/vendors/wcfm-view-vendors.php:103
msgid "Gross Sales"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:854
#: includes/reports/class-wcfm-report-sales-by-date.php:730
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:649
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:566
#: includes/reports/class-wcpvendors-report-sales-by-date.php:469
#: includes/reports/class-wcvendors-report-sales-by-date.php:595
msgid "Order Counts"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:862
#: includes/reports/class-wcfm-report-sales-by-date.php:739
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:658
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:574
#: includes/reports/class-wcpvendors-report-sales-by-date.php:477
#: includes/reports/class-wcvendors-report-sales-by-date.php:603
msgid "Order Item Counts"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:870
#: includes/reports/class-wcfm-report-sales-by-date.php:757
msgid "Coupon Amounts"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:878
#: includes/reports/class-wcfm-report-sales-by-date.php:766
msgid "Refund Amounts"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:889
#: includes/reports/class-wcfm-report-sales-by-date.php:778
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:670
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:585
#: includes/reports/class-wcpvendors-report-sales-by-date.php:488
#: includes/reports/class-wcvendors-report-sales-by-date.php:614
msgid "Sales Report by Date"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:916
#: includes/reports/class-wcfm-report-sales-by-date.php:806
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:698
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:612
#: includes/reports/class-wcpvendors-report-sales-by-date.php:515
#: includes/reports/class-wcvendors-report-sales-by-date.php:641
#: views/coupons/wcfm-view-coupons.php:57
#: views/coupons/wcfm-view-coupons.php:68 views/orders/wcfm-view-orders.php:114
#: views/orders/wcfm-view-orders.php:145
#: views/withdrawal/dokan/wcfm-view-payments.php:68
#: views/withdrawal/dokan/wcfm-view-payments.php:77
#: views/withdrawal/wcfm/wcfm-view-payments.php:79
#: views/withdrawal/wcfm/wcfm-view-payments.php:94
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:74
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:91
msgid "Amount"
msgstr ""

#: includes/reports/class-wcfm-report-analytics.php:117
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:194
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:164
#: includes/reports/class-wcpvendors-report-sales-by-date.php:171
#: includes/reports/class-wcvendors-report-sales-by-date.php:201
msgid "%s average daily sales"
msgstr ""

#: includes/reports/class-wcfm-report-analytics.php:121
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:198
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:168
#: includes/reports/class-wcpvendors-report-sales-by-date.php:175
#: includes/reports/class-wcvendors-report-sales-by-date.php:205
msgid "%s average monthly sales"
msgstr ""

#: includes/reports/class-wcfm-report-analytics.php:126
msgid "%s total earned commission"
msgstr ""

#: includes/reports/class-wcfm-report-analytics.php:142
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:307
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:260
#: includes/reports/class-wcpvendors-report-sales-by-date.php:254
#: includes/reports/class-wcvendors-report-sales-by-date.php:284
#: views/enquiry/wcfm-view-enquiry.php:27
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:30
#: views/reports/wcfm-view-reports-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:57
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:30
msgid "Last Month"
msgstr ""

#: includes/reports/class-wcfm-report-analytics.php:144
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:309
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:262
#: includes/reports/class-wcpvendors-report-sales-by-date.php:256
#: includes/reports/class-wcvendors-report-sales-by-date.php:286
#: views/enquiry/wcfm-view-enquiry.php:25
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:32
#: views/reports/wcfm-view-reports-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:59
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:32
msgid "Last 7 Days"
msgstr ""

#: includes/reports/class-wcfm-report-analytics.php:260
msgid "Daily Views"
msgstr ""

#: includes/reports/class-wcfm-report-analytics.php:270
#: views/dashboard/wcfm-view-dashboard.php:212
#: views/dashboard/wcfm-view-dokan-dashboard.php:210
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:221
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:231
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:238
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:217
msgid "Store Analytics"
msgstr ""

#: includes/reports/class-wcfm-report-sales-by-date.php:505
#: includes/reports/class-wcpvendors-report-sales-by-date.php:190
#. translators: %s: net sales
msgid "%s net sales in this period"
msgstr ""

#: includes/reports/class-wcfm-report-sales-by-date.php:508
#: includes/reports/class-wcpvendors-report-sales-by-date.php:191
msgid ""
"This is the sum of the order totals after any refunds and excluding "
"shipping and taxes."
msgstr ""

#: includes/reports/class-wcfm-report-sales-by-date.php:542
#. translators: 1: total refunds 2: total refunded orders 3: refunded items
msgid "%1$s refunded %2$d order (%3$d item)"
msgid_plural "%1$s refunded %2$d orders (%3$d items)"
msgstr[0] ""
msgstr[1] ""

#: includes/reports/class-wcfm-report-sales-by-date.php:748
msgid "Net Sales"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:217
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:187
msgid "%s total admin fees"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:218
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:188
msgid ""
"This is the sum of the admin fees including shipping and taxes if "
"applicable."
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:226
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:196
msgid "%s total paid fees"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:227
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:197
msgid ""
"This is the sum of the admin fees paid including shipping and taxes if "
"applicable."
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:245
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:215
#: includes/reports/class-wcpvendors-report-sales-by-date.php:209
#: includes/reports/class-wcvendors-report-sales-by-date.php:230
msgid ""
"This is the sum of the commission paid including shipping and taxes if "
"applicable."
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:254
msgid "%s total refund"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:255
msgid "This is the sum of the refunds and partial refunds."
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:283
msgid "%s total tax"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:597
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:537
msgid "Admin Fees"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:608
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:547
#: views/vendors/wcfm-view-vendors.php:85
#: views/vendors/wcfm-view-vendors.php:106
msgid "Paid Fees"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:618
msgid "Refunds"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:628
msgid "Tax Amounts"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:639
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:557
#: includes/reports/class-wcpvendors-report-sales-by-date.php:460
#: includes/reports/class-wcvendors-report-sales-by-date.php:574
#: includes/reports/class-wcvendors-report-sales-by-date.php:585
msgid "Shipping Amounts"
msgstr ""

#: includes/shortcodes/class-wcfm-shortcode-follow.php:33
msgid "Follow Me"
msgstr ""

#: includes/shortcodes/class-wcfm-shortcode-notification.php:51
#: views/wcfm-view-header-panels.php:64
msgid "Notification Board"
msgstr ""

#: includes/shortcodes/class-wcfm-shortcode-notification.php:55
#: views/enquiry/wcfm-view-enquiry.php:37
msgid "Enquiry Board"
msgstr ""

#: includes/shortcodes/class-wcfm-shortcode-notification.php:59
msgid "Notice Board"
msgstr ""

#: views/articles/wcfm-view-articles-manage-tabs.php:53
msgid "Yoast SEO"
msgstr ""

#: views/articles/wcfm-view-articles-manage-tabs.php:58
#: views/integrations/wcfm-view-integrations-products-manage.php:165
msgid "Enter a focus keyword"
msgstr ""

#: views/articles/wcfm-view-articles-manage-tabs.php:58
#: views/articles/wcfm-view-articles-manage-tabs.php:97
#: views/integrations/wcfm-view-integrations-products-manage.php:165
#: views/integrations/wcfm-view-integrations-products-manage.php:203
msgid "It should appear in title and first paragraph of the copy."
msgstr ""

#: views/articles/wcfm-view-articles-manage-tabs.php:59
#: views/integrations/wcfm-view-integrations-products-manage.php:166
msgid "Meta description"
msgstr ""

#: views/articles/wcfm-view-articles-manage-tabs.php:59
#: views/integrations/wcfm-view-integrations-products-manage.php:166
msgid "It should not be more than 156 characters."
msgstr ""

#: views/articles/wcfm-view-articles-manage-tabs.php:78
#: views/integrations/wcfm-view-integrations-products-manage.php:184
#: views/integrations/wcfm-view-integrations-products-manage.php:222
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:81
#: views/knowledgebase/wcfm-view-knowledgebase.php:76
#: views/knowledgebase/wcfm-view-knowledgebase.php:88
#: views/notice/wcfm-view-notice-manage.php:85
#: views/notice/wcfm-view-notices.php:56 views/notice/wcfm-view-notices.php:67
msgid "Title"
msgstr ""

#: views/articles/wcfm-view-articles-manage-tabs.php:78
#: views/integrations/wcfm-view-integrations-products-manage.php:184
msgid "Most search engines use a maximum of 60 chars for the title."
msgstr ""

#: views/articles/wcfm-view-articles-manage-tabs.php:79
#: views/articles/wcfm-view-articles-manage-tabs.php:98
#: views/integrations/wcfm-view-integrations-products-manage.php:185
#: views/integrations/wcfm-view-integrations-products-manage.php:204
msgid "Most search engines use a maximum of 160 chars for the description."
msgstr ""

#: views/articles/wcfm-view-articles-manage-tabs.php:97
#: views/integrations/wcfm-view-integrations-products-manage.php:203
msgid "Enter focus keyword(s) comma separated"
msgstr ""

#: views/articles/wcfm-view-articles-manage-tabs.php:98
#: views/integrations/wcfm-view-integrations-products-manage.php:204
#: views/settings/wcfm-view-dokan-settings.php:494
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1067
msgid "Meta Description"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:122
msgid "Manage Article"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:130
msgid "Edit Article"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:130
msgid "Add Article"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:139
#: views/articles/wcfm-view-articles.php:112
#: views/articles/wcfm-view-articles.php:123
#: views/listings/wcfm-view-listings.php:124
#: views/listings/wcfm-view-listings.php:139
#: views/products/wcfm-view-products.php:239
#: views/products/wcfm-view-products.php:263
#: views/products-manager/wcfm-view-products-manage.php:477
msgid "Views"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:158
#: views/articles/wcfm-view-articles.php:65
msgid "Add New Article"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:176
msgid "Article Title"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:229
#: views/articles/wcfm-view-articles-manage.php:367
#: views/products-manager/wcfm-view-products-manage.php:623
#: views/products-manager/wcfm-view-products-manage.php:630
#: views/products-manager/wcfm-view-products-manage.php:832
#: views/products-manager/wcfm-view-products-manage.php:839
#: views/products-popup/wcfm-view-product-popup.php:210
#: views/products-popup/wcfm-view-product-popup.php:217
msgid "Tags"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:229
#: views/articles/wcfm-view-articles-manage.php:367
msgid "Separate Article Tags with commas"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:229
#: views/articles/wcfm-view-articles-manage.php:367
#: views/products-manager/wcfm-view-products-manage.php:623
#: views/products-manager/wcfm-view-products-manage.php:832
msgid "Choose from the most used tags"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:256
#: views/articles/wcfm-view-articles-manage.php:400
#: views/products-manager/wcfm-view-products-manage.php:672
#: views/products-manager/wcfm-view-products-manage.php:884
#: views/products-popup/wcfm-view-product-popup.php:290
msgid "Short Description"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:302
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:100
#: views/products-manager/wcfm-view-products-manage.php:730
msgid "Add new category"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:308
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:106
#: views/products-manager/wcfm-view-products-manage.php:737
msgid "-- Parent category --"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:318
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:116
#: views/products-manager/wcfm-view-products-manage-tabs.php:189
#: views/products-manager/wcfm-view-products-manage.php:748
#: views/products-manager/wcfm-view-products-manage.php:813
#: views/vendors/wcfm-view-vendors-new.php:69
msgid "Add"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:426
#: views/articles/wcfm-view-articles-manage.php:428
#: views/coupons/wcfm-view-coupons-manage.php:161
#: views/coupons/wcfm-view-coupons-manage.php:163
#: views/customers/wcfm-view-customers-manage.php:253
#: views/enquiry/wcfm-view-enquiry-form.php:214
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:132
#: views/notice/wcfm-view-notice-manage.php:102
#: views/products-manager/wcfm-view-products-manage.php:938
#: views/products-manager/wcfm-view-products-manage.php:941
#: views/products-popup/wcfm-view-product-popup.php:314
#: views/products-popup/wcfm-view-product-popup.php:316
#: views/vendors/wcfm-view-vendors-new.php:156
msgid "Submit"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:426
#: views/articles/wcfm-view-articles-manage.php:428
#: views/coupons/wcfm-view-coupons-manage.php:161
#: views/coupons/wcfm-view-coupons-manage.php:163
#: views/products-manager/wcfm-view-products-manage.php:938
#: views/products-manager/wcfm-view-products-manage.php:941
#: views/products-popup/wcfm-view-product-popup.php:314
#: views/products-popup/wcfm-view-product-popup.php:316
msgid "Submit for Review"
msgstr ""

#: views/articles/wcfm-view-articles.php:83
msgid "Select a category"
msgstr ""

#: views/articles/wcfm-view-articles.php:109
#: views/articles/wcfm-view-articles.php:120
#: views/products/wcfm-view-products.php:231
#: views/products/wcfm-view-products.php:255
#: views/products-manager/wcfm-view-products-manage-tabs.php:285
msgid "Image"
msgstr ""

#: views/articles/wcfm-view-articles.php:115
#: views/articles/wcfm-view-articles.php:126
#: views/customers/wcfm-view-customers-details.php:214
#: views/customers/wcfm-view-customers-details.php:225
#: views/customers/wcfm-view-customers-details.php:252
#: views/customers/wcfm-view-customers-details.php:263
#: views/customers/wcfm-view-customers-details.php:289
#: views/customers/wcfm-view-customers-details.php:299
#: views/customers/wcfm-view-customers.php:88
#: views/customers/wcfm-view-customers.php:104
#: views/enquiry/wcfm-view-enquiry.php:90
#: views/enquiry/wcfm-view-enquiry.php:105
#: views/enquiry/wcfm-view-my-account-enquiry.php:48
#: views/enquiry/wcfm-view-my-account-enquiry.php:79
#: views/knowledgebase/wcfm-view-knowledgebase.php:78
#: views/knowledgebase/wcfm-view-knowledgebase.php:90
#: views/messages/wcfm-view-messages.php:82
#: views/messages/wcfm-view-messages.php:95
#: views/notice/wcfm-view-notices.php:57 views/notice/wcfm-view-notices.php:68
#: views/orders/wcfm-view-orders.php:123 views/orders/wcfm-view-orders.php:154
#: views/products/wcfm-view-products.php:243
#: views/products/wcfm-view-products.php:267
#: views/reports/wcfm-view-reports-out-of-stock.php:54
#: views/reports/wcfm-view-reports-out-of-stock.php:63
#: views/wc_bookings/wcfm-view-wcbookings.php:125
#: views/wc_bookings/wcfm-view-wcbookings.php:137
msgid "Actions"
msgstr ""

#: views/capability/wcfm-view-capability.php:172
#: views/settings/wcfm-view-settings.php:121
msgid "Capability Controller"
msgstr ""

#: views/capability/wcfm-view-capability.php:179
msgid "Capability Settings"
msgstr ""

#: views/capability/wcfm-view-capability.php:182
msgid "Dashboard Settings"
msgstr ""

#: views/capability/wcfm-view-capability.php:198
#: views/vendors/wcfm-view-vendors-manage.php:367
msgid "Vendors Capability"
msgstr ""

#: views/capability/wcfm-view-capability.php:203
msgid "Configure what to hide from all Vendors"
msgstr ""

#: views/capability/wcfm-view-capability.php:212
msgid "Manage Products"
msgstr ""

#: views/capability/wcfm-view-capability.php:213
msgid "Add Products"
msgstr ""

#: views/capability/wcfm-view-capability.php:216
msgid "Auto Publish Live Products"
msgstr ""

#: views/capability/wcfm-view-capability.php:222
msgid "Types"
msgstr ""

#: views/capability/wcfm-view-capability.php:229
msgid "External / Affiliate"
msgstr ""

#: views/capability/wcfm-view-capability.php:234
msgid "Panels"
msgstr ""

#: views/capability/wcfm-view-capability.php:238
#: views/products-manager/wcfm-view-products-manage-tabs.php:23
#: views/products-manager/wcfm-view-products-manage-tabs.php:248
msgid "Inventory"
msgstr ""

#: views/capability/wcfm-view-capability.php:240
msgid "Taxes"
msgstr ""

#: views/capability/wcfm-view-capability.php:241
#: views/products-manager/wcfm-view-products-manage-tabs.php:316
msgid "Linked"
msgstr ""

#: views/capability/wcfm-view-capability.php:242
#: views/products/wcfm-view-products-export.php:97
#: views/products-manager/wcfm-view-products-manage-tabs.php:163
#: views/products-manager/wcfm-view-products-manage-tabs.php:171
msgid "Attributes"
msgstr ""

#: views/capability/wcfm-view-capability.php:255
msgid "Access"
msgstr ""

#: views/capability/wcfm-view-capability.php:265
msgid "Marketplace"
msgstr ""

#: views/capability/wcfm-view-capability.php:270
msgid "Show Sold By"
msgstr ""

#: views/capability/wcfm-view-capability.php:271
msgid "Show Email"
msgstr ""

#: views/capability/wcfm-view-capability.php:272
msgid "Show Phone"
msgstr ""

#: views/capability/wcfm-view-capability.php:273
msgid "Show Address"
msgstr ""

#: views/capability/wcfm-view-capability.php:274
msgid "Show Map"
msgstr ""

#: views/capability/wcfm-view-capability.php:275
msgid "Show Social"
msgstr ""

#: views/capability/wcfm-view-capability.php:276
msgid "Show Follower"
msgstr ""

#: views/capability/wcfm-view-capability.php:277
msgid "Show Policy"
msgstr ""

#: views/capability/wcfm-view-capability.php:278
msgid "Store Hours"
msgstr ""

#: views/capability/wcfm-view-capability.php:279
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1120
#: views/settings/wcfm-view-wcmarketplace-settings.php:713
msgid "Customer Support"
msgstr ""

#: views/capability/wcfm-view-capability.php:281
msgid "Reviews Manage"
msgstr ""

#: views/capability/wcfm-view-capability.php:282
#: views/settings/wcfm-view-settings.php:426
msgid "Ledger Book"
msgstr ""

#: views/capability/wcfm-view-capability.php:283
msgid "Product Multivendor"
msgstr ""

#: views/capability/wcfm-view-capability.php:284
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:340
msgid "Video Banner"
msgstr ""

#: views/capability/wcfm-view-capability.php:285
msgid "Slider Banner"
msgstr ""

#: views/capability/wcfm-view-capability.php:332
#: views/withdrawal/dokan/wcfm-view-withdrawal.php:64
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:66
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:42
msgid "Transactions"
msgstr ""

#: views/capability/wcfm-view-capability.php:343
msgid "Integrations"
msgstr ""

#: views/capability/wcfm-view-capability.php:364
msgid "Associate Listings"
msgstr ""

#: views/capability/wcfm-view-capability.php:370
msgid "PDF Vouchers"
msgstr ""

#: views/capability/wcfm-view-capability.php:370
msgid "by WC PDF Vouchers."
msgstr ""

#: views/capability/wcfm-view-capability.php:375
msgid "WooCommerce Germanized"
msgstr ""

#: views/capability/wcfm-view-capability.php:375
msgid "by WooCommerce Germanized."
msgstr ""

#: views/capability/wcfm-view-capability.php:380
msgid "Box Office"
msgstr ""

#: views/capability/wcfm-view-capability.php:380
msgid "by WooCommerce Box Office."
msgstr ""

#: views/capability/wcfm-view-capability.php:385
msgid "by WooCommerce Lottery."
msgstr ""

#: views/capability/wcfm-view-capability.php:390
msgid "Deposits"
msgstr ""

#: views/capability/wcfm-view-capability.php:390
msgid "by WooCommerce Deposits."
msgstr ""

#: views/capability/wcfm-view-capability.php:395
msgid "Tabs Manager"
msgstr ""

#: views/capability/wcfm-view-capability.php:395
msgid "by WooCommerce Tabs Manager."
msgstr ""

#: views/capability/wcfm-view-capability.php:400
msgid "Warranty"
msgstr ""

#: views/capability/wcfm-view-capability.php:400
msgid "by WooCommerce Warranty."
msgstr ""

#: views/capability/wcfm-view-capability.php:405
msgid "Waitlist"
msgstr ""

#: views/capability/wcfm-view-capability.php:405
msgid "by WooCommerce Waitlist."
msgstr ""

#: views/capability/wcfm-view-capability.php:410
msgid "FooEvents"
msgstr ""

#: views/capability/wcfm-view-capability.php:410
msgid "by WooCommerce FooEvents."
msgstr ""

#: views/capability/wcfm-view-capability.php:415
msgid "Measurement Calculator"
msgstr ""

#: views/capability/wcfm-view-capability.php:415
msgid "by WC Measurement & Price Calculator."
msgstr ""

#: views/capability/wcfm-view-capability.php:420
msgid "Product Labels"
msgstr ""

#: views/capability/wcfm-view-capability.php:420
msgid "by WC Advanced Product Labels."
msgstr ""

#: views/capability/wcfm-view-capability.php:425
msgid "Wholesale Price"
msgstr ""

#: views/capability/wcfm-view-capability.php:425
msgid "by WooCommerce Wholesale Price."
msgstr ""

#: views/capability/wcfm-view-capability.php:430
msgid "Min/Max Quantities"
msgstr ""

#: views/capability/wcfm-view-capability.php:430
msgid "by WooCommerce Min/Max Quantities."
msgstr ""

#: views/capability/wcfm-view-capability.php:435
msgid "360° Images"
msgstr ""

#: views/capability/wcfm-view-capability.php:435
msgid "by WooCommerce 360° Images."
msgstr ""

#: views/capability/wcfm-view-capability.php:440
msgid "Product Badge"
msgstr ""

#: views/capability/wcfm-view-capability.php:440
msgid "by WooCommerce Product Badge."
msgstr ""

#: views/capability/wcfm-view-capability.php:445
msgid "Product Addon"
msgstr ""

#: views/capability/wcfm-view-capability.php:445
msgid "by WooCommerce Product Addon."
msgstr ""

#: views/capability/wcfm-view-capability.php:450
msgid "Fancy Product Designer"
msgstr ""

#: views/capability/wcfm-view-capability.php:450
msgid "by Fancy Product Designer."
msgstr ""

#: views/capability/wcfm-view-capability.php:455
msgid "Variation Swatch"
msgstr ""

#: views/capability/wcfm-view-capability.php:455
msgid "by WooCommerce Variation Swatches."
msgstr ""

#: views/capability/wcfm-view-capability.php:460
msgid "WooCommerce Quotation"
msgstr ""

#: views/capability/wcfm-view-capability.php:460
msgid "by WooCommerce Quotation."
msgstr ""

#: views/capability/wcfm-view-capability.php:465
msgid "Dynamic Pricing"
msgstr ""

#: views/capability/wcfm-view-capability.php:465
msgid "by WooCommerce Dynamic Pricing."
msgstr ""

#: views/capability/wcfm-view-capability.php:470
#: views/integrations/wcfm-view-integrations-products-manage.php:255
msgid "MSRP Pricing"
msgstr ""

#: views/capability/wcfm-view-capability.php:470
msgid "by MSRP for WooCommerce."
msgstr ""

#: views/capability/wcfm-view-capability.php:475
msgid "Cost of Goods"
msgstr ""

#: views/capability/wcfm-view-capability.php:475
msgid "by Cost of Goods for WooCommerce."
msgstr ""

#: views/capability/wcfm-view-capability.php:480
msgid "License Manager"
msgstr ""

#: views/capability/wcfm-view-capability.php:480
msgid "by Licence Manager for WooCommerce."
msgstr ""

#: views/capability/wcfm-view-capability.php:485
msgid "Role based Price"
msgstr ""

#: views/capability/wcfm-view-capability.php:485
msgid "by ELEX Role based Price."
msgstr ""

#: views/capability/wcfm-view-capability.php:490
msgid "Gift Cards"
msgstr ""

#: views/capability/wcfm-view-capability.php:490
msgid "by PW Gift Cards."
msgstr ""

#: views/capability/wcfm-view-capability.php:496
msgid "Product Scheduler"
msgstr ""

#: views/capability/wcfm-view-capability.php:496
msgid "by WC Product Scheduler."
msgstr ""

#: views/capability/wcfm-view-capability.php:505
msgid "Manage Articles"
msgstr ""

#: views/capability/wcfm-view-capability.php:506
msgid "Add Articles"
msgstr ""

#: views/capability/wcfm-view-capability.php:507
msgid "Publish Articles"
msgstr ""

#: views/capability/wcfm-view-capability.php:508
msgid "Edit Live Articles"
msgstr ""

#: views/capability/wcfm-view-capability.php:509
msgid "Auto Publish Live Articles"
msgstr ""

#: views/capability/wcfm-view-capability.php:510
msgid "Delete Articles"
msgstr ""

#: views/capability/wcfm-view-capability.php:521
msgid "Manage Coupons"
msgstr ""

#: views/capability/wcfm-view-capability.php:522
msgid "Add Coupons"
msgstr ""

#: views/capability/wcfm-view-capability.php:523
msgid "Publish Coupons"
msgstr ""

#: views/capability/wcfm-view-capability.php:524
msgid "Edit Live Coupons"
msgstr ""

#: views/capability/wcfm-view-capability.php:525
msgid "Auto Publish Live Coupons"
msgstr ""

#: views/capability/wcfm-view-capability.php:526
msgid "Delete Coupons"
msgstr ""

#: views/capability/wcfm-view-capability.php:527
msgid "Allow Free Shipping"
msgstr ""

#: views/capability/wcfm-view-capability.php:539
msgid "Manual Booking"
msgstr ""

#: views/capability/wcfm-view-capability.php:540
msgid "Manage Resource"
msgstr ""

#: views/capability/wcfm-view-capability.php:552
#: views/customers/wcfm-view-customers-details.php:240
msgid "Appointments"
msgstr ""

#: views/capability/wcfm-view-capability.php:555
msgid "Manage Appointments"
msgstr ""

#: views/capability/wcfm-view-capability.php:556
msgid "Manual Appointment"
msgstr ""

#: views/capability/wcfm-view-capability.php:557
msgid "Manage Staff"
msgstr ""

#: views/capability/wcfm-view-capability.php:558
msgid "Appointments List"
msgstr ""

#: views/capability/wcfm-view-capability.php:559
msgid "Appointments Calendar"
msgstr ""

#: views/capability/wcfm-view-capability.php:573
msgid "Subscriptions List"
msgstr ""

#: views/capability/wcfm-view-capability.php:574
msgid "Subscription Details"
msgstr ""

#: views/capability/wcfm-view-capability.php:575
msgid "Subscription Status Update"
msgstr ""

#: views/capability/wcfm-view-capability.php:576
msgid "Subscription Schedule Update"
msgstr ""

#: views/capability/wcfm-view-capability.php:589
msgid "Add/Edit Order"
msgstr ""

#: views/capability/wcfm-view-capability.php:590
msgid "Delete Order"
msgstr ""

#: views/capability/wcfm-view-capability.php:591
msgid "View Comments"
msgstr ""

#: views/capability/wcfm-view-capability.php:592
msgid "Submit Comments"
msgstr ""

#: views/capability/wcfm-view-capability.php:594
msgid "View Commission"
msgstr ""

#: views/capability/wcfm-view-capability.php:604
msgid "Store Invoice"
msgstr ""

#: views/capability/wcfm-view-capability.php:604
msgid "Send out vendor store specific invoice to customer."
msgstr ""

#: views/capability/wcfm-view-capability.php:605
msgid "Commission Invoice"
msgstr ""

#: views/capability/wcfm-view-capability.php:615
#: views/customers/wcfm-view-customers-details.php:115
#: views/customers/wcfm-view-customers-manage.php:139
#: views/customers/wcfm-view-customers.php:32
msgid "Manage Customers"
msgstr ""

#: views/capability/wcfm-view-capability.php:616
#: views/customers/wcfm-view-customers-manage.php:130
msgid "Add Customer"
msgstr ""

#: views/capability/wcfm-view-capability.php:617
msgid "View Customer"
msgstr ""

#: views/capability/wcfm-view-capability.php:619
msgid "Delete Customer"
msgstr ""

#: views/capability/wcfm-view-capability.php:620
msgid "View Customer Orders"
msgstr ""

#: views/capability/wcfm-view-capability.php:621
msgid "View Customer Name"
msgstr ""

#: views/capability/wcfm-view-capability.php:622
msgid "View Customer Email"
msgstr ""

#: views/capability/wcfm-view-capability.php:623
#: views/orders/wcfm-view-orders.php:102 views/orders/wcfm-view-orders.php:133
msgid "Billing Address"
msgstr ""

#: views/capability/wcfm-view-capability.php:624
#: views/orders/wcfm-view-orders.php:103 views/orders/wcfm-view-orders.php:134
msgid "Shipping Address"
msgstr ""

#: views/capability/wcfm-view-capability.php:628
msgid "Customer Limit"
msgstr ""

#: views/capability/wcfm-view-capability.php:628
msgid "No. of Customers allow to add by an user."
msgstr ""

#: views/capability/wcfm-view-capability.php:628
msgid "Set `-1` if you want to restrict limit at `0`."
msgstr ""

#: views/capability/wcfm-view-capability.php:651
msgid "Advanced Capability"
msgstr ""

#: views/capability/wcfm-view-capability.php:668
#: views/capability/wcfm-view-capability.php:678
msgid "Shop Managers Capability"
msgstr ""

#: views/capability/wcfm-view-capability.php:690
#: views/capability/wcfm-view-capability.php:700
msgid "Shop Staffs Capability"
msgstr ""

#: views/capability/wcfm-view-capability.php:709
msgid "*** Vendor Managers are treated as Shop Staff for a Vendor Store."
msgstr ""

#: views/capability/wcfm-view-capability.php:721
#: views/profile/wcfm-view-profile.php:359
#: views/settings/wcfm-view-dokan-settings.php:553
#: views/settings/wcfm-view-settings.php:603
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1181
#: views/settings/wcfm-view-wcmarketplace-settings.php:780
#: views/settings/wcfm-view-wcpvendors-settings.php:475
#: views/settings/wcfm-view-wcvendors-settings.php:473
msgid "Save"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:89
msgid "Manage Coupon"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:96
msgid "Edit Coupon"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:96
msgid "Add Coupon"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:111
#: views/coupons/wcfm-view-coupons.php:43
msgid "Add New Coupon"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:126
#: views/coupons/wcfm-view-coupons.php:55
#: views/coupons/wcfm-view-coupons.php:66
msgid "Code"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:128
msgid "Discount Type"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:128
msgid "Percentage discount"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:128
msgid "Fixed Cart Discount"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:128
msgid "Fixed Product Discount"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:129
msgid "Coupon Amount"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:130
msgid "Coupon expiry date"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:135
msgid "Allow free shipping"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:135
msgid ""
"Check this box if the coupon grants free shipping. The free shipping method "
"must be enabled and be set to require \"a valid free shipping coupon\" (see "
"the \"Free Shipping Requires\" setting)."
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:147
msgid "Show on store"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:147
msgid "Check this box if you want to show this coupon in store page."
msgstr ""

#: views/coupons/wcfm-view-coupons.php:24
msgid "Coupons Listing"
msgstr ""

#: views/coupons/wcfm-view-coupons.php:35
#: views/listings/wcfm-view-listings.php:82
#: views/orders/wcfm-view-orders.php:76
#: views/products/wcfm-view-products.php:79
#: views/wc_bookings/wcfm-view-wcbookings.php:63
msgid "Screen Manager"
msgstr ""

#: views/coupons/wcfm-view-coupons.php:59
#: views/coupons/wcfm-view-coupons.php:70
msgid "Usage Limit"
msgstr ""

#: views/coupons/wcfm-view-coupons.php:60
#: views/coupons/wcfm-view-coupons.php:71
msgid "Expiry date"
msgstr ""

#: views/coupons/wcfm-view-coupons.php:61
#: views/coupons/wcfm-view-coupons.php:72
#: views/listings/wcfm-view-applications.php:65
#: views/listings/wcfm-view-applications.php:78
#: views/listings/wcfm-view-listings.php:128
#: views/listings/wcfm-view-listings.php:143
#: views/vendors/wcfm-view-vendors.php:91
#: views/vendors/wcfm-view-vendors.php:112
msgid "Action"
msgstr ""

#: views/customers/wcfm-view-customers-details.php:63
msgid "Customer Details"
msgstr ""

#: views/customers/wcfm-view-customers-details.php:122
#: views/customers/wcfm-view-customers-manage.php:142
#: views/customers/wcfm-view-customers.php:42
msgid "Add New Customer"
msgstr ""

#: views/customers/wcfm-view-customers-details.php:144
msgid "total money spent"
msgstr ""

#: views/customers/wcfm-view-customers-details.php:155
msgid "<strong>%s order</strong><br />"
msgid_plural "<strong>%s orders</strong><br />"
msgstr[0] ""
msgstr[1] ""

#: views/customers/wcfm-view-customers-details.php:157
msgid "total order placed"
msgstr ""

#: views/customers/wcfm-view-customers-details.php:178
#: views/customers/wcfm-view-customers-manage.php:206
#: views/customers/wcfm-view-customers-manage.php:231
msgid "Company Name"
msgstr ""

#: views/customers/wcfm-view-customers-details.php:212
#: views/customers/wcfm-view-customers-details.php:223
#: views/customers/wcfm-view-customers-details.php:250
#: views/customers/wcfm-view-customers-details.php:261
#: views/wc_bookings/wcfm-view-wcbookings.php:122
#: views/wc_bookings/wcfm-view-wcbookings.php:134
msgid "Start Date"
msgstr ""

#: views/customers/wcfm-view-customers-details.php:213
#: views/customers/wcfm-view-customers-details.php:224
#: views/customers/wcfm-view-customers-details.php:251
#: views/customers/wcfm-view-customers-details.php:262
#: views/wc_bookings/wcfm-view-wcbookings.php:123
#: views/wc_bookings/wcfm-view-wcbookings.php:135
msgid "End Date"
msgstr ""

#: views/customers/wcfm-view-customers-details.php:286
#: views/customers/wcfm-view-customers-details.php:296
#: views/orders/wcfm-view-orders.php:100 views/orders/wcfm-view-orders.php:131
msgid "Purchased"
msgstr ""

#: views/customers/wcfm-view-customers-manage.php:194
#: views/profile/wcfm-view-profile.php:287
#: views/vendors/wcfm-view-vendors-manage.php:305
#: views/vendors/wcfm-view-vendors-new.php:108
msgid "Address"
msgstr ""

#: views/customers/wcfm-view-customers-manage.php:199
#: views/profile/wcfm-view-profile.php:291
#: views/vendors/wcfm-view-vendors-new.php:113
msgid "Billing"
msgstr ""

#: views/customers/wcfm-view-customers-manage.php:211
#: views/customers/wcfm-view-customers-manage.php:235
#: views/profile/wcfm-view-profile.php:299
#: views/profile/wcfm-view-profile.php:315
#: views/settings/wcfm-view-dokan-settings.php:202
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:527
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1131
#: views/settings/wcfm-view-wcmarketplace-settings.php:233
#: views/settings/wcfm-view-wcmarketplace-settings.php:732
#: views/settings/wcfm-view-wcvendors-settings.php:222
#: views/settings/wcfm-view-wcvendors-settings.php:420
#: views/vendors/wcfm-view-vendors-new.php:122
#: views/vendors/wcfm-view-vendors-new.php:139
msgid "City/Town"
msgstr ""

#: views/customers/wcfm-view-customers-manage.php:213
#: views/customers/wcfm-view-customers-manage.php:237
#: views/profile/wcfm-view-profile.php:301
#: views/profile/wcfm-view-profile.php:317
#: views/settings/wcfm-view-dokan-settings.php:203
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:528
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1133
#: views/settings/wcfm-view-wcmarketplace-settings.php:234
#: views/settings/wcfm-view-wcmarketplace-settings.php:734
#: views/settings/wcfm-view-wcvendors-settings.php:224
#: views/settings/wcfm-view-wcvendors-settings.php:422
#: views/vendors/wcfm-view-vendors-new.php:124
#: views/vendors/wcfm-view-vendors-new.php:141
msgid "Postcode/Zip"
msgstr ""

#: views/customers/wcfm-view-customers-manage.php:228
#: views/profile/wcfm-view-profile.php:309
msgid "Same as Billing"
msgstr ""

#: views/customers/wcfm-view-customers.php:81
#: views/customers/wcfm-view-customers.php:97
#: views/integrations/wcfm-view-geomywp-products-manage.php:24
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:512
msgid "Location"
msgstr ""

#: views/customers/wcfm-view-customers.php:85
#: views/customers/wcfm-view-customers.php:101
msgid "Money Spent"
msgstr ""

#: views/customers/wcfm-view-customers.php:86
#: views/customers/wcfm-view-customers.php:102
msgid "Last Order"
msgstr ""

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:51
msgid "Welcome to the %s Dashboard"
msgstr ""

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:53
msgid "Welcome to your Store Manager"
msgstr ""

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:70
msgid "Last Login:"
msgstr ""

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:78
#: views/vendors/wcfm-view-vendors.php:80
#: views/vendors/wcfm-view-vendors.php:101
msgid "Product Limit Stats"
msgstr ""

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:80
#: views/vendors/wcfm-view-vendors.php:81
#: views/vendors/wcfm-view-vendors.php:102
msgid "Disk Space Usage Stats"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:117
msgid "gross sales in last 7 days"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:151
msgid "admin fees in last 7 days"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:151
msgid "commission in last 7 days"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:161
#: views/dashboard/wcfm-view-dokan-dashboard.php:163
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:170
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:183
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:191
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:170
msgid "<strong>%s item</strong>"
msgid_plural "<strong>%s items</strong>"
msgstr[0] ""
msgstr[1] ""

#: views/dashboard/wcfm-view-dashboard.php:162
msgid "sold in last 7 days"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:175
#: views/dashboard/wcfm-view-dokan-dashboard.php:174
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:183
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:194
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:202
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:181
msgid "<strong>%s order</strong>"
msgid_plural "<strong>%s orders</strong>"
msgstr[0] ""
msgstr[1] ""

#: views/dashboard/wcfm-view-dashboard.php:176
msgid "received in last 7 days"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:236
#: views/dashboard/wcfm-view-dokan-dashboard.php:234
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:245
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:255
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:262
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:241
msgid "Product Stats"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:258
#: views/dashboard/wcfm-view-dokan-dashboard.php:254
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:267
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:275
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:282
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:261
msgid "Store Stats"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:269
#: views/dashboard/wcfm-view-dokan-dashboard.php:266
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:279
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:287
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:273
msgid "%s top seller in last 7 days (sold %d)"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:282
#: views/dashboard/wcfm-view-dokan-dashboard.php:280
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:293
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:301
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:313
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:287
msgid "<strong>%s order</strong> - processing"
msgid_plural "<strong>%s orders</strong> - processing"
msgstr[0] ""
msgstr[1] ""

#: views/dashboard/wcfm-view-dashboard.php:288
msgid "<strong>%s order</strong> - on-hold"
msgid_plural "<strong>%s orders</strong> - on-hold"
msgstr[0] ""
msgstr[1] ""

#: views/dashboard/wcfm-view-dashboard.php:299
#: views/dashboard/wcfm-view-dokan-dashboard.php:299
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:312
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:320
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:332
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:306
msgid "<strong>%s product</strong> - low in stock"
msgid_plural "<strong>%s products</strong> - low in stock"
msgstr[0] ""
msgstr[1] ""

#: views/dashboard/wcfm-view-dashboard.php:305
#: views/dashboard/wcfm-view-dokan-dashboard.php:305
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:318
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:326
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:338
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:312
msgid "<strong>%s product</strong> - out of stock"
msgid_plural "<strong>%s products</strong> - out of stock"
msgstr[0] ""
msgstr[1] ""

#: views/dashboard/wcfm-view-dashboard.php:327
#: views/dashboard/wcfm-view-dokan-dashboard.php:326
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:341
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:348
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:359
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:333
msgid "Sales by Product"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:342
#: views/dashboard/wcfm-view-dokan-dashboard.php:342
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:357
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:364
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:375
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:349
msgid "Top Regions"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:362
#: views/dashboard/wcfm-view-dokan-dashboard.php:362
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:377
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:384
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:395
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:369
msgid "Latest Topics"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:386
#: views/dashboard/wcfm-view-dokan-dashboard.php:384
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:401
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:406
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:417
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:391
msgid "There is no topic yet!!"
msgstr ""

#: views/dashboard/wcfm-view-dokan-dashboard.php:143
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:147
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:163
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:171
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:150
#: views/vendors/wcfm-view-vendors-manage.php:229
msgid "gross sales in this month"
msgstr ""

#: views/dashboard/wcfm-view-dokan-dashboard.php:154
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:159
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:174
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:182
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:161
#: views/vendors/wcfm-view-vendors-manage.php:247
msgid "earnings in this month"
msgstr ""

#: views/dashboard/wcfm-view-dokan-dashboard.php:164
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:171
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:184
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:192
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:171
#: views/vendors/wcfm-view-vendors-manage.php:276
msgid "sold in this month"
msgstr ""

#: views/dashboard/wcfm-view-dokan-dashboard.php:175
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:184
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:195
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:203
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:182
msgid "received in this month"
msgstr ""

#: views/dashboard/wcfm-view-dokan-dashboard.php:287
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:300
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:308
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:320
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:294
msgid "<strong>%s product</strong> - awaiting fulfillment"
msgid_plural "<strong>%s products</strong> - awaiting fulfillment"
msgstr[0] ""
msgstr[1] ""

#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:159
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:174
#: views/vendors/wcfm-view-vendors-manage.php:247
msgid "admin fees in this month"
msgstr ""

#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:302
msgid "%s top seller this month (sold %d)"
msgstr ""

#: views/emails/new-enquiry.php:19
msgid "Hi,"
msgstr ""

#: views/emails/new-enquiry.php:23
msgid "You have a recent inquiry for %s."
msgstr ""

#: views/emails/new-enquiry.php:39
msgid "To respond this Inquiry, please %sClick Here%s."
msgstr ""

#: views/emails/plain/new-enquiry.php:20
msgid "You have a recent enquiry for %s."
msgstr ""

#: views/emails/plain/new-enquiry.php:25
msgid "To respond to this Enquiry, please %sClick Here%s"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-form.php:61
msgid "Your email address will not be published."
msgstr ""

#: views/enquiry/wcfm-view-enquiry-form.php:66
msgid "Your enquiry"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:83
msgid "Manage Enquiry"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:90
msgid "Edit Enquiry"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:90
msgid "Add Enquiry"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:94
msgid "View Product"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:117
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:92
msgid "Inquiry For"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:125
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:100
msgid "Inquiry for Product"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:128
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:103
msgid "Inquiry for %s"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:140
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:116
msgid "Inquiry for"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:192
#: views/enquiry/wcfm-view-enquiry.php:88
#: views/enquiry/wcfm-view-enquiry.php:103
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:153
#: views/notice/wcfm-view-notice-view.php:111
msgid "Replies"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:265
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:224
#: views/notice/wcfm-view-notice-view.php:159
msgid "New Reply"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:280
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:230
#: views/messages/wcfm-view-messages-send-reply.php:15
#: views/messages/wcfm-view-messages.php:78
#: views/messages/wcfm-view-messages.php:91
msgid "Message"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:283
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:233
msgid "Add File"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:284
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:234
msgid "Please upload any of these file types: %1$s"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:286
msgid "Stick at Product Page"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:286
msgid "Enable to stick this reply at product page"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:306
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:255
#: views/messages/wcfm-view-messages-send-reply.php:29
#: views/messages/wcfm-view-messages.php:138
#: views/notice/wcfm-view-notice-view.php:177
#: views/vendors/wcfm-view-vendors-manage.php:434
msgid "Send"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-tab.php:34
msgid "General Enquiries"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-tab.php:39
msgid "There are no enquiries yet."
msgstr ""

#: views/enquiry/wcfm-view-enquiry-tab.php:74
msgid "Reply by"
msgstr ""

#: views/enquiry/wcfm-view-enquiry.php:56
msgid "Bulk Delete"
msgstr ""

#: views/enquiry/wcfm-view-enquiry.php:81
#: views/enquiry/wcfm-view-enquiry.php:96
#: views/messages/wcfm-view-messages.php:75
#: views/messages/wcfm-view-messages.php:88
msgid "Select all for mark as read or delete"
msgstr ""

#: views/enquiry/wcfm-view-enquiry.php:83
#: views/enquiry/wcfm-view-enquiry.php:98
#: views/enquiry/wcfm-view-my-account-enquiry.php:42
#: views/enquiry/wcfm-view-my-account-enquiry.php:54
#: views/enquiry/wcfm-view-my-account-enquiry.php:85
msgid "Query"
msgstr ""

#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:198
msgid "Keymaster"
msgstr ""

#: views/enquiry/wcfm-view-my-account-enquiry.php:86
msgid "You do not have any enquiry yet!"
msgstr ""

#: views/integrations/wcfm-view-epeken-products-manage.php:42
msgid "Epeken Product Config"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:216
msgid "Custom Tabs"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:221
msgid "Tabs"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:222
msgid "Required for tab to be visible"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:223
msgid "HTML or Text to display ..."
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:236
msgid "Barcode & ISBN"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:241
msgid "Barcode"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:242
msgid "ISBN"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:260
msgid "MSRP Price"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:273
msgid "Quantities and Units"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:278
msgid "Deactivate Quantity Rules"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:279
msgid "Override Quantity Rules"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:280
msgid "Step Value"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:281
msgid "Minimum Quantity"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:282
msgid "Maximum Quantity"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:283
msgid "Out of Stock Minimum"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:284
msgid "Out of Stock Maximum"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:298
msgid "Product Fees"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:303
msgid "Fee Name"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:303
msgid "This will be shown at the checkout description the added fee."
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:304
msgid "Fee Amount"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:304
msgid ""
"Enter a monetary decimal without any currency symbols or thousand "
"separator. This field also accepts percentages."
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:305
msgid "Multiple Fee by Quantity"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:305
msgid "Multiply the fee by the quantity of this product that is added to the cart."
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:318
msgid "Bulk Discount"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:323
msgid "Bulk Discount enabled"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:324
msgid "Bulk discount special offer text in product description"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:325
msgid "Discount Rules"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:326
msgid "Quantity (min.)"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:327
msgid "Discount (%)"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:341
msgid "Role Based Price"
msgstr ""

#: views/integrations/wcfm-view-integrations-products-manage.php:353
msgid "Selling Price"
msgstr ""

#: views/integrations/wcfm-view-wc-german-market-products-manage.php:50
msgid "Fill in attribute description!"
msgstr ""

#: views/integrations/wcfm-view-wc-german-market-products-manage.php:60
msgid "Scale Unit"
msgstr ""

#: views/integrations/wcfm-view-wc-german-market-products-manage.php:61
msgid "Complete product quantity"
msgstr ""

#: views/integrations/wcfm-view-wc-german-market-products-manage.php:62
msgid "Quantity to display"
msgstr ""

#: views/integrations/wcfm-view-wc-german-market-products-manage.php:72
msgid "Age Rating"
msgstr ""

#: views/integrations/wcfm-view-wc-german-market-products-manage.php:77
msgid "Required age to buy this product"
msgstr ""

#: views/integrations/wcfm-view-wc-german-market-products-manage.php:77
msgid "Years"
msgstr ""

#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:58
msgid "Tiered Price"
msgstr ""

#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:72
msgid "Layout"
msgstr ""

#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:76
msgid "Table"
msgstr ""

#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:77
msgid "Blocks"
msgstr ""

#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:79
msgid "Dropdown"
msgstr ""

#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:80
msgid "Tooltip"
msgstr ""

#: views/integrations/wcfm-view-wc-tiered-price-table-products-manage.php:85
msgid ""
"Specify a tiered pricing layout for the product. Leave the default to use "
"the default layout from the settings"
msgstr ""

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:56
msgid "Manage Knowledgebase"
msgstr ""

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:63
msgid "Edit Knowledgebase"
msgstr ""

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:63
msgid "Add Knowledgebase"
msgstr ""

#: views/knowledgebase/wcfm-view-knowledgebase.php:33
msgid "Guidelines for Store Users"
msgstr ""

#: views/knowledgebase/wcfm-view-knowledgebase.php:38
msgid "Add New Knowledgebase"
msgstr ""

#: views/knowledgebase/wcfm-view-knowledgebase.php:57
msgid "Show all category"
msgstr ""

#: views/knowledgebase/wcfm-view-knowledgebase.php:77
#: views/knowledgebase/wcfm-view-knowledgebase.php:89
msgid "Category"
msgstr ""

#: views/listings/wcfm-view-applications.php:25
#: views/listings/wcfm-view-listings.php:122
#: views/listings/wcfm-view-listings.php:137
msgid "Applications"
msgstr ""

#: views/listings/wcfm-view-applications.php:58
#: views/listings/wcfm-view-applications.php:71
msgid "Candidate"
msgstr ""

#: views/listings/wcfm-view-applications.php:59
#: views/listings/wcfm-view-applications.php:72
msgid "Job"
msgstr ""

#: views/listings/wcfm-view-applications.php:60
#: views/listings/wcfm-view-applications.php:73
msgid "Rating"
msgstr ""

#: views/listings/wcfm-view-applications.php:61
#: views/listings/wcfm-view-applications.php:74
msgid "Notes"
msgstr ""

#: views/listings/wcfm-view-applications.php:63
#: views/listings/wcfm-view-applications.php:76
msgid "Posted"
msgstr ""

#: views/listings/wcfm-view-listings.php:118
#: views/listings/wcfm-view-listings.php:133
#: views/vendors/wcfm-view-vendors.php:25
msgid "Listing"
msgstr ""

#: views/listings/wcfm-view-listings.php:123
#: views/listings/wcfm-view-listings.php:138
msgid "Filled?"
msgstr ""

#: views/listings/wcfm-view-listings.php:125
#: views/listings/wcfm-view-listings.php:140
msgid "Date Posted"
msgstr ""

#: views/listings/wcfm-view-listings.php:126
#: views/listings/wcfm-view-listings.php:141
msgid "Listing Expires"
msgstr ""

#: views/login-popup/wcfm-login-popup-form.php:15
msgid "Username / E-mail Address"
msgstr ""

#: views/login-popup/wcfm-login-popup-form.php:36
msgid "No account yet! Click here to register"
msgstr ""

#: views/messages/wcfm-view-messages.php:36
msgid "Notification Dashboard"
msgstr ""

#: views/messages/wcfm-view-messages.php:57
msgid "Only Unread"
msgstr ""

#: views/messages/wcfm-view-messages.php:58
msgid "Only Read"
msgstr ""

#: views/messages/wcfm-view-messages.php:109
msgid "Send Direct Message"
msgstr ""

#: views/messages/wcfm-view-messages.php:109
msgid "To Store Admin"
msgstr ""

#: views/messages/wcfm-view-messages.php:109
msgid "To Store Vendors"
msgstr ""

#: views/messages/wcfm-view-messages.php:132
msgid "Direct TO:"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:54
msgid "Manage Topic"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:62
#: views/notice/wcfm-view-notice-view.php:66
msgid "Edit Topic"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:62
msgid "Add Topic"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:65
#: views/notice/wcfm-view-notice-view.php:64
#: views/notice/wcfm-view-notices.php:33
msgid "Topics"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:66
msgid "View Topic"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:86
msgid "Allow Reply"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:87
msgid "Close for New Reply"
msgstr ""

#: views/notice/wcfm-view-notice-view.php:54
msgid "Topic"
msgstr ""

#: views/notice/wcfm-view-notices.php:37
msgid "Add New Topic"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:75
#: views/products-manager/wcfm-view-products-manage.php:383
#: views/products-manager/wcfm-view-products-manage.php:384
#: views/products-popup/wcfm-view-product-popup.php:68
#: views/products-popup/wcfm-view-product-popup.php:69
msgid "Standard"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:127
msgid "Order #"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:142
msgid "CSV Export"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:171
msgid "Order date:"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:178
msgid "Order status:"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:184
msgid "Customer payment page"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:201
#: views/vendors/wcfm-view-vendors-manage.php:375
#: views/wc_bookings/wcfm-view-wcbookings-details.php:153
#: views/wc_bookings/wcfm-view-wcbookings-details.php:313
msgid "Update"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:215
msgid "Customer:"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:223
msgid "View other orders"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:252
msgid "Payment via %s"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:267
#. translators: 1: date 2: time
msgid "Paid on %1$s @ %2$s"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:275
msgid "Customer IP"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:287
msgid "Billing Details"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:294
msgid "Shipping Details"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:312
#: views/wc_bookings/wcfm-view-wcbookings-details.php:385
msgid "No billing address set."
msgstr ""

#: views/orders/wcfm-view-orders-details.php:356
msgid "No shipping address set."
msgstr ""

#: views/orders/wcfm-view-orders-details.php:383
msgid "Customer Provided Note"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:408
msgid "Store Invoice(s)"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:421
msgid "Download Store Invoice"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:442
msgid "Order Items"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:449
msgid "Item"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:451
#: views/products-manager/wcfm-view-wcbookings-products-manage.php:80
#: views/settings/wcfm-view-dokan-settings.php:459
#: views/settings/wcfm-view-dokan-settings.php:462
msgid "Cost"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:452
msgid "Qty"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:510
msgid "Variation ID:"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:514
msgid "No longer exists"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:575
msgid "%s discount"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:636
msgid "Shipping Item(s)"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:715
msgid "Fee Item(s)"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:731
#: views/withdrawal/wcmp/wcfm-view-payments.php:66
#: views/withdrawal/wcmp/wcfm-view-payments.php:77
msgid "Fee"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:788
msgid "Refund(s)"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:809
#. translators: 1: refund id 2: date
msgid "Refund #%1$s - %2$s"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:812
msgid "ID: %d"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:854
msgid "Coupon(s) Used"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:876
msgid "This is the total discount. Discounts are defined per line item."
msgstr ""

#: views/orders/wcfm-view-orders-details.php:888
msgid "This is the shipping and handling total costs for the order."
msgstr ""

#: views/orders/wcfm-view-orders-details.php:928
msgid "Order Total"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:948
msgid "Vendor(s) Earning"
msgstr ""

#: views/orders/wcfm-view-orders.php:42
msgid "Orders Listing"
msgstr ""

#: views/orders/wcfm-view-orders.php:105 views/orders/wcfm-view-orders.php:136
msgid "Gross Sales Amount"
msgstr ""

#: views/orders/wcfm-view-orders.php:111 views/orders/wcfm-view-orders.php:118
#: views/orders/wcfm-view-orders.php:142 views/orders/wcfm-view-orders.php:149
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:87
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:100
msgid "Vendor Earning"
msgstr ""

#: views/orders/wcfm-view-orders.php:116 views/orders/wcfm-view-orders.php:118
#: views/orders/wcfm-view-orders.php:147 views/orders/wcfm-view-orders.php:149
msgid " Amount"
msgstr ""

#: views/products/wcfm-view-products-export.php:40
msgid "Export products to a CSV file"
msgstr ""

#: views/products/wcfm-view-products-export.php:64
#: views/products/wcfm-view-products.php:124
#: views/products-manager/wcfm-view-products-manage.php:505
#: views/products-popup/wcfm-view-product-popup-button.php:2
msgid "Add New Product"
msgstr ""

#: views/products/wcfm-view-products-export.php:80
msgid ""
"This tool allows you to generate and download a CSV file containing a list "
"of all products."
msgstr ""

#: views/products/wcfm-view-products-export.php:87
msgid "Which columns should be exported?"
msgstr ""

#: views/products/wcfm-view-products-export.php:90
msgid "Export all columns"
msgstr ""

#: views/products/wcfm-view-products-export.php:96
msgid "Downloads"
msgstr ""

#: views/products/wcfm-view-products-export.php:103
msgid "Which product types should be exported?"
msgstr ""

#: views/products/wcfm-view-products-export.php:106
msgid "Export all products"
msgstr ""

#: views/products/wcfm-view-products-export.php:112
msgid "Product variations"
msgstr ""

#: views/products/wcfm-view-products-export.php:118
msgid "Export custom meta?"
msgstr ""

#: views/products/wcfm-view-products-export.php:122
msgid "Yes, export all custom meta"
msgstr ""

#: views/products/wcfm-view-products-export.php:130
msgid "Generate CSV"
msgstr ""

#: views/products/wcfm-view-products.php:113
#: views/products/wcfm-view-products.php:118
msgid "Stock Manager"
msgstr ""

#: views/products/wcfm-view-products.php:164
msgid "Show all "
msgstr ""

#: views/products/wcfm-view-products.php:180
msgid "All product types"
msgstr ""

#: views/products/wcfm-view-products.php:226
#: views/products/wcfm-view-products.php:250
msgid "Select all for bulk edit"
msgstr ""

#: views/products/wcfm-view-products.php:233
#: views/products/wcfm-view-products.php:257
#: views/products-manager/wcfm-view-products-manage-tabs.php:29
#: views/products-manager/wcfm-view-products-manage-tabs.php:293
msgid "SKU"
msgstr ""

#: views/products/wcfm-view-products.php:235
#: views/products/wcfm-view-products.php:259
#: views/products-manager/wcfm-view-products-manage-tabs.php:251
msgid "Stock"
msgstr ""

#: views/products/wcfm-view-products.php:237
#: views/products/wcfm-view-products.php:261
#: views/products-popup/wcfm-view-product-popup.php:152
msgid "Taxonomies"
msgstr ""

#: views/products-manager/wcfm-view-customfield-products-manage.php:147
msgid "-Select-"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:29
msgid ""
"SKU refers to a Stock-keeping unit, a unique identifier for each distinct "
"product and service that can be purchased."
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:30
msgid "Manage Stock?"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:30
msgid "Enable stock management at product level"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:31
#: views/products-manager/wcfm-view-products-manage-tabs.php:291
msgid "Stock Qty"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:31
msgid ""
"Stock quantity. If this is a variable product this value will be used to "
"control stock for all variations, unless you define stock at variation "
"level."
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:32
msgid "Allow Backorders?"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:32
#: views/products-manager/wcfm-view-products-manage-tabs.php:292
msgid "Do not Allow"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:32
#: views/products-manager/wcfm-view-products-manage-tabs.php:292
msgid "Allow, but notify customer"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:32
#: views/products-manager/wcfm-view-products-manage-tabs.php:292
msgid "Allow"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:32
msgid ""
"If managing stock, this controls whether or not backorders are allowed. If "
"enabled, stock quantity can go below 0."
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:33
#: views/products-manager/wcfm-view-products-manage-tabs.php:294
msgid "Stock status"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:33
msgid ""
"Controls whether or not the product is listed as \"in stock\" or \"out of "
"stock\" on the frontend."
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:34
msgid "Sold Individually"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:34
msgid "Enable this to only allow one of this item to be bought in a single order"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:58
msgid "Files"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:60
msgid "File"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:65
msgid "Never"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:78
msgid "Grouped Products"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:83
msgid "Grouped products"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:83
msgid "This lets you choose which products are part of this group."
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:110
msgid "Dimensions"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:113
msgid "Shipping class"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:142
msgid "Tax Status"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:142
msgid "Taxable"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:142
msgid "Shipping only"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:142
msgid ""
"Define whether or not the entire product is taxable, or just the cost of "
"shipping it."
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:143
msgid "Tax Class"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:143
msgid ""
"Choose a tax class for this product. Tax classes are used to apply "
"different tax rates specific to certain types of product."
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:175
msgid "Value(s):"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:175
msgid "Enter some text, some attributes by \"%s\" separating values."
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:187
msgid "Add attribute"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:205
msgid "Variations"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:208
msgid ""
"Before you can add a variation you need to add some variation attributes on "
"the Attributes tab. %sLearn more%s"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:215
msgid "Default Form Values:"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:222
#: views/products-manager/wcfm-view-products-manage-tabs.php:223
msgid "Variations Bulk Options"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:225
msgid "Choose option"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:226
msgid "Create variations from all attributes"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:228
msgid "Enable all Variations"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:229
msgid "Disable all Variations"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:231
msgid "Set variations \"Downloadable\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:232
msgid "Set variations \"Non-Downloadable\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:235
msgid "Set variations \"Virtual\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:236
msgid "Set variations \"Non-Virtual\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:239
msgid "Pricing"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:240
msgid "Regular prices"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:241
msgid "Regular price increase"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:242
msgid "Regular price decrease"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:243
msgid "Sale prices"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:244
msgid "Sale price increase"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:245
msgid "Sale price decrease"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:249
msgid "ON \"Manage stock\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:250
msgid "OFF \"Manage stock\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:253
msgid "Set Status - In stock"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:254
msgid "Set Status - Out of stock"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:255
msgid "Set Status - On backorder"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:267
msgid "Downloadable products"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:268
msgid "Download limit"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:269
msgid "Download expiry"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:283
msgid "Manage Stock"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:292
msgid "Backorders?"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:322
msgid "Up-sells"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:322
msgid ""
"Up-sells are products which you recommend instead of the currently viewed "
"product, for example, products that are more profitable or better quality "
"or more expensive."
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:323
msgid "Cross-sells"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:323
msgid ""
"Cross-sells are products which you promote in the cart, based on the "
"current product."
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:38
#: views/products-manager/wcfm-view-products-manage.php:465
#: views/products-popup/wcfm-view-product-popup.php:118
msgid "Add Product"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:44
msgid "You have reached your product limit!"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:371
#: views/products-popup/wcfm-view-product-popup.php:56
msgid "No shipping class"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:456
msgid "Manage Product"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:465
msgid "Edit Product"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:510
msgid "Duplicate Product"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:549
#: views/products-popup/wcfm-view-product-popup.php:129
msgid "Product Title"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:554
#: views/products-popup/wcfm-view-product-popup.php:134
#: views/settings/wcfm-view-settings.php:486
msgid "URL"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:554
#: views/products-popup/wcfm-view-product-popup.php:134
msgid "Enter the external URL to the product."
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:555
#: views/products-popup/wcfm-view-product-popup.php:135
msgid "Button Text"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:555
#: views/products-popup/wcfm-view-product-popup.php:135
msgid "This text will be shown on the button linking to the external product."
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:557
#: views/products-popup/wcfm-view-product-popup.php:137
msgid "schedule"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:623
#: views/products-manager/wcfm-view-products-manage.php:832
#: views/products-popup/wcfm-view-product-popup.php:210
msgid "Separate Product Tags with commas"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:623
#: views/products-manager/wcfm-view-products-manage.php:630
#: views/products-manager/wcfm-view-products-manage.php:832
#: views/products-manager/wcfm-view-products-manage.php:839
#: views/products-popup/wcfm-view-product-popup.php:210
#: views/products-popup/wcfm-view-product-popup.php:217
msgid ""
"Product tags are descriptive labels you can add to your products. Popular "
"search engines can use tags to get information about your store. You can "
"add more than one tag separating them with a comma."
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:733
msgid "Category Name"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:795
msgid "Add new"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:802
msgid "-- Parent taxonomy --"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:906
msgid ""
"It's a translated product, so product meta fields are blocked for editing "
"because WPML will copy its value from the original language."
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:935
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:118
msgid "Reject"
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:63
msgid "Max bookings per block"
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:63
msgid ""
"The maximum bookings allowed for each block. Can be overridden at resource "
"level."
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:64
msgid "Minimum block bookable"
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:65
#: views/products-manager/wcfm-view-wcbookings-products-manage.php:67
msgid "into the future"
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:66
msgid "Maximum block bookable"
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:68
msgid "Require a buffer period of"
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:68
msgid "between bookings"
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:69
msgid "Adjacent Buffering?"
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:69
msgid ""
"By default buffer period applies forward into the future of a booking. "
"Enabling this option will apply adjacently ( Before and After Bookings)."
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:70
msgid "All dates are..."
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:70
msgid "available by default"
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:70
msgid "not-available by default"
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:70
msgid "This option affects how you use the rules below."
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:86
msgid "Base cost"
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:86
msgid "One-off cost for the booking as a whole."
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:87
msgid "Block cost"
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:87
msgid ""
"This is the cost per block booked. All other costs (for resources and "
"persons) are added to this."
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:88
msgid "Display cost"
msgstr ""

#: views/products-manager/wcfm-view-wcbookings-products-manage.php:88
msgid ""
"The cost is displayed to the user on the frontend. Leave blank to have it "
"calculated for you. If a booking has varying costs, this will be prefixed "
"with the word `from:`."
msgstr ""

#: views/products-popup/wcfm-view-product-popup.php:253
msgid "Image Gallery"
msgstr ""

#: views/profile/wcfm-view-profile.php:182
msgid "Profile Manager"
msgstr ""

#: views/profile/wcfm-view-profile.php:198
msgid "Personal"
msgstr ""

#: views/profile/wcfm-view-profile.php:205
msgid "Avatar"
msgstr ""

#: views/profile/wcfm-view-profile.php:221
msgid "Email already verified"
msgstr ""

#: views/profile/wcfm-view-profile.php:230
msgid "Email Verification Code: "
msgstr ""

#: views/profile/wcfm-view-profile.php:230
msgid "Verification Code"
msgstr ""

#: views/profile/wcfm-view-profile.php:231
msgid "Get Code"
msgstr ""

#: views/profile/wcfm-view-profile.php:245
msgid "Set New Password – Leave blank to retain current password"
msgstr ""

#: views/profile/wcfm-view-profile.php:261
msgid "Site Default"
msgstr ""

#: views/profile/wcfm-view-profile.php:269
msgid "Language"
msgstr ""

#: views/profile/wcfm-view-profile.php:275
msgid "About"
msgstr ""

#: views/profile/wcfm-view-profile.php:330
#: views/settings/wcfm-view-dokan-settings.php:141
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:260
#: views/settings/wcfm-view-wcmarketplace-settings.php:148
#: views/settings/wcfm-view-wcpvendors-settings.php:85
#: views/settings/wcfm-view-wcvendors-settings.php:132
msgid "Social"
msgstr ""

#: views/profile/wcfm-view-profile.php:336
msgid "Twitter"
msgstr ""

#: views/profile/wcfm-view-profile.php:336
msgid "Twitter Handler"
msgstr ""

#: views/profile/wcfm-view-profile.php:337
msgid "Facebook"
msgstr ""

#: views/profile/wcfm-view-profile.php:337
msgid "Facebook Handler"
msgstr ""

#: views/profile/wcfm-view-profile.php:338
msgid "Instagram"
msgstr ""

#: views/profile/wcfm-view-profile.php:338
msgid "Instagram Username"
msgstr ""

#: views/profile/wcfm-view-profile.php:339
msgid "Youtube"
msgstr ""

#: views/profile/wcfm-view-profile.php:339
msgid "YouTube Channel Name"
msgstr ""

#: views/profile/wcfm-view-profile.php:340
msgid "Linkedin"
msgstr ""

#: views/profile/wcfm-view-profile.php:340
msgid "Linkdin Username"
msgstr ""

#: views/profile/wcfm-view-profile.php:341
msgid "Google Plus"
msgstr ""

#: views/profile/wcfm-view-profile.php:341
msgid "Google Plus Profile ID"
msgstr ""

#: views/profile/wcfm-view-profile.php:342
msgid "Snapchat"
msgstr ""

#: views/profile/wcfm-view-profile.php:342
msgid "Snapchat ID"
msgstr ""

#: views/profile/wcfm-view-profile.php:343
msgid "Pinterest"
msgstr ""

#: views/profile/wcfm-view-profile.php:343
msgid "Pinterest Username"
msgstr ""

#: views/reports/wcfm-html-report-sales-by-date.php:31
msgid "Custom:"
msgstr ""

#: views/reports/wcfm-html-report-sales-by-date.php:37
msgid "Print Report"
msgstr ""

#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:63
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:65
#: views/reports/wcfm-view-reports-sales-by-date.php:63
#: views/reports/wcfm-view-reports-sales-by-date.php:65
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:61
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:63
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:64
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:61
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:63
msgid "Sales BY Date"
msgstr ""

#: views/reports/wcfm-view-reports-menu.php:5
#: views/reports/wcfm-view-reports-menu.php:10
msgid "Sales by date"
msgstr ""

#: views/reports/wcfm-view-reports-menu.php:6
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:91
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:93
msgid "Sales by"
msgstr ""

#: views/reports/wcfm-view-reports-out-of-stock.php:26
msgid "Out of Stock"
msgstr ""

#: views/reports/wcfm-view-reports-out-of-stock.php:51
#: views/reports/wcfm-view-reports-out-of-stock.php:60
msgid "Parent"
msgstr ""

#: views/reports/wcfm-view-reports-out-of-stock.php:52
#: views/reports/wcfm-view-reports-out-of-stock.php:61
msgid "Unit in stock"
msgstr ""

#: views/reports/wcfm-view-reports-out-of-stock.php:53
#: views/reports/wcfm-view-reports-out-of-stock.php:62
msgid "Stock Status"
msgstr ""

#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:97
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:99
msgid "Sales by Date"
msgstr ""

#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:145
#: views/vendors/wcfm-view-vendors-manage.php:164
#: views/vendors/wcfm-view-vendors-new.php:62
msgid "Manage"
msgstr ""

#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:154
msgid "Choose Vendor"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:119
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:218
msgid "Upload a banner for your store. Banner size is (%sx%s) pixels."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:136
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:255
#: views/settings/wcfm-view-wcmarketplace-settings.php:143
#: views/settings/wcfm-view-wcpvendors-settings.php:80
#: views/settings/wcfm-view-wcpvendors-settings.php:103
#: views/settings/wcfm-view-wcvendors-settings.php:127
msgid "Store Settings"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:165
msgid "Profile Image"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:166
#: views/settings/wcfm-view-wcmarketplace-settings.php:207
#: views/settings/wcfm-view-wcvendors-settings.php:195
msgid "Banner"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:167
#: views/settings/wcfm-view-wcmarketplace-settings.php:173
#: views/settings/wcfm-view-wcpvendors-settings.php:110
#: views/settings/wcfm-view-wcvendors-settings.php:157
msgid "Shop Name"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:168
msgid "Store Product Per Page"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:169
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:293
#: views/settings/wcfm-view-wcvendors-settings.php:197
msgid "Store Phone"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:170
msgid "Show email in store"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:171
msgid "Show tab on product single page view"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:196
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:519
#: views/settings/wcfm-view-wcmarketplace-settings.php:225
#: views/settings/wcfm-view-wcvendors-settings.php:215
msgid "Store Address"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:200
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:525
msgid "Street"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:200
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:525
msgid "Street address"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:201
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:526
msgid "Street 2"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:201
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:526
msgid "Apartment, suite, unit etc. (optional)"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:202
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:527
msgid "Town / City"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:203
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:528
msgid "Postcode / Zip"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:211
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:547
#: views/settings/wcfm-view-wcmarketplace-settings.php:252
msgid "Store Location"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:215
msgid "Find Address"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:215
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:553
#: views/settings/wcfm-view-wcmarketplace-settings.php:258
msgid "Type an address to find"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:231
msgid "Terms and Conditions"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:235
msgid "Show terms and conditions in store page"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:236
msgid "TOC Details"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:259
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:587
#: views/settings/wcfm-view-wcmarketplace-settings.php:310
#: views/settings/wcfm-view-wcvendors-settings.php:252
msgid "PayPal Email"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:260
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:588
msgid "Skrill Email"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:261
msgid "Wallet"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:261
msgid "Wallet Credit"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:272
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:597
msgid "Bank Details"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:276
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:602
#: views/settings/wcfm-view-wcvendors-settings.php:257
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:76
msgid "Account Name"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:276
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:602
msgid "Your bank account name"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:277
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:603
#: views/settings/wcfm-view-wcmarketplace-settings.php:312
#: views/settings/wcfm-view-wcvendors-settings.php:258
#: views/settings/wcfm-view-wcvendors-settings.php:315
#: views/settings/wcfm-view-wcvendors-settings.php:318
#: views/settings/wcfm-view-wcvendors-settings.php:325
#: views/settings/wcfm-view-wcvendors-settings.php:329
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:77
msgid "Account Number"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:277
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:603
msgid "Your bank account number"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:278
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:604
#: views/settings/wcfm-view-wcmarketplace-settings.php:313
#: views/settings/wcfm-view-wcvendors-settings.php:259
#: views/settings/wcfm-view-wcvendors-settings.php:322
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:78
msgid "Bank Name"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:278
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:604
msgid "Name of bank"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:279
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:605
#: views/settings/wcfm-view-wcmarketplace-settings.php:315
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:79
msgid "Bank Address"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:279
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:605
msgid "Address of your bank"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:280
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:606
#: views/settings/wcfm-view-wcvendors-settings.php:260
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:80
msgid "Routing Number"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:280
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:606
msgid "Routing number"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:281
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:607
#: views/settings/wcfm-view-wcvendors-settings.php:261
#: views/settings/wcfm-view-wcvendors-settings.php:312
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:81
msgid "IBAN"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:282
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:608
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:82
msgid "Swift Code"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:282
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:608
msgid "Swift code"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:297
#: views/settings/wcfm-view-wcmarketplace-settings.php:94
msgid "Stripe Connect"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:302
msgid ""
"Your account is not yet connected with Stripe. Connect with Stripe to "
"receive your commissions."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:343
msgid "Your account is connected with Stripe."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:344
msgid "Disconnect"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:356
msgid "Moip Connect"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:391
msgid "You have no permission to view this page"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:396
#: views/settings/wcfm-view-dokan-settings.php:423
msgid "Shipping functionality is currentlly disabled by site owner"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:406
msgid "If you want to use Country-State wise Shipping system then"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:416
msgid ""
"A shipping zone is a geographic region where a certain set of shipping "
"methods are offered. System will match a customer to a single zone using "
"their shipping address and present the shipping methods within that zone to "
"them."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:426
msgid ""
"This page contains your store-wide shipping settings, costs, shipping and "
"refund policy."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:427
msgid ""
"You can enable/disable shipping for your products. Also you can override "
"these shipping costs while creating or editing a product."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:430
msgid "Enable Shipping"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:430
msgid "Check this if you want to enable shipping for your store"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:431
msgid "Default Shipping Price"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:431
msgid ""
"This is the base price and will be the starting shipping price for each "
"product"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:432
msgid "Per Product Additional Price"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:432
msgid ""
"If a customer buys more than one type product from your store, first "
"product of the every second type will be charged with this price"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:433
msgid "Per Qty Additional Price"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:433
msgid "Every second product of same type will be charged with this price"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:434
msgid "The time required before sending the product for delivery"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:437
msgid "Ships from:"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:437
msgid ""
"Location from where the products are shipped for delivery. Usually it is "
"same as the store."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:457
#: views/settings/wcfm-view-wcvendors-settings.php:390
msgid "Shipping Rates"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:457
msgid ""
"Add the countries you deliver your products to. You can specify states as "
"well. If the shipping price is same except some countries/states, there is "
"an option Everywhere Else, you can use that."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:460
msgid "State Shipping Rates"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:461
#: views/settings/wcfm-view-wcvendors-settings.php:392
msgid "State"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:474
msgid "Dokan Pro Shipping Settings"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:493
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1066
msgid "SEO Title"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:493
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1066
msgid "SEO Title is shown as the title of your store page"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:494
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1067
msgid ""
"The meta description is often shown as the black text under the title in a "
"search result. For this to work it has to contain the keyword that was "
"searched for and should be less than 156 chars."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:495
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1068
msgid "Meta Keywords"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:495
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1068
msgid "Insert some comma separated keywords for better ranking of your store page."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:496
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1083
msgid "Facebook Title"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:497
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1084
msgid "Facebook Description"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:498
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1085
msgid "Facebook Image"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:499
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1101
msgid "Twitter Title"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:500
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1102
msgid "Twitter Description"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:501
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1103
msgid "Twitter Image"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:506
msgid "Dokan Pro SEO Settings"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:523
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1151
#: views/settings/wcfm-view-wcmarketplace-settings.php:751
#: views/settings/wcfm-view-wcpvendors-settings.php:144
#: views/settings/wcfm-view-wcpvendors-settings.php:160
#: views/settings/wcfm-view-wcvendors-settings.php:446
msgid "Vacation Mode"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:529
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1157
#: views/settings/wcfm-view-wcmarketplace-settings.php:757
#: views/settings/wcfm-view-wcpvendors-settings.php:151
#: views/settings/wcfm-view-wcvendors-settings.php:452
msgid "Enable Vacation Mode"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:530
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1158
#: views/settings/wcfm-view-wcmarketplace-settings.php:758
#: views/settings/wcfm-view-wcpvendors-settings.php:152
#: views/settings/wcfm-view-wcvendors-settings.php:453
msgid "Disable Purchase During Vacation"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:531
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1159
#: views/settings/wcfm-view-wcmarketplace-settings.php:759
#: views/settings/wcfm-view-wcpvendors-settings.php:153
#: views/settings/wcfm-view-wcvendors-settings.php:454
msgid "Vacation Type"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:531
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1159
#: views/settings/wcfm-view-wcmarketplace-settings.php:759
#: views/settings/wcfm-view-wcpvendors-settings.php:153
#: views/settings/wcfm-view-wcvendors-settings.php:454
msgid "Instantly Close"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:531
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1159
#: views/settings/wcfm-view-wcmarketplace-settings.php:759
#: views/settings/wcfm-view-wcpvendors-settings.php:153
#: views/settings/wcfm-view-wcvendors-settings.php:454
msgid "Date wise close"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:534
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1162
#: views/settings/wcfm-view-wcmarketplace-settings.php:762
#: views/settings/wcfm-view-wcpvendors-settings.php:156
#: views/settings/wcfm-view-wcvendors-settings.php:457
msgid "Vacation Message"
msgstr ""

#: views/settings/wcfm-view-settings.php:107
msgid "Bookings Global Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:115
msgid "Appointments Global Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:124
msgid "Membership Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:146
msgid "Dashboard Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:150
#: views/settings/wcfm-view-wcmarketplace-settings.php:172
#: views/settings/wcfm-view-wcpvendors-settings.php:109
#: views/settings/wcfm-view-wcvendors-settings.php:156
msgid "Logo"
msgstr ""

#: views/settings/wcfm-view-settings.php:151
msgid "Quick access icon"
msgstr ""

#: views/settings/wcfm-view-settings.php:152
msgid "My Store Label"
msgstr ""

#: views/settings/wcfm-view-settings.php:153
msgid "Disable Quick Access"
msgstr ""

#: views/settings/wcfm-view-settings.php:155
msgid "Disable Welcome Box"
msgstr ""

#: views/settings/wcfm-view-settings.php:156
msgid "Disable WCFM Menu"
msgstr ""

#: views/settings/wcfm-view-settings.php:157
msgid "Disable Theme Header"
msgstr ""

#: views/settings/wcfm-view-settings.php:158
msgid "Disable WCFM Full View"
msgstr ""

#: views/settings/wcfm-view-settings.php:159
msgid "Disable WCFM Slick Menu"
msgstr ""

#: views/settings/wcfm-view-settings.php:161
msgid "Disable WCFM Header Panel"
msgstr ""

#: views/settings/wcfm-view-settings.php:162
msgid "Disable Float Button"
msgstr ""

#: views/settings/wcfm-view-settings.php:164
msgid "Disable Category Checklist"
msgstr ""

#: views/settings/wcfm-view-settings.php:164
msgid ""
"Disable this to have product manager category/custom taxonomy selector as "
"search-list."
msgstr ""

#: views/settings/wcfm-view-settings.php:165
msgid "Disable Tags Input Box"
msgstr ""

#: views/settings/wcfm-view-settings.php:165
msgid ""
"Disable this to have product manager tags input as drop-down, which will "
"restrict to use only pre-defined tags."
msgstr ""

#: views/settings/wcfm-view-settings.php:166
msgid "Disable Ultimate Notice"
msgstr ""

#: views/settings/wcfm-view-settings.php:180
msgid "Modules"
msgstr ""

#: views/settings/wcfm-view-settings.php:184
msgid "Module Controller"
msgstr ""

#: views/settings/wcfm-view-settings.php:186
msgid "Configure what to hide from your dashboard"
msgstr ""

#: views/settings/wcfm-view-settings.php:241
msgid "Dashboard Display Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:253
msgid "Reset to Default"
msgstr ""

#: views/settings/wcfm-view-settings.php:265
msgid "Dashboard Pages"
msgstr ""

#: views/settings/wcfm-view-settings.php:269
msgid "Dashboard Page/Endpoint Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:297
msgid "Refresh Permalink"
msgstr ""

#: views/settings/wcfm-view-settings.php:297
msgid ""
"Check to refresh WCfM page permalinks. Only apply if you are getting error "
"(e.g. 404 not found) for any pages."
msgstr ""

#: views/settings/wcfm-view-settings.php:298
msgid "This page should have shortcode - wc_frontend_manager"
msgstr ""

#: views/settings/wcfm-view-settings.php:303
msgid "WCFM Endpoints"
msgstr ""

#: views/settings/wcfm-view-settings.php:307
msgid "Dashboard End Points"
msgstr ""

#: views/settings/wcfm-view-settings.php:317
msgid "Store End Points"
msgstr ""

#: views/settings/wcfm-view-settings.php:342
msgid "My Account End Points"
msgstr ""

#: views/settings/wcfm-view-settings.php:380
msgid "Menu Manager"
msgstr ""

#: views/settings/wcfm-view-settings.php:384
msgid "Dashboard Menu Manager"
msgstr ""

#: views/settings/wcfm-view-settings.php:387 views/wcfm-view-menu.php:80
msgid "Home"
msgstr ""

#: views/settings/wcfm-view-settings.php:417
msgid "Chat Box"
msgstr ""

#: views/settings/wcfm-view-settings.php:432
msgid "Media"
msgstr ""

#: views/settings/wcfm-view-settings.php:438
msgid "Add to My Store"
msgstr ""

#: views/settings/wcfm-view-settings.php:480
msgid "Home Menu Label"
msgstr ""

#: views/settings/wcfm-view-settings.php:484
msgid "Icon"
msgstr ""

#: views/settings/wcfm-view-settings.php:484
msgid "Insert a valid Font-awesome icon class."
msgstr ""

#: views/settings/wcfm-view-settings.php:485
msgid "Slug"
msgstr ""

#: views/settings/wcfm-view-settings.php:488
msgid "Has New?"
msgstr ""

#: views/settings/wcfm-view-settings.php:489
#: views/settings/wcfm-view-settings.php:491
msgid "New Menu Class"
msgstr ""

#: views/settings/wcfm-view-settings.php:490
msgid "New Menu URL"
msgstr ""

#: views/settings/wcfm-view-settings.php:492
msgid "Sub Menu Capability"
msgstr ""

#: views/settings/wcfm-view-settings.php:493
msgid "Menu For"
msgstr ""

#: views/settings/wcfm-view-settings.php:493
msgid "All Users"
msgstr ""

#: views/settings/wcfm-view-settings.php:493
msgid "Only Admin"
msgstr ""

#: views/settings/wcfm-view-settings.php:493
msgid "Only Vendors"
msgstr ""

#: views/settings/wcfm-view-settings.php:494
msgid "Open in new tab?"
msgstr ""

#: views/settings/wcfm-view-settings.php:510
#: views/settings/wcfm-view-settings.php:514
msgid "Email Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:518
msgid "Email from name"
msgstr ""

#: views/settings/wcfm-view-settings.php:518
msgid ""
"Notification emails will be triggered with this name. By default Site Name "
"will be used"
msgstr ""

#: views/settings/wcfm-view-settings.php:519
msgid "Email from address"
msgstr ""

#: views/settings/wcfm-view-settings.php:519
msgid ""
"Notification emails will be triggered from this email address. By default "
"Site Admin Email will be used"
msgstr ""

#: views/settings/wcfm-view-settings.php:520
msgid "CC Email address"
msgstr ""

#: views/settings/wcfm-view-settings.php:520
msgid "Notification emails will be CC to this email address."
msgstr ""

#: views/settings/wcfm-view-settings.php:521
msgid "BCC Email address"
msgstr ""

#: views/settings/wcfm-view-settings.php:521
msgid "Notification emails will be BCC to this email address."
msgstr ""

#: views/settings/wcfm-view-settings.php:533
msgid "Inquiry Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:537
msgid "Inquiry Module"
msgstr ""

#: views/settings/wcfm-view-settings.php:543
msgid "Button Label"
msgstr ""

#: views/settings/wcfm-view-settings.php:544
msgid "Require Login?"
msgstr ""

#: views/settings/wcfm-view-settings.php:544
msgid "Whether customer has to be logged-in to submit inquiry."
msgstr ""

#: views/settings/wcfm-view-settings.php:545
msgid "Reply Attachment?"
msgstr ""

#: views/settings/wcfm-view-settings.php:545
msgid ""
"Whether vendors and customers are allowed to add attachment(s) with inquiry "
"reply."
msgstr ""

#: views/settings/wcfm-view-settings.php:546
msgid "Button Position"
msgstr ""

#: views/settings/wcfm-view-settings.php:546
msgid "Inquiry button display position at Single Product Page."
msgstr ""

#: views/settings/wcfm-view-settings.php:547
msgid "Inquiry Form Custom Fields"
msgstr ""

#: views/settings/wcfm-view-settings.php:551
msgid "Insert option values | separated"
msgstr ""

#: views/settings/wcfm-view-settings.php:566
msgid "Product Type Categories"
msgstr ""

#: views/settings/wcfm-view-settings.php:570
msgid "Product Type Specific Category Setup"
msgstr ""

#: views/settings/wcfm-view-settings.php:591
msgid ""
"Create group of your Store Categories as per Product Types. Product Manager "
"will work according to that."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:223
msgid "This Banner will be visible when someone browse store from Mobile."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:224
msgid "Preferred size is (%sx%s) pixels."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:229
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:230
msgid "Static Image"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:229
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:341
msgid "Slider"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:229
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:230
msgid "Video"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:284
msgid "General Setting"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:291
msgid "Store Slug"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:292
msgid "Store Email"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:330
msgid "Store Brand Setup"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:336
msgid "Store Logo"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:336
msgid "Preferred  size is (125x125) pixels."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:338
msgid "Store Banner Type"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:339
msgid "Store Banner"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:340
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:352
msgid "Insert YouTube video URL."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:343
msgid "Slider Hyperlink"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:347
msgid "Mobile Banner"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:349
msgid "Store List Banner Type"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:350
msgid "Store List Banner"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:350
msgid "This Banner will be visible at Store List Page."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:352
msgid "Store List Video Banner"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:354
#: views/settings/wcfm-view-wcmarketplace-settings.php:175
#: views/settings/wcfm-view-wcvendors-settings.php:159
msgid "Shop Description"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:354
#: views/settings/wcfm-view-wcmarketplace-settings.php:175
#: views/settings/wcfm-view-wcvendors-settings.php:159
msgid "This is displayed on your shop page."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:355
msgid "Select Shipping Countries"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:447
msgid "Store Visibility Setup"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:453
msgid "Store name position at you Store Page."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:454
msgid "No of products at you Store Page."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:455
msgid "Hide Email from Store"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:456
msgid "Hide Phone from Store"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:457
msgid "Hide Address from Store"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:458
msgid "Hide Map from Store"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:459
msgid "Hide About from Store"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:460
msgid "Hide Policy from Store"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:553
#: views/settings/wcfm-view-wcmarketplace-settings.php:258
msgid "Find Location"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:584
msgid "Choose Withdrawal Payment Method"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:586
msgid "Preferred Payment Method"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:609
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:83
msgid "IFSC Code"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:609
msgid "IFSC code"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:677
msgid "You are connected with stripe."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:679
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:795
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:918
#: views/settings/wcfm-view-wcmarketplace-settings.php:397
#: views/settings/wcfm-view-wcmarketplace-settings.php:526
msgid "Disconnect Stripe Account"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:682
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:973
#: views/settings/wcfm-view-wcmarketplace-settings.php:566
#: views/settings/wcfm-view-wcpvendors-settings.php:417
msgid "You are not connected with stripe."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:684
msgid "Connect with Stripe"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:718
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1011
msgid "Stripe not setup properly, please contact your site admin."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:789
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:910
#: views/settings/wcfm-view-wcmarketplace-settings.php:391
#: views/settings/wcfm-view-wcmarketplace-settings.php:520
#: views/settings/wcfm-view-wcpvendors-settings.php:250
#: views/settings/wcfm-view-wcpvendors-settings.php:372
msgid "You are connected with Stripe"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:814
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:834
#: views/settings/wcfm-view-wcmarketplace-settings.php:418
#: views/settings/wcfm-view-wcmarketplace-settings.php:439
#: views/settings/wcfm-view-wcpvendors-settings.php:277
#: views/settings/wcfm-view-wcpvendors-settings.php:298
msgid "Please Retry!!!"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:886
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:889
msgid "Unable to disconnect your account pleease try again"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:995
#: views/settings/wcfm-view-wcmarketplace-settings.php:588
#: views/settings/wcfm-view-wcpvendors-settings.php:439
msgid "Please connected with stripe again."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1060
msgid "General Setup"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1077
msgid "Facebook Setup"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:1095
msgid "Twitter Setup"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:86
msgid "Payment Mode"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:88
msgid "PayPal Masspay"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:91
msgid "PayPal Payout"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:97
msgid "Direct Bank"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:172
msgid "Preferred logo should be 200x200 px."
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:173
#: views/settings/wcfm-view-wcpvendors-settings.php:110
#: views/settings/wcfm-view-wcvendors-settings.php:157
msgid "Your shop name is public and must be unique."
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:174
msgid "Shop Slug"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:174
msgid "Your shop slug is public and must be unique."
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:201
#: views/settings/wcfm-view-wcvendors-settings.php:187
msgid "Branding"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:207
msgid "Preferred banner should be 1200x245 px."
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:209
msgid "Shop Phone"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:209
msgid "Your store phone no."
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:244
#: views/settings/wcfm-view-wcmarketplace-settings.php:245
#: views/settings/wcfm-view-wcpvendors-settings.php:130
#: views/settings/wcfm-view-wcpvendors-settings.php:131
msgid "Timezone"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:244
#: views/settings/wcfm-view-wcpvendors-settings.php:130
msgid "Set the local timezone."
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:270
msgid "Please contact your administrator to enable Google map feature."
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:278
msgid "Shop Template"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:309
#: views/withdrawal/dokan/wcfm-view-withdrawal.php:94
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:143
msgid "Payment Method"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:311
#: views/settings/wcfm-view-wcvendors-settings.php:310
msgid "Account Type"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:311
msgid "Current"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:311
msgid "Savings"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:314
msgid "ABA Routing Number"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:316
msgid "Destination Currency"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:317
msgid "Account IBAN"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:318
msgid "Account Holder Name"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:649
#: views/settings/wcfm-view-wcmarketplace-settings.php:658
msgid "Shipping Zone"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:679
msgid "Shipping Rules"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:680
msgid "Condition"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:680
msgid "None"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:680
msgid "Item count"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:682
msgid "Min"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:683
msgid "Max"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:684
msgid "Row cost"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:685
msgid "Item cost"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:686
msgid "lbs cost"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:687
msgid "% cost"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:698
msgid ""
"There is no shipping zone or Flat Rate shipping method not associated for "
"the zones to set shipping prices, kindly contact your Store Admin."
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:111
msgid "Vendor Email"
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:111
msgid ""
"Enter the email for this vendor. This is the email where all notifications "
"will be send such as new orders and customer inquiries. You may enter more "
"than one email separating each with a comma."
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:112
msgid "Enter the profile information you would like for customer to see."
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:180
msgid "Paypal Email"
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:180
msgid "PayPal email account where you will receive your commission."
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:181
msgid ""
"Default commission you will receive per product sale. Please note product "
"level commission can override this. Check your product to confirm."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:158
#: views/vendors/wcfm-view-vendors-manage.php:314
msgid "Seller Info"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:158
msgid "This is displayed on each of your products."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:196
msgid "Store Website / Blog URL"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:196
msgid "Your company/blog URL here."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:197
msgid "This is your store contact number."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:232
#: views/settings/wcfm-view-wcvendors-settings.php:429
msgid "WCV Pro Settings"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:252
msgid "Your PayPal address is used to send you your commission."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:262
msgid "BIC / Swift"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:307
msgid "Bank Payment (Mangopay)"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:313
#: views/settings/wcfm-view-wcvendors-settings.php:328
msgid "BIC"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:316
msgid "Sort Code"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:319
msgid "ABA"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:320
msgid "Deposit Account Type"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:320
msgid "CHECKING"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:320
msgid "SAVINGS"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:323
msgid "Institution Number"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:324
msgid "Branch Code"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:331
msgid "Account holder&apos;s name"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:332
#: views/settings/wcfm-view-wcvendors-settings.php:333
msgid "Account holder&apos;s address"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:334
msgid "Account holder&apos;s city"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:335
msgid "Account holder&apos;s postal code"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:336
msgid "Account holder&apos;s country"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:337
msgid "Account holder&apos;s region"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:375
msgid "Default National Shipping Fee"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:375
msgid ""
"The default shipping fee within your country, this can be overridden on a "
"per product basis."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:376
msgid "Free national shipping"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:376
msgid "Free national shipping."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:377
#: views/settings/wcfm-view-wcvendors-settings.php:384
msgid "Charge once per product"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:377
msgid ""
"Charge once per product for national shipping, even if more than one is "
"purchased."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:378
msgid "Disable national shipping"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:378
msgid "Disable national shipping."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:382
msgid "Default International Shipping Fee"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:382
msgid ""
"The default shipping fee outside your country, this can be overridden on a "
"per product basis. "
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:383
msgid "Free international shipping"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:383
msgid "Free international shipping."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:384
msgid ""
"Charge once per product for international shipping, even if more than one "
"is purchased."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:385
msgid "Disable international shipping"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:385
msgid "Disable international shipping."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:394
msgid "Shipping Fee"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:395
msgid "Override Qty"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:401
msgid "Min Charge Order"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:401
msgid "The minimum shipping fee charged for an order."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:402
msgid "Max Charge Order"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:402
msgid "The maximum shipping fee charged for an order."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:403
msgid "Free Shipping Order"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:403
msgid ""
"Free shipping for order spends over this amount. This will override the max "
"shipping charge above."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:404
msgid "Max Charge Product"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:404
msgid "The maximum shipping charged per product no matter the quantity."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:405
msgid "Free Shipping Product"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:405
msgid ""
"Free shipping if the spend per product is over this amount. This will "
"override the max shipping charge above."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:406
msgid "Product handling fee"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:406
msgid "Leave empty to disable"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:406
msgid ""
"The product handling fee, this can be overridden on a per product basis. "
"Amount (5.00) or Percentage (5%)."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:413
msgid "From Address"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:260
msgid "<strong>%s product</strong><br />"
msgid_plural "<strong>%s products</strong><br />"
msgstr[0] ""
msgstr[1] ""

#: views/vendors/wcfm-view-vendors-manage.php:262
msgid "total products posted"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:274
msgid "<strong>%s item</strong><br />"
msgid_plural "<strong>%s items</strong><br />"
msgstr[0] ""
msgstr[1] ""

#: views/vendors/wcfm-view-vendors-manage.php:298
#: views/vendors/wcfm-view-vendors-manage.php:300
msgid "Store Admin"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:327
msgid "Disable Account"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:335
msgid "Enable Account"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:403
msgid "Vendor not yet subscribed for a membership!"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:417
msgid "Send Message"
msgstr ""

#: views/vendors/wcfm-view-vendors.php:55
msgid "Show all ..."
msgstr ""

#: views/vendors/wcfm-view-vendors.php:87
#: views/vendors/wcfm-view-vendors.php:108
msgid "Earnings"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:36
msgid "Global Availability"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:52
#: views/wc_bookings/wcfm-view-wcbookings.php:70
msgid "Create Booking"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:64
#: views/wc_bookings/wcfm-view-wcbookings-details.php:94
#: views/wc_bookings/wcfm-view-wcbookings.php:81
msgid "Create Bookable"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:81
#: views/wc_bookings/wcfm-view-wcbookings-details.php:89
#: views/wc_bookings/wcfm-view-wcbookings.php:76
msgid "Manage Resources"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:63
msgid "Booking Details"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:70
msgid "Booking #"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:81
#: views/wc_bookings/wcfm-view-wcbookings.php:86
msgid "Calendar View"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:105
msgid "Overview"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:111
msgid "Booking Created:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:116
msgid "Order Number:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:132
msgid "Booking Cost:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:145
msgid "Booking status:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:159
msgid "Confirm"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:160
msgid "Decline"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:182
msgid "Booked product:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:196
msgid "Resource:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:211
#: views/wc_bookings/wcfm-view-wcbookings-details.php:236
msgid "Person(s):"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:254
#: views/wc_bookings/wcfm-view-wcbookings-details.php:320
msgid "Start date:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:277
#: views/wc_bookings/wcfm-view-wcbookings-details.php:325
msgid "End date:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:299
#: views/wc_bookings/wcfm-view-wcbookings-details.php:329
msgid "All day booking:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:355
msgid "Name:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:367
msgid "User Email:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:380
msgid "Address:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:393
msgid "Billing Email:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:399
msgid "Billing Phone:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:410
msgid "View Order"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings.php:99
msgid "Filter Bookings"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings.php:101
msgid "By appointable product"
msgstr ""

#: views/wcfm-view-header-panels.php:41 views/wcfm-view-header-panels.php:44
msgid "Toggle Menu"
msgstr ""

#: views/wcfm-view-header-panels.php:68
msgid "Inquiry Board"
msgstr ""

#: views/wcfm-view-header-panels.php:82 views/wcfm-view-menu.php:169
msgid "Logout"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-payments.php:34
#: views/withdrawal/wcfm/wcfm-view-payments.php:40
#: views/withdrawal/wcmp/wcfm-view-payments.php:34
msgid "Transactions for: "
msgstr ""

#: views/withdrawal/dokan/wcfm-view-payments.php:51
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:37
#: views/withdrawal/wcfm/wcfm-view-payments.php:60
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:45
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:58
msgid "Show all .."
msgstr ""

#: views/withdrawal/dokan/wcfm-view-payments.php:52
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:38
#: views/withdrawal/wcfm/wcfm-view-payments.php:61
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:46
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:59
msgid "Approved"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-payments.php:69
#: views/withdrawal/dokan/wcfm-view-payments.php:78
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:164
#: views/withdrawal/wcmp/wcfm-view-payments.php:68
#: views/withdrawal/wcmp/wcfm-view-payments.php:79
msgid "Pay Mode"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-payments.php:70
#: views/withdrawal/dokan/wcfm-view-payments.php:79
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:56
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:66
#: views/withdrawal/wcfm/wcfm-view-payments.php:84
#: views/withdrawal/wcfm/wcfm-view-payments.php:99
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:79
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:96
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:90
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:103
msgid "Note"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:52
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:62
msgid "Requests"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:80
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:111
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:118
msgid "Note to Vendor(s)"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:34
msgid "Current Balance: %s "
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:37
msgid "<br>Minimum Withdraw amount: %s "
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:40
msgid "<br>Withdraw Threshold: %d days "
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:64
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:113
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:66
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:53
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:42
msgid "Transaction History"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:75
msgid "You already have pending withdraw request(s)."
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:75
msgid ""
"Please submit your request after approval or cancellation of your previous "
"request."
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:84
msgid "You don't have sufficient balance for a withdraw request!"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:93
msgid "Withdraw Amount"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:105
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:131
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:88
msgid "Request"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:111
msgid ""
"No withdraw method is available. Please update your payment method to "
"withdraw funds."
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:111
msgid "Payment Settings Setup"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-payments.php:76
#: views/withdrawal/wcfm/wcfm-view-payments.php:91
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:71
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:88
msgid "Invoice ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-payments.php:77
#: views/withdrawal/wcfm/wcfm-view-payments.php:92
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:72
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:89
msgid "Order IDs"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-payments.php:78
#: views/withdrawal/wcfm/wcfm-view-payments.php:93
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:77
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:94
#: views/withdrawal/wcmp/wcfm-view-payments.php:65
#: views/withdrawal/wcmp/wcfm-view-payments.php:76
msgid "Commission IDs"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-payments.php:80
#: views/withdrawal/wcfm/wcfm-view-payments.php:95
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:75
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:92
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:94
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:108
msgid "Charges"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-payments.php:82
#: views/withdrawal/wcfm/wcfm-view-payments.php:97
msgid "Mode"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:84
msgid "Payment Received Account"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:85
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:88
msgid "Transaction ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:86
msgid "Transaction Status"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:87
msgid "Request ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:89
msgid "Transaction Ref"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:90
msgid "Transfer Mode"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:104
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:43
msgid "Transaction #"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:141
msgid "Order ID(s)"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:142
msgid "Commission ID(s)"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:178
msgid "By Split Pay"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:180
msgid "By Wirecard"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:186
msgid "Total Amount"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:194
msgid "Paid Amount"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:194
msgid "Payable Amount"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:69
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:86
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:83
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:96
msgid "Select all to approve or cancel"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:121
msgid "Approve Manually"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:30
msgid "Reverse Withdrawals"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:39
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:60
msgid "Reverse pay balance "
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:40
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:61
msgid "Threshold Limit: %s"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:42
msgid "Reverse Withdrawal Requests"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:84
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:97
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:91
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:105
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:58
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:67
msgid "Order ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:51
msgid "Pending Withdrawals: "
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:55
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:36
msgid "Threshold for withdrawals: "
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:89
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:103
msgid "Select all to send request"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:92
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:106
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:59
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:68
msgid "Commission ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:93
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:107
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:60
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:69
msgid "My Earnings"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:120
msgid ""
"Withdrawal charges will be re-calculated depending upon total withdrawal "
"amount."
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:133
msgid "Withdrawal disable due to high reverse balance."
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:136
msgid "Withdrawal disable due to low account balance."
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-payments.php:64
#: views/withdrawal/wcmp/wcfm-view-payments.php:75
msgid "Transc.ID"
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-payments.php:67
#: views/withdrawal/wcmp/wcfm-view-payments.php:78
msgid "Net Earnings"
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:101
msgid ""
"Hello,<br>Unfortunately your request for withdrawal amount could not be "
"completed. You may try again later, or check you PayPal settings in your "
"account page, or contact the admin at <b>%s</b>"
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:57
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:66
msgid "Send Request"
msgstr ""

#. Plugin Name of the plugin/theme
msgid "WCFM - WooCommerce Frontend Manager"
msgstr ""

#. Author URI of the plugin/theme
msgid "https://wclovers.com"
msgstr ""

#. Description of the plugin/theme
msgid ""
"WooCommerce is really Easy and Beautiful. We are here to make your life "
"much more Easier and Peaceful."
msgstr ""

#. Author of the plugin/theme
msgid "WC Lovers"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:125
#: controllers/customers/wcfm-controller-customers-details.php:131
#: controllers/orders/wcfm-controller-dokan-orders.php:173
#: controllers/orders/wcfm-controller-dokan-orders.php:180
#: controllers/orders/wcfm-controller-orders.php:213
#: controllers/orders/wcfm-controller-orders.php:219
#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:221
#: controllers/orders/wcfm-controller-wcfmmarketplace-itemized-orders.php:228
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:273
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:279
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:197
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:204
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:166
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:173
#: controllers/orders/wcfm-controller-wcvendors-orders.php:194
#: controllers/orders/wcfm-controller-wcvendors-orders.php:201
msgctxt "full name"
msgid "%1$s %2$s"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:328
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:131
msgctxt "Guest string with name from booking order in brackets"
msgid "Guest (%s)"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:515
msgctxt "Guest string with name from appointment order in brackets"
msgid "Guest (%s)"
msgstr ""

#: controllers/customers/wcfm-controller-customers.php:222
#: controllers/customers/wcfm-controller-customers.php:224
msgctxt "hash before order number"
msgid "#"
msgstr ""

#: core/class-wcfm-integrations.php:712 core/class-wcfm-integrations.php:780
msgctxt "trusted-shops"
msgid "GTIN"
msgstr ""

#: core/class-wcfm-integrations.php:712 core/class-wcfm-integrations.php:780
msgctxt "trusted-shops"
msgid ""
"ID that allows your products to be identified worldwide. If you want to "
"display your Trusted Shops Product Reviews in Google Shopping and paid "
"Google adverts, Google needs the GTIN."
msgstr ""

#: core/class-wcfm-integrations.php:713 core/class-wcfm-integrations.php:781
msgctxt "trusted-shops"
msgid "MPN"
msgstr ""

#: core/class-wcfm-integrations.php:713 core/class-wcfm-integrations.php:781
msgctxt "trusted-shops"
msgid ""
"If you don't have a GTIN for your products, you can pass the brand name and "
"the MPN on to Google to use the Trusted Shops Google Integration."
msgstr ""

#: core/class-wcfm.php:147
msgctxt "wc-frontend-manager"
msgid "Archived"
msgstr ""

#: helpers/class-wcfm-install.php:102
msgctxt "page_slug"
msgid "store-manager"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:120 helpers/class-wcfm-setup.php:120
msgctxt "enhanced select"
msgid "No matches found"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:121 helpers/class-wcfm-setup.php:121
msgctxt "enhanced select"
msgid "Loading failed"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:122 helpers/class-wcfm-setup.php:122
msgctxt "enhanced select"
msgid "Please enter 1 or more characters"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:123 helpers/class-wcfm-setup.php:123
msgctxt "enhanced select"
msgid "Please enter %qty% or more characters"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:124 helpers/class-wcfm-setup.php:124
msgctxt "enhanced select"
msgid "Please delete 1 character"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:125 helpers/class-wcfm-setup.php:125
msgctxt "enhanced select"
msgid "Please delete %qty% characters"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:126 helpers/class-wcfm-setup.php:126
msgctxt "enhanced select"
msgid "You can only select 1 item"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:127 helpers/class-wcfm-setup.php:127
msgctxt "enhanced select"
msgid "You can only select %qty% items"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:128 helpers/class-wcfm-setup.php:128
msgctxt "enhanced select"
msgid "Loading more results&hellip;"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:129 helpers/class-wcfm-setup.php:129
msgctxt "enhanced select"
msgid "Searching&hellip;"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:812
msgctxt "Ex: Refund - $date >by< $username"
msgid "by"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:142
msgctxt "Tax status"
msgid "None"
msgstr ""

#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:63
#: views/reports/wcfm-view-reports-sales-by-date.php:63
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:91
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:97
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:61
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:61
msgctxt "start date and end date"
msgid "From %s to %s"
msgstr ""