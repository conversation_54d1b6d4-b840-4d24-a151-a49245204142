@media screen and (max-width: 1920px) {
	
    
}
@media screen and (max-width: 1600px) {
	
    
}
@media screen and (max-width: 1300px) {
	
    
}
@media screen and (max-width: 1200px) {
    
	.wrapper{width:96%;}
}
@media screen and (max-width: 1170px) {
    
}
@media screen and (max-width: 1100px) {
	
    
}
@media screen and (max-width: 1024px) {
    
	#wcfmmp-store .right_side{width:100%;}
	#wcfmmp-store .left_sidebar{width:100%; margin-top:50px;}
	
	#wcfmmp-store .banner_text h1{font-size:50px; line-height:56px; margin-bottom:15px;}
	#wcfmmp-store .banner_text a{font-size:16px; line-height:20px; padding:10px 20px; width:auto; height:auto;}
	#wcfmmp-store .logo_area{width:90px; height:90px;top:-70px;position:relative;}
	#wcfmmp-store .logo_area_after {top: 50px;position: absolute;text-align: center;left:25px;}
	#wcfmmp-store .header_left{width:100%; margin:0; padding:20px; box-sizing:border-box; -moz-box-sizing:border-box; -ms-box-sizing:border-box; -o-box-sizing:border-box; -webkit-box-sizing:border-box;}
  #wcfmmp-store .header_right{width:100%; margin:0; padding:10px 20px; box-sizing:border-box; -moz-box-sizing:border-box; -ms-box-sizing:border-box; -o-box-sizing:border-box; -webkit-box-sizing:border-box;}

	#wcfmmp-store .address{float:left; width:80%;}    
	#wcfmmp-store .social_area{width:auto;}
	
	#wcfmmp-store .slider_text h1 {font-size:20px;line-height:25px;}
	
	#wcfmmp-store .reviews_area {padding: 20px;}
	
	#wcfmmp-store .social_area{position:absolute;top:0;right:10px;background:none !important; min-height:auto; width:100%; margin-top:20px;z-index:2;}
	#wcfmmp-store .social_area ul{position:relative; text-align:right; top:0; left:0; transform: translateY(0);}
	                                          
}
@media screen and (max-width: 960px) {

	#wcfmmp-store .address{position:relative; top:0; left:0; transform:translateY(0); -moz-transform:translateY(0); -ms-transform:translateY(0); -o-transform:translateY(0); -webkit-transform:translateY(0); padding-left:10px; margin-left:10px;}
	#wcfmmp-store .bd_icon_box{position:relative; margin-right:15px;}
}
@media screen and (max-width: 768px) {

  #wcfmmp-store h1{font-size:18px;}
    
	#wcfmmp-store .right_side{width:100%;}
	#wcfmmp-store .left_sidebar{width:100%; margin-top:50px;}
	
	#wcfmmp-store .banner_text h1{font-size:50px; line-height:56px; margin-bottom:15px;}
	#wcfmmp-store .banner_text a{font-size:16px; line-height:20px; padding:10px 20px; width:auto; height:auto;}
	#wcfmmp-store .logo_area{width:90px; height:90px;top:-70px;position:relative;}
	#wcfmmp-store .logo_area_after {top: 50px;position: absolute;text-align: center;left:25px;}
	#wcfmmp-store .header_left{width:100%; margin:0; padding:20px; box-sizing:border-box; -moz-box-sizing:border-box; -ms-box-sizing:border-box; -o-box-sizing:border-box; -webkit-box-sizing:border-box;}
  #wcfmmp-store .header_right{width:100%; margin:0; padding:10px 20px; box-sizing:border-box; -moz-box-sizing:border-box; -ms-box-sizing:border-box; -o-box-sizing:border-box; -webkit-box-sizing:border-box;}

	#wcfmmp-store .address{float:left; width:80%;}    
	#wcfmmp-store .social_area{width:auto;}
	
	#wcfmmp-store .slider_text h1 {font-size:20px;line-height:25px;}
	
	#wcfmmp-store .reviews_area {padding: 20px;}
	
}
@media screen and (max-width: 736px) {
	#wcfmmp-store .bd_icon_area{float:left;position:relative;top:0px;margin:15px 0 0px 0}
	#wcfmmp-store .social_area{position:relative;margin-top:15px;float:right;right:0;}
	#wcfmmp-store .social_area ul li{margin-top:0;}
	#wcfmmp-store .bd_icon_box{margin-bottom:0px;}
	#wcfmmp-store .bd_icon_box span{font-size:12px; line-height:16px;}
	#wcfmmp-store .bd_icon_box .follow, #wcfmmp-store .bd_icon_box .wcfm_store_enquiry,#wcfmmp-store .bd_icon_box .wcfm_store_chatnow{padding:0 5px;}
	
}
@media screen and (max-width: 667px) {
	
	#wcfm_enquiry_form .comment-form-author, #wcfm_enquiry_form .comment-form-email {
		width: 100%;
	}
	
	#wcfmmp-store .address{width:82%;}
	
	#wcfmmp-store .bd_icon_box{margin-right:5px;}
	#wcfmmp-store .bd_icon_box .follow, #wcfmmp-store .bd_icon_box .wcfm_store_enquiry,#wcfmmp-store .bd_icon_box .wcfm_store_chatnow{height:28px;}
	#wcfmmp-store .bd_icon_box span{position:inherit; margin-left:0; transform:translateX(-50%); -moz-transform:translateX(-50%); -ms-transform:translateX(-50%); -o-transform:translateX(-50%); -webkit-transform:translateX(-50%); opacity:1;}
	#wcfmmp-store .bd_icon_box:hover span{opacity:1;}
	#wcfmmp-store .bd_icon_box .follow span, #wcfmmp-store .bd_icon_box .wcfm_store_chatnow span{display:none;}
	#wcfmmp-store .bd_icon_box .follow i, #wcfmmp-store .bd_icon_box .wcfm_store_chatnow i{margin-right:0px;}
	
	#wcfmmp-store .review_text{width:86%;}
	#wcfmmp-store .user_review_sec .user_review_sec_left{width:100% !important;}
	#wcfmmp-store .user_review_sec .bd_rating_area{display:none !important;}
	
}
@media screen and (max-width: 627px) {
	
	#wcfmmp-store .header_right{width:100%;}
	#wcfmmp-store .bd_icon_area{float:left;}
	#wcfmmp-store .social_area{float:right;}
	#wcfmmp-store .social_area ul li{margin-top:0;}
	#wcfmmp-store .address{width:80%;	}
	
	#wcfmmp-store .banner_text h1{font-size:40px; line-height:46px; padding:0 30px;}
	#wcfmmp-store .banner_text h1::after, .banner_text h1::before{width:50px;}
	#wcfmmp-store .banner_text h1::before{right:-48px;}
	
	#wcfmmp-store .banner_text a{font-size:14px; line-height:20px; padding:6px 20px;}
	
	#wcfmmp-store .slider_text h1 {font-size:15px;line-height:20px;}
	
	#wcfmmp-store .bd_icon_area, #wcfmmp-store .social_area{margin-top:2px;}
	#wcfmmp-store .social_area ul {transform: translateY(10%);padding:0px;}
	#wcfmmp-store .social_area ul li{width:20px;height:20px;}
	#wcfmmp-store .social_area ul li a i{font-size:10px;}
	
    
}
@media screen and (max-width: 568px) {
    
	.review_text, .user_review_sec{width:80%;}
	
	#wcfmmp-store .address span{font-size:12px; line-height:16px;}
	
}
@media screen and (max-width: 540px) {
    
	#wcfmmp-store .address{width:78%;}
	
	#wcfmmp-store .banner_text h1{font-size:30px; line-height:36px; margin-bottom:10px;}
	#wcfmmp-store .banner_text a{font-size:12px; line-height:15px; padding:5px 15px;}
	
	#wcfmmp-store .slider_text h1 {font-size:12px;line-height:15px;}
}
@media screen and (max-width: 480px) {

	#wcfmmp-store .address{width:74%;}
	#wcfmmp-store .product_box{width:48%;}
	
	#wcfmmp-store .tab_area .tab_links{margin-bottom:20px;}
	
	#wcfmmp-store .paginations ul li:first-child{padding-right:5px;}
	#wcfmmp-store .paginations ul li:last-child{padding-left:5px;}
	#wcfmmp-store .paginations ul li{padding:0 1px;}
	
	#wcfmmp-store .header_left{text-align:center;}
	#wcfmmp-store .logo_area{float:none; display:inline-block; margin-bottom:5px; }
	#wcfmmp-store .logo_area_after{left:40%;}
 	
	#wcfmmp-store .address{width:100%; border-left:0; padding-left:0; margin-left:0;}
	
	#wcfmmp-store .review_text, .user_review_sec{width:100%;}
	
	#wcfmmp-store .product_box{width:98%;}
	
	#wcfmmp-store #wcfm_store_header{padding-bottom:0;}
	#wcfmmp-store .header_right{padding:10px 10px; box-sizing:border-box; -moz-box-sizing:border-box; -ms-box-sizing:border-box; -o-box-sizing:border-box; -webkit-box-sizing:border-box;}

	#wcfmmp-store .bd_icon_box .follow{line-height:inherit;}
	
	#wcfmmp-store .tab_area .tab_links li{width:100%; border-bottom:1px solid #93a8b3;margin-right:0px;margin-bottom:0px;}
	#wcfmmp-store .tab_area .tab_links li:hover, .tab_area .tab_links li.active{border-bottom:1px solid #fff;}
	#wcfmmp-store .tab_area .tab_links li.active {border-top: 1px solid #93a8b3 !important;border-bottom:0px !important;border-left: 4px solid #17a2b8 !important;}
	#wcfmmp-store .tab_area .tab_links li a{display:block;}
	
	#wcfmmp-store .user_review_sec{width:65%;}
	#wcfmmp-store .address span{display:initial;}
    
}

@media screen and (max-width: 414px) {
    
}
@media screen and (max-width: 384px) {
    
}
@media screen and (max-width: 375px) {
    
}
@media screen and (max-width: 360px) {
    
}
@media screen and (max-width: 320px) {
    
}