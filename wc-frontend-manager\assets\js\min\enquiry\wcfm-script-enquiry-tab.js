$wcfm_enquiry_submited=!1,jQuery(document).ready(function(e){function a(){var a={action:"wcfm_enquiry_form_content",store:0,product:0,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:a,success:function(a){e("body").append(a),e("#enquiry_form").find(".wcfm_datepicker").each(function(){e(this).datepicker({closeText:wcfm_datepicker_params.closeText,currentText:wcfm_datepicker_params.currentText,monthNames:wcfm_datepicker_params.monthNames,monthNamesShort:wcfm_datepicker_params.monthNamesShort,dayNames:wcfm_datepicker_params.dayNames,dayNamesShort:wcfm_datepicker_params.dayNamesShort,dayNamesMin:wcfm_datepicker_params.dayNamesMin,firstDay:wcfm_datepicker_params.firstDay,isRTL:wcfm_datepicker_params.isRTL,dateFormat:wcfm_datepicker_params.dateFormat,changeMonth:!0,changeYear:!0})}),initiateTip(),e("#wcfm_enquiry_submit_button").off("click").on("click",function(a){a.preventDefault(),function(a){if($is_valid=function(a){if($is_valid=!0,jQuery(".wcfm-message").html("").removeClass("wcfm-success").removeClass("wcfm-error").slideUp(),0==jQuery.trim(a.find("#enquiry_comment").val()).length&&($is_valid=!1,a.find(".wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfm_enquiry_manage_messages.no_enquiry).addClass("wcfm-error").slideDown()),a.find("#enquiry_author").length>0){var s=jQuery.trim(a.find("#enquiry_author").val());0==s.length&&($is_valid?a.find(".wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfm_enquiry_manage_messages.no_name).addClass("wcfm-error").slideDown():a.find(".wcfm-message").append('<br /><span class="wcicon-status-cancelled"></span>'+wcfm_enquiry_manage_messages.no_name).addClass("wcfm-error").slideDown(),$is_valid=!1)}if(a.find("#enquiry_email").length>0){var r=jQuery.trim(a.find("#enquiry_email").val());0==r.length&&($is_valid?a.find(".wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfm_enquiry_manage_messages.no_email).addClass("wcfm-error").slideDown():a.find(".wcfm-message").append('<br /><span class="wcicon-status-cancelled"></span>'+wcfm_enquiry_manage_messages.no_email).addClass("wcfm-error").slideDown(),$is_valid=!1)}return $wcfm_is_valid_form=$is_valid,e(document.body).trigger("wcfm_form_validate",a),$is_valid=$wcfm_is_valid_form,$is_valid}(a),$is_valid){e("#enquiry_form_wrapper").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var s={action:"wcfm_ajax_controller",controller:"wcfm-enquiry-tab",wcfm_enquiry_tab_form:a.serialize(),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce,status:"submit"};jQuery.post(wcfm_params.ajax_url,s,function(s){s&&($response_json=jQuery.parseJSON(s),a.find(".wcfm-message").html("").removeClass("wcfm-success").removeClass("wcfm-error").slideUp(),wcfm_notification_sound.play(),$response_json.status?(a.find(".wcfm-message").html('<span class="wcicon-status-completed"></span>'+$response_json.message).addClass("wcfm-success").slideDown("slow"),setTimeout(function(){e.colorbox.remove(),a.find("#enquiry_comment").val(""),jQuery(".wcfm-message").html("").removeClass("wcfm-success").removeClass("wcfm-error").slideUp()},2e3)):a.find(".wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+$response_json.message).addClass("wcfm-error").slideDown(),jQuery(".wcfm_gglcptch_wrapper").length>0&&"undefined"!=typeof grecaptcha&&grecaptcha.reset(),e("#enquiry_form_wrapper").unblock())})}}(e("#wcfm_enquiry_form"))}),$inquiryFormLoaded=!0}})}e(".enquiry-form").length>1&&e(".enquiry-form")[1].remove(),$inquiryFormLoaded=!1,e(".add_enquiry").length>0&&a(),e(".wcfm_catalog_enquiry").length>0&&($inquiryFormLoaded||a()),e(".wcfm_store_enquiry").length>0&&($inquiryFormLoaded||a()),$wcfm_anr_loaded=!1,e(".add_enquiry, .wcfm_catalog_enquiry, .wcfm_store_enquiry").each(function(){e(this).click(function(a){if(a.preventDefault(),!$inquiryFormLoaded)return!1;$store=e(this).data("store"),$product=e(this).data("product"),e.colorbox({inline:!0,href:"#enquiry_form_wrapper",width:$popup_width,onComplete:function(){e("#wcfm_enquiry_form").find("#enquiry_vendor_id").val($store),e("#wcfm_enquiry_form").find("#enquiry_product_id").val($product),jQuery(".anr_captcha_field").length>0&&"undefined"!=typeof grecaptcha&&($wcfm_anr_loaded?grecaptcha.reset():wcfm_anr_onloadCallback(),$wcfm_anr_loaded=!0)}})})})});