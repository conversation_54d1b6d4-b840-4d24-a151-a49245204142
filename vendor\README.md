# Vendor - Multi-vendor Marketplace Plugin

Modern React tabanlı çoklu satıcı marketplace WordPress eklentisi. WooCommerce ve Tutor LMS ile tam entegrasyon.

## Özellikler

### 🏪 Temel Çoklu Satıcı Özellikleri
- Satıcı ekleme/çıkarma yönetimi
- Gelişmiş para çekme sistemi (PayPal, Banka Transferi)
- Kupon oluşturma ve yönetimi
- Ürün oluşturma ve yönetimi
- Detaylı analiz ve raporlama sistemi

### ⚛️ Modern React Tabanlı Dashboard
- Sol sidebar navigasyon
- Responsive tasarım
- Real-time veri güncellemeleri
- Modern kullanıcı arayüzü

### 📊 Dashboard Sekmeleri
1. **Dashboard** - Ana sayfa/genel bakış
2. **Ürünler** - Ürün yönetimi
3. **Siparişler** - Sipariş takibi
4. **Kuponlar** - Kupon yönetimi
5. **Analiz** - Satış analitiği
6. **Değerlendirmeler** - Müşteri yorumları
7. **Para İşlemleri** - Komisyon ve para çekme
8. **Kurslarım** - Tutor LMS entegrasyonu
9. **Duyuruları Yönet** - Kurs duyuruları
10. **Soru ve Cevaplar** - Q&A yönetimi
11. **Sınav Denemeleri** - Quiz sonuçları
12. **Öğrenciler** - Öğrenci listesi
13. **Ayarlar** - Mağaza ayarları

### 🔧 WordPress Admin Entegrasyonu
- **Dashboard:** Satıcı istatistikleri
- **Vendor:** Satıcı yönetimi
- **Para İşlemleri:** Withdrawal yönetimi
- **Ayarlar:** Eklenti konfigürasyonu

## Gereksinimler

- WordPress 5.0+
- PHP 7.4+
- WooCommerce 4.0+
- MySQL 5.6+

## Kurulum

1. **Eklenti Dosyalarını Yükleyin**
   ```bash
   # WordPress plugins dizinine kopyalayın
   cp -r vendor/ /path/to/wordpress/wp-content/plugins/
   ```

2. **WordPress Admin Panelinden Aktifleştirin**
   - Eklentiler > Yüklü Eklentiler
   - "Vendor" eklentisini aktifleştirin

3. **Veritabanı Tablolarını Oluşturun**
   - Eklenti aktifleştirildiğinde otomatik olarak oluşturulur

4. **Temel Ayarları Yapın**
   - Vendor > Ayarlar menüsünden gerekli konfigürasyonları yapın

## Konfigürasyon

### Genel Ayarlar
```php
// vendor/includes/admin/views/settings.php
$general_settings = array(
    'vendor_registration' => 'approval', // open, approval, closed
    'auto_approve_vendors' => 'no',
    'vendor_dashboard_page' => get_option('vendor_dashboard_page_id'),
    'vendor_store_url' => 'store'
);
```

### Komisyon Ayarları
```php
$commission_settings = array(
    'default_commission_rate' => 10.00,
    'commission_type' => 'percentage',
    'auto_approve_commissions' => 'yes'
);
```

### Para Çekme Ayarları
```php
$withdrawal_settings = array(
    'minimum_withdrawal' => 50.00,
    'withdrawal_schedule' => 'manual',
    'auto_withdrawal' => 'no'
);
```

## API Endpoints

### Vendor Dashboard API
```
GET /wp-json/vendor/v1/dashboard/stats
GET /wp-json/vendor/v1/vendor/profile
POST /wp-json/vendor/v1/vendor/profile
GET /wp-json/vendor/v1/products
POST /wp-json/vendor/v1/products
GET /wp-json/vendor/v1/orders
GET /wp-json/vendor/v1/commissions
GET /wp-json/vendor/v1/withdrawals
POST /wp-json/vendor/v1/withdrawals
```

## Veritabanı Şeması

### vendor_vendors
```sql
CREATE TABLE vendor_vendors (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    user_id bigint(20) NOT NULL,
    store_name varchar(255) NOT NULL,
    store_slug varchar(255) NOT NULL,
    commission_rate decimal(5,2) DEFAULT 10.00,
    status varchar(20) DEFAULT 'pending',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);
```

### vendor_commissions
```sql
CREATE TABLE vendor_commissions (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    vendor_id bigint(20) NOT NULL,
    order_id bigint(20) NOT NULL,
    commission_amount decimal(10,2) NOT NULL,
    status varchar(20) DEFAULT 'pending',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);
```

### vendor_withdrawals
```sql
CREATE TABLE vendor_withdrawals (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    vendor_id bigint(20) NOT NULL,
    amount decimal(10,2) NOT NULL,
    payment_method varchar(50) NOT NULL,
    status varchar(20) DEFAULT 'pending',
    requested_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);
```

## Hooks ve Filtreler

### Actions
```php
// Vendor oluşturulduğunda
do_action('vendor_created', $vendor_id, $user_id, $vendor_data);

// Komisyon oluşturulduğunda
do_action('vendor_commission_created', $commission_id, $vendor_id, $order_id);

// Para çekme işlemi tamamlandığında
do_action('vendor_withdrawal_completed', $withdrawal_id, $vendor_id);
```

### Filters
```php
// Komisyon miktarını değiştir
$commission = apply_filters('vendor_commission_amount', $commission, $vendor_id, $order_id);

// Para çekme ücretlerini değiştir
$charges = apply_filters('vendor_withdrawal_charges', $charges, $amount, $gateway);

// Payment gateway'leri ekle
$gateways = apply_filters('vendor_payment_gateways', $gateways);
```

## Özelleştirme

### Yeni Payment Gateway Ekleme
```php
class Custom_Gateway {
    public $id = 'custom_gateway';
    public $title = 'Custom Gateway';
    
    public function process_payment($withdrawal_id, $vendor_id, $amount, $details) {
        // Payment processing logic
        return array(
            'success' => true,
            'transaction_id' => 'txn_123',
            'message' => 'Payment processed'
        );
    }
}

// Gateway'i ekle
add_filter('vendor_payment_gateways', function($gateways) {
    $gateways['custom_gateway'] = new Custom_Gateway();
    return $gateways;
});
```

### Template Özelleştirme
```php
// Tema klasöründe vendor/dashboard.php oluşturun
// vendor/templates/dashboard.php dosyasını override eder
```

## Güvenlik

- Nonce verification tüm AJAX isteklerinde
- Capability checks tüm admin işlemlerinde
- SQL injection koruması prepared statements ile
- XSS koruması esc_* fonksiyonları ile
- CSRF koruması wp_nonce_field ile

## Performans Optimizasyonları

- Database query'leri optimize edilmiş
- AJAX istekleri cache'lenmiş
- CSS/JS dosyaları minified
- Lazy loading React bileşenlerinde
- Database indexleri optimize edilmiş

## Troubleshooting

### Yaygın Sorunlar

1. **Dashboard yüklenmiyor**
   - JavaScript console'da hata kontrolü yapın
   - REST API endpoint'lerinin erişilebilir olduğunu kontrol edin

2. **Komisyonlar hesaplanmıyor**
   - WooCommerce order status'larını kontrol edin
   - Vendor ID'nin ürünlere atandığını kontrol edin

3. **Para çekme işlemi başarısız**
   - Payment gateway ayarlarını kontrol edin
   - Minimum withdrawal miktarını kontrol edin

## Destek

- GitHub Issues: [Repository URL]
- Dokümantasyon: [Documentation URL]
- E-posta: <EMAIL>

## Lisans

GPL v2 or later

## Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit yapın (`git commit -m 'Add amazing feature'`)
4. Push yapın (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## Changelog

### 1.0.0
- İlk sürüm
- Temel vendor yönetimi
- React dashboard
- PayPal ve Bank Transfer entegrasyonu
- Tutor LMS entegrasyonu
- WooCommerce tam entegrasyonu
