.wcfmmp_sold_by_wrapper, .wcfm_vendor_badges {
	
}

.wcfmmp_sold_by_container_advanced {
	padding: 5px;
	color: #666;
	border-radius: 5px;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	
	border: 1px solid #dae2ed;
	margin-bottom: 20px;
	display: inline-block;
	width:auto;
	min-width:250px;
}

.wcfmmp_sold_by_container_advanced .wcfmmp_sold_by_label {color: #666;font-size:12px;margin-left:60px;font-style: italic;letter-spacing: 2px;text-transform: lowercase;}
.wcfmmp_sold_by_container_advanced .wcfmmp_sold_by_label .wcfm_vendor_badges {display:inline-block;margin-bottom:0px;}

.wcfmmp_sold_by_container_left {width: 45px;display:inline-block;vertical-align: top;margin:5px;}
.wcfmmp_sold_by_container_right{display:inline-block;}

.wcfmmp_sold_by_container_left img {width: 45px;border-radius:50%;display: inline-block !important;border:0px;}


.wcfmmp_sold_by_container_advanced .wcfmmp_sold_by_wrapper .wcfmmp_sold_by_store {color: #333;line-height:25px;font-size:20px;display:block;}
.wcfmmp_sold_by_container_advanced .wcfmmp_sold_by_wrapper .wcfmmp_sold_by_store a {color: #333 !important;}

.wcfmmp_sold_by_container_advanced .wcfmmp-store-rating, .wcfmmp_store_info .wcfmmp-store-rating, .wcfmmp_sold_by_container .wcfmmp-store-rating {
	overflow: hidden;
	position: relative;
	height: 1.618em;
	line-height: 1.618;
	font-size: 1em;
	width: 6em !important;
	font-family: 'Font Awesome 5 Free' !important;
	font-weight: 900;
}

.wcfmmp_sold_by_container_advanced .wcfmmp-store-rating::before, .wcfmmp_store_info .wcfmmp-store-rating::before, .wcfmmp_sold_by_container .wcfmmp-store-rating::before {
	content: "" "" "" "" "";
	opacity: .25;
	float: left;
	top: 0;
	left: 0;
	position: absolute;
	color: #adb5b6;
}

.wcfmmp_sold_by_container_advanced .wcfmmp-store-rating span, .wcfmmp_store_info .wcfmmp-store-rating span, .wcfmmp_sold_by_container .wcfmmp-store-rating span {
	overflow: hidden;
	float: left;
	top: 0;
	left: 0;
	position: absolute;
	padding-top: 1.5em;
}

.wcfmmp_sold_by_container_advanced .wcfmmp-store-rating span:before, .wcfmmp_store_info .wcfmmp-store-rating span:before, .wcfmmp_sold_by_container .wcfmmp-store-rating span:before {
	content: "" "" "" "" "";
	top: 0;
	position: absolute;
	left: 0;
	color:#FF912C;
}

.wcfmmp_store_info {
	margin: 20px auto;
}

.wcfmmp_store_info * {
	margin: 0 auto;
	display: block;
	text-align: center;
}

.wcfmmp_store_tab_info * {
	margin-left: 0px;
	text-align: left !important;
}

.wcfmmp_store_tab_info i {margin-right:8px;}

.wcfmmp_store_info_store_social ul{padding:0; margin: 10px 0px;}
.wcfmmp_store_info_store_social ul li{margin:0 4px; padding:0; display:inline-block; width:30px; height:30px; background:#fff; border-radius:50%; border:1px solid #ccc; text-align:center; position:relative;}                                     
.wcfmmp_store_info_store_social ul li a i{color:#646464; position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); -moz-transform:translate(-50%,-50%); -ms-transform:translate(-50%,-50%); -o-transform:translate(-50%,-50%); -webkit-transform:translate(-50%,-50%); font-size:16px;}
.wcfmmp_store_info_store_social ul li:hover{background:#17a2b8;}
.wcfmmp_store_info_store_social ul li:hover a i{color:#fff;}

.wcfmmp_store_info_store_location .wcfmmp-store-map {
	width: 100%; 
	border: 1px solid #DFDFDF; 
	margin-right: 10px;
}
.wcfmmp_store_tab_info .wcfmmp-store-map {
	width: 250px;
}
.wcfmmp_store_info_store_location .wcfmmp-store-map div[role="button"], .wcfmmp_store_info_store_location .wcfmmp-store-map div.gm-svpc {
	display: none !important;
}
.wcfmmp_store_info_store_location .wcfmmp-store-map div[draggable="false"] {
	width: 20px !important;
	height: 42px !important;
}
.wcfmmp_store_info_store_location .wcfmmp-store-map button {
	width: 20px !important;
	height: 20px !important;
}
.wcfmmp_store_info_store_location .wcfmmp-store-map button img {
	width: 12px !important;
	height: 12px !important;
	margin: 5px 4px 5px !important;
}

.wcfmmp_store_shipping_rules .wcfmmp-store-shipping-rule {
	color: #17a2b8;
	width: auto;
	min-width: 90px;
	font-weight: 600;
	display: inline-block;
}

.wcfmmp_store_hours .wcfmmp-store-hours-day {
	color: #17a2b8;
	width: auto;
	min-width: 90px;
	font-weight: 600;
	display: inline-block;
}

.wcfm_store_close_msg {
	width: 100%;
	padding: .5em;
	margin-bottom: 1em;
	background-color: #17a2b8;
	margin-left: 0;
	color: #fff;
	clear: both;
	border-left: .618em solid rgba(0,0,0,.15);
	border-right: .618em solid rgba(0,0,0,.15);
	text-align: center;
}

.wcfmmp_store_coupons .wcfmmp-store-coupon-single {
	margin: 10px 5px;
	border: 1px dashed #17a2b8;
	background-color: #d9f2f6;
	color: #222;
	padding: 10px;
	height: 48px;
  line-height: 30px;
  font-weight: 600;
}

.wcfmmp-store-recent-articles ul li {
	margin-bottom: 1em;
	line-height: 1.41575em;
}

.wcfmmp-store-recent-articles ul li::before {
  content: "\f15b";
  -webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	display: inline-block;
	font-style: normal;
	font-variant: normal;
	font-weight: normal;
	line-height: 1;
	vertical-align: -.125em;
	font-family: "Font Awesome 5 Free" !important;
	font-weight: 900;
	line-height: inherit;
	vertical-align: baseline;
	margin-right: 0.5407911001em;
	display: block;
	float: left;
	opacity: 0.35;
	margin-left: -1.618em;
	width: 1em;
}

.wcfmmp_shipment_processing_display {
	font-style: italic;
	color: #47525d;
	font-size: 15px;
}