<?php
/**
 * Admin Withdrawals View
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

$current_status = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
$current_vendor = isset($_GET['vendor_id']) ? intval($_GET['vendor_id']) : '';
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php _e('Withdrawals', 'vendor'); ?></h1>
    <hr class="wp-header-end">
    
    <!-- Status Filter -->
    <ul class="subsubsub">
        <li class="all">
            <a href="<?php echo admin_url('admin.php?page=vendor-withdrawals'); ?>" <?php echo empty($current_status) ? 'class="current"' : ''; ?>>
                <?php _e('All', 'vendor'); ?>
                <span class="count">(<?php echo vendor()->withdrawal->get_withdrawal_summary()->total_requests ?: 0; ?>)</span>
            </a> |
        </li>
        <li class="pending">
            <a href="<?php echo admin_url('admin.php?page=vendor-withdrawals&status=pending'); ?>" <?php echo $current_status === 'pending' ? 'class="current"' : ''; ?>>
                <?php _e('Pending', 'vendor'); ?>
                <span class="count">(<?php 
                    $pending_summary = vendor()->withdrawal->get_withdrawal_summary();
                    echo $pending_summary ? count(vendor()->withdrawal->get_all_withdrawals(array('status' => 'pending', 'limit' => -1))) : 0;
                ?>)</span>
            </a> |
        </li>
        <li class="processing">
            <a href="<?php echo admin_url('admin.php?page=vendor-withdrawals&status=processing'); ?>" <?php echo $current_status === 'processing' ? 'class="current"' : ''; ?>>
                <?php _e('Processing', 'vendor'); ?>
                <span class="count">(<?php 
                    echo count(vendor()->withdrawal->get_all_withdrawals(array('status' => 'processing', 'limit' => -1)));
                ?>)</span>
            </a> |
        </li>
        <li class="completed">
            <a href="<?php echo admin_url('admin.php?page=vendor-withdrawals&status=completed'); ?>" <?php echo $current_status === 'completed' ? 'class="current"' : ''; ?>>
                <?php _e('Completed', 'vendor'); ?>
                <span class="count">(<?php 
                    echo count(vendor()->withdrawal->get_all_withdrawals(array('status' => 'completed', 'limit' => -1)));
                ?>)</span>
            </a> |
        </li>
        <li class="cancelled">
            <a href="<?php echo admin_url('admin.php?page=vendor-withdrawals&status=cancelled'); ?>" <?php echo $current_status === 'cancelled' ? 'class="current"' : ''; ?>>
                <?php _e('Cancelled', 'vendor'); ?>
                <span class="count">(<?php 
                    echo count(vendor()->withdrawal->get_all_withdrawals(array('status' => 'cancelled', 'limit' => -1)));
                ?>)</span>
            </a>
        </li>
    </ul>
    
    <!-- Filters -->
    <div class="vendor-filters" style="margin: 20px 0;">
        <form method="get" action="">
            <input type="hidden" name="page" value="vendor-withdrawals">
            <?php if ($current_status) : ?>
                <input type="hidden" name="status" value="<?php echo esc_attr($current_status); ?>">
            <?php endif; ?>
            
            <select name="vendor_id" onchange="this.form.submit()">
                <option value=""><?php _e('All Vendors', 'vendor'); ?></option>
                <?php
                $vendors = vendor()->vendor_manager->get_vendors(array('limit' => -1));
                foreach ($vendors as $vendor_item) :
                ?>
                <option value="<?php echo esc_attr($vendor_item->id); ?>" <?php selected($current_vendor, $vendor_item->id); ?>>
                    <?php echo esc_html($vendor_item->store_name); ?>
                </option>
                <?php endforeach; ?>
            </select>
        </form>
    </div>
    
    <div class="vendor-withdrawals-table">
        <?php if ($withdrawals) : ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th scope="col" class="manage-column column-cb check-column">
                        <input type="checkbox" />
                    </th>
                    <th scope="col" class="manage-column column-vendor column-primary">
                        <?php _e('Vendor', 'vendor'); ?>
                    </th>
                    <th scope="col" class="manage-column column-amount">
                        <?php _e('Amount', 'vendor'); ?>
                    </th>
                    <th scope="col" class="manage-column column-charges">
                        <?php _e('Charges', 'vendor'); ?>
                    </th>
                    <th scope="col" class="manage-column column-net-amount">
                        <?php _e('Net Amount', 'vendor'); ?>
                    </th>
                    <th scope="col" class="manage-column column-method">
                        <?php _e('Method', 'vendor'); ?>
                    </th>
                    <th scope="col" class="manage-column column-status">
                        <?php _e('Status', 'vendor'); ?>
                    </th>
                    <th scope="col" class="manage-column column-date">
                        <?php _e('Date Requested', 'vendor'); ?>
                    </th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($withdrawals as $withdrawal) : ?>
                <tr>
                    <th scope="row" class="check-column">
                        <input type="checkbox" name="withdrawal[]" value="<?php echo esc_attr($withdrawal->id); ?>" />
                    </th>
                    <td class="column-vendor column-primary">
                        <strong>
                            <a href="<?php echo admin_url('admin.php?page=vendor-withdrawals&action=view&withdrawal_id=' . $withdrawal->id); ?>">
                                <?php echo esc_html($withdrawal->store_name); ?>
                            </a>
                        </strong>
                        <div class="row-actions">
                            <span class="view">
                                <a href="<?php echo admin_url('admin.php?page=vendor-withdrawals&action=view&withdrawal_id=' . $withdrawal->id); ?>">
                                    <?php _e('View', 'vendor'); ?>
                                </a>
                            </span>
                            <?php if ($withdrawal->status === 'pending') : ?>
                            | <span class="approve">
                                <a href="#" class="vendor-approve-withdrawal" data-withdrawal-id="<?php echo esc_attr($withdrawal->id); ?>">
                                    <?php _e('Approve', 'vendor'); ?>
                                </a>
                            </span>
                            | <span class="reject">
                                <a href="#" class="vendor-reject-withdrawal" data-withdrawal-id="<?php echo esc_attr($withdrawal->id); ?>">
                                    <?php _e('Reject', 'vendor'); ?>
                                </a>
                            </span>
                            <?php endif; ?>
                        </div>
                        <button type="button" class="toggle-row">
                            <span class="screen-reader-text"><?php _e('Show more details', 'vendor'); ?></span>
                        </button>
                    </td>
                    <td class="column-amount" data-colname="<?php _e('Amount', 'vendor'); ?>">
                        <?php echo wc_price($withdrawal->amount); ?>
                    </td>
                    <td class="column-charges" data-colname="<?php _e('Charges', 'vendor'); ?>">
                        <?php echo wc_price($withdrawal->charges); ?>
                    </td>
                    <td class="column-net-amount" data-colname="<?php _e('Net Amount', 'vendor'); ?>">
                        <strong><?php echo wc_price($withdrawal->net_amount); ?></strong>
                    </td>
                    <td class="column-method" data-colname="<?php _e('Method', 'vendor'); ?>">
                        <?php echo esc_html(ucwords(str_replace('_', ' ', $withdrawal->payment_method))); ?>
                    </td>
                    <td class="column-status" data-colname="<?php _e('Status', 'vendor'); ?>">
                        <span class="vendor-status vendor-status-<?php echo esc_attr($withdrawal->status); ?>">
                            <?php echo esc_html(ucfirst($withdrawal->status)); ?>
                        </span>
                    </td>
                    <td class="column-date" data-colname="<?php _e('Date Requested', 'vendor'); ?>">
                        <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($withdrawal->requested_at))); ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <!-- Pagination -->
        <?php
        $total_withdrawals = count(vendor()->withdrawal->get_all_withdrawals(array('status' => $current_status, 'vendor_id' => $current_vendor, 'limit' => -1)));
        $per_page = 20;
        $total_pages = ceil($total_withdrawals / $per_page);
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        
        if ($total_pages > 1) :
            $page_links = paginate_links(array(
                'base' => add_query_arg('paged', '%#%'),
                'format' => '',
                'prev_text' => __('&laquo;'),
                'next_text' => __('&raquo;'),
                'total' => $total_pages,
                'current' => $current_page
            ));
            
            if ($page_links) :
        ?>
        <div class="tablenav">
            <div class="tablenav-pages">
                <?php echo $page_links; ?>
            </div>
        </div>
        <?php 
            endif;
        endif; 
        ?>
        
        <?php else : ?>
        <div class="vendor-no-withdrawals">
            <p><?php _e('No withdrawal requests found.', 'vendor'); ?></p>
        </div>
        <?php endif; ?>
    </div>
</div>

<style>
.vendor-filters {
    background: #fff;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 20px;
}

.vendor-filters select {
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-right: 10px;
}

.vendor-no-withdrawals {
    background: #fff;
    border: 1px solid #ddd;
    padding: 20px;
    text-align: center;
    border-radius: 4px;
}

.vendor-approve-withdrawal,
.vendor-reject-withdrawal {
    color: #0073aa;
    text-decoration: none;
}

.vendor-approve-withdrawal:hover {
    color: #46b450;
}

.vendor-reject-withdrawal:hover {
    color: #dc3232;
}
</style>
