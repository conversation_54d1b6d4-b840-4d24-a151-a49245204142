.wcfm-store-page #main, .wcfm-store-page #primary, .wcfm-store-page .content-area {
	width: 100% !important;
	max-width: 100% !important;
	left: 0% !important;
	margin: 0px !important;
	padding: 0px !important;
  border: 0 !important;
  float: none !important;
}
body:not(.electro-v1).wcfm-store-page .content-area{flex: 0 0 100% !important}

#secondary, .sidebar-primary, .wcfm-store-page .sidebar-container, .wcfm-store-page .shop-page-title {
	display: none !important;
}

.wcfmfa{font-weight:300;}

.wcfm-store-page .row,.wcfm-store-page .vc_row{margin-left:auto;margin-right:auto;}

#wcfmmp-store {
 box-shadow: 0 0 4px 0 #ccc;
 height: auto !important;
}

#wcfmmp-store * {box-sizing: border-box;}

#wcfmmp-store section {
	display: block;
	margin: 0px !important;
}

#wcfmmp-store img {
   border:none;
	-ms-interpolation-mode: bicubic;
	outline:none;
	max-width:100%;
}

#wcfmmp-store .banner_img, #wcfmmp-store .banner_video, #wcfmmp-store .wcfm_slideshow_container {
	max-height: 315px;
	width: 100%;
	overflow:hidden;
}

#wcfmmp-store .banner_img {
  background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	height: 315px;
}

#wcfmmp-store .banner_img img{max-width:100%;width:auto;height:auto;margin:0 auto;display:none;}

#wcfmmp-store ul.product_list_widget li {
    list-style: none;
    padding: 10px 0 5px 75px;
    min-height: 80px;
    position: relative;
    overflow: hidden;
    vertical-align: top;
    line-height: 1.33;
}

#wcfmmp-store ul.product_list_widget li img {
	top: 10px;
	position: absolute;
	left: 0;
	width: 50px;
	height: 50px;
	margin-bottom: 5px;
	object-fit: cover;
	object-position: 50% 50%;
	max-width: 100% !important;
}

#wcfmmp-store ul.product_list_widget li a {
	display: block;
	margin-bottom: 5px;
	padding: 0;
	overflow: hidden;
	text-overflow: ellipsis;
	line-height: 1.3;
	flex: 1;
}

#wcfmmp-store ul.product_list_widget li .wcfm_vendor_badge img {
	position: relative;
	left: 0;
	top: 0;
	width: 20px;
	height: 20px;
	margin-bottom: 5px;
}

#wcfmmp-store .woocommerce-product-search button {display:none;}

.wcfmmp-store-recent-articles ul li {
	margin-bottom: 1em;
	line-height: 1.41575em;
}

.wcfmmp-store-recent-articles ul li::before {
  content: "\f15c";
  -webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	display: inline-block;
	font-style: normal;
	font-variant: normal;
	font-weight: normal;
	line-height: 1;
	vertical-align: -.125em;
	font-family: "Font Awesome 5 Free"!important;
	font-weight: 900;
	line-height: inherit;
	vertical-align: baseline;
	margin-right: 0.5407911001em;
	display: block;
	float: left;
	opacity: 0.35;
	margin-left: -1.618em;
	width: 1em;
}

#wcfmmp-store .wcfmmp_store_info {
	margin: 20px auto;
}

#wcfmmp-store .wcfmmp_store_info * {
	margin: 0 auto;
	display: block;
	text-align: center;
}

#wcfmmp-store .left_sidebar .wcfmmp_store_info .star-rating {
	float: none;
	display: block;
}

#wcfmmp-store .wcfmmp_store_info .star-rating {
	overflow: hidden;
	position: relative;
	height: 1.618em;
	line-height: 1.618;
	font-size: 1em;
	width: 5.3em;
	font-family: star;
	font-weight: 400;
}

#wcfmmp-store .wcfmmp_store_info .star-rating::before {
	opacity: .25;
	float: left;
	top: 0;
	left: 0;
	position: absolute;
}

#wcfmmp-store .wcfmmp_store_info .star-rating span {
	overflow: hidden;
	float: left;
	top: 0;
	left: 0;
	position: absolute;
	padding-top: 1.5em;
}

#wcfmmp-store .wcfmmp_store_info .star-rating span::before {
	top: 0;
	position: absolute;
	left: 0;
	color:#FF912C;
}

#wcfmmp-store blockquote, q {
	quotes:none;
}

#wcfmmp-store blockquote:before, #wcfmmp-store blockquote:after,
#wcfmmp-store q:before, #wcfmmp-store q:after {
	content:'';
	content:none;
}

#wcfmmp-store table {
	border-collapse: collapse;
	border-spacing: 0;
}

#wcfmmp-store caption,
#wcfmmp-store th,
#wcfmmp-store td {
	font-weight: normal;
	text-align: left;
}
#wcfmmp-store audio,
#wcfmmp-store canvas,
#wcfmmp-store video {
display: inline-block;
}
#wcfmmp-store audio:not([controls]) {
	display: none;
}
#wcfmmp-store del {
	color: #333;
}
#wcfmmp-store ins {
	background: #fff9c0;
	color: #000;
	text-decoration: none;
}
#wcfmmp-store hr {
	background-color: #ccc;
	border: 0;
	height: 1px;
	margin: 24px;
	margin-bottom: 1.714285714rem;
}
#wcfmmp-store sub,
#wcfmmp-store sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}
#wcfmmp-store sup {
	top: -0.5em;
}
#wcfmmp-store sub {
	bottom: -0.25em;
}
#wcfmmp-store small {
	font-size: smaller;
}


/* change colours to suit your needs */
#wcfmmp-store mark {
	background-color: #ff9;
	color: #000;
	font-style: italic;
	font-weight: bold;
}

#wcfmmp-store abbr[title], #wcfmmp-store dfn[title] {
	border-bottom: 1px dotted;
	cursor: help;
}

#wcfmmp-store ol, #wcfmmp-store ul, #wcfmmp-store li {
	list-style: none;
}

#wcfmmp-store ul {
	margin:0; 
	padding:0 0 12px 0; 
	list-style-type:none;
}

#wcfmmp-store .woocommerce .shop-toolbar.multiple ul.woocommerce-ordering {margin-right:45px;padding:0;}

#wcfmmp-store ul.page-numbers {
	padding: 0px;
}

#wcfmmp-store ul li{
	padding: 0 0 6px 19px;	
}

#wcfmmp-store p {
	padding:0 0 20px 0; line-height:18px;
	margin: 0px;
}

#wcfmmp-store p.woocommerce-info {
	padding: 15px 0px 20px 50px;
}

#wcfmmp-store div.store_info_parallal {
	display: inline-block;
}

#wcfmmp-store a {
	-webkit-transition: all 0.2s linear;
	-moz-transition: all 0.2s linear;
	-o-transition: all 0.2s linear;
	transition: all 0.2s linear;
	outline:none; 
	text-decoration:none;		
}

#wcfmmp-store outline {
	border: 0;
}
#wcfmmp-store .lft {
	float: left;
	margin: 0px;
}
#wcfmmp-store .rgt {
	float: right;
}
#wcfmmp-store .center{text-align:center;}

#wcfmmp-store .spacer{
	clear:both; 
	font-size:0; 
	line-height:0;
}
#wcfmmp-store .nomar{
	margin-right:0 !important;		
}
#wcfmmp-store .nopadd{
	padding-right:0 !important;	
}
#wcfmmp-store .alignleft {
	float: left;
	margin:0 10px 10px 0;
}
#wcfmmp-store .alignright {
	float: right;
	margin:0 0 10px 10px;
}
#wcfmmp-store .aligncenter {
	display: block;
	margin-left: auto;
	margin-right: auto;
	margin-bottom:10px;
}

#wcfmmp-store input[type="text"],
#wcfmmp-store input[type="email"]{
	border:1px solid #666;
	color:#000;
	padding:0 5px 0 5px;	
	margin:0 0 8px 0;
	background-color:#fff;
	height:22px;
	outline:none;
}
#wcfmmp-store textarea{
	border:1px solid #666;
	color:#000;
	padding:5px;	
	margin:0 0 8px 0;
	background-color:#fff;
	height:130px;
	outline:none;
}

#wcfmmp-store .error{
	border:1px solid red !important;	
}

#wcfmmp-store textarea {
	overflow: auto;
	vertical-align: top;
	resize:vertical;
}

#wcfmmp-store .header_wrapper {
	width: 100%;
	margin: 0 auto;
	
}

#wcfmmp-store .parallax {
	background-attachment: fixed;
	background-position: 50% 50%;
	background-repeat: no-repeat;
	background-size: cover;
}

#wcfmmp-store .box_sizing{box-sizing:border-box; -moz-box-sizing:border-box; -ms-box-sizing:border-box; -o-box-sizing:border-box; -webkit-box-sizing:border-box;}

#wcfmmp-store h1, #wcfmmp-store h2 {
	font-size: 1.618em;
	clear: both;
	font-weight: 500;
	margin: 0 0 .5407911001em;
	color: #525252;
}
#wcfmmp-store h1.wcfm_store_title{color: #ffffff;}
#wcfmmp-store .woocommerce ul.products li.product h2 {margin:0;font-size:14px;font-weight:400;}
#wcfmmp-store h2.woocommerce-loop-product__title {font-size: 1em;}
#wcfmmp-store .address h2{color: #fff;margin: .2em 0 .2em;}


#wcfmmp-store .banner_area{position:relative;}
#wcfmmp-store .banner_text{position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); -moz-transform:translate(-50%,-50%); -ms-transform:translate(-50%,-50%); -o-transform:translate(-50%,-50%); -webkit-transform:translate(-50%,-50%); text-align:center;}
#wcfmmp-store .banner_text h1{text-transform:uppercase; position:relative; font-size:64px; line-height:70px; color:#fff; padding:0 40px; margin-bottom:12px;}
#wcfmmp-store .banner_text h1:after, #wcfmmp-store .banner_text h1:before{position:absolute; content:" "; width:80px; height:1px; background:#fff; top:50%; transform:translateX(-50%);}
#wcfmmp-store .banner_text h1:after{left:0;}
#wcfmmp-store .banner_text h1:before{display:inline-block;right:-80px;margin:0;}

#wcfmmp-store .banner_text a{border:2px solid #fff; font-size:30px; line-height:58px; text-transform:uppercase; color:#fff; width:240px; height:58px; display:inline-block;}

#wcfmmp-store .video_text{position:absolute; bottom:0px;width:100%;text-align:center;}
#wcfmmp-store .video_text h1{text-transform:uppercase; position:relative; font-size:35px; line-height:40px; color:#fff; padding:0px; margin-bottom:10px;}

/* Slideshow container */
#wcfmmp-store .wcfm_slideshow_container {
  max-width: 100%;
  position: relative;
  margin: auto;
}

/* Hide the images by default */
#wcfmmp-store .wcfmSlides {
	display: none;
}

#wcfmmp-store .wcfm_slideshow_container .prev, .wcfm_slideshow_container .next {
  cursor: pointer;
  position: absolute;
  top: 50%;
  width: auto;
  margin-top: -22px;
  padding: 16px;
  color: white;
  font-weight: bold;
  font-size: 18px;
  transition: 0.6s ease;
  border-radius: 0 3px 3px 0;
}

#wcfmmp-store .wcfm_slideshow_container .next {
  right: 0;
  border-radius: 3px 0 0 3px;
}

#wcfmmp-store .wcfm_slideshow_container .prev:hover, .wcfm_slideshow_container .next:hover {
  background-color: rgba(0,0,0,0.8);
}

#wcfmmp-store .slider_text {
  padding: 8px 12px;
  position: absolute;
  bottom: 8px;
  width: 100%;
  text-align: center;
}
#wcfmmp-store .slider_text h1 {
	color: #ffffff;
  font-size: 35px;
  line-height:40px;
  position:relative;
  text-transform:uppercase;
}

#wcfmmp-store .numbertext {
  color: #f2f2f2;
  font-size: 12px;
  padding: 8px 12px;
  position: absolute;
  top: 0;
  display: none;
}

#wcfmmp-store .wcfm_slide_active {
  background-color: #717171;
}

/* Fading animation */
#wcfmmp-store .wcfm_slide_fade {
  -webkit-animation-name: fade;
  -webkit-animation-duration: 1s;
  animation-name: fade;
  animation-duration: 1s;
}

@-webkit-keyframes wcfm_slide_fade {
  from {opacity: .3} 
  to {opacity: 1}
}

@keyframes wcfm_slide_fade {
  from {opacity: .3} 
  to {opacity: 1}
}

#wcfmmp-store #wcfm_store_header{background:#2f2f2f; display:block;min-height:130px;}
                                            

#wcfmmp-store .header_left{position:relative; margin-left:5%; padding:15px 0;z-index:2}
#wcfmmp-store .logo_area{width:150px; height:150px; border-radius:50%; background:#fff; box-shadow:0 0 6px 0 #cccccc; position:absolute;top:-100px;}
#wcfmmp-store .logo_area a{position:initial!important}
#wcfmmp-store .logo_area a img{position:absolute; top:50%; left:50%; border-radius:50%; transform:translate(-50%,-50%); -moz-transform:translate(-50%,-50%); -ms-transform:translate(-50%,-50%); -webkit-transform:translate(-50%,-50%); -o-transform:translate(-50%,-50%); width:100%; height:100%;}
#wcfmmp-store .logo_area_after{top:44px;position:relative;text-align:center;left:35%}

#wcfm_store_header .wcfmmp-store-rating {
	overflow: hidden;
	position: relative;
	height: 1.618em;
	line-height: 1.618;
	font-size: 1em;
	width: 6em !important;
	font-family: 'Font Awesome 5 Free' !important;
	font-weight: 900;
}

#wcfm_store_header .wcfmmp-store-rating::before {
	content: "" "" "" "" "";
	opacity: .25;
	float: left;
	top: 0;
	left: 0;
	position: absolute;
	color: #adb5b6;
}

#wcfm_store_header .wcfmmp-store-rating span {
	overflow: hidden;
	float: left;
	top: 0;
	left: 0;
	position: absolute;
	padding-top: 1.5em;
}

#wcfm_store_header .wcfmmp-store-rating span:before {
	content: "" "" "" "" "";
	top: 0;
	position: absolute;
	left: 0;
	color:#FF912C;
}

#wcfmmp-store .address{padding-left:36px; position:absolute; top:15%; left:140px;width:600px;}
#wcfmmp-store .address i{font-size:20px;display:inline-block;vertical-align:top;line-height:20px;color:#979595;}
#wcfmmp-store .address span{font-size:15px; line-height:20px; color:#fff; margin-left:10px;display:inline-block;vertical-align: top; width:90%;}
#wcfmmp-store .address .header_store_name i{font-size:15px;line-height:18px;}
#wcfmmp-store .address .header_store_name span{font-size:13px; line-height:18px;}
#wcfmmp-store .address span a{vertical-align: top;}
#wcfmmp-store div.store_info_parallal span {width:auto;}
#wcfmmp-store .address a{color:#fff;}
#wcfmmp-store .address p{padding-bottom:10px;}
#wcfmmp-store .address p:nth-last-child(1){padding-bottom:0;}


#wcfmmp-store .header_right{text-align:center;position:relative} 
#wcfmmp-store .bd_icon_area{position: absolute;right:0;top:-70px;margin:25px 0 20px 0}                                    
#wcfmmp-store .bd_icon_area a{color:#fff;}
/*#wcfmmp-store .bd_icon{width:36px; height:36px; background:#fff; border-radius:50%; text-align:center; position:relative; display:inline-block; vertical-align:middle;}
#wcfmmp-store .bd_icon_box:hover .bd_icon{background:#17a2b8;}
#wcfmmp-store .bd_icon_box:hover i{color:#fff;}
#wcfmmp-store .bd_icon i{color:#646464; position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); -moz-transform:translate(-50%,-50%); -ms-transform:translate(-50%,-50%); -o-transform:translate(-50%,-50%); -webkit-transform:translate(-50%,-50%); font-size:16px;}*/
#wcfmmp-store .bd_icon{display:inline-block; vertical-align:middle;}
#wcfmmp-store .bd_icon i{color:#979595; font-size:28px;}
#wcfmmp-store .wcfmmp_store_mobile_badges_with_store_name{display:inline-block;margin-left:10px;}
#wcfmmp-store .wcfm_vendor_badges div.wcfm_vendor_badge{float: left;margin-top:5px;}
#wcfmmp-store .wcfmmp_store_mobile_badges{}
#wcfmmp-store .wcfmmp_store_mobile_badges .wcfm_vendor_badges div.wcfm_vendor_badge{float:none;}

#wcfmmp-store .bd_icon_box{margin-right:10px;margin-bottom:10px;}
#wcfmmp-store .bd_icon_box:nth-last-child(1){margin-right:0;}
#wcfmmp-store .bd_icon_box span{font-size:15px; line-height:20px; color:#fff; margin-left:10px;}
#wcfmmp-store .bd_icon_box .follow, #wcfmmp-store .bd_icon_box .wcfm_store_enquiry,#wcfmmp-store .bd_icon_box .wcfm_store_chatnow{min-width:50px;width:auto;padding: 0px 15px;height:30px;background:#fff;color:#17a2b8;border-radius:5px;display:inline-block;cursor:pointer}
#wcfmmp-store .bd_icon_box .follow i, #wcfmmp-store .bd_icon_box .wcfm_store_enquiry i, #wcfmmp-store .bd_icon_box .wcfm_store_chatnow i{margin-right:5px;}
#wcfmmp-store .bd_icon_box .follow:hover, #wcfmmp-store .bd_icon_box .wcfm_store_enquiry:hover, #wcfmmp-store .bd_icon_box .wcfm_store_chatnow:hover{background:#17a2b8;}
#wcfmmp-store .bd_icon_box .follow:hover span, #wcfmmp-store .bd_icon_box .follow:hover i, #wcfmmp-store .bd_icon_box .wcfm_store_enquiry:hover span, #wcfmmp-store .bd_icon_box .wcfm_store_enquiry:hover i, #wcfmmp-store .bd_icon_box .wcfm_store_chatnow:hover span, #wcfmmp-store .bd_icon_box .wcfm_store_chatnow:hover i{color:#fff;}
#wcfmmp-store .bd_icon_box .follow span, #wcfmmp-store .bd_icon_box .wcfm_store_enquiry span,#wcfmmp-store .bd_icon_box .wcfm_store_chatnow span{cursor:pointer;color:#17a2b8; margin-left:0; line-height:30px; display:inline-block; font-size:13px; position:relative; top:inherit; left:inherit; transform:translateX(0); -moz-transform:translateX(0); -ms-transform:translateX(0); -o-transform:translateX(0); -webkit-transform:translateX(0); opacity:1;}

#wcfmmp-store .social_area{width:235px; background:#212121; min-height:130px; position:relative;}
#wcfmmp-store .social_area ul{padding:0; text-align:center; position:absolute; top:50%; transform:translateY(-50%); width:100%;}
#wcfmmp-store .social_area ul li{margin:0 4px; padding:0; display:inline-block; width:30px; height:30px; background:#fff; border-radius:50%; text-align:center; position:relative;}                                     
#wcfmmp-store .social_area ul li a i{color:#646464; position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); -moz-transform:translate(-50%,-50%); -ms-transform:translate(-50%,-50%); -o-transform:translate(-50%,-50%); -webkit-transform:translate(-50%,-50%); font-size:16px;}
#wcfmmp-store .social_area ul li:hover{background:#17a2b8;}
#wcfmmp-store .social_area ul li:hover a i{color:#fff;}

.wcfmmp_store_info_store_social ul{padding:0; margin: 10px 0px;}
.wcfmmp_store_info_store_social ul li{margin:0 4px; padding:0; display:inline-block !important; width:30px; height:30px; background:#fff; border-radius:50%; border:1px solid #ccc; text-align:center; position:relative;}                                     
.wcfmmp_store_info_store_social ul li a i{color:#646464; position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); -moz-transform:translate(-50%,-50%); -ms-transform:translate(-50%,-50%); -o-transform:translate(-50%,-50%); -webkit-transform:translate(-50%,-50%); font-size:16px;}
.wcfmmp_store_info_store_social ul li:hover{background:#17a2b8;}
.wcfmmp_store_info_store_social ul li:hover a i{color:#fff;}

#wcfmmp-store .padding_section{padding:0 5%;}

#wcfmmp-store .left_sidebar{width:25%; padding:5% 3% 20% 3%; background:#efefef;border: 1px solid #efefef;}
#wcfmmp-store .right_side{width:75%;  padding:5% 5% 0 5%;}
#wcfmmp-store .right_side_full{width:100%;}

#wcfmmp-store .sidebar_heading{margin-bottom:35px;}
#wcfmmp-store .sidebar_heading h4{font-size:17px; line-height:21px; color:#525252; text-transform:uppercase;}

#wcfmmp-store .categories_list ul{}
#wcfmmp-store .categories_list ul li{background:url(../../images/list_arow.png) no-repeat 0 center; margin:15px 0; padding:0 0 0px 14px;}
#wcfmmp-store .categories_list ul li.parent_cat a{font-size:16px; line-height:20px;}
#wcfmmp-store .categories_list ul li.child_cat{margin-left: 10px;}
#wcfmmp-store .categories_list ul li.child2_cat{margin-left: 20px;}
#wcfmmp-store .categories_list ul li.child3_cat{margin-left: 30px;}
#wcfmmp-store .categories_list ul li.child4_cat{margin-left: 35px;}
#wcfmmp-store .categories_list ul li.child5_cat{margin-left: 40px;}
#wcfmmp-store .categories_list ul li.child6_cat{margin-left: 45px;}
#wcfmmp-store .categories_list ul li.child7_cat{margin-left: 50px;}
#wcfmmp-store .categories_list ul li.child8_cat{margin-left: 55px;}
#wcfmmp-store .categories_list ul li a{font-size:15px; line-height:20px; color:#9f9e9e;}
#wcfmmp-store .categories_list ul li a.active, #wcfmmp-store .categories_list ul li a:hover{color:#17a2b8!important;}

#wcfmmp-store .wcfmmp-store-map {
	width: 100%; 
	border: 1px solid #DFDFDF; 
	margin-right: 10px;
}
#wcfmmp-store .wcfmmp-store-map div[role="button"], #wcfmmp-store .wcfmmp-store-map div.gm-svpc {
	display: none !important;
}
#wcfmmp-store .wcfmmp-store-map div[draggable="false"] {
	width: 20px !important;
	height: 42px !important;
}
#wcfmmp-store .wcfmmp-store-map button {
	width: 20px !important;
	height: 20px !important;
}
#wcfmmp-store .wcfmmp-store-map button img {
	width: 12px !important;
	height: 12px !important;
	margin: 5px 4px 5px !important;
}

#wcfmmp-store .widget {
	padding: 0px;
	background: transparent;
	border: 0px;
	box-shadow: none;
}


/*Tab Area*/

#wcfmmp-store .tab_area .tab_links{padding:0; background:#fff; border-bottom: 1px solid #93a8b3; float:left; width:100%;margin-bottom:50px;}
#wcfmmp-store .tab_area .tab_links li{padding:0; float:left; border: 1px solid #93a8b3; border-bottom: 1px solid rgba(0, 0, 0, 0); position:relative;margin:0px;margin-right:2px;margin-bottom:-2px;}
#wcfmmp-store .tab_area .tab_links li:nth-last-child(1){}
#wcfmmp-store .tab_area .tab_links li:after{position:absolute; content:" "; width:100%; height:2px; background:#17a2b8; opacity:0; top:0; left:0; transition:all 0.2s linear; -moz-transition:all 0.2s linear; -ms-transition:all 0.2s linear; -o-transition:all 0.2s linear; -webkit-transition:all 0.2s linear;}
#wcfmmp-store .tab_area .tab_links li:hover:after{bottom:100%; opacity:1;}
#wcfmmp-store .tab_area .tab_links li.active{border-top:2px solid #17a2b8 !important;border-bottom: 1px solid rgba(0, 0, 0, 0) !important;background-color: #ffffff;}
#wcfmmp-store .tab_area .tab_links li a{font-size:12px; line-height:18px; letter-spacing: 0.3px; color:#93a8b3; padding:10px 19px 10px 19px; display:inline-block; text-transform:uppercase;}
#wcfmmp-store .tab_area .tab_links li:hover a, #wcfmmp-store .tab_area .tab_links li.active a{color:#17a2b8;}

/*Tab Area*/

#wcfmmp-store .product_area{margin:0px;}
#wcfmmp-store .product_box{float:left; margin:1%; text-align:center; width:31.3%;}
#wcfmmp-store .product_img img{display:block; width:100%;}
#wcfmmp-store .product_text{font-size:15px; line-height:20px; color:#9f9e9e; padding:35px 0 20px 0; transition:all 0.2s linear; -moz-transition:all 0.2s linear; -ms-transition:all 0.2s linear; -o-transition:all 0.2s linear; -webkit-transition:all 0.2s linear;}
#wcfmmp-store .product_box:hover .product_text{background:#17a2b8; color:#fff;}

#wcfmmp-store .product_area ul li{padding:0;}
#wcfmmp-store .product_area .products-wrapper ul li.product {padding:0 15px;margin-bottom: 15px;}
#wcfmmp-store .product_area .products-wrapper ul li.product img{margin-bottom:0px;}

#wcfmmp-store .products-wrapper .products-nav .catalog-ordering .orderby-order-container .order > li.desc, #wcfmmp-store .products-wrapper .products-nav .catalog-ordering .orderby-order-container .order > li.asc {padding: 8px 10px 6px;}

#wcfmmp-store .paginations{text-align:center; margin-top:30px;padding-bottom:70px;}
#wcfmmp-store .paginations ul{padding:0; margin:0 auto; width:217px;}
#wcfmmp-store .paginations ul li{padding:0; float:left; padding:0 3px;}

#wcfmmp-store .paginations ul li a, #wcfmmp-store .paginations ul li span {
	font-size:13px;
	color: #999999;
	background: #eeeeee;
	height:22px;
	line-height:22px;
	width:25px;
	display: block;
	text-align: center;
	padding: 0px;
}
#wcfmmp-store .paginations ul li:first-child a, #wcfmmp-store .paginations ul li:last-child a {
	background:none;
	margin-top:1px;
}
#wcfmmp-store .paginations ul li:first-child {
	background:none;
}
#wcfmmp-store .paginations ul li:last-child {
	background:none;
}
#wcfmmp-store .paginations ul li a.active, #wcfmmp-store .paginations ul li a:hover, #wcfmmp-store .paginations ul li span.current {
	color: #fff;
	background: #17a2b8;
}

#wcfmmp-store .reviews_area, #wcfmmp-store .policies_area, #wcfmmp-store .wcfm_store_description{background:#fff; box-shadow:0 0 4px 0 #ccc; padding:30px; box-sizing:border-box; -moz-box-sizing:border-box; -ms-box-sizing:border-box; -o-box-sizing:border-box; -webkit-box-sizing:border-box; margin:20px 0; display:inline-block; width:100%;}
#wcfmmp-store .reviews_heading, #wcfmmp-store .wcfm_policies_heading{text-transform:uppercase; font-size:17px; line-height:23px; color:#525252; border-bottom:1px solid #ededed; padding-bottom:22px; font-weight:bold; margin-bottom:25px;}
#wcfmmp-store .reviews_heading a{float:right; font-size:15px; color:#17a2b8; font-weight:normal; text-transform:none;}

#wcfmmp-store .add_review{}
#wcfmmp-store .woocommerce .reviews_area .add_review{display:block}
#wcfmmp-store .add_review input{width:100%; border:0; height:46px; padding:0; text-indent:36px; box-shadow:0 0 6px 0 #ccc; background:#fff url(../../images/write.jpg) no-repeat 10px center; margin-bottom:22px; color:#cfcece;}
#wcfmmp-store .add_review button{height:40px; background:#17a2b8; border:0; color:#fff;text-transform: none;}
#wcfmmp-store .add_review button:hover{background:#000;}

#wcfmmp-store .review_photo{width:70px; height:70px; border-radius:50%; overflow:hidden;}
#wcfmmp-store .review_photo img{}

#wcfmmp-store .rating_area{border-bottom:1px solid #ededed; margin-bottom:25px; padding-bottom:24px;}

#wcfmmp-store .review_text{width:88%;}
#wcfmmp-store .rating_box{margin:5px 0;}
#wcfmmp-store .rating_box span{font-size:13px; line-height:17px; color:#b5b5b5; margin-left:6px;}
#wcfmmp-store .rating_box i{font-size:15px;cursor:pointer;font-weight:900;}

/* Rating Star Widgets Style */
#wcfmmp-store .rating-stars ul {list-style-type:none;padding:0; -moz-user-select:none; -webkit-user-select:none;display:inline-block;}
#wcfmmp-store .rating-stars ul > li.star {display:inline-block;padding:0px;}
#wcfmmp-store .rating-stars ul > li.star > i.wcfmfa {font-size:15px;cursor:pointer;font-weight:900;}
#wcfmmp-store .rating-stars ul > li.star.hover > i.wcfmfa {color:#FFCC36;}
#wcfmmp-store .rating-stars ul > li.star.selected > i.wcfmfa {color:#FF912C;}
#wcfmmp-store .rating_box i.selected, .logo_area_after .wcfmmp-store-rating span:before {color:#FF912C;}

#wcfmmp-store .add_review_box textarea{height:100px; width:99%; border:0; padding:10px 0 0 10px; box-shadow:0 0 6px 0 #ccc; background:#fff; margin-bottom:22px; font-size:15px; color:#7b7b7b; font-family: 'Roboto', sans-serif;}
#wcfmmp-store .add_review_box.add_review button{float:right;}

#wcfmmp-store .famous_reviewers{margin-bottom:25px;}
#wcfmmp-store .famous_reviewers_picture:nth-last-child(3), #wcfmmp-store .famous_reviewers_picture:nth-last-child(2){
    margin-left: -15px;
}
#wcfmmp-store .famous_reviewers_picture{
    height: 36px;
    width: 36px;
	border: 1.5px solid #fff;
    border-radius: 100px;
	overflow:hidden;
}
#wcfmmp-store .m10{margin:10px 0 0 10px;}
#wcfmmp-store .reviews_count{font-size:15px; color:#7b7b7b;}
#wcfmmp-store .reviews_count a{color:#17a2b8;}
#wcfmmp-store .reviews_count a:hover{color:#000;}

#wcfmmp-store .rating_count{text-align:center;}
#wcfmmp-store .rating_number{background:#4e4e4e; width:80px; text-align:center; font-size:26px; color:#fff; padding:5px 0;}
#wcfmmp-store .rating_number sub{bottom:0;}

#wcfmmp-store .review_section{border-top:1px solid #ededed; margin-top:35px; padding-top:35px;}

#wcfmmp-store .rated{text-align:center; margin-top:20px;}
#wcfmmp-store .rated strong{text-transform:uppercase; font-size:13px; line-height:17px; color:#4e4e4e; display:block;}
#wcfmmp-store .user_rated{width:30px; height:25px; line-height:25px; background:#17a2b8; display:inline-block; color:#fff; margin-top:6px;}
                                                                                                                                     
#wcfmmp-store .user_review_sec{margin:15px 0 0 15px; width:85%;}
#wcfmmp-store .user_name{font-size:15px; line-height:20px; color:#525252; text-transform:uppercase; margin-bottom:15px;}

#wcfmmp-store .user_review_area span{font-size:13px; line-height:17px; color:#b5b5b5;}
#wcfmmp-store .user_date{margin-left:50px;}

#wcfmmp-store .user_review_text{margin-top:35px;}
#wcfmmp-store .user_review_text p{font-size:15px; line-height:25px; color:#7b7b7b;}

#wcfmmp-store .reply_bt button{width:70px; height:30px; border:0; background:#e5e5e5; font-size:13px; color:#4e4e4e; padding:0px;}
#wcfmmp-store .reply_bt button:hover{background:#17a2b8; color:#fff;}

#wcfmmp-store .wcfm_vednor_follower {
	border: 1px solid #ccc;
	border-radius: 5px;
	-moz-border-radius: 5px;
	padding: 20px;
	text-align: center;
}

#wcfmmp-store .wcfm_vednor_follower img {
  height: 175px;
  display:inline;
}

.enquiry_form_wrapper_hide {
	display: none;
}

#wcfm_enquiry_form label {
	display: block;
	margin-bottom: 0.4em;
}

#wcfm_enquiry_form .comment-form-author, #wcfm_enquiry_form .comment-form-email {
  width: 44%;
}

.wcfmmp_store_hours .wcfmmp-store-hours-day {
	color: #17a2b8;
	width: auto;
	min-width: 90px;
	font-weight: 600;
	display: inline-block;
}

.wcfmmp_store_coupons .wcfmmp-store-coupon-single {
	margin: 10px 5px;
	border: 1px dashed #17a2b8;
	background-color: #d9f2f6;
	color: #222;
	padding: 10px;
  font-weight: 600;
  display: inline-block;
}