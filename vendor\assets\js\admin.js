/**
 * Vendor Admin JavaScript
 */

jQuery(document).ready(function($) {
    
    // Vendor approval/rejection
    $('.vendor-approve-vendor').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm(vendorAdmin.strings.confirm_approve)) {
            return;
        }
        
        var vendorId = $(this).data('vendor-id');
        var $row = $(this).closest('tr');
        
        $.ajax({
            url: vendorAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'vendor_approve_vendor',
                vendor_id: vendorId,
                nonce: vendorAdmin.nonce
            },
            beforeSend: function() {
                $row.addClass('vendor-processing');
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || vendorAdmin.strings.error);
                }
            },
            error: function() {
                alert(vendorAdmin.strings.error);
            },
            complete: function() {
                $row.removeClass('vendor-processing');
            }
        });
    });
    
    $('.vendor-reject-vendor').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm(vendorAdmin.strings.confirm_reject)) {
            return;
        }
        
        var vendorId = $(this).data('vendor-id');
        var $row = $(this).closest('tr');
        
        $.ajax({
            url: vendorAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'vendor_reject_vendor',
                vendor_id: vendorId,
                nonce: vendorAdmin.nonce
            },
            beforeSend: function() {
                $row.addClass('vendor-processing');
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || vendorAdmin.strings.error);
                }
            },
            error: function() {
                alert(vendorAdmin.strings.error);
            },
            complete: function() {
                $row.removeClass('vendor-processing');
            }
        });
    });
    
    // Withdrawal approval/rejection
    $('.vendor-approve-withdrawal').on('click', function(e) {
        e.preventDefault();
        
        var withdrawalId = $(this).data('withdrawal-id');
        var note = prompt('Add a note (optional):');
        
        if (note === null) {
            return; // User cancelled
        }
        
        var $row = $(this).closest('tr');
        
        $.ajax({
            url: vendorAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'vendor_approve_withdrawal',
                withdrawal_id: withdrawalId,
                note: note,
                nonce: vendorAdmin.nonce
            },
            beforeSend: function() {
                $row.addClass('vendor-processing');
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || vendorAdmin.strings.error);
                }
            },
            error: function() {
                alert(vendorAdmin.strings.error);
            },
            complete: function() {
                $row.removeClass('vendor-processing');
            }
        });
    });
    
    $('.vendor-reject-withdrawal').on('click', function(e) {
        e.preventDefault();
        
        var withdrawalId = $(this).data('withdrawal-id');
        var note = prompt('Reason for rejection:');
        
        if (!note) {
            alert('Please provide a reason for rejection.');
            return;
        }
        
        var $row = $(this).closest('tr');
        
        $.ajax({
            url: vendorAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'vendor_reject_withdrawal',
                withdrawal_id: withdrawalId,
                note: note,
                nonce: vendorAdmin.nonce
            },
            beforeSend: function() {
                $row.addClass('vendor-processing');
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || vendorAdmin.strings.error);
                }
            },
            error: function() {
                alert(vendorAdmin.strings.error);
            },
            complete: function() {
                $row.removeClass('vendor-processing');
            }
        });
    });
    
    // Settings tabs
    $('.vendor-settings-tabs .nav-tab').on('click', function(e) {
        e.preventDefault();
        
        var tab = $(this).data('tab');
        
        // Update active tab
        $('.vendor-settings-tabs .nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');
        
        // Show/hide tab content
        $('.vendor-settings-content .tab-content').hide();
        $('.vendor-settings-content .tab-content[data-tab="' + tab + '"]').show();
        
        // Update URL
        if (history.pushState) {
            var newUrl = window.location.href.split('&tab=')[0] + '&tab=' + tab;
            history.pushState(null, null, newUrl);
        }
    });
    
    // Initialize active tab from URL
    var urlParams = new URLSearchParams(window.location.search);
    var activeTab = urlParams.get('tab') || 'general';
    $('.vendor-settings-tabs .nav-tab[data-tab="' + activeTab + '"]').click();
    
    // Commission rate validation
    $('input[name*="commission_rate"]').on('input', function() {
        var value = parseFloat($(this).val());
        if (value < 0 || value > 100) {
            $(this).addClass('error');
            $(this).next('.description').remove();
            $(this).after('<p class="description error">Commission rate must be between 0 and 100.</p>');
        } else {
            $(this).removeClass('error');
            $(this).next('.description.error').remove();
        }
    });
    
    // Bulk actions
    $('#doaction, #doaction2').on('click', function(e) {
        var action = $(this).prev('select').val();
        var checked = $('input[name="vendor[]"]:checked, input[name="withdrawal[]"]:checked');
        
        if (action === '-1') {
            return;
        }
        
        if (checked.length === 0) {
            e.preventDefault();
            alert('Please select items to perform bulk action.');
            return;
        }
        
        var confirmMessage = '';
        switch (action) {
            case 'approve':
                confirmMessage = 'Are you sure you want to approve selected items?';
                break;
            case 'reject':
                confirmMessage = 'Are you sure you want to reject selected items?';
                break;
            case 'delete':
                confirmMessage = 'Are you sure you want to delete selected items? This action cannot be undone.';
                break;
        }
        
        if (confirmMessage && !confirm(confirmMessage)) {
            e.preventDefault();
        }
    });
    
    // Select all checkbox
    $('#cb-select-all-1, #cb-select-all-2').on('change', function() {
        var checked = $(this).prop('checked');
        $('input[name="vendor[]"], input[name="withdrawal[]"]').prop('checked', checked);
    });
    
    // Individual checkbox change
    $('input[name="vendor[]"], input[name="withdrawal[]"]').on('change', function() {
        var total = $('input[name="vendor[]"], input[name="withdrawal[]"]').length;
        var checked = $('input[name="vendor[]"]:checked, input[name="withdrawal[]"]:checked').length;
        
        $('#cb-select-all-1, #cb-select-all-2').prop('checked', total === checked);
    });
    
    // Processing state styles
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .vendor-processing {
                opacity: 0.6;
                pointer-events: none;
            }
            .vendor-processing::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 20px;
                height: 20px;
                margin: -10px 0 0 -10px;
                border: 2px solid #f3f3f3;
                border-top: 2px solid #0073aa;
                border-radius: 50%;
                animation: vendor-admin-spin 1s linear infinite;
            }
            @keyframes vendor-admin-spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .description.error {
                color: #dc3232;
                font-weight: bold;
            }
            input.error {
                border-color: #dc3232;
                box-shadow: 0 0 2px rgba(220, 50, 50, 0.8);
            }
        `)
        .appendTo('head');
    
    // Dashboard stats refresh
    $('.vendor-stats-refresh').on('click', function(e) {
        e.preventDefault();
        location.reload();
    });
    
    // Auto-refresh dashboard stats every 5 minutes
    if ($('.vendor-dashboard-stats').length > 0) {
        setInterval(function() {
            $('.vendor-stats-refresh').click();
        }, 300000); // 5 minutes
    }
    
    // Tooltips for help text
    $('[data-tooltip]').each(function() {
        $(this).attr('title', $(this).data('tooltip'));
    });
    
    // Form validation
    $('form').on('submit', function(e) {
        var hasErrors = false;
        
        // Check required fields
        $(this).find('input[required], select[required], textarea[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('error');
                hasErrors = true;
            } else {
                $(this).removeClass('error');
            }
        });
        
        // Check email fields
        $(this).find('input[type="email"]').each(function() {
            var email = $(this).val();
            if (email && !isValidEmail(email)) {
                $(this).addClass('error');
                hasErrors = true;
            } else {
                $(this).removeClass('error');
            }
        });
        
        if (hasErrors) {
            e.preventDefault();
            alert('Please fix the errors in the form before submitting.');
        }
    });
    
    // Helper function to validate email
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
});
