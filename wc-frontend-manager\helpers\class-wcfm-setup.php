<?php
/**
 * WCFM Dashboard Setup Class
 * 
 * @since 3.1.3
 * @package wcfm/helpers
 * <AUTHOR> Lovers
 */
if (!defined('ABSPATH')) {
    exit;
}

class WCFM_Dashboard_Setup {

	/** @var string Currenct Step */
	private $step = '';

	/** @var array Steps for the setup wizard */
	private $steps = array();

	public function __construct() {
		add_action( 'admin_menu', array( $this, 'wcfm_admin_menus' ) );
		add_action( 'admin_init', array( $this, 'wcfm_dashboard_setup' ) );
	}

	/**
	 * Add admin menus/screens.
	 */
	public function wcfm_admin_menus() {
		add_dashboard_page( '', '', 'manage_options', 'wcfm-setup', '' );
	}

	/**
	 * Show the setup wizard.
	 */
	public function wcfm_dashboard_setup() {
		global $WCFM;
		if ( filter_input(INPUT_GET, 'page') != 'wcfm-setup') {
			return;
		}

		$current_user = is_user_logged_in() ? wp_get_current_user() : false;
		$allowed_roles = apply_filters( 'wcfm_setup_page_allowed_roles', ['administrator'] );
		$is_allowed = $current_user && array_intersect( $allowed_roles, $current_user->roles );

		if (!apply_filters( 'wcfm_allow_setup_page_access', $is_allowed )) {
			wp_die(
				__( "You don't have permission to access this page. Please contact the site administrator for assistance.", 'wc-frontend-manager' ),
				__( 'Access Denied', 'wc-frontend-manager' )
			);
		}

		if ( isset($_POST['wcfm_install_wcfmmp']) ) {
			$this->install_wcfmmp();
			exit();
		}
		if ( isset($_POST['wcfm_install_wcfmvm']) ) {
			$this->install_wcfm_registration();
			exit();
		}
		$default_steps = array(
				'introduction' => array(
					'name' => __('Introduction', 'wc-frontend-manager' ),
					'view' => array($this, 'wcfm_setup_introduction'),
					'handler' => '',
				),
				'dashboard' => array(
					'name' => __('Dashboard Setup', 'wc-frontend-manager'),
					'view' => array($this, 'wcfm_setup_dashboard'),
					'handler' => array($this, 'wcfm_setup_dashboard_save')
				),
				'marketplace' => array(
					'name' => __('Marketplace Setup', 'wc-frontend-manager'),
					'view' => array($this, 'wcfm_setup_marketplace'),
					'handler' => array($this, 'wcfm_setup_marketplace_save')
				),
				'commission' => array(
					'name' => __('Commission Setup', 'wc-frontend-manager'),
					'view' => array($this, 'wcfm_setup_commission'),
					'handler' => array($this, 'wcfm_setup_commission_save')
				),
				'withdrawal' => array(
					'name' => __('Withdrawal Setup', 'wc-frontend-manager'),
					'view' => array($this, 'wcfm_setup_withdrawal'),
					'handler' => array($this, 'wcfm_setup_withdrawal_save')
				),
				'registration' => array(
					'name' => __('Registration Setup', 'wc-frontend-manager'),
					'view' => array($this, 'wcfm_setup_registration'),
					'handler' => array($this, 'wcfm_setup_registration_save')
				),
				'style' => array(
					'name' => __('Style', 'wc-frontend-manager'),
					'view' => array($this, 'wcfm_setup_style'),
					'handler' => array($this, 'wcfm_setup_style_save')
				),
				'capability' => array(
					'name' => __('Capability', 'wc-frontend-manager'),
					'view' => array($this, 'wcfm_setup_capability'),
					'handler' => array($this, 'wcfm_setup_capability_save')
				),
				'next_steps' => array(
					'name' => __('Ready!', 'wc-frontend-manager'),
					'view' => array($this, 'wcfm_setup_ready'),
					'handler' => '',
				),
		);
		$is_marketplace = wcfm_is_marketplace();
		/*if( !$is_marketplace ) {
			unset( $default_steps['commission'] );
			unset( $default_steps['withdrawal'] );
			unset( $default_steps['registration'] );
			unset( $default_steps['capability'] );
		} elseif( $is_marketplace != 'wcfmmarketplace' ) {
			unset( $default_steps['commission'] );
			unset( $default_steps['withdrawal'] );
			unset( $default_steps['registration'] );
		}*/
		if( WCFM_Dependencies::wcfmvm_plugin_active_check()) {
			unset( $default_steps['registration'] );
		}
		
		$this->steps = apply_filters('wcfm_dashboard_setup_steps', $default_steps);
		$current_step = filter_input(INPUT_GET, 'step');
		$this->step = $current_step ? sanitize_key($current_step) : current(array_keys($this->steps));
		$suffix = defined('SCRIPT_DEBUG') && SCRIPT_DEBUG ? '' : '.min';
		wp_register_script('jquery-blockui', WC()->plugin_url() . '/assets/js/jquery-blockui/jquery.blockUI' . $suffix . '.js', array('jquery'), '2.70', true);
		wp_register_script('select2', WC()->plugin_url() . '/assets/js/select2/select2.full' . $suffix . '.js', array('jquery'), '4.0.3');
		wp_register_script( 'wc-enhanced-select', WC()->plugin_url() . '/assets/js/admin/wc-enhanced-select' . $suffix . '.js', array( 'jquery', 'selectWoo' ), WC_VERSION );
		wp_localize_script('wc-enhanced-select', 'wc_enhanced_select_params', array(
				'i18n_no_matches' => _x('No matches found', 'enhanced select', 'wc-frontend-manager'),
				'i18n_ajax_error' => _x('Loading failed', 'enhanced select', 'wc-frontend-manager'),
				'i18n_input_too_short_1' => _x('Please enter 1 or more characters', 'enhanced select', 'wc-frontend-manager'),
				'i18n_input_too_short_n' => _x('Please enter %qty% or more characters', 'enhanced select', 'wc-frontend-manager'),
				'i18n_input_too_long_1' => _x('Please delete 1 character', 'enhanced select', 'wc-frontend-manager'),
				'i18n_input_too_long_n' => _x('Please delete %qty% characters', 'enhanced select', 'wc-frontend-manager'),
				'i18n_selection_too_long_1' => _x('You can only select 1 item', 'enhanced select', 'wc-frontend-manager'),
				'i18n_selection_too_long_n' => _x('You can only select %qty% items', 'enhanced select', 'wc-frontend-manager'),
				'i18n_load_more' => _x('Loading more results&hellip;', 'enhanced select', 'wc-frontend-manager'),
				'i18n_searching' => _x('Searching&hellip;', 'enhanced select', 'wc-frontend-manager'),
				'ajax_url' => admin_url('admin-ajax.php'),
				'search_products_nonce' => wp_create_nonce('search-products'),
				'search_customers_nonce' => wp_create_nonce('search-customers'),
		));

		wp_enqueue_style( 'woocommerce_admin_styles', WC()->plugin_url() . '/assets/css/admin.css', array(), WC_VERSION);
		wp_enqueue_style( 'wc-setup', WC()->plugin_url() . '/assets/css/wc-setup.css', array('dashicons', 'install'), WC_VERSION);
		wp_enqueue_style( 'wcfm-setup', $WCFM->plugin_url . 'assets/css/setup/wcfm-style-dashboard-setup.css', array('wc-setup'), $WCFM->version );
		wp_register_script('wcfm-setup', $WCFM->plugin_url . 'assets/js/setup/wcfm-script-setup.js', array('jquery'), $WCFM->version);
		wp_register_script('wc-setup', WC()->plugin_url() . '/assets/js/admin/wc-setup' . $suffix . '.js', array('jquery', 'wc-enhanced-select', 'jquery-blockui', 'wp-util', 'jquery-tiptip'), WC_VERSION);
		wp_localize_script('wc-setup', 'wc_setup_params', array(
				'locale_info' => json_encode(include( WC()->plugin_path() . '/i18n/locale-info.php' )),
		));
		
		// Color Picker
		wp_enqueue_style( 'wp-color-picker' );
    wp_register_script( 'colorpicker_init', $WCFM->plugin_url . 'includes/libs/colorpicker/colorpicker.js', array( 'jquery', 'wp-color-picker' ), $WCFM->version );
		wp_register_script( 'iris', admin_url('js/iris.min.js'),array('jquery-ui-draggable', 'jquery-ui-slider', 'jquery-touch-punch') );
		wp_register_script( 'wp-color-picker', admin_url('js/color-picker.min.js'), array('iris') );
		
		// Checkbox OFF-ON
		$WCFM->library->load_checkbox_offon_lib();
		
		$colorpicker_l10n = array('clear' => __('Clear'), 'defaultString' => __('Default'), 'pick' => __('Select Color'));
		wp_localize_script( 'wp-color-picker', 'wpColorPickerL10n', $colorpicker_l10n );
		
		if (!empty($_POST['save_step']) && isset($this->steps[$this->step]['handler'])) {
				call_user_func($this->steps[$this->step]['handler'], $this);
		}

		ob_start();
		$this->dashboard_setup_header();
		$this->dashboard_setup_steps();
		$this->dashboard_setup_content();
		$this->dashboard_setup_footer();
		exit();
	}

	/**
	 * Get slug from path
	 * @param  string $key
	 * @return string
	 */
	private static function format_plugin_slug($key) {
			$slug = explode('/', $key);
			$slug = explode('.', end($slug));
			return $slug[0];
	}

	/**
	 * Get the URL for the next step's screen.
	 * @param string step   slug (default: current step)
	 * @return string       URL for next step if a next step exists.
	 *                      Admin URL if it's the last step.
	 *                      Empty string on failure.
	 * @since 2.7.7
	 */
	public function get_next_step_link($step = '') {
			if (!$step) {
					$step = $this->step;
			}

			$keys = array_keys($this->steps);
			if (end($keys) === $step) {
					return admin_url();
			}

			$step_index = array_search($step, $keys);
			if (false === $step_index) {
					return '';
			}

			return add_query_arg('step', $keys[$step_index + 1]);
	}

	/**
	 * Setup Wizard Header.
	 */
	public function dashboard_setup_header() {
		global $WCFM;
		$is_marketplace = wcfm_is_marketplace();
		
		set_current_screen();
		
		?>
		<!DOCTYPE html>
		<html <?php language_attributes(); ?>>
				<head>
						<meta name="viewport" content="width=device-width" />
						<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
						<title><?php esc_html_e('WCFM &rsaquo; Setup Wizard', 'wc-frontend-manager'); ?></title>
						<?php wp_print_scripts( 'wc-enhanced-select'); ?>
						<?php wp_print_scripts('wc-setup'); ?>
						<?php wp_print_scripts('wcfm-setup'); ?>
						<?php do_action('admin_print_styles'); ?>
						<?php do_action('admin_head'); ?>
						<style type="text/css">
								.wc-setup-steps {
										justify-content: center;
								}
						</style>
				</head>
				<body class="wc-setup wp-core-ui">
				   <?php if( $is_marketplace == 'wcfmmarketplace' ) { ?>
						 <h1 id="wc-logo"><a href="http://wclovers.com/"><img width="75" src="<?php echo apply_filters( 'wcfmmp_store_default_logo', esc_url($WCFM->plugin_url) . 'assets/images/wcfmmp-blue.png' ); ?>" alt="WCFM" /><span>WCFM Marketplace</span></a></h1>
					 <?php } else { ?> 
						 <h1 id="wc-logo"><a href="http://wclovers.com/"><img src="<?php echo esc_url($WCFM->plugin_url); ?>assets/images/wcfm-transparent.png" alt="WCFM" /><span>WC Frontend Manager</span></a></h1>
					<?php } ?>
						<?php
	}

	/**
	 * Output the steps.
	 */
	public function dashboard_setup_steps() {
			$ouput_steps = $this->steps;
			array_shift($ouput_steps);
			?>
			<ol class="wc-setup-steps">
					<?php foreach ($ouput_steps as $step_key => $step) : ?>
							<li class="<?php
							if ($step_key === $this->step) {
									echo 'active';
							} elseif (array_search($this->step, array_keys($this->steps)) > array_search($step_key, array_keys($this->steps))) {
									echo 'done';
							}
							?>"><?php echo esc_html($step['name']); ?></li>
			<?php endforeach; ?>
			</ol>
			<?php
	}

	/**
	 * Output the content for the current step.
	 */
	public function dashboard_setup_content() {
			echo '<div class="wc-setup-content">';
			call_user_func($this->steps[$this->step]['view'], $this);
			echo '</div>';
	}

	/**
	 * Introduction step.
	 */
	public function wcfm_setup_introduction() {
		$is_marketplace = wcfm_is_marketplace();
		?>
		<?php if( $is_marketplace && ( $is_marketplace  == 'wcfmmarketplace' ) ) { ?>
			<h1><?php esc_html_e("Welcome to WooCommerce Multi-vendor Marketplace!", 'wc-frontend-manager'); ?></h1>
			<p><?php _e('Thank you for choosing WCFM Marketplace! This quick setup wizard will help you to configure the basic settings and you will have your marketplace ready in no time.', 'wc-frontend-manager'); ?></p>
		<?php } else { ?>
			<h1><?php esc_html_e("Let's experience the best ever WC Frontend Dashboard!!", 'wc-frontend-manager'); ?></h1>
			<p><?php _e('Thank you for choosing WCFM! This quick setup wizard will help you to configure the basic settings and you will have your dashboard ready in no time. <strong>It’s completely optional as WCFM already auto-setup.</strong>', 'wc-frontend-manager'); ?></p>
		<?php } ?>
		<p><?php esc_html_e("If you don't want to go through the wizard right now, you can skip and return to the WordPress dashboard. Come back anytime if you change your mind!", 'wc-frontend-manager'); ?></p>
		<p class="wc-setup-actions step">
			<a href="<?php echo esc_url($this->get_next_step_link()); ?>" class="button-primary button button-large button-next"><?php esc_html_e("Let's go!", 'wc-frontend-manager'); ?></a>
			<a href="<?php echo esc_url(admin_url()); ?>" class="button button-large"><?php esc_html_e('Not right now', 'wc-frontend-manager'); ?></a>
		</p>
		<?php
	}

	/**
	 * Dashboard setup content
	 */
	public function wcfm_setup_dashboard() {
		global $WCFM;
		$wcfm_options = (array) get_option( 'wcfm_options' );
		$is_dashboard_full_view_disabled = isset( $wcfm_options['dashboard_full_view_disabled'] ) ? $wcfm_options['dashboard_full_view_disabled'] : 'no';
		$is_dashboard_theme_header_disabled = isset( $wcfm_options['dashboard_theme_header_disabled'] ) ? $wcfm_options['dashboard_theme_header_disabled'] : 'no';
		$is_slick_menu_disabled = isset( $wcfm_options['slick_menu_disabled'] ) ? $wcfm_options['slick_menu_disabled'] : 'no';
		$is_headpanel_disabled = isset( $wcfm_options['headpanel_disabled'] ) ? $wcfm_options['headpanel_disabled'] : 'no';
		$is_welcome_box_disabled = isset( $wcfm_options['welcome_box_disabled'] ) ? $wcfm_options['welcome_box_disabled'] : 'no';
		$is_checklist_view_disabled = isset( $wcfm_options['checklist_view_disabled'] ) ? $wcfm_options['checklist_view_disabled'] : 'no';
		$is_quick_access_disabled = isset( $wcfm_options['quick_access_disabled'] ) ? $wcfm_options['quick_access_disabled'] : 'yes';
		$is_responsive_float_menu_disabled = isset( $wcfm_options['responsive_float_menu_disabled'] ) ? $wcfm_options['responsive_float_menu_disabled'] : 'yes';
		$is_float_button_disabled = isset( $wcfm_options['float_button_disabled'] ) ? $wcfm_options['float_button_disabled'] : 'yes';
		?>
		<h1><?php esc_html_e('Dashboard setup', 'wc-frontend-manager'); ?></h1>
		<form method="post">
			<table class="form-table">
				<?php
				$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_settings_fields_style', array(
																																												"dashboard_full_view_disabled" => array('label' => __('WCFM Full View', 'wc-frontend-manager') , 'name' => 'dashboard_full_view_disabled','type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $is_dashboard_full_view_disabled),
																																												"dashboard_theme_header_disabled" => array('label' => __('Theme Header', 'wc-frontend-manager') , 'name' => 'dashboard_theme_header_disabled','type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $is_dashboard_theme_header_disabled),
																																												"slick_menu_disabled" => array('label' => __('WCFM Slick Menu', 'wc-frontend-manager') , 'name' => 'slick_menu_disabled','type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $is_slick_menu_disabled),
																																												"headpanel_disabled" => array('label' => __('WCFM Header Panel', 'wc-frontend-manager') , 'name' => 'headpanel_disabled','type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $is_headpanel_disabled),
																																												"welcome_box_disabled" => array('label' => __('Welcome Box', 'wc-frontend-manager') , 'name' => 'welcome_box_disabled','type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $is_welcome_box_disabled),
																																												"checklist_view_disabled" => array('label' => __('Category Checklist View', 'wc-frontend-manager') , 'name' => 'checklist_view_disabled','type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $is_checklist_view_disabled, 'hints' => __( 'Disable this to have Product Manager Category/Custom Taxonomy Selector - Flat View.', 'wc-frontend-manager' ) ),
																																												"quick_access_disabled" => array('label' => __('Quick Access', 'wc-frontend-manager') , 'name' => 'quick_access_disabled','type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $is_quick_access_disabled),
																																												//"responsive_float_menu_disabled" => array('label' => __('Disable Responsive Float Menu', 'wc-frontend-manager') , 'name' => 'responsive_float_menu_disabled','type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $is_responsive_float_menu_disabled),
																																												"float_button_disabled" => array('label' => __('Float Button', 'wc-frontend-manager') , 'name' => 'float_button_disabled','type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $is_float_button_disabled),
																																												) ) );
				?>
			</table>
			<p class="wc-setup-actions step">
				<input type="submit" class="button-primary button button-large button-next" value="<?php esc_attr_e('Continue', 'wc-frontend-manager'); ?>" name="save_step" />
				<a href="<?php echo esc_url($this->get_next_step_link()); ?>" class="button button-large button-next"><?php esc_html_e('Skip this step', 'wc-frontend-manager'); ?></a>
				<?php wp_nonce_field('wcfm-setup'); ?>
			</p>
		</form>
		<?php
	}
	
	/**
	 * Marketplace step.
	 */
	public function wcfm_setup_marketplace() {
		global $WCFM, $WCFMmp;
		$is_marketplace = wcfm_is_marketplace();
		$multivendor_plugins = array( 'dokan' => 'Dokan Mutivendor', 'wcmarketplace' => 'WC Marketplace', 'wcvendors' => 'WC Vendors', 'wcpvendors' => 'WC Product Vendors' );
		?>
		<?php if( !$is_marketplace || ( $is_marketplace != 'wcfmmarketplace' ) ) { ?>
			<style>
			  .wcfm-install-woocommerce {
						box-shadow: 0 1px 3px rgba(0,0,0,.13);
						padding: 24px 24px 0;
						margin: 0 0 20px;
						background: #fff;
						overflow: hidden;
						zoom: 1;
				}
				.wcfm-install-woocommerce .button-primary{
						font-size: 1.25em;
						padding: .5em 1em;
						line-height: 1em;
						margin-right: .5em;
						margin-bottom: 2px;
						height: auto;
				}
				.wcfm-install-woocommerce{
						font-family: sans-serif;
						text-align: center;    
				}
				.wcfm-install-woocommerce form .button-primary{
						color: #fff;
						background-color: #00798b;
						font-size: 16px;
						border: 1px solid #00798b;
						width: auto	;
						padding: 10px;
						margin: 25px auto;
						display: block;
						cursor: pointer;
				}
				.wcfm-install-woocommerce form .button-primary:hover{
						background-color: #000000;
				}
			</style>
			<div class="wcfm-install-woocommerce">
				<p><?php _e('Setup your multi-vendor marketplace in minutes!', 'wc-frontend-manager'); ?></p>
				<form method="post" action="" name="wcfm_install_wcfmmarketplace">
					<?php submit_button(__('Install & Setup Multi-vendor Module', 'wc-frontend-manager'), 'primary', 'wcfm_install_wcfmmp'); ?>
					<?php wp_nonce_field('wcfm-install-wcfmmp'); ?>
				</form>
			</div>
		<?php } else { ?>
			<h1><?php esc_html_e('Marketplace setup', 'wc-frontend-manager'); ?></h1>
			<form method="post">
				<table class="form-table">
					<?php
					$wcfm_marketplace_options = get_option( 'wcfm_marketplace_options', array() );
					$wcfmmp_marketplace_shipping_options = get_option( 'wcfm_shipping_options', array() );
          $wcfmmp_marketplace_shipping_weight_options = get_option( 'woocommerce_wcfmmp_product_shipping_by_weight_settings', array() );
					$wcfmmp_marketplace_shipping_enabled = ( !empty($wcfmmp_marketplace_shipping_options) && !empty($wcfmmp_marketplace_shipping_options['enable_store_shipping']) ) ? 'no' : 'no';
          $wcfmmp_marketplace_shipping_by_weight_enabled = ( !empty($wcfmmp_marketplace_shipping_weight_options) && !empty($wcfmmp_marketplace_shipping_weight_options['enabled']) ) ? 'no' : 'no';
				
					$wcfm_store_url = isset( $wcfm_marketplace_options['wcfm_store_url'] ) ? $wcfm_marketplace_options['wcfm_store_url'] : 'store';
					$vendor_sold_by = isset( $wcfm_marketplace_options['vendor_sold_by'] ) ? 'no' : 'no';
					$vendor_sold_by_template = isset( $wcfm_marketplace_options['vendor_sold_by_template'] ) ? $wcfm_marketplace_options['vendor_sold_by_template'] : 'advanced';
					$vendor_sold_by_position = isset( $wcfm_marketplace_options['vendor_sold_by_position'] ) ? $wcfm_marketplace_options['vendor_sold_by_position'] : 'bellow_atc';
					$store_name_position = isset( $wcfm_marketplace_options['store_name_position'] ) ? $wcfm_marketplace_options['store_name_position'] : 'on_header';
					$store_ppp           =  isset( $wcfm_marketplace_options['store_ppp'] ) ? $wcfm_marketplace_options['store_ppp'] : get_option('posts_per_page');
					$store_sidebar_pos   = isset( $wcfm_marketplace_options['store_sidebar_pos'] ) ? $wcfm_marketplace_options['store_sidebar_pos'] : 'left';
					$store_related_products   =  isset( $wcfm_marketplace_options['store_related_products'] ) ? $wcfm_marketplace_options['store_related_products'] : 'default';
					$store_sidebar       = isset( $wcfm_marketplace_options['store_sidebar'] ) ? 'no' : 'no';
					$order_sync          = isset( $wcfm_marketplace_options['order_sync'] ) ? 'no' : 'no';
					//$product_mulivendor  = isset( $wcfm_marketplace_options['product_mulivendor'] ) ? 'no' : 'no';
					$wcfm_google_map_api = isset( $wcfm_marketplace_options['wcfm_google_map_api'] ) ? $wcfm_marketplace_options['wcfm_google_map_api'] : '';
					
					$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_marketplace_settings_fields_store', array(
																																											"vendor_store_url" => array('label' => __('Store URL Base', 'wc-multivendor-marketplace') , 'type' => 'text', 'class' => 'wcfm-text wcfm_ele', 'label_class' => 'wcfm_title wcfm_ele', 'in_table' => 'yes', 'desc_class' => 'wcfm_page_options_desc', 'value' => $wcfm_store_url, 'desc' => sprintf( __( 'Define the seller store URL  (%s/[this-text]/[seller-name])', 'wc-multivendor-marketplace' ), get_site_url() )  ),
																																											"vendor_sold_by" => array('label' => __('Visible Sold By', 'wc-multivendor-marketplace'), 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_title', 'value' => 'yes', 'dfvalue' => $vendor_sold_by, 'desc_class' => 'wcfm_page_options_desc', 'desc' => __( 'Uncheck this to disable Sold By display for products.', 'wc-multivendor-marketplace' ) ),
																																											"vendor_sold_by_template" => array('label' => __('Sold By Template', 'wc-multivendor-marketplace'), 'type' => 'select', 'in_table' => 'yes', 'options' => array( 'simple' => __( 'Simple', 'wc-multivendor-marketplace' ), 'advanced' => __( 'Advanced', 'wc-multivendor-marketplace' ), 'tab' => __( 'As Tab', 'wc-multivendor-marketplace' ) ), 'class' => 'wcfm-select wcfm_ele', 'label_class' => 'wcfm_title', 'value' => $vendor_sold_by_template, 'desc_class' => 'wcfm_page_options_desc', 'desc' => __( 'Single product page Sold By template.', 'wc-multivendor-marketplace' ) ),
																																											"sold_by_template_simple" => array( 'label' => '&nbsp;', 'type' => 'html', 'in_table' => 'yes', 'wrapper_class' => 'vendor_sold_by_type vendor_sold_by_type_simple', 'label_class' => 'wcfm_title wcfm_ele', 'value' => '<img src="'.esc_url($WCFMmp->plugin_url).'assets/images/sold_by_simple.png" />', 'attributes' => array( 'style' => 'border: 1px dotted #ccc;margin-bottom:15px;' ) ),
																																											"sold_by_template_advanced" => array( 'label' => '&nbsp;', 'type' => 'html', 'in_table' => 'yes', 'wrapper_class' => 'vendor_sold_by_type vendor_sold_by_type_advanced', 'label_class' => 'wcfm_title wcfm_ele', 'value' => '<img src="'.esc_url($WCFMmp->plugin_url).'assets/images/sold_by_advanced.png" />', 'attributes' => array( 'style' => 'border: 1px dotted #ccc;margin-bottom:15px;' ) ),
																																											"sold_by_template_tab" => array( 'label' => '&nbsp;', 'type' => 'html', 'in_table' => 'yes', 'wrapper_class' => 'vendor_sold_by_type vendor_sold_by_type_tab', 'label_class' => 'wcfm_title wcfm_ele', 'value' => '<img src="'.esc_url($WCFMmp->plugin_url).'assets/images/sold_by_tab.png" />', 'attributes' => array( 'style' => 'border: 1px dotted #ccc;margin-bottom:15px;' ) ),
																																											"vendor_sold_by_position" => array( 'label' => __('Sold By Position', 'wc-multivendor-marketplace'), 'type' => 'select', 'in_table' => 'yes', 'options' => array( 'bellow_price' => __( 'Below Price', 'wc-multivendor-marketplace' ), 'bellow_sc' => __( 'Below Short Description', 'wc-multivendor-marketplace' ), 'bellow_atc' => __( 'Below Add to Cart', 'wc-multivendor-marketplace' ) ), 'class' => 'wcfm-select wcfm_ele', 'label_class' => 'wcfm_title', 'value' => $vendor_sold_by_position, 'desc_class' => 'wcfm_page_options_desc', 'desc' => __( 'Sold by display position at Single Product Page.', 'wc-multivendor-marketplace' ) ),
																																											"store_name_position" => array( 'label' => __('Store Name Position', 'wc-multivendor-marketplace'), 'type' => 'select', 'in_table' => 'yes', 'options' => array( 'on_banner' => __( 'On Banner', 'wc-multivendor-marketplace' ), 'on_header' => __( 'At Header', 'wc-multivendor-marketplace' ) ), 'class' => 'wcfm-select wcfm_ele', 'label_class' => 'wcfm_title', 'value' => $store_name_position, 'desc_class' => 'wcfm_page_options_desc', 'desc' => __( 'Store name position at Vendor Store Page.', 'wc-multivendor-marketplace' ) ),
																																											"store_sidebar" => array( 'label' => __('Store Sidebar', 'wc-multivendor-marketplace'), 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_title', 'value' => 'yes', 'dfvalue' => $store_sidebar, 'desc_class' => 'wcfm_page_options_desc', 'desc' => __( 'Uncheck this to disable vendor store sidebar.', 'wc-multivendor-marketplace' ) ),
																																											"store_sidebar_pos" => array( 'label' => __('Store Sidebar Position', 'wc-multivendor-marketplace'), 'type' => 'select', 'in_table' => 'yes', 'options' => array( 'left' => __( 'At Left', 'wc-multivendor-marketplace' ), 'right' => __( 'At Right', 'wc-multivendor-marketplace' ) ), 'class' => 'wcfm-select wcfm_ele', 'label_class' => 'wcfm_title', 'value' => $store_sidebar_pos ),
																																											"store_related_products" => array( 'label' => __('Store Related Products', 'wc-multivendor-marketplace'), 'type' => 'select', 'in_table' => 'yes', 'options' => array( 'default' => __( 'As per WC Default Rule', 'wc-multivendor-marketplace' ), 'store' => __( 'Only same Store Products', 'wc-multivendor-marketplace' ) ), 'class' => 'wcfm-select wcfm_ele', 'label_class' => 'wcfm_title', 'value' => $store_related_products, 'desc_class' => 'wcfm_page_options_desc', 'desc' => __( 'Single product page related products rule.', 'wc-frontend-manager' ) ),
																																											"store_ppp" => array( 'label' => __('Products per page', 'wc-multivendor-marketplace'), 'type' => 'number', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele', 'label_class' => 'wcfm_title', 'value' => $store_ppp, 'attributes' => array( 'min'=> 1, 'step' => 1 ), 'desc_class' => 'wcfm_page_options_desc', 'desc' => __( 'No of products at Store per Page.', 'wc-frontend-manager' ) ),
																																											//"order_sync" => array('label' => __('Order Sync', 'wc-multivendor-marketplace'), 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_title', 'value' => 'yes', 'dfvalue' => $order_sync, 'desc_class' => 'wcfm_page_options_desc', 'desc' => __( 'Enable this to sync WC main order status when vendors update their order status.', 'wc-multivendor-marketplace' ) ),
																																											//"product_mulivendor" => array('label' => __('Product Mulivendor', 'wc-multivendor-marketplace'), 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_title', 'value' => 'yes', 'dfvalue' => $product_mulivendor, 'desc_class' => 'wcfm_page_options_desc','desc' => __( 'Enable this to allow vendors to sell other vendor products, single product multiple seller.', 'wc-multivendor-marketplace' ) ),
																																											"enable_marketplace_shipping" => array('label' => __('Marketplace Shipping', 'wc-multivendor-marketplace'), 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_title', 'value' => 'yes', 'dfvalue' => $wcfmmp_marketplace_shipping_enabled, 'desc_class' => 'wcfm_page_options_desc', 'desc' => __( 'Enable this to allow your vendors to setup their own shipping by country.', 'wc-multivendor-marketplace' ) ),
                                                                                      "enable_marketplace_shipping_by_weight" => array('label' => __('Marketplace Shipping by Weight', 'wc-multivendor-marketplace'), 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_title', 'value' => 'yes', 'dfvalue' => $wcfmmp_marketplace_shipping_by_weight_enabled, 'desc_class' => 'wcfm_page_options_desc', 'desc' => __( 'Enable this to allow your vendors to setup their own shipping by weight.', 'wc-multivendor-marketplace' ) ),
																																											"wcfm_google_map_api" => array('label' => __('Google Map API Key', 'wc-multivendor-marketplace') , 'type' => 'text', 'class' => 'wcfm-text wcfm_ele', 'label_class' => 'wcfm_title wcfm_ele', 'in_table' => 'yes', 'desc_class' => 'wcfm_page_options_desc', 'value' => $wcfm_google_map_api, 'desc' => sprintf( __( '%sAPI Key%s is needed to display map on store page', 'wc-multivendor-marketplace' ), '<a target="_blank" href="https://developers.google.com/maps/documentation/javascript/">', '</a>' ) ),
																																											) ) );
					?>
				</table>
				<p class="wc-setup-actions step">
					<input type="submit" class="button-primary button button-large button-next" value="<?php esc_attr_e('Continue', 'wc-frontend-manager'); ?>" name="save_step" />
					<a href="<?php echo esc_url($this->get_next_step_link()); ?>" class="button button-large button-next"><?php esc_html_e('Skip this step', 'wc-frontend-manager'); ?></a>
					<?php wp_nonce_field('wcfm-setup'); ?>
				</p>
			</form>
		<?php } ?>
		<?php
	}
	
	/**
	 * Install wcfm marketplace if not exist
	 * @throws Exception
	 */
	public function install_wcfmmp() {
		check_admin_referer('wcfm-install-wcfmmp');
		include_once(ABSPATH . 'wp-admin/includes/file.php');
		include_once(ABSPATH . 'wp-admin/includes/plugin-install.php');
		include_once(ABSPATH . 'wp-admin/includes/class-wp-upgrader.php');
		include_once(ABSPATH . 'wp-admin/includes/plugin.php');

		WP_Filesystem();
		$skin = new Automatic_Upgrader_Skin;
		$upgrader = new WP_Upgrader($skin);
		$installed_plugins = array_map(array(__CLASS__, 'format_plugin_slug'), array_keys(get_plugins()));
		$plugin_slug = 'wc-multivendor-marketplace';
		$plugin = 'wc-multivendor-marketplace/wc-multivendor-marketplace.php';
		$installed = false;
		$activate = false;
		// See if the plugin is installed already
		if (in_array($plugin_slug, $installed_plugins)) {
			$installed = true;
			$activate = !is_plugin_active($plugin);
		}
		// Install this thing!
		if (!$installed) {
			// Suppress feedback
			ob_start();

			try {
				// Open the commented line after WordPress.org issue is resolved
				/*
				$plugin_information = plugins_api('plugin_information', array(
					'slug' => $plugin_slug,
					'fields' => array(
						'short_description' => false,
						'sections' => false,
						'requires' => false,
						'rating' => false,
						'ratings' => false,
						'downloaded' => false,
						'last_updated' => false,
						'added' => false,
						'tags' => false,
						'homepage' => false,
						'donate_link' => false,
						'author_profile' => false,
						'author' => false,
					),
				));

				if (is_wp_error($plugin_information)) {
					throw new Exception($plugin_information->get_error_message());
				}

				$package = $plugin_information->download_link;
				*/

				// Use direct link for downloading the plugin for now
				$package = 'https://downloads.wordpress.org/plugin/wc-multivendor-marketplace.zip';
				$download = $upgrader->download_package($package);

				if (is_wp_error($download)) {
					throw new Exception($download->get_error_message());
				}

				$working_dir = $upgrader->unpack_package($download, true);

				if (is_wp_error($working_dir)) {
					throw new Exception($working_dir->get_error_message());
				}

				$result = $upgrader->install_package(array(
					'source' => $working_dir,
					'destination' => WP_PLUGIN_DIR,
					'clear_destination' => false,
					'abort_if_destination_exists' => false,
					'clear_working' => true,
					'hook_extra' => array(
						'type' => 'plugin',
						'action' => 'install',
					),
				));

				if (is_wp_error($result)) {
					throw new Exception($result->get_error_message());
				}

				$activate = true;
			} catch (Exception $e) {
				printf(
					__('%1$s could not be installed (%2$s). <a href="%3$s">Please install it manually by clicking here.</a>', 'wc-frontend-manager'),
					'WCFM Marketplace',
					$e->getMessage(),
					esc_url(admin_url('plugin-install.php?tab=search&s=wc-multivendor-marketplace'))
				);
				exit();
			}

			// Discard feedback
			ob_end_clean();
		}

		wp_clean_plugins_cache();
		// Activate this thing
		if ($activate) {
			try {
				$result = activate_plugin($plugin);

				if (is_wp_error($result)) {
					throw new Exception($result->get_error_message());
				}
			} catch (Exception $e) {
				printf(
					__('%1$s was installed but could not be activated. <a href="%2$s">Please activate it manually by clicking here.</a>', 'wc-frontend-manager'),
					'WC Frontend Manager',
					admin_url('plugins.php')
				);
				exit();
			}
		}
		wp_safe_redirect(admin_url('index.php?page=wcfm-setup&step=marketplace'));
	}
	
	/**
	 * Registration step.
	 */
	public function wcfm_setup_registration() {
		global $WCFM;
		?>
		<style>
			.wcfm-install-woocommerce {
					box-shadow: 0 1px 3px rgba(0,0,0,.13);
					padding: 24px 24px 0;
					margin: 0 0 20px;
					background: #fff;
					overflow: hidden;
					zoom: 1;
			}
			.wcfm-install-woocommerce p.submit {
				 text-align:center;
			}
			.wcfm-install-woocommerce .button-primary{
					font-size: 1.25em;
					padding: .5em 1em;
					line-height: 1em;
					margin-right: .5em;
					margin-bottom: 2px;
					height: auto;
			}
			.wcfm-install-woocommerce{
					font-family: sans-serif;
					text-align: center;    
			}
			.wcfm-install-woocommerce form .button-primary{
					color: #fff;
					background-color: #00798b;
					font-size: 16px;
					border: 1px solid #00798b;
					width: 230px;
					padding: 10px;
					margin: 25px 0 20px;
					cursor: pointer;
			}
			.wcfm-install-woocommerce form .button-primary:hover{
					background-color: #000000;
			}
		</style>
		<div class="wcfm-install-woocommerce">
			<p><?php _e('Setup WCFM Maketplace vendor registration:', 'wc-frontend-manager'); ?></p>
			<form method="post" action="" name="wcfm_install_wcfmmembership">
				<?php submit_button(__('Setup Registration', 'wc-frontend-manager'), 'primary', 'wcfm_install_wcfmvm'); ?>
				<?php wp_nonce_field('wcfm-install-wcfmvm'); ?>
			</form>
		</div>
		<?php
	}
	
	/**
	 * Install wcfm marketplace if not exist
	 * @throws Exception
	 */
	public function install_wcfm_registration() {
		check_admin_referer('wcfm-install-wcfmvm');
		include_once( ABSPATH . 'wp-admin/includes/file.php' );
		include_once( ABSPATH . 'wp-admin/includes/plugin-install.php' );
		include_once( ABSPATH . 'wp-admin/includes/class-wp-upgrader.php' );
		include_once( ABSPATH . 'wp-admin/includes/plugin.php' );

		WP_Filesystem();
		$skin = new Automatic_Upgrader_Skin;
		$upgrader = new WP_Upgrader($skin);
		$installed_plugins = array_map(array(__CLASS__, 'format_plugin_slug'), array_keys(get_plugins()));
		$plugin_slug = 'wc-multivendor-membership';
		$plugin = 'wc-multivendor-membership/wc-multivendor-membership.php';
		$installed = false;
		$activate = false;
		// See if the plugin is installed already
		if (in_array($plugin_slug, $installed_plugins)) {
				$installed = true;
				$activate = !is_plugin_active($plugin);
		}
		// Install this thing!
		if (!$installed) {
			// Suppress feedback
			ob_start();
	
			try {
				$plugin_information = plugins_api('plugin_information', array(
						'slug' => $plugin_slug,
						'fields' => array(
								'short_description' => false,
								'sections' => false,
								'requires' => false,
								'rating' => false,
								'ratings' => false,
								'downloaded' => false,
								'last_updated' => false,
								'added' => false,
								'tags' => false,
								'homepage' => false,
								'donate_link' => false,
								'author_profile' => false,
								'author' => false,
						),
				));

				if (is_wp_error($plugin_information)) {
					throw new Exception($plugin_information->get_error_message());
				}

				$package = $plugin_information->download_link;
				$download = $upgrader->download_package($package);

				if (is_wp_error($download)) {
					throw new Exception($download->get_error_message());
				}

				$working_dir = $upgrader->unpack_package($download, true);

				if (is_wp_error($working_dir)) {
					throw new Exception($working_dir->get_error_message());
				}

				$result = $upgrader->install_package(array(
						'source' => $working_dir,
						'destination' => WP_PLUGIN_DIR,
						'clear_destination' => false,
						'abort_if_destination_exists' => false,
						'clear_working' => true,
						'hook_extra' => array(
								'type' => 'plugin',
								'action' => 'install',
						),
				));

				if (is_wp_error($result)) {
					throw new Exception($result->get_error_message());
				}

				$activate = true;
			} catch (Exception $e) {
				printf(
						__('%1$s could not be installed (%2$s). <a href="%3$s">Please install it manually by clicking here.</a>', 'wc-frontend-manager'), 'WCFM Membership', $e->getMessage(), esc_url(admin_url('plugin-install.php?tab=search&s=wc-multivendor-membership'))
				);
				exit();
			}

			// Discard feedback
			ob_end_clean();
		}

		wp_clean_plugins_cache();
		// Activate this thing
		if ($activate) {
			try {
				$result = activate_plugin($plugin);

				if (is_wp_error($result)) {
					throw new Exception($result->get_error_message());
				}
			} catch (Exception $e) {
				printf(
					__('%1$s was installed but could not be activated. <a href="%2$s">Please activate it manually by clicking here.</a>', 'wc-frontend-manager'), 'WCFM Membership', admin_url('plugins.php')
				);
				exit();
			}
		}
		wp_safe_redirect(admin_url('index.php?page=wcfm-setup&step=style'));
	}
	
	/**
	 * Commission setup content
	 */
	public function wcfm_setup_commission() {
		global $WCFM;
		$wcfm_commission_options = get_option( 'wcfm_commission_options', array() );
		
		$wcfm_commission_types = get_wcfm_marketplace_commission_types();
		unset( $wcfm_commission_types['by_sales'] );
		unset( $wcfm_commission_types['by_products'] );
		unset( $wcfm_commission_types['by_quantity'] );
		
		$vendor_commission_for         = isset( $wcfm_commission_options['commission_for'] ) ? $wcfm_commission_options['commission_for'] : 'vendor';
		$vendor_commission_mode        = isset( $wcfm_commission_options['commission_mode'] ) ? $wcfm_commission_options['commission_mode'] : 'percent';
		$vendor_commission_fixed       = isset( $wcfm_commission_options['commission_fixed'] ) ? $wcfm_commission_options['commission_fixed'] : '';
		$vendor_commission_percent     = isset( $wcfm_commission_options['commission_percent'] ) ? $wcfm_commission_options['commission_percent'] : '90';
		$vendor_get_shipping           = isset( $wcfm_commission_options['get_shipping'] ) ? 'no' : 'yes';
		$vendor_get_tax                = isset( $wcfm_commission_options['get_tax'] ) ? 'no' : 'yes';
		$vendor_coupon_deduct          = isset( $wcfm_commission_options['coupon_deduct'] ) ? 'no' : 'yes';
		$admin_coupon_deduct           = isset( $wcfm_commission_options['admin_coupon_deduct'] ) ? 'no' : 'no';
		
		$tax_enable                    = isset( $wcfm_commission_options['tax_enable'] ) ? 'no' : 'yes';
		$tax_name                      = isset( $wcfm_commission_options['tax_name'] ) ? $wcfm_commission_options['tax_name'] : '';
		$tax_percent                   = isset( $wcfm_commission_options['tax_percent'] ) ? $wcfm_commission_options['tax_percent'] : '';
		?>
		<h1><?php esc_html_e('Commission setup', 'wc-frontend-manager'); ?></h1>
		<form method="post">
			<table class="form-table">
				<?php
				$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_marketplace_settings_fields_commission', array(
																																									"vendor_commission_for" => array('label' => __('Commission For', 'wc-multivendor-marketplace'), 'type' => 'select', 'in_table' => 'yes', 'options' => array( 'vendor' => __( 'Vendor', 'wc-multivendor-marketplace' ), 'admin' => __( 'Admin', 'wc-multivendor-marketplace' ) ), 'class' => 'wcfm-select wcfm_ele', 'label_class' => 'wcfm_title wcfm_ele', 'value' => $vendor_commission_for ),
					                                                                        "vendor_commission_mode" => array('label' => __('Commission Mode', 'wc-multivendor-marketplace'), 'type' => 'select', 'in_table' => 'yes', 'options' => $wcfm_commission_types, 'class' => 'wcfm-select wcfm_ele', 'label_class' => 'wcfm_title wcfm_ele', 'value' => $vendor_commission_mode, 'desc' => __( 'You may setup more commission rules (By Sales Total and Product Price) from setting panel.', 'wc-frontend-manager' ) ),
					                                                                        "vendor_commission_percent" => array('label' => __('Commission Percent(%)', 'wc-multivendor-marketplace'), 'type' => 'number', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele commission_mode_field commission_mode_percent commission_mode_percent_fixed', 'label_class' => 'wcfm_title wcfm_ele commission_mode_field commission_mode_percent commission_mode_percent_fixed', 'value' => $vendor_commission_percent, 'attributes' => array( 'min' => '1', 'step' => '0.1') ),
					                                                                        "vendor_commission_fixed" => array('label' => __('Commission Fixed', 'wc-multivendor-marketplace') . '(' . get_woocommerce_currency_symbol() . ')', 'type' => 'number', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele commission_mode_field commission_mode_fixed commission_mode_percent_fixed', 'label_class' => 'wcfm_title wcfm_ele commission_mode_field commission_mode_fixed commission_mode_percent_fixed', 'value' => $vendor_commission_fixed, 'attributes' => array( 'min' => '1', 'step' => '0.1') ),
																																									"vendor_get_shipping" => array('label' => __('Shipping cost goes to vendor?', 'wc-multivendor-marketplace'), 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_title', 'value' => 'yes', 'dfvalue' => $vendor_get_shipping ),
																																									"vendor_get_tax" => array('label' => __('Tax goes to vendor?', 'wc-multivendor-marketplace'), 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_title', 'value' => 'yes', 'dfvalue' => $vendor_get_tax ),
																																									"vendor_coupon_deduct" => array('label' => __('Commission after consider Vendor Coupon?', 'wc-multivendor-marketplace'), 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_title', 'value' => 'yes', 'dfvalue' => $vendor_coupon_deduct, 'hints' => __( 'Generate vendor commission after deduct Vendor Coupon discounts.', 'wc-multivendor-marketplace' ) ),
																																									"admin_coupon_deduct" => array('label' => __('Commission after consider Admin Coupon?', 'wc-multivendor-marketplace'), 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox input-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_title', 'value' => 'yes', 'dfvalue' => $admin_coupon_deduct, 'hints' => __( 'Generate vendor commission after deduct Admin Coupon discounts.', 'wc-multivendor-marketplace' ) ),
																																									) ) );
				
				$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_marketplace_settings_fields_commission_tax', array(  
					                                                            'tax_fields_heading' => array( 'type' => 'html', 'in_table' => 'yes', 'value' => '<h2>' . __('Commission Tax Settings', 'wc-multivendor-marketplace') . '</h2><div class="wcfm_clearfix"></div>' ),
																																			'tax_enable' => array( 'label' => __( 'Enable', 'wc-multivendor-marketplace' ), 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_title', 'value' => 'yes', 'dfvalue' => $tax_enable, 'desc_class' => 'wcfm_page_options_desc', 'desc' => __( 'Enable this to deduct tax from vendor\'s commission.', 'wc-multivendor-marketplace' ) ),
																																			'tax_name' => array( 'label' => __( 'Tax Label', 'wc-multivendor-marketplace' ), 'placeholder' => __( 'Tax', 'wc-multivendor-marketplace' ), 'type' => 'text', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele', 'label_class' => 'wcfm_title', 'value' => $tax_name ),
																																			'tax_percent' => array( 'label' => __( 'Tax Percent (%)', 'wc-multivendor-marketplace' ), 'type' => 'number', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele wcfm_non_negative_input', 'label_class' => 'wcfm_title', 'value' => $tax_percent ),
																																			) ) );
				?>
			</table>
			<p class="wc-setup-actions step">
				<input type="submit" class="button-primary button button-large button-next" value="<?php esc_attr_e('Continue', 'wc-frontend-manager'); ?>" name="save_step" />
				<a href="<?php echo esc_url($this->get_next_step_link()); ?>" class="button button-large button-next"><?php esc_html_e('Skip this step', 'wc-frontend-manager'); ?></a>
				<?php wp_nonce_field('wcfm-setup'); ?>
			</p>
		</form>
		<?php
	}
	
	/**
	 * Withdrawal setup content
	 */
	public function wcfm_setup_withdrawal() {
		global $WCFM;
		
		$wcfm_withdrawal_options = get_option( 'wcfm_withdrawal_options', array() );
		
		$wcfm_marketplace_withdrwal_payment_methods = get_wcfm_marketplace_withdrwal_payment_methods();
		$wcfm_marketplace_withdrawal_order_status   = get_wcfm_marketplace_withdrwal_order_status();
		$wcfm_marketplace_disallow_order_payment_methods = get_wcfm_marketplace_disallow_order_payment_methods();
		
		$request_auto_approve     = isset( $wcfm_withdrawal_options['request_auto_approve'] ) ? $wcfm_withdrawal_options['request_auto_approve'] : 'no';
		$generate_auto_withdrawal = isset( $wcfm_withdrawal_options['generate_auto_withdrawal'] ) ? $wcfm_withdrawal_options['generate_auto_withdrawal'] : 'no';
		$auto_withdrawal_status   = isset( $wcfm_withdrawal_options['auto_withdrawal_status'] ) ? $wcfm_withdrawal_options['auto_withdrawal_status'] : 'wc-processing';
		$order_status             = isset( $wcfm_withdrawal_options['order_status'] ) ? $wcfm_withdrawal_options['order_status'] : array( 'wc-completed', 'wc-processing' );
		$withdrawal_schedule      = isset( $wcfm_withdrawal_options['withdrawal_schedule'] ) ? $wcfm_withdrawal_options['withdrawal_schedule'] : 'week';
		$withdrawal_limit         = isset( $wcfm_withdrawal_options['withdrawal_limit'] ) ? $wcfm_withdrawal_options['withdrawal_limit'] : '';
		$withdrawal_thresold      = isset( $wcfm_withdrawal_options['withdrawal_thresold'] ) ? $wcfm_withdrawal_options['withdrawal_thresold'] : '';
		
		if( isset( $wcfm_withdrawal_options['withdrawal_mode'] ) ) {
			$withdrawal_mode        = isset( $wcfm_withdrawal_options['withdrawal_mode'] ) ? $wcfm_withdrawal_options['withdrawal_mode'] : '';
		} elseif( $generate_auto_withdrawal == 'yes' ) {
			$withdrawal_mode        = 'by_order_status';
		} else {
			$withdrawal_mode        = 'by_manual';
		}
		
		$payment_methods = isset( $wcfm_withdrawal_options['payment_methods'] ) ? $wcfm_withdrawal_options['payment_methods'] : array( 'paypal', 'bank_transfer' );
		$withdrawal_test_mode = isset( $wcfm_withdrawal_options['test_mode'] ) ? 'yes' : 'no';
		
		$withdrawal_paypal_client_id          = isset( $wcfm_withdrawal_options['paypal_client_id'] ) ? $wcfm_withdrawal_options['paypal_client_id'] : '';
		$withdrawal_paypal_secret_key         = isset( $wcfm_withdrawal_options['paypal_secret_key'] ) ? $wcfm_withdrawal_options['paypal_secret_key'] : '';
		$withdrawal_stripe_client_id          = isset( $wcfm_withdrawal_options['stripe_client_id'] ) ? $wcfm_withdrawal_options['stripe_client_id'] : '';
		$withdrawal_stripe_published_key      = isset( $wcfm_withdrawal_options['stripe_published_key'] ) ? $wcfm_withdrawal_options['stripe_published_key'] : '';
		$withdrawal_stripe_secret_key         = isset( $wcfm_withdrawal_options['stripe_secret_key'] ) ? $wcfm_withdrawal_options['stripe_secret_key'] : '';
		
		$withdrawal_paypal_test_client_id     = isset( $wcfm_withdrawal_options['paypal_test_client_id'] ) ? $wcfm_withdrawal_options['paypal_test_client_id'] : '';
		$withdrawal_paypal_test_secret_key    = isset( $wcfm_withdrawal_options['paypal_test_secret_key'] ) ? $wcfm_withdrawal_options['paypal_test_secret_key'] : '';
		$withdrawal_stripe_test_client_id     = isset( $wcfm_withdrawal_options['stripe_test_client_id'] ) ? $wcfm_withdrawal_options['stripe_test_client_id'] : '';
		$withdrawal_stripe_test_published_key = isset( $wcfm_withdrawal_options['stripe_test_published_key'] ) ? $wcfm_withdrawal_options['stripe_test_published_key'] : '';
		$withdrawal_stripe_test_secret_key    = isset( $wcfm_withdrawal_options['stripe_test_secret_key'] ) ? $wcfm_withdrawal_options['stripe_test_secret_key'] : '';
		
		$withdrawal_stripe_is_3d_secure       = isset( $wcfm_withdrawal_options['stripe_3d_secure'] ) ? 'yes' : 'no';
		$withdrawal_stripe_split_pay_mode     = isset( $wcfm_withdrawal_options['stripe_split_pay_mode'] ) ? $wcfm_withdrawal_options['stripe_split_pay_mode'] : 'direct_charges';
		
		$withdrawal_charge_type = isset( $wcfm_withdrawal_options['withdrawal_charge_type'] ) ? $wcfm_withdrawal_options['withdrawal_charge_type'] : 'no';
		
		$withdrawal_charge               = isset( $wcfm_withdrawal_options['withdrawal_charge'] ) ? $wcfm_withdrawal_options['withdrawal_charge'] : array();
		$withdrawal_charge_paypal        = isset( $withdrawal_charge['paypal'] ) ? $withdrawal_charge['paypal'] : array();
		$withdrawal_charge_stripe        = isset( $withdrawal_charge['stripe'] ) ? $withdrawal_charge['stripe'] : array();
		$withdrawal_charge_skrill        = isset( $withdrawal_charge['skrill'] ) ? $withdrawal_charge['skrill'] : array();
		$withdrawal_charge_bank_transfer = isset( $withdrawal_charge['bank_transfer'] ) ? $withdrawal_charge['bank_transfer'] : array();
		
		$disallow_order_payment_methods = isset( $wcfm_withdrawal_options['disallow_order_payment_methods'] ) ? $wcfm_withdrawal_options['disallow_order_payment_methods'] : array();
		$withdrawal_reverse              = isset( $wcfm_withdrawal_options['withdrawal_reverse'] ) ? 'yes' : 'no';
		$withdrawal_reverse_limit        = isset( $wcfm_withdrawal_options['withdrawal_reverse_limit'] ) ? $wcfm_withdrawal_options['withdrawal_reverse_limit'] : '';
		?>
		<h1><?php esc_html_e('Withdrawal setup', 'wc-frontend-manager'); ?></h1>
		<form method="post">
			<div>
				<?php
				$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_marketplace_settings_fields_withdrawal', array(
					                                                                        "withdrawal_request_auto_approve" => array('label' => __('Request auto-approve?', 'wc-multivendor-marketplace'), 'type' => 'checkbox', 'name' => 'wcfm_withdrawal_options[request_auto_approve]', 'class' => 'wcfm-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_spl_title checkbox_title', 'value' => 'yes', 'dfvalue' => $request_auto_approve, 'desc_class' => 'instructions', 'desc' => __( 'Check this to automatically disburse payments to vendors on request, no admin approval required. Auto disbursement only works for auto-payment gateways, e.g. PayPal, Stripe etc. Bank Transfer or other non-autopay mode always requires approval, as these are manual transactions.', 'wc-multivendor-membership' ) ),
					                                                         ) ) );
				?>
				
				<table class="form-table">
				  <?php
					$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_marketplace_settings_fields_withdrawal', array(
																																										"withdrawal_mode"   => array( 'label' => __( 'Withdrawal Mode', 'wc-multivendor-marketplace' ), 'name' => 'wcfm_withdrawal_options[withdrawal_mode]', 'type' => 'select', 'in_table' => 'yes', 'class' => 'wcfm-select wcfm_ele withdrawal_mode', 'label_class' => 'wcfm_title withdrawal_mode', 'options' => apply_filters( 'wcfm_withdrawal_modes', array( 'by_manual' => __( 'Manual Withdrawal', 'wc-multivendor-marketplace' ), 'by_schedule' => __( 'Periodic Withdrawal', 'wc-multivendor-marketplace' ), 'by_order_status' => __( 'By Order Status', 'wc-multivendor-marketplace' ) ) ), 'value' => $withdrawal_mode  ),
																																											
																																										"withdrawal_auto_withdrawal_status"   => array( 'label' => __( 'Order Status', 'wc-multivendor-marketplace' ), 'name' => 'wcfm_withdrawal_options[auto_withdrawal_status]', 'type' => 'select', 'in_table' => 'yes', 'wrapper_class' => 'auto_withdrawal_order_status', 'class' => 'wcfm-select wcfm_ele auto_withdrawal_order_status', 'label_class' => 'wcfm_title auto_withdrawal_order_status', 'options' => $wcfm_marketplace_withdrawal_order_status, 'value' => $auto_withdrawal_status, 'desc_class' => 'wcfm_page_options_desc auto_withdrawal_order_status', 'desc' => __( 'Order status for generate withdrawal request automatically.', 'wc-multivendor-marketplace' ),  ),
																																										
																																										"withdrawal_schedule"   => array( 'label' => __( 'Schedule', 'wc-multivendor-marketplace' ), 'name' => 'wcfm_withdrawal_options[withdrawal_schedule]', 'type' => 'select', 'in_table' => 'yes', 'wrapper_class' => 'schedule_withdrawal_threshold_ele', 'class' => 'wcfm-select wcfm_ele schedule_withdrawal_threshold_ele', 'label_class' => 'wcfm_title schedule_withdrawal_threshold_ele', 'options' => apply_filters( 'wcfm_withdrawal_schedule_periods', array( 'week' => __( 'Every 7 Days', 'wc-multivendor-marketplace' ), '15days' => __( 'Every 15 Days', 'wc-multivendor-marketplace' ), 'month' => __( 'Every 30 Days', 'wc-multivendor-marketplace' ), '2months' => __( 'Every 60 Days', 'wc-multivendor-marketplace' ), 'quarter' => __( 'Every 90 Days', 'wc-multivendor-marketplace' ) ) ), 'value' => $withdrawal_schedule  ),
																																			) ) );
					?>
				</table>
				
				<?php
				$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_marketplace_settings_fields_withdrawal', array(
					                                                                        "withdrawal_order_status" => array( 'label' => __( 'Order Status for Withdraw', 'wc-multivendor-marketplace' ), 'name' => 'wcfm_withdrawal_options[order_status]', 'type' => 'checklist', 'wrapper_class' => 'manual_withdrawal_ele', 'class' => 'wcfm-checkbox wcfm_ele payment_options', 'label_class' => 'wcfm_title wcfm_full_title manual_withdrawal_ele', 'options' => $wcfm_marketplace_withdrawal_order_status, 'value' => $order_status  )
					                                                          ) ) );
					                                                                        
				?>
			</div>
			
			<table class="form-table">
			  <?php
			  $WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_marketplace_settings_fields_withdrawal_rules', array(
					                                                                        "withdrawal_limit" => array('label' => __('Minimum Withdraw Limit', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[withdrawal_limit]', 'type' => 'number', 'wrapper_class' => 'withdrawal_threshold_ele', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele', 'label_class' => 'wcfm_title', 'desc_class'=> 'wcfm_page_options_desc', 'value' => $withdrawal_limit, 'attributes' => array( 'min' => '0.1', 'step' => '0.1'), 'desc' => __( 'Minimum balance required to make a withdraw request. Leave blank to set no minimum limits.', 'wc-multivendor-marketplace') ),
																																									"withdrawal_thresold" => array('label' => __('Withdraw Threshold', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[withdrawal_thresold]', 'type' => 'number', 'wrapper_class' => 'withdrawal_threshold_ele', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele', 'label_class' => 'wcfm_title wcfm_ele', 'desc_class' => 'wcfm_page_options_desc', 'value' => $withdrawal_thresold , 'attributes' => array( 'min' => '1', 'step' => '1'), 'desc' => __('Withdraw Threshold Days, (Make order matured to make a withdraw request). Leave empty to inactive this option.', 'wc-multivendor-marketplace') ),
					                                                                        ), $wcfm_withdrawal_options ) );
				?>
			</table>	
			<div>
				<?php
				$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_marketplace_settings_fields_withdrawal_payment', array(
																														 										  "withdrawal_payment_methods" => array( 'label' => __( 'Withdraw Payment Methods', 'wc-multivendor-membership' ), 'name' => 'wcfm_withdrawal_options[payment_methods]', 'type' => 'checklist', 'class' => 'wcfm-checkbox wcfm_ele payment_options', 'label_class' => 'wcfm_title wcfm_full_title', 'options' => $wcfm_marketplace_withdrwal_payment_methods, 'value' => $payment_methods  ),
					                                                                        "withdrawal_test_mode" => array('label' => __('Enable Test Mode', 'wc-multivendor-marketplace'), 'type' => 'checkbox', 'name' => 'wcfm_withdrawal_options[test_mode]', 'class' => 'wcfm-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_title checkbox_spl_title', 'value' => 'yes', 'dfvalue' => $withdrawal_test_mode ),
																																									), $wcfm_withdrawal_options ) );
				?>	
			</div>
		  <table class="form-table">	
		    <?php
				$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_marketplace_settings_fields_withdrawal_payment_keys', array(   
					                                                                        "withdrawal_stripe_is_3d_secure" => array('label' => __('Stripe 3D Secure and SCA?', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[stripe_3d_secure]', 'type' => 'checkbox', 'in_table' => 'yes', 'class' => 'wcfm-checkbox wcfm_ele withdrawal_mode withdrawal_mode_stripe_split', 'label_class' => 'wcfm_title checkbox_title withdrawal_mode withdrawal_mode_stripe_split', 'value' => 'yes', 'dfvalue' => $withdrawal_stripe_is_3d_secure, 'attributes' => array( 'style' => 'display:block!important;width:auto!important;' ), 'desc_class' => 'withdrawal_mode withdrawal_mode_stripe_split wcfm_page_options_desc', 'desc' => __( '3D Secure and SCA ready transaction is only supported when both your platform and the connected account (Vendor) are in the same region: both in Europe or both in the U.S.', 'wc-multivendor-marketplace' ) ),
																														 										  "withdrawal_stripe_split_pay_mode" => array('label' => __('Stripe Split Pay Mode', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[stripe_split_pay_mode]', 'type' => 'select', 'in_table' => 'yes', 'options' => array( 'direct_charges' => __( 'Direct Charges',  'wc-multivendor-marketplace' ), 'destination_charges' => __( 'Destination Charges',  'wc-multivendor-marketplace' ), 'transfers_charges' => __( 'Transfer Charges',  'wc-multivendor-marketplace' )  ), 'class' => 'wcfm-select wcfm_ele withdrawal_mode withdrawal_mode_stripe_split withdrawal_charge_type_ele', 'label_class' => 'wcfm_title withdrawal_mode withdrawal_mode_stripe_split withdrawal_charge_type_ele', 'value' => $withdrawal_stripe_split_pay_mode, 'desc_class' => 'withdrawal_mode withdrawal_mode_stripe_split wcfm_page_options_desc withdrawal_charge_type_ele', 'desc' => __( 'Set your preferred Stripe Split pay mode.', 'wc-multivendor-marketplace' ) ),
					                                                                        "withdrawal_paypal_client_id" => array('label' => __('PayPal Client ID', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[paypal_client_id]', 'type' => 'password', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele withdrawal_mode withdrawal_mode_paypal withdrawal_mode_live', 'label_class' => 'wcfm_title withdrawal_mode withdrawal_mode_paypal withdrawal_mode_live', 'value' => $withdrawal_paypal_client_id ),
					                                                                        "withdrawal_paypal_secret_key" => array('label' => __('PayPal Secret Key', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[paypal_secret_key]', 'type' => 'password', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele withdrawal_mode withdrawal_mode_paypal withdrawal_mode_live', 'label_class' => 'wcfm_title withdrawal_mode withdrawal_mode_paypal withdrawal_mode_live', 'value' => $withdrawal_paypal_secret_key ),
					                                                                        "withdrawal_stripe_client_id" => array('label' => __('Stripe Client ID', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[stripe_client_id]', 'type' => 'password', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele withdrawal_mode withdrawal_mode_stripe withdrawal_mode_stripe_split withdrawal_mode_live', 'label_class' => 'wcfm_title withdrawal_mode withdrawal_mode_stripe withdrawal_mode_stripe_split withdrawal_mode_live', 'value' => $withdrawal_stripe_client_id ),
					                                                                        "withdrawal_stripe_published_key" => array('label' => __('Stripe Publish Key', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[stripe_published_key]', 'type' => 'text', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele withdrawal_mode withdrawal_mode_stripe withdrawal_mode_stripe_split withdrawal_mode_live', 'label_class' => 'wcfm_title withdrawal_mode withdrawal_mode_stripe withdrawal_mode_stripe_split withdrawal_mode_live', 'value' => $withdrawal_stripe_published_key ),
					                                                                        "withdrawal_stripe_secret_key" => array('label' => __('Stripe Secret Key', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[stripe_secret_key]', 'type' => 'password', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele withdrawal_mode withdrawal_mode_stripe withdrawal_mode_stripe_split withdrawal_mode_live', 'label_class' => 'wcfm_title withdrawal_mode withdrawal_mode_stripe withdrawal_mode_stripe_split withdrawal_mode_live', 'value' => $withdrawal_stripe_secret_key ),
					                                                          ), $wcfm_withdrawal_options ) );
				
				$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_marketplace_settings_fields_withdrawal_payment_test_keys', array(
					                                                                        "withdrawal_paypal_test_client_id" => array('label' => __('PayPal Client ID', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[paypal_test_client_id]', 'type' => 'password', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele withdrawal_mode withdrawal_mode_paypal withdrawal_mode_test', 'label_class' => 'wcfm_title withdrawal_mode withdrawal_mode_paypal withdrawal_mode_test', 'value' => $withdrawal_paypal_test_client_id ),
					                                                                        "withdrawal_paypal_test_secret_key" => array('label' => __('PayPal Secret Key', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[paypal_test_secret_key]', 'type' => 'password', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele withdrawal_mode withdrawal_mode_paypal withdrawal_mode_test', 'label_class' => 'wcfm_title withdrawal_mode withdrawal_mode_paypal withdrawal_mode_test', 'value' => $withdrawal_paypal_test_secret_key ),
					                                                                        "withdrawal_stripe_test_client_id" => array('label' => __('Stripe Client ID', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[stripe_test_client_id]', 'type' => 'password', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele withdrawal_mode withdrawal_mode_stripe withdrawal_mode_stripe_split withdrawal_mode_test', 'label_class' => 'wcfm_title withdrawal_mode withdrawal_mode_stripe withdrawal_mode_stripe_split withdrawal_mode_test', 'value' => $withdrawal_stripe_test_client_id ),
					                                                                        "withdrawal_stripe_test_published_key" => array('label' => __('Stripe Publish Key', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[stripe_test_published_key]', 'type' => 'text', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele withdrawal_mode withdrawal_mode_stripe withdrawal_mode_stripe_split withdrawal_mode_test', 'label_class' => 'wcfm_title withdrawal_mode withdrawal_mode_stripe withdrawal_mode_stripe_split withdrawal_mode_test', 'value' => $withdrawal_stripe_test_published_key ),
					                                                                        "withdrawal_stripe_test_secret_key" => array('label' => __('Stripe Secret Key', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[stripe_test_secret_key]', 'type' => 'password', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele withdrawal_mode withdrawal_mode_stripe withdrawal_mode_stripe_split withdrawal_mode_test', 'label_class' => 'wcfm_title withdrawal_mode withdrawal_mode_stripe withdrawal_mode_stripe_split withdrawal_mode_test', 'value' => $withdrawal_stripe_test_secret_key ),
					                                                           ), $wcfm_withdrawal_options ) ); 
				?>
			</table>
			
			<table class="form-table">	
			  <?php
					$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_marketplace_settings_fields_withdrawal_charges', array(																																				
																																									"withdrawal_charge_type" => array('label' => __('Withdrawal Charges', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[withdrawal_charge_type]', 'type' => 'select', 'in_table' => 'yes', 'options' => array( 'no' => __( 'No Charge', 'wc-multivendor-marketplace' ), 'percent' => __( 'Percent', 'wc-multivendor-marketplace' ), 'fixed'   => __( 'Fixed', 'wc-multivendor-marketplace' ), 'percent_fixed' => __( 'Percent + Fixed', 'wc-multivendor-marketplace' ) ), 'class' => 'wcfm-select wcfm_ele', 'label_class' => 'wcfm_title wcfm_ele', 'desc_class' => 'wcfm_page_options_desc', 'value' => $withdrawal_charge_type , 'desc' => __('Charges applicable for each withdarwal.', 'wc-multivendor-marketplace') ),
																																									
																																									"withdrawal_charge_paypal" => array( 'label' => __('PayPal Charge', 'wc-multivendor-marketplace'), 'type' => 'multiinput', 'in_table' => 'yes', 'name' => 'wcfm_withdrawal_options[withdrawal_charge][paypal]', 'wrapper_class' => 'withdraw_charge_block withdraw_charge_paypal', 'label_class' => 'wcfm_title wcfm_ele wcfm_fill_ele withdraw_charge_block withdraw_charge_paypal', 'value' => $withdrawal_charge_paypal, 'custom_attributes' => array( 'limit' => 1 ), 'options' => array(
																																																											"percent" => array('label' => __('Percent Charge(%)', 'wc-multivendor-marketplace'),  'type' => 'number', 'class' => 'wcfm-text wcfm_ele withdraw_charge_field withdraw_charge_percent withdraw_charge_percent_fixed', 'label_class' => 'wcfm_title wcfm_ele withdraw_charge_field withdraw_charge_percent withdraw_charge_percent_fixed', 'attributes' => array( 'min' => '0.1', 'step' => '0.1') ),
																																																											"fixed" => array('label' => __('Fixed Charge', 'wc-multivendor-marketplace'), 'type' => 'number', 'class' => 'wcfm-text wcfm_ele withdraw_charge_field withdraw_charge_fixed withdraw_charge_percent_fixed', 'label_class' => 'wcfm_title wcfm_ele withdraw_charge_field withdraw_charge_fixed withdraw_charge_percent_fixed', 'attributes' => array( 'min' => '0.1', 'step' => '0.1') ),
																																																											"tax" => array('label' => __('Charge Tax', 'wc-multivendor-marketplace'), 'type' => 'number', 'class' => 'wcfm-text wcfm_ele', 'label_class' => 'wcfm_title wcfm_ele', 'attributes' => array( 'min' => '0.1', 'step' => '0.1'), 'hints' => __( 'Tax for withdrawal charge, calculate in percent.', 'wc-multivendor-marketplace' ) ),
																																																											) ),
																																									"withdrawal_charge_stripe" => array( 'label' => __('Stripe Charge', 'wc-multivendor-marketplace'), 'type' => 'multiinput', 'in_table' => 'yes', 'name' => 'wcfm_withdrawal_options[withdrawal_charge][stripe]', 'wrapper_class' => 'withdraw_charge_block withdraw_charge_stripe', 'label_class' => 'wcfm_title wcfm_ele wcfm_fill_ele withdraw_charge_block withdraw_charge_stripe', 'value' => $withdrawal_charge_stripe, 'custom_attributes' => array( 'limit' => 1 ), 'options' => array(
																																																											"percent" => array('label' => __('Percent Charge(%)', 'wc-multivendor-marketplace'),  'type' => 'number', 'class' => 'wcfm-text wcfm_ele withdraw_charge_field withdraw_charge_percent withdraw_charge_percent_fixed', 'label_class' => 'wcfm_title wcfm_ele withdraw_charge_field withdraw_charge_percent withdraw_charge_percent_fixed', 'attributes' => array( 'min' => '0.1', 'step' => '0.1') ),
																																																											"fixed" => array('label' => __('Fixed Charge', 'wc-multivendor-marketplace'), 'type' => 'number', 'class' => 'wcfm-text wcfm_ele withdraw_charge_field withdraw_charge_fixed withdraw_charge_percent_fixed', 'label_class' => 'wcfm_title wcfm_ele withdraw_charge_field withdraw_charge_fixed withdraw_charge_percent_fixed', 'attributes' => array( 'min' => '0.1', 'step' => '0.1') ),
																																																											"tax" => array('label' => __('Charge Tax', 'wc-multivendor-marketplace'), 'type' => 'number', 'class' => 'wcfm-text wcfm_ele', 'label_class' => 'wcfm_title wcfm_ele', 'attributes' => array( 'min' => '0.1', 'step' => '0.1'), 'hints' => __( 'Tax for withdrawal charge, calculate in percent.', 'wc-multivendor-marketplace' ) ),
																																																											) ),
																																									"withdrawal_charge_skrill" => array( 'label' => __('Skrill Charge', 'wc-multivendor-marketplace'), 'type' => 'multiinput', 'in_table' => 'yes', 'name' => 'wcfm_withdrawal_options[withdrawal_charge][skrill]', 'wrapper_class' => 'withdraw_charge_block withdraw_charge_skrill', 'label_class' => 'wcfm_title wcfm_ele wcfm_fill_ele withdraw_charge_block withdraw_charge_skrill', 'value' => $withdrawal_charge_skrill, 'custom_attributes' => array( 'limit' => 1 ), 'options' => array(
																																																											"percent" => array('label' => __('Percent Charge(%)', 'wc-multivendor-marketplace'),  'type' => 'number', 'class' => 'wcfm-text wcfm_ele withdraw_charge_field withdraw_charge_percent withdraw_charge_percent_fixed', 'label_class' => 'wcfm_title wcfm_ele withdraw_charge_field withdraw_charge_percent withdraw_charge_percent_fixed', 'attributes' => array( 'min' => '0.1', 'step' => '0.1') ),
																																																											"fixed" => array('label' => __('Fixed Charge', 'wc-multivendor-marketplace'), 'type' => 'number', 'class' => 'wcfm-text wcfm_ele withdraw_charge_field withdraw_charge_fixed withdraw_charge_percent_fixed', 'label_class' => 'wcfm_title wcfm_ele withdraw_charge_field withdraw_charge_fixed withdraw_charge_percent_fixed', 'attributes' => array( 'min' => '0.1', 'step' => '0.1') ),
																																																											"tax" => array('label' => __('Charge Tax', 'wc-multivendor-marketplace'), 'type' => 'number', 'class' => 'wcfm-text wcfm_ele', 'label_class' => 'wcfm_title wcfm_ele', 'attributes' => array( 'min' => '0.1', 'step' => '0.1'), 'hints' => __( 'Tax for withdrawal charge, calculate in percent.', 'wc-multivendor-marketplace' ) ),
																																																											) ),
																																									"withdrawal_charge_bank_transfer" => array( 'label' => __('Bank Transfer Charge', 'wc-multivendor-marketplace'), 'type' => 'multiinput', 'in_table' => 'yes', 'name' => 'wcfm_withdrawal_options[withdrawal_charge][bank_transfer]', 'wrapper_class' => 'withdraw_charge_block withdraw_charge_bank_transfer', 'label_class' => 'wcfm_title wcfm_ele wcfm_fill_ele withdraw_charge_block withdraw_charge_bank_transfer', 'value' => $withdrawal_charge_bank_transfer, 'custom_attributes' => array( 'limit' => 1 ), 'options' => array(
																																																											"percent" => array('label' => __('Percent Charge(%)', 'wc-multivendor-marketplace'),  'type' => 'number', 'class' => 'wcfm-text wcfm_ele withdraw_charge_field withdraw_charge_percent withdraw_charge_percent_fixed', 'label_class' => 'wcfm_title wcfm_ele withdraw_charge_field withdraw_charge_percent withdraw_charge_percent_fixed', 'attributes' => array( 'min' => '0.1', 'step' => '0.1') ),
																																																											"fixed" => array('label' => __('Fixed Charge', 'wc-multivendor-marketplace'), 'type' => 'number', 'class' => 'wcfm-text wcfm_ele withdraw_charge_field withdraw_charge_fixed withdraw_charge_percent_fixed', 'label_class' => 'wcfm_title wcfm_ele withdraw_charge_field withdraw_charge_fixed withdraw_charge_percent_fixed', 'attributes' => array( 'min' => '0.1', 'step' => '0.1') ),
																																																											"tax" => array('label' => __('Charge Tax', 'wc-multivendor-marketplace'), 'type' => 'number', 'class' => 'wcfm-text wcfm_ele', 'label_class' => 'wcfm_title wcfm_ele', 'attributes' => array( 'min' => '0.1', 'step' => '0.1'), 'hints' => __( 'Tax for withdrawal charge, calculate in percent.', 'wc-multivendor-marketplace' ) ),
																																																											) )
																																									), $wcfm_withdrawal_options, $withdrawal_charge ) );
				?>
			</table>
			
			<h1><?php esc_html_e('Reverse Withdrawal setup', 'wc-frontend-manager'); ?></h1>
			<div>	
			  <?php
				$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_marketplace_settings_fields_reverse_withdrawal', array(
																																										"withdrawal_reverse" => array('label' => __('Reverse Withdrawal', 'wc-multivendor-marketplace'), 'type' => 'checkbox', 'name' => 'wcfm_withdrawal_options[withdrawal_reverse]', 'class' => 'wcfm-checkbox wcfm_ele', 'label_class' => 'wcfm_title checkbox_spl_title checkbox_title', 'value' => 'yes', 'dfvalue' => $withdrawal_reverse, 'desc_class' => 'instructions', 'desc' => __( 'Enable this to keep track reverse withdrawals. In case vendor receive full payment (e.g. COD) from customer then they have to reverse-pay admin commission. This is only applicable for auto-withdrawal payment methods.', 'wc-multivendor-marketplace' ) ),
																																										"withdrawal_order_payment_methods" => array( 'label' => __( 'Reverse or No Withdrawal Payment Methods', 'wc-multivendor-marketplace' ), 'name' => 'wcfm_withdrawal_options[disallow_order_payment_methods]', 'type' => 'checklist', 'wrapper_class' => 'reverse_withdrawal_ele',  'class' => 'wcfm-checkbox wcfm_ele payment_options reverse_withdrawal_ele', 'label_class' => 'wcfm_title wcfm_full_title reverse_withdrawal_ele', 'options' => $wcfm_marketplace_disallow_order_payment_methods, 'value' => $disallow_order_payment_methods, 'desc_class' => 'reverse_withdrawal_ele', 'desc' => __( 'Order Payment Methods which are not applicable for vendor withdrawal request. e.g Order payment method COD and vendor receiving that amount directly from customers. So, no more require withdrawal request. You may also enable Reverse Withdrawal to track reverse pending payments for such payment options.', 'wc-multivendor-membership' )  ),
																																										
																																										"withdrawal_reverse_limit" => array('label' => __('Reverse Withdraw Limit', 'wc-multivendor-marketplace'), 'name' => 'wcfm_withdrawal_options[withdrawal_reverse_limit]', 'type' => 'number', 'class' => 'wcfm-text wcfm_ele reverse_withdrawal_ele', 'label_class' => 'wcfm_title checkbox_spl_title wcfm_ele reverse_withdrawal_ele', 'value' => $withdrawal_reverse_limit, 'desc_class' => 'wcfm_page_options_desc', 'attributes' => array( 'min' => '1', 'step' => '1'), 'desc_class' => 'reverse_withdrawal_ele', 'desc' => __('Set reverse withdrawal threshold limit, if reverse-pay balance reach this limit then vendor will not allow to withdrawal anymore. Leave empty to inactive this option.', 'wc-multivendor-marketplace') ),
																																										), $wcfm_withdrawal_options ) );
				?>
			</div>
			<p class="wc-setup-actions step">
				<input type="submit" class="button-primary button button-large button-next" value="<?php esc_attr_e('Continue', 'wc-frontend-manager'); ?>" name="save_step" />
				<a href="<?php echo esc_url($this->get_next_step_link()); ?>" class="button button-large button-next"><?php esc_html_e('Skip this step', 'wc-frontend-manager'); ?></a>
				<?php wp_nonce_field('wcfm-setup'); ?>
			</p>
		</form>
		<?php
	}
	
	/**
	 * Style setup content
	 */
	public function wcfm_setup_style() {
		global $WCFM;
		wp_print_scripts('wp-color-picker');
		wp_print_scripts('colorpicker_init');
		wp_print_scripts('iris');
		$wcfm_options = (array) get_option( 'wcfm_options' );
	  $color_options = $WCFM->wcfm_color_setting_options();
		?>
		<h1><?php esc_html_e('Dashboard Style', 'wc-frontend-manager'); ?></h1>
		<form method="post">
			<table class="form-table">
				<?php
					$color_options_array = array();
					foreach( $color_options as $color_option_key => $color_option ) {
						$color_options_array[$color_option['name']] = array( 'label' => $color_option['label'] , 'type' => 'colorpicker', 'in_table' => 'yes', 'class' => 'wcfm-text wcfm_ele colorpicker', 'label_class' => 'wcfm_title wcfm_ele', 'value' => ( isset($wcfm_options[$color_option['name']]) ) ? $wcfm_options[$color_option['name']] : $color_option['default'] );
					}
					$WCFM->wcfm_fields->wcfm_generate_form_field( $color_options_array );
				?>
			</table>
			<p class="wc-setup-actions step">
				<input type="submit" class="button-primary button button-large button-next" value="<?php esc_attr_e('Continue', 'wc-frontend-manager'); ?>" name="save_step" />
				<a href="<?php echo esc_url($this->get_next_step_link()); ?>" class="button button-large button-next"><?php esc_html_e('Skip this step', 'wc-frontend-manager'); ?></a>
				<?php wp_nonce_field('wcfm-setup'); ?>
			</p>
		</form>
		<?php
	}

	/**
	 * capability setup content
	 */
	public function wcfm_setup_capability() {
		global $WCFM;
		$wcfm_capability_options = (array) get_option( 'wcfm_capability_options' );
		
		$vnd_wpadmin = ( isset( $wcfm_capability_options['vnd_wpadmin'] ) ) ? $wcfm_capability_options['vnd_wpadmin'] : 'yes';
		
		$submit_products = ( isset( $wcfm_capability_options['submit_products'] ) ) ? $wcfm_capability_options['submit_products'] : 'no';
		$publish_products = ( isset( $wcfm_capability_options['publish_products'] ) ) ? $wcfm_capability_options['publish_products'] : 'no';
		$edit_live_products = ( isset( $wcfm_capability_options['edit_live_products'] ) ) ? $wcfm_capability_options['edit_live_products'] : 'no';
		$delete_products = ( isset( $wcfm_capability_options['delete_products'] ) ) ? $wcfm_capability_options['delete_products'] : 'no';
		
		// Miscellaneous Capabilities
		$manage_booking = ( isset( $wcfm_capability_options['manage_booking'] ) ) ? $wcfm_capability_options['manage_booking'] : 'no';
		$manage_subscription = ( isset( $wcfm_capability_options['manage_subscription'] ) ) ? $wcfm_capability_options['manage_subscription'] : 'no';
		$associate_listings = ( isset( $wcfm_capability_options['associate_listings'] ) ) ? $wcfm_capability_options['associate_listings'] : 'no';
		
		$view_orders  = ( isset( $wcfm_capability_options['view_orders'] ) ) ? $wcfm_capability_options['view_orders'] : 'no';
		$order_status_update  = ( isset( $wcfm_capability_options['order_status_update'] ) ) ? $wcfm_capability_options['order_status_update'] : 'no';
		$view_reports  = ( isset( $wcfm_capability_options['view_reports'] ) ) ? $wcfm_capability_options['view_reports'] : 'no';
		?>
		<h1><?php esc_html_e('Capability', 'wc-frontend-manager'); ?></h1>
		<form method="post">
			<table class="form-table">
			  <?php
			  $WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_settings_fields_vendor_access', array(  
																																						 "vnd_wpadmin" => array('label' => __('Backend Access', 'wc-frontend-manager') . ' (wp-admin)', 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $vnd_wpadmin),
																															) ) );
			  
			  $WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_settings_fields_vendor_products', array("submit_products" => array('label' => __('Submit Products', 'wc-frontend-manager') , 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $submit_products),
																																																									 "publish_products" => array('label' => __('Publish Products', 'wc-frontend-manager') , 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $publish_products),
																																																									 "edit_live_products" => array('label' => __('Edit Live Products', 'wc-frontend-manager') , 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $edit_live_products),
																																																									 "delete_products" => array('label' => __('Delete Products', 'wc-frontend-manager') , 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $delete_products)
																													) ) );
				
				if( wcfm_is_booking() ) {
					$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_settings_fields_vendor_booking', array(  "manage_booking" => array('label' => __('Manage Bookings', 'wc-frontend-manager') , 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $manage_booking),
																											) ) );
				}
				
				if( wcfm_is_subscription() || wcfm_is_xa_subscription() ) {
					$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_settings_fields_vendor_subscription', array(  "manage_subscription" => array('label' => __('Manage Subscriptions', 'wc-frontend-manager') , 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $manage_subscription),
																											) ) );
				}
				
				if( WCFM_Dependencies::wcfm_wp_job_manager_plugin_active_check() ) {
					$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_settings_fields_vendor_listings', array(  "associate_listings" => array('label' => __('Listings', 'wc-frontend-manager') , 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'desc' => __( 'by WP Job Manager.', 'wc-frontend-manager' ), 'dfvalue' => $associate_listings),
																											) ) );
				}
				
				$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_settings_fields_vendor_orders', array(  "view_orders" => array('label' => __('View Orders', 'wc-frontend-manager') , 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $view_orders),
																																																									 "order_status_update" => array('label' => __('Status Update', 'wc-frontend-manager') , 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $order_status_update),
																										) ) );
				
				$WCFM->wcfm_fields->wcfm_generate_form_field( apply_filters( 'wcfm_settings_fields_vendor_reports', array("view_reports" => array('label' => __('View Reports', 'wc-frontend-manager') , 'type' => 'checkboxoffon', 'in_table' => 'yes', 'class' => 'wcfm-checkbox wcfm_ele', 'value' => 'yes', 'label_class' => 'wcfm_title checkbox_title', 'dfvalue' => $view_reports),
																										 ) ) );
			  ?>
			</table>
			<p class="wc-setup-actions step">
				<input type="submit" class="button-primary button button-large button-next" value="<?php esc_attr_e('Continue', 'wc-frontend-manager'); ?>" name="save_step" />
				<a href="<?php echo esc_url($this->get_next_step_link()); ?>" class="button button-large button-next"><?php esc_html_e('Skip this step', 'wc-frontend-manager'); ?></a>
				<?php wp_nonce_field('wcfm-setup'); ?>
			</p>
		</form>
		<?php
	}

	/**
	 * Ready to go content
	 */
	public function wcfm_setup_ready() {
		global $WCFM;
		$is_marketplace = wcfm_is_marketplace();
		?>
		<a href="https://twitter.com/share" class="twitter-share-button" data-url="<?php echo site_url(); ?>" data-text="Hey Guys! Our new e-commerce store is now live and ready to be ransacked! Check it out at" data-via="wcfmmp" data-size="large">Tweet</a>
		<script>!function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0];if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src="//platform.twitter.com/widgets.js";fjs.parentNode.insertBefore(js,fjs);}}(document,"script","twitter-wjs");</script>
		<h1><?php esc_html_e('We are done!', 'wc-frontend-manager'); ?></h1>
		<div class="woocommerce-message woocommerce-tracker">
		 <?php if( $is_marketplace && ( $is_marketplace  == 'wcfmmarketplace' ) ) { ?>
				<p><?php esc_html_e("Your marketplace is ready. It's time to experience the things more Easily and Peacefully. Also you will be a bit more relax than ever before, have fun!!", 'wc-frontend-manager') ?></p>
			<?php } else { ?>
				<p><?php esc_html_e("Your front-end dashboard is ready. It's time to experience the things more Easily and Peacefully. Also you will be a bit more relax than ever before, have fun!!", 'wc-frontend-manager') ?></p>
			<?php } ?>
		</div>
		<div class="wc-setup-next-steps">
				<div class="wc-setup-next-steps-first">
						<h2><?php esc_html_e( 'Next steps', 'wc-frontend-manager' ); ?></h2>
						<ul>
								<li class="setup-product"><a class="button button-primary button-large" href="<?php echo esc_url( get_wcfm_url() ); ?>"><?php esc_html_e( "Let's go to the Dashboard", 'wc-frontend-manager' ); ?></a></li>
						</ul>
				</div>
				<div class="wc-setup-next-steps-last">
						<h2><?php _e( 'Learn more', 'wc-frontend-manager' ); ?></h2>
						<ul>
								<li class="video-walkthrough"><a target="_blank" href="https://www.youtube.com/channel/UCJ0c60fv3l1K9mBbHdmR-5Q"><?php esc_html_e( 'Watch the tutorial videos', 'wc-frontend-manager' ); ?></a></li>
								<li class="knowledgebase"><a target="_blank" href="https://wclovers.com/blog/woocommerce-frontend-manager/"><?php esc_html_e( 'WCFM - What & Why?', 'wc-frontend-manager' ); ?></a></li>
								<li class="learn-more"><a target="_blank" href="http://wclovers.com/blog/choose-best-woocommerce-multi-vendor-marketplace-plugin/"><?php esc_html_e( 'Choose your multi-vendor plugin', 'wc-frontend-manager' ); ?></a></li>
						</ul>
				</div>
		</div>
		<?php
	}

	/**
	 * Save dashboard settings
	 */
	public function wcfm_setup_dashboard_save() {
		global $WCFM;
		check_admin_referer('wcfm-setup');
		
		$options = get_option( 'wcfm_options' );
		
		$dashboard_full_view_disabled = filter_input(INPUT_POST, 'dashboard_full_view_disabled');
		$dashboard_theme_header_disabled = filter_input(INPUT_POST, 'dashboard_theme_header_disabled');
		$slick_menu_disabled = filter_input(INPUT_POST, 'slick_menu_disabled');
		$headpanel_disabled = filter_input(INPUT_POST, 'headpanel_disabled');
		$checklist_view_disabled = filter_input(INPUT_POST, 'checklist_view_disabled');
		
		$welcome_box_disabled = filter_input(INPUT_POST, 'welcome_box_disabled');
		$quick_access_disabled = filter_input(INPUT_POST, 'quick_access_disabled');
		//$responsive_float_menu_disabled = filter_input(INPUT_POST, 'responsive_float_menu_disabled');
		$float_button_disabled = filter_input(INPUT_POST, 'float_button_disabled');
		
		// Menu Disabled
		if( !$dashboard_full_view_disabled ) $options['dashboard_full_view_disabled'] = 'no';
		else $options['dashboard_full_view_disabled'] = 'yes';
		
		// Theme Header Disabled
		if( !$dashboard_theme_header_disabled ) $options['dashboard_theme_header_disabled'] = 'no';
		else $options['dashboard_theme_header_disabled'] = 'yes';
		
		// Slick Menu Disabled
		if( !$slick_menu_disabled ) $options['slick_menu_disabled'] = 'no';
		else $options['slick_menu_disabled'] = 'yes';
		
		// Header Panel Disabled
		if( !$headpanel_disabled ) $options['headpanel_disabled'] = 'no';
		else $options['headpanel_disabled'] = 'yes';
		
		// Taxonomy Checklist view Disabled
		if( !$checklist_view_disabled ) $options['checklist_view_disabled'] = 'no';
		else $options['checklist_view_disabled'] = 'yes';
		
		// Dashboard Welcome Box Disabled
		if( !$welcome_box_disabled ) $options['welcome_box_disabled'] = 'no';
		else $options['welcome_box_disabled'] = 'yes';
		
		// WCFM Quick Access Disabled
		if( !$quick_access_disabled ) $options['quick_access_disabled'] = 'no';
		else $options['quick_access_disabled'] = 'yes';
		
		// Responsive Float Menu Disabled
		//if( !$responsive_float_menu_disabled ) $options['responsive_float_menu_disabled'] = 'no';
		$options['responsive_float_menu_disabled'] = 'yes';
		
		// Float Button Disabled
		if( !$float_button_disabled ) $options['float_button_disabled'] = 'no';
		else $options['float_button_disabled'] = 'yes';
		
		$options['module_options']['membership'] = 'yes';
		$options['module_options']['buddypress'] = 'yes';
		$options['module_options']['shipstation'] = 'yes';
		$options['module_options']['facebook_marketplace'] = 'yes';
		
		update_option( 'wcfm_options', $options );
		
		wp_redirect(esc_url_raw($this->get_next_step_link()));
		exit;
	}
	
	/**
	 * Save marketplace settings
	 */
	public function wcfm_setup_marketplace_save() {
		global $WCFM;
		check_admin_referer('wcfm-setup');
		
		$wcfm_marketplace_options = get_option( 'wcfm_marketplace_options', array() );
		
		$vendor_store_url = filter_input(INPUT_POST, 'vendor_store_url');
		$vendor_sold_by = filter_input(INPUT_POST, 'vendor_sold_by');
		$vendor_sold_by_template = filter_input(INPUT_POST, 'vendor_sold_by_template');
		$vendor_sold_by_position = filter_input(INPUT_POST, 'vendor_sold_by_position');
		$store_name_position = filter_input(INPUT_POST, 'store_name_position');
		$store_sidebar = filter_input(INPUT_POST, 'store_sidebar');
		$store_sidebar_pos = filter_input(INPUT_POST, 'store_sidebar_pos');
		$store_related_products = filter_input(INPUT_POST, 'store_related_products');
		$store_ppp           = filter_input(INPUT_POST, 'store_ppp');
		$order_sync          = filter_input(INPUT_POST, 'order_sync');
		//$product_mulivendor  = filter_input(INPUT_POST, 'product_mulivendor');
		$enable_marketplace_shipping = filter_input(INPUT_POST, 'enable_marketplace_shipping');
    $enable_marketplace_shipping_by_weight = filter_input(INPUT_POST, 'enable_marketplace_shipping_by_weight');
		$wcfm_google_map_api = filter_input(INPUT_POST, 'wcfm_google_map_api');
		
		if( !$vendor_store_url ) $vendor_store_url = 'store';
		$wcfm_marketplace_options['wcfm_store_url'] = sanitize_title( $vendor_store_url );
		update_option( 'wcfm_store_url', sanitize_title( $vendor_store_url ) );
		
		if( !$vendor_sold_by ) $wcfm_marketplace_options['vendor_sold_by'] = 'yes';
		else $wcfm_marketplace_options['vendor_sold_by'] = 'no';
		
		$wcfm_marketplace_options['vendor_sold_by_template'] = $vendor_sold_by_template;
		
		$wcfm_marketplace_options['vendor_sold_by_position'] = $vendor_sold_by_position;
		
		$wcfm_marketplace_options['store_name_position'] = $store_name_position;
		
		$wcfm_marketplace_options['store_ppp'] = $store_ppp;
		
		$wcfm_marketplace_options['store_sidebar_pos'] = $store_sidebar_pos;
		
		$wcfm_marketplace_options['store_related_products'] = $store_related_products;
		
		if( !$store_sidebar ) $wcfm_marketplace_options['store_sidebar'] = 'yes';
		else $wcfm_marketplace_options['store_sidebar'] = 'no';
		
		//if( !$product_mulivendor ) $wcfm_marketplace_options['product_mulivendor'] = 'yes';
		$wcfm_marketplace_options['product_mulivendor'] = 'no';
		
		//if( !$order_sync ) $wcfm_marketplace_options['order_sync'] = 'yes';
		//else $wcfm_marketplace_options['order_sync'] = 'no';
		
		$wcfm_marketplace_options['order_sync'] = 'no';
		
		$wcfm_marketplace_options['wcfm_google_map_api'] = $wcfm_google_map_api;
		
		update_option( 'wcfm_marketplace_options', $wcfm_marketplace_options );
		
		if( !$enable_marketplace_shipping ) $enable_marketplace_shipping = 'yes';
		else $enable_marketplace_shipping = 'no';
    
    if( !$enable_marketplace_shipping_by_weight ) $enable_marketplace_shipping_by_weight = 'yes';
		else $enable_marketplace_shipping_by_weight = 'no';
		
		$wcfm_shipping_options = get_option( 'wcfm_shipping_options', array() );
		$wcfm_shipping_options['enable_store_shipping'] = $enable_marketplace_shipping;
    update_option( 'wcfm_shipping_options', $wcfm_shipping_options );
		
		$woocommerce_wcfmmp_product_shipping_by_zone_settings = get_option( 'woocommerce_wcfmmp_product_shipping_by_zone_settings', array() );
    $woocommerce_wcfmmp_product_shipping_by_zone_settings['enabled'] = $enable_marketplace_shipping;
    update_option( 'woocommerce_wcfmmp_product_shipping_by_zone_settings', $woocommerce_wcfmmp_product_shipping_by_zone_settings );
		
		$woocommerce_wcfmmp_product_shipping_by_country_settings = get_option( 'woocommerce_wcfmmp_product_shipping_by_country_settings', array() );
    $woocommerce_wcfmmp_product_shipping_by_country_settings['enabled'] = $enable_marketplace_shipping;
    update_option( 'woocommerce_wcfmmp_product_shipping_by_country_settings', $woocommerce_wcfmmp_product_shipping_by_country_settings );
    
    $wcfmmp_marketplace_by_weight_shipping_options = get_option( 'woocommerce_wcfmmp_product_shipping_by_weight_settings', array() );
    $wcfmmp_marketplace_by_weight_shipping_options['enabled'] = $enable_marketplace_shipping_by_weight;
    update_option( 'woocommerce_wcfmmp_product_shipping_by_weight_settings', $wcfmmp_marketplace_by_weight_shipping_options );
    
    $wcfmmp_marketplace_by_distance_shipping_options = get_option( 'woocommerce_wcfmmp_product_shipping_by_distance_settings', array() );
    $wcfmmp_marketplace_by_distance_shipping_options['enabled'] = 'yes';
    update_option( 'woocommerce_wcfmmp_product_shipping_by_distance_settings', $wcfmmp_marketplace_by_distance_shipping_options );
		
		wp_redirect(esc_url_raw($this->get_next_step_link()));
		exit;
	}
	
	/**
	 * Save commission settings
	 */
	public function wcfm_setup_commission_save() {
		global $WCFM;
		check_admin_referer('wcfm-setup');
		
		$wcfm_commission_options = get_option( 'wcfm_commission_options', array() );
		
		$vendor_commission_for = filter_input(INPUT_POST, 'vendor_commission_for');
		$vendor_commission_mode = filter_input(INPUT_POST, 'vendor_commission_mode');
		$vendor_commission_fixed = filter_input(INPUT_POST, 'vendor_commission_fixed');
		$vendor_commission_percent = filter_input(INPUT_POST, 'vendor_commission_percent');
		
		$vendor_get_shipping = filter_input(INPUT_POST, 'vendor_get_shipping');
		$vendor_get_tax = filter_input(INPUT_POST, 'vendor_get_tax');
		$vendor_coupon_deduct = filter_input(INPUT_POST, 'vendor_coupon_deduct');
		$admin_coupon_deduct = filter_input(INPUT_POST, 'admin_coupon_deduct');
		
		$tax_enable = filter_input(INPUT_POST, 'tax_enable');
		$tax_name = filter_input(INPUT_POST, 'tax_name');
		$tax_percent = filter_input(INPUT_POST, 'tax_percent');
		
		if( $vendor_commission_for ) {
			$wcfm_commission_options['commission_for'] = $vendor_commission_for;
		}
		
		if( $vendor_commission_mode ) {
			$wcfm_commission_options['commission_mode'] = $vendor_commission_mode;
		}
		
		if( $vendor_commission_fixed ) {
			$wcfm_commission_options['commission_fixed'] = $vendor_commission_fixed;
		}
		
		if( $vendor_commission_percent ) {
			$wcfm_commission_options['commission_percent'] = $vendor_commission_percent;
		}
		
		if( !$vendor_get_shipping ) $wcfm_commission_options['get_shipping'] = 'yes';
		else $wcfm_commission_options['get_shipping'] = 'no';
		
		if( !$vendor_get_tax ) $wcfm_commission_options['get_tax'] = 'yes';
		else $wcfm_commission_options['get_tax'] = 'no';
		
		if( !$vendor_coupon_deduct ) $wcfm_commission_options['coupon_deduct'] = 'yes';
		else $wcfm_commission_options['coupon_deduct'] = 'no';
		
		if( !$admin_coupon_deduct ) $wcfm_commission_options['admin_coupon_deduct'] = 'yes';
		else $wcfm_commission_options['admin_coupon_deduct'] = 'no';
		
		if( !$tax_enable ) $wcfm_commission_options['tax_enable'] = 'yes';
		else $wcfm_commission_options['tax_enable'] = 'no';
		
		if( $tax_name ) {
			$wcfm_commission_options['tax_name'] = $tax_name;
		}
		
		if( $tax_percent ) {
			$wcfm_commission_options['tax_percent'] = $tax_percent;
		}
		
		update_option( 'wcfm_commission_options', $wcfm_commission_options );
		
		wp_redirect(esc_url_raw($this->get_next_step_link()));
		exit;
	}
	
	/**
	 * Save withdrawal settings
	 */
	public function wcfm_setup_withdrawal_save() {
		global $WCFM;
		check_admin_referer('wcfm-setup');
		
		$wcfm_withdrawal_options = get_option( 'wcfm_withdrawal_options', array() );
		
		if( isset( $_POST['wcfm_withdrawal_options'] ) ) {
			update_option( 'wcfm_withdrawal_options', wc_clean( $_POST['wcfm_withdrawal_options'] ) );
		}
		
		wp_redirect(esc_url_raw($this->get_next_step_link()));
		exit;
	}
	
	/**
	 * Save dashboard style settings
	 */
	public function wcfm_setup_style_save() {
		global $WCFM;
		check_admin_referer('wcfm-setup');
		
		$options = get_option( 'wcfm_options' );
		
		$color_options = $WCFM->wcfm_color_setting_options();
		foreach( $color_options as $color_option_key => $color_option ) {
			$color_value = filter_input( INPUT_POST, $color_option['name'] );
			if( $color_value ) { $options[$color_option['name']] = $color_value; } else { $options[$color_option['name']] = $color_option['default']; }
		}
		
		update_option( 'wcfm_options', $options );
		$WCFM->wcfm_options = $options;
		
		// Init WCFM Custom CSS file
		$wcfm_style_custom = $WCFM->wcfm_create_custom_css();
		
		wp_redirect(esc_url_raw($this->get_next_step_link()));
		exit;
	}

	/**
	 * save capability settings
	 * @global object $WCFM
	 */
	public function wcfm_setup_capability_save() {
			global $WCFM;
			check_admin_referer('wcfm-setup');

			$wcfm_capability_options = (array) get_option( 'wcfm_capability_options' );
			
			$vnd_wpadmin = filter_input(INPUT_POST, 'vnd_wpadmin');
			
			$submit_products = filter_input(INPUT_POST, 'submit_products');
			$publish_products = filter_input(INPUT_POST, 'publish_products');
			$edit_live_products = filter_input(INPUT_POST, 'edit_live_products');
			$delete_products = filter_input(INPUT_POST, 'delete_products');
			
			$manage_booking = filter_input(INPUT_POST, 'manage_booking');
			$manage_subscription = filter_input(INPUT_POST, 'manage_subscription');
			$associate_listings = filter_input(INPUT_POST, 'associate_listings');
			
			$view_orders = filter_input(INPUT_POST, 'view_orders');
			$view_reports = filter_input(INPUT_POST, 'view_reports');
			
			if( !$vnd_wpadmin ) $wcfm_capability_options['vnd_wpadmin'] = 'no';
			else $wcfm_capability_options['vnd_wpadmin'] = 'yes';
			
			if( !$submit_products ) $wcfm_capability_options['submit_products'] = 'no';
			else $wcfm_capability_options['submit_products'] = 'yes';
			
			if( !$publish_products ) $wcfm_capability_options['publish_products'] = 'no';
			else $wcfm_capability_options['publish_products'] = 'yes';
			
			if( !$edit_live_products ) $wcfm_capability_options['edit_live_products'] = 'no';
			else $wcfm_capability_options['edit_live_products'] = 'yes';
			
			if( !$delete_products ) $wcfm_capability_options['delete_products'] = 'no';
			else $wcfm_capability_options['delete_products'] = 'yes';
			
			if( !$manage_booking ) $wcfm_capability_options['manage_booking'] = 'no';
			else $wcfm_capability_options['manage_booking'] = 'yes';
			
			if( !$manage_subscription ) $wcfm_capability_options['manage_subscription'] = 'no';
			else $wcfm_capability_options['manage_subscription'] = 'yes';
			
			if( !$associate_listings ) $wcfm_capability_options['associate_listings'] = 'no';
			else $wcfm_capability_options['associate_listings'] = 'yes';
			
			if( !$view_orders ) $wcfm_capability_options['view_orders'] = 'no';
			else $wcfm_capability_options['view_orders'] = 'yes';
			
			if( !$view_reports ) $wcfm_capability_options['view_reports'] = 'no';
			else $wcfm_capability_options['view_reports'] = 'yes';
			
			update_option( 'wcfm_capability_options', $wcfm_capability_options );
			
			$WCFM->wcfm_vendor_support->vendors_capability_option_updates();

			wp_redirect(esc_url_raw($this->get_next_step_link()));
			exit;
	}

	/**
	 * Setup Wizard Footer.
	 */
	public function dashboard_setup_footer() {
			if ('next_steps' === $this->step) :
					?>
					<a class="wc-return-to-dashboard" href="<?php echo esc_url(admin_url()); ?>"><?php esc_html_e('Return to the WordPress Dashboard', 'wc-frontend-manager'); ?></a>
	<?php endif; ?>
			</body>
	</html>
	<?php
	}
}

new WCFM_Dashboard_Setup();
