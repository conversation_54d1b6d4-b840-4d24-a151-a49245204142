<?php
/**
 * Admin Settings View
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
if (isset($_POST['submit']) && wp_verify_nonce($_POST['_wpnonce'], 'vendor_settings')) {
    $tab = sanitize_text_field($_POST['tab']);
    
    switch ($tab) {
        case 'general':
            $general_settings = array(
                'vendor_registration' => sanitize_text_field($_POST['vendor_registration']),
                'auto_approve_vendors' => sanitize_text_field($_POST['auto_approve_vendors']),
                'vendor_dashboard_page' => intval($_POST['vendor_dashboard_page']),
                'vendor_store_url' => sanitize_text_field($_POST['vendor_store_url'])
            );
            update_option('vendor_general_settings', $general_settings);
            break;
            
        case 'commission':
            $commission_settings = array(
                'default_commission_rate' => floatval($_POST['default_commission_rate']),
                'commission_type' => sanitize_text_field($_POST['commission_type']),
                'auto_approve_commissions' => sanitize_text_field($_POST['auto_approve_commissions'])
            );
            update_option('vendor_commission_settings', $commission_settings);
            break;
            
        case 'withdrawal':
            $withdrawal_settings = array(
                'minimum_withdrawal' => floatval($_POST['minimum_withdrawal']),
                'withdrawal_schedule' => sanitize_text_field($_POST['withdrawal_schedule']),
                'auto_withdrawal' => sanitize_text_field($_POST['auto_withdrawal'])
            );
            update_option('vendor_withdrawal_settings', $withdrawal_settings);
            break;
    }
    
    echo '<div class="notice notice-success"><p>' . __('Settings saved successfully.', 'vendor') . '</p></div>';
}

// Get current settings
$general_settings = get_option('vendor_general_settings', array());
$commission_settings = get_option('vendor_commission_settings', array());
$withdrawal_settings = get_option('vendor_withdrawal_settings', array());
?>

<div class="wrap">
    <h1><?php _e('Vendor Settings', 'vendor'); ?></h1>
    
    <nav class="nav-tab-wrapper vendor-settings-tabs">
        <a href="#" class="nav-tab <?php echo $active_tab === 'general' ? 'nav-tab-active' : ''; ?>" data-tab="general">
            <?php _e('General', 'vendor'); ?>
        </a>
        <a href="#" class="nav-tab <?php echo $active_tab === 'commission' ? 'nav-tab-active' : ''; ?>" data-tab="commission">
            <?php _e('Commission', 'vendor'); ?>
        </a>
        <a href="#" class="nav-tab <?php echo $active_tab === 'withdrawal' ? 'nav-tab-active' : ''; ?>" data-tab="withdrawal">
            <?php _e('Withdrawal', 'vendor'); ?>
        </a>
        <a href="#" class="nav-tab <?php echo $active_tab === 'gateways' ? 'nav-tab-active' : ''; ?>" data-tab="gateways">
            <?php _e('Payment Gateways', 'vendor'); ?>
        </a>
    </nav>
    
    <div class="vendor-settings-content">
        <!-- General Settings -->
        <div class="tab-content" data-tab="general">
            <form method="post" action="">
                <?php wp_nonce_field('vendor_settings'); ?>
                <input type="hidden" name="tab" value="general">
                
                <table class="form-table vendor-form-table">
                    <tr>
                        <th scope="row">
                            <label for="vendor_registration"><?php _e('Vendor Registration', 'vendor'); ?></label>
                        </th>
                        <td>
                            <select name="vendor_registration" id="vendor_registration">
                                <option value="open" <?php selected(isset($general_settings['vendor_registration']) ? $general_settings['vendor_registration'] : 'open', 'open'); ?>>
                                    <?php _e('Open Registration', 'vendor'); ?>
                                </option>
                                <option value="approval" <?php selected(isset($general_settings['vendor_registration']) ? $general_settings['vendor_registration'] : 'open', 'approval'); ?>>
                                    <?php _e('Admin Approval Required', 'vendor'); ?>
                                </option>
                                <option value="closed" <?php selected(isset($general_settings['vendor_registration']) ? $general_settings['vendor_registration'] : 'open', 'closed'); ?>>
                                    <?php _e('Closed Registration', 'vendor'); ?>
                                </option>
                            </select>
                            <p class="description"><?php _e('Control how vendors can register on your site.', 'vendor'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="auto_approve_vendors"><?php _e('Auto Approve Vendors', 'vendor'); ?></label>
                        </th>
                        <td>
                            <input type="checkbox" name="auto_approve_vendors" id="auto_approve_vendors" value="yes" <?php checked(isset($general_settings['auto_approve_vendors']) ? $general_settings['auto_approve_vendors'] : 'no', 'yes'); ?>>
                            <p class="description"><?php _e('Automatically approve new vendor registrations.', 'vendor'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="vendor_dashboard_page"><?php _e('Vendor Dashboard Page', 'vendor'); ?></label>
                        </th>
                        <td>
                            <?php
                            wp_dropdown_pages(array(
                                'name' => 'vendor_dashboard_page',
                                'id' => 'vendor_dashboard_page',
                                'selected' => isset($general_settings['vendor_dashboard_page']) ? $general_settings['vendor_dashboard_page'] : get_option('vendor_dashboard_page_id'),
                                'show_option_none' => __('Select Page', 'vendor')
                            ));
                            ?>
                            <p class="description"><?php _e('Select the page that will serve as the vendor dashboard.', 'vendor'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="vendor_store_url"><?php _e('Store URL Structure', 'vendor'); ?></label>
                        </th>
                        <td>
                            <input type="text" name="vendor_store_url" id="vendor_store_url" value="<?php echo esc_attr(isset($general_settings['vendor_store_url']) ? $general_settings['vendor_store_url'] : 'store'); ?>" class="regular-text">
                            <p class="description"><?php _e('URL structure for vendor stores. Example: yoursite.com/store/vendor-name', 'vendor'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
        </div>
        
        <!-- Commission Settings -->
        <div class="tab-content" data-tab="commission">
            <form method="post" action="">
                <?php wp_nonce_field('vendor_settings'); ?>
                <input type="hidden" name="tab" value="commission">
                
                <table class="form-table vendor-form-table">
                    <tr>
                        <th scope="row">
                            <label for="default_commission_rate"><?php _e('Default Commission Rate (%)', 'vendor'); ?></label>
                        </th>
                        <td>
                            <input type="number" name="default_commission_rate" id="default_commission_rate" value="<?php echo esc_attr(isset($commission_settings['default_commission_rate']) ? $commission_settings['default_commission_rate'] : 10); ?>" min="0" max="100" step="0.01" class="small-text">
                            <p class="description"><?php _e('Default commission rate for new vendors.', 'vendor'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="commission_type"><?php _e('Commission Type', 'vendor'); ?></label>
                        </th>
                        <td>
                            <select name="commission_type" id="commission_type">
                                <option value="percentage" <?php selected(isset($commission_settings['commission_type']) ? $commission_settings['commission_type'] : 'percentage', 'percentage'); ?>>
                                    <?php _e('Percentage', 'vendor'); ?>
                                </option>
                                <option value="fixed" <?php selected(isset($commission_settings['commission_type']) ? $commission_settings['commission_type'] : 'percentage', 'fixed'); ?>>
                                    <?php _e('Fixed Amount', 'vendor'); ?>
                                </option>
                            </select>
                            <p class="description"><?php _e('How commission is calculated.', 'vendor'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="auto_approve_commissions"><?php _e('Auto Approve Commissions', 'vendor'); ?></label>
                        </th>
                        <td>
                            <input type="checkbox" name="auto_approve_commissions" id="auto_approve_commissions" value="yes" <?php checked(isset($commission_settings['auto_approve_commissions']) ? $commission_settings['auto_approve_commissions'] : 'yes', 'yes'); ?>>
                            <p class="description"><?php _e('Automatically approve commissions when orders are completed.', 'vendor'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
        </div>
        
        <!-- Withdrawal Settings -->
        <div class="tab-content" data-tab="withdrawal">
            <form method="post" action="">
                <?php wp_nonce_field('vendor_settings'); ?>
                <input type="hidden" name="tab" value="withdrawal">
                
                <table class="form-table vendor-form-table">
                    <tr>
                        <th scope="row">
                            <label for="minimum_withdrawal"><?php _e('Minimum Withdrawal Amount', 'vendor'); ?></label>
                        </th>
                        <td>
                            <input type="number" name="minimum_withdrawal" id="minimum_withdrawal" value="<?php echo esc_attr(isset($withdrawal_settings['minimum_withdrawal']) ? $withdrawal_settings['minimum_withdrawal'] : 50); ?>" min="0" step="0.01" class="small-text">
                            <p class="description"><?php _e('Minimum amount vendors can withdraw.', 'vendor'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="withdrawal_schedule"><?php _e('Withdrawal Schedule', 'vendor'); ?></label>
                        </th>
                        <td>
                            <select name="withdrawal_schedule" id="withdrawal_schedule">
                                <option value="instant" <?php selected(isset($withdrawal_settings['withdrawal_schedule']) ? $withdrawal_settings['withdrawal_schedule'] : 'manual', 'instant'); ?>>
                                    <?php _e('Instant', 'vendor'); ?>
                                </option>
                                <option value="daily" <?php selected(isset($withdrawal_settings['withdrawal_schedule']) ? $withdrawal_settings['withdrawal_schedule'] : 'manual', 'daily'); ?>>
                                    <?php _e('Daily', 'vendor'); ?>
                                </option>
                                <option value="weekly" <?php selected(isset($withdrawal_settings['withdrawal_schedule']) ? $withdrawal_settings['withdrawal_schedule'] : 'manual', 'weekly'); ?>>
                                    <?php _e('Weekly', 'vendor'); ?>
                                </option>
                                <option value="monthly" <?php selected(isset($withdrawal_settings['withdrawal_schedule']) ? $withdrawal_settings['withdrawal_schedule'] : 'manual', 'monthly'); ?>>
                                    <?php _e('Monthly', 'vendor'); ?>
                                </option>
                                <option value="manual" <?php selected(isset($withdrawal_settings['withdrawal_schedule']) ? $withdrawal_settings['withdrawal_schedule'] : 'manual', 'manual'); ?>>
                                    <?php _e('Manual Approval', 'vendor'); ?>
                                </option>
                            </select>
                            <p class="description"><?php _e('How often withdrawals are processed.', 'vendor'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="auto_withdrawal"><?php _e('Enable Auto Withdrawal', 'vendor'); ?></label>
                        </th>
                        <td>
                            <input type="checkbox" name="auto_withdrawal" id="auto_withdrawal" value="yes" <?php checked(isset($withdrawal_settings['auto_withdrawal']) ? $withdrawal_settings['auto_withdrawal'] : 'no', 'yes'); ?>>
                            <p class="description"><?php _e('Allow vendors to set up automatic withdrawals.', 'vendor'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
        </div>
        
        <!-- Payment Gateways -->
        <div class="tab-content" data-tab="gateways">
            <h3><?php _e('Payment Gateway Settings', 'vendor'); ?></h3>
            <p><?php _e('Configure payment gateways for vendor withdrawals.', 'vendor'); ?></p>
            
            <?php
            $gateways = vendor()->gateway_manager->get_gateways();
            foreach ($gateways as $gateway_id => $gateway) :
            ?>
            <div class="vendor-gateway-settings">
                <h4><?php echo esc_html($gateway->title); ?></h4>
                <p><?php echo esc_html($gateway->description); ?></p>
                
                <form method="post" action="">
                    <?php wp_nonce_field('vendor_gateway_settings_' . $gateway_id); ?>
                    <input type="hidden" name="gateway_id" value="<?php echo esc_attr($gateway_id); ?>">
                    
                    <table class="form-table">
                        <?php
                        $fields = vendor()->gateway_manager->get_gateway_settings_fields($gateway_id);
                        foreach ($fields as $field_id => $field) :
                        ?>
                        <tr>
                            <th scope="row">
                                <label for="<?php echo esc_attr($gateway_id . '_' . $field_id); ?>">
                                    <?php echo esc_html($field['title']); ?>
                                </label>
                            </th>
                            <td>
                                <?php
                                $field_name = $gateway_id . '_' . $field_id;
                                $field_value = isset($gateway->settings[$field_id]) ? $gateway->settings[$field_id] : $field['default'];
                                
                                switch ($field['type']) {
                                    case 'checkbox':
                                        echo '<input type="checkbox" name="' . esc_attr($field_name) . '" id="' . esc_attr($field_name) . '" value="yes" ' . checked($field_value, 'yes', false) . '>';
                                        break;
                                    case 'password':
                                        echo '<input type="password" name="' . esc_attr($field_name) . '" id="' . esc_attr($field_name) . '" value="' . esc_attr($field_value) . '" class="regular-text">';
                                        break;
                                    case 'number':
                                        echo '<input type="number" name="' . esc_attr($field_name) . '" id="' . esc_attr($field_name) . '" value="' . esc_attr($field_value) . '" class="small-text">';
                                        break;
                                    default:
                                        echo '<input type="text" name="' . esc_attr($field_name) . '" id="' . esc_attr($field_name) . '" value="' . esc_attr($field_value) . '" class="regular-text">';
                                        break;
                                }
                                
                                if (isset($field['description'])) {
                                    echo '<p class="description">' . esc_html($field['description']) . '</p>';
                                }
                                ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                    
                    <?php submit_button(__('Save Gateway Settings', 'vendor')); ?>
                </form>
            </div>
            <hr>
            <?php endforeach; ?>
        </div>
    </div>
</div>
