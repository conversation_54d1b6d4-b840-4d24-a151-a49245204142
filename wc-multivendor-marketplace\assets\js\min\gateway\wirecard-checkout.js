/*! DO NOT EDIT THIS FILE. This file is a auto generated on 2023-03-25 */
jQuery(function(o){jQuery(document).on("click","#place_order",function(){var r,e,c,a,i;if(!0===jQuery("input#payment_method_wirecard").prop("checked"))return r=o("#wirecard-card-number").val(),e=o("#wirecard-card-cvc").val(),c=(a=o("#wirecard-card-expiry").val()).substr(0,2),a=a.substr(5),MoipSdkJs.MoipValidator.isValidNumber(r)?MoipSdkJs.MoipValidator.isSecurityCodeValid(r,e)?MoipSdkJs.MoipValidator.isExpiryDateValid(c,a)?((i=jQuery("form.checkout, form#order_review")).find("input.wirecard_hash").val(""),MoipSdkJs.MoipCreditCard.setPubKey(wcfmmp_wirecard_params.public_key).setCreditCard({number:r,cvc:e,expirationMonth:c,expirationYear:a}).hash().then(function(r){i.find("input.wirecard_hash").remove(),i.append("<input type='hidden' class='wirecard_hash' name='wirecard_hash' value='"+r+"'/>"),i.submit()})):o("#wirecard-card-expiry").closest("p").before('<ul class="woocommerce_error woocommerce-error"><li>'+wcfmmp_wirecard_params.expriy_error+"</li></ul>"):o("#wirecard-card-cvc").closest("p").before('<ul class="woocommerce_error woocommerce-error"><li>'+wcfmmp_wirecard_params.cvc_error+"</li></ul>"):o("#wirecard-card-number").closest("p").before('<ul class="woocommerce_error woocommerce-error"><li>'+wcfmmp_wirecard_params.card_error+"</li></ul>"),!1})});