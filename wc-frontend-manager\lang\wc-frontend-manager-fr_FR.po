# Translation of Plugins - WooCommerce Frontend Manager &#8211; WC Marketplace, WC Vendors &amp; Dokan with Bookings &amp; Listings - Stable (latest release) in French (France)
# This file is distributed under the same license as the Plugins - WooCommerce Frontend Manager &#8211; WC Marketplace, WC Vendors &amp; Dokan with Bookings &amp; Listings - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2019-06-05 14:44+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Poedit 1.8.6\n"
"Language: fr_FR\n"
"Project-Id-Version: Plugins - WooCommerce Frontend Manager &#8211; WC "
"Marketplace, WC Vendors &amp; Dokan with Bookings &amp; Listings - Stable "
"(latest release)\n"
"POT-Creation-Date: 2018-10-30 21:42+0000\n"
"Last-Translator: admin <<EMAIL>>\n"
"Language-Team: French (France)\n"
"Report-Msgid-Bugs-To: \n"

#: core/class-wcfm-admin.php:100
msgid ""
"<h2>\n"
"\t\t\t\t\t\t\t\t\t\t           Is there anything missing in your front-end "
"dashboard !!!\n"
"\t\t\t\t\t\t\t\t               </h2>"
msgstr ""
"<h2>\n"
"\t\t\t\t\t\t\t\t\t\t           Est-ce qu'il manque quelque chose dans votre "
"tableau de bord frontal ???\n"
"\t\t\t\t\t\t\t\t               </h2>"

#: core/class-wcfm-admin.php:106
msgid ""
"<p>WooCommerce Frontend Manage - Ultimate is there to fill up all those for "
"you. Store Invoice, Support Ticket, Shipment Tracking, Direct Messaging, "
"Followers, Badges, Verificaton, Product Importer, Bulk Edit and many more, "
"almost a never ending features list for you.</p>"
msgstr ""

#: core/class-wcfm-admin.php:112
msgid "WCFM U >>"
msgstr "WCFM U >>"

#: core/class-wcfm-admin.php:148
msgid ""
"<h2>\n"
"\t\t\t\t\t\t\t\t\t\t           Now setup your vendor membership subscription "
"in minutes & it's FREE !!!\n"
"\t\t\t\t\t\t\t\t               </h2>"
msgstr ""
"<h2>Maintenant, installez votre abonnement fournisseur en quelques minutes "
"et c'est gratuit!!!\n"
"\t\t\t\t\t\t\t\t               </h2>"

#: core/class-wcfm-admin.php:151
msgid ""
"<p>A simple membership plugin for offering FREE AND PREMIUM SUBSCRIPTION for "
"your multi-vendor marketplace. You may set up unlimited membership levels "
"(example: free, silver, gold etc) with different pricing plan, capabilities "
"and commission. Also you will have Pay for Product option.</p>"
msgstr ""

#: core/class-wcfm-admin.php:157
#: controllers/customers/wcfm-controller-customers-details.php:163
#: controllers/customers/wcfm-controller-customers-details.php:360
#: controllers/customers/wcfm-controller-customers-details.php:533
#: controllers/orders/wcfm-controller-dokan-orders.php:253
#: controllers/orders/wcfm-controller-orders.php:231
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:325
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:307
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:289
#: controllers/orders/wcfm-controller-wcvendors-orders.php:338
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:206
#: views/capability/wcfm-view-capability.php:393
msgid "View Details"
msgstr "Voir les détails"

#: core/class-wcfm-admin.php:193
msgid ""
"<h2>\n"
"\t\t\t\t\t\t\t\t\t\t           Do you want to have different capabilities "
"for each membership levels !!!\n"
"\t\t\t\t\t\t\t\t               </h2>"
msgstr ""
"<h2>Voulez-vous avoir des capacités différentes pour chaque niveau "
"d'adhésion!!!\n"
"\t\t\t\t\t\t\t\t               </h2>"

#: core/class-wcfm-admin.php:199
msgid ""
"<p>WCFM - Groups & Staffs will empower you to set up totaly different "
"capability rules for your each membership levels very easily.</p>"
msgstr ""
"<p> WCFM-groupes et personnel vous permettra d'établir des règles de "
"capacité totalement différentes pour vos niveaux d'adhésion très facilement. "
"</p>"

#: core/class-wcfm-admin.php:205
msgid "WCFM GS >>"
msgstr "WCFM GS >>"

#: core/class-wcfm-admin.php:225 core/class-wcfm-admin.php:265
#: core/class-wcfm-admin.php:266 core/class-wcfm-admin.php:267
msgid "WCFM View"
msgstr "Vue WCFM"

#: core/class-wcfm-admin.php:284 core/class-wcfm-admin.php:294
#: core/class-wcfm-admin.php:296 core/class-wcfm-admin.php:298
#: core/class-wcfm-wcvendors.php:196
msgid "WCFM Home"
msgstr "Accueil WCFM"

#: core/class-wcfm-admin.php:343
#, php-format
msgid ""
"WCFM totally works from front-end ... check dashboard settings %shere >>%s"
msgstr ""
"WCFM fonctionne totalement en front-end... Vérifiez les paramètres du "
"tableau de bord %sici >> %s"

#: core/class-wcfm-admin.php:346
msgid "This page should contain \"[wc_frontend_manager]\" short code"
msgstr "Cette page doit contenir le code court \"[wc_frontend_manager]\""

#: core/class-wcfm-admin.php:382 views/settings/wcfm-view-settings.php:277
msgid ""
"DO NOT USE WCFM DASHBOARD PAGE FOR OTHER PAGE SETTINGS, you will break your "
"site if you do."
msgstr ""
"N'UTILISEZ PAS LA PAGE DE BORD DU TABLEAU DE BORD DE WCFM POUR D'AUTRES "
"PARAMÈTRES DE PAGE, vous cassez votre site si vous le faites."

#: core/class-wcfm-ajax.php:273
#, php-format
msgid "A new %s <b>%s</b> added to the store by <b>%s</b>"
msgstr "Un nouveau %s <b>%s</b> ajouté au magasin par <b>%s</b>"

#: core/class-wcfm-ajax.php:430 core/class-wcfm-ajax.php:464
#, php-format
msgid "<b>%s</b> order status updated to <b>%s</b> by <b>%s</b>"
msgstr "<b>%s</b>Etat de la commande passé à <b>%s</b> by <b>%s</b>"

#: core/class-wcfm-ajax.php:473
msgid "Order status updated."
msgstr "Etat de la commande Mis à jour"

#: core/class-wcfm-ajax.php:645
msgid "Email Verification Code"
msgstr "Code de vérification email"

#: core/class-wcfm-ajax.php:646 core/class-wcfm-notification.php:364
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:101
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:184
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:136
msgid "Hi"
msgstr "Bonjour"

#: core/class-wcfm-ajax.php:648
#, php-format
msgid "Here is your email verification code - <b>%s</b>"
msgstr "Votre code de vérification - <b>%s</b>"

#: core/class-wcfm-ajax.php:649 core/class-wcfm-notification.php:371
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:108
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:191
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:144
msgid "Thank You"
msgstr "Merci"

#: core/class-wcfm-ajax.php:659
msgid "Email verification code send to your email."
msgstr "Votre code de vérificatin est envoyé par email"

#: core/class-wcfm-ajax.php:661
msgid "Email verification not working right now, please try after sometime."
msgstr "La vérification n'a pas fonctionné, réessayez plus tard"

#: core/class-wcfm-ajax.php:685
#, php-format
msgid "<b>%s</b> (Store: <b>%s</b>) has been disabled."
msgstr "<b>%s</b> (Boutique: <b>%s</b>) a été désactivée"

#: core/class-wcfm-ajax.php:689
#, php-format
msgid "Your Store: <b>%s</b> has been disabled."
msgstr "Votre boutique: <b>%s</b> a été désactivée"

#: core/class-wcfm-ajax.php:692
msgid "Vendor successfully disabled."
msgstr ""

#: core/class-wcfm-ajax.php:695
msgid "Vendor can not be disabled right now, please try after sometime."
msgstr ""

#: core/class-wcfm-ajax.php:726
#, php-format
msgid "<b>%s</b> (Store: <b>%s</b>) has been enabled."
msgstr ""

#: core/class-wcfm-ajax.php:730
#, php-format
msgid "Your Store: <b>%s</b> has been enabled."
msgstr ""

#: core/class-wcfm-ajax.php:734
msgid "Vendor successfully enabled."
msgstr ""

#: core/class-wcfm-ajax.php:737
msgid "Vendor can not be enabled right now, please try after sometime."
msgstr ""

#: core/class-wcfm-article.php:71
msgid "Articles Dashboard"
msgstr "Tableau de bord des articles"

#: core/class-wcfm-article.php:74
msgid "Articles Manager"
msgstr "Responsable des Articles"

#: core/class-wcfm-article.php:118 views/articles/wcfm-view-articles.php:32
#: views/capability/wcfm-view-capability.php:308
msgid "Articles"
msgstr "Articles"

#: core/class-wcfm-capability.php:295
msgid "Products Limit: "
msgstr "Limite Produits "

#: core/class-wcfm-capability.php:303 core/class-wcfm-capability.php:308
#: core/class-wcfm-capability.php:311 core/class-wcfm-capability.php:522
#: core/class-wcfm-capability.php:527 core/class-wcfm-capability.php:531
#: core/class-wcfm-capability.php:847 core/class-wcfm-capability.php:852
#: core/class-wcfm-capability.php:868
msgid "remaining"
msgstr "restant"

#: core/class-wcfm-capability.php:314 core/class-wcfm-capability.php:534
#: core/class-wcfm-capability.php:871
#: core/class-wcfm-thirdparty-support.php:380
#: views/products-manager/wcfm-view-products-manage-tabs.php:52
msgid "Unlimited"
msgstr "Illimité"

#: core/class-wcfm-capability.php:518
msgid "Articles Limit: "
msgstr ""

#: core/class-wcfm-capability.php:843
msgid "Customers Limit: "
msgstr ""

#: core/class-wcfm-catalog.php:41 core/class-wcfm.php:736
#: views/capability/wcfm-view-capability.php:212
msgid "Catalog"
msgstr "Catalogue"

#: core/class-wcfm-catalog.php:63
msgid "Catalog Mode"
msgstr "Mode catalogue"

#: core/class-wcfm-catalog.php:68
msgid "Disable Add to Cart?"
msgstr "Désactiver ajouter au panier?"

#: core/class-wcfm-catalog.php:69
msgid "Hide Price?"
msgstr "Cacher le prix?"

#: core/class-wcfm-customer.php:85
msgid "Customers Dashboard"
msgstr "L'espace des clients"

#: core/class-wcfm-customer.php:88
msgid "Customers Manager"
msgstr "Gestionnaire client"

#: core/class-wcfm-customer.php:91
msgid "Customers Details"
msgstr "Détails des clients"

#: core/class-wcfm-customer.php:136
#: views/capability/wcfm-view-capability.php:415
#: views/customers/wcfm-view-customers.php:25
msgid "Customers"
msgstr "Clients"

#: core/class-wcfm-customer.php:331
msgid "New Customer"
msgstr "Nouveau client"

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:145
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:385
#: views/products-manager/wcfm-view-products-manage.php:390
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/products-popup/wcfm-view-product-popup.php:97
#: views/settings/wcfm-view-settings.php:506
msgid "Simple Product"
msgstr "Produit simple"

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:145
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:385
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/settings/wcfm-view-settings.php:506
msgid "Variable Product"
msgstr "Produit Variable"

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:145
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:385
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/settings/wcfm-view-settings.php:506
msgid "Grouped Product"
msgstr "Produits groupés"

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:145
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:385
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/settings/wcfm-view-settings.php:506
msgid "External/Affiliate Product"
msgstr "Prestation externe ou d'affiliation"

#: core/class-wcfm-customfield-support.php:32
msgid "Do not show"
msgstr "Ne pas afficher"

#: core/class-wcfm-customfield-support.php:38
#: core/class-wcfm-customfield-support.php:42
msgid "Product Custom Field"
msgstr "Champ personnalisé du produit"

#: core/class-wcfm-customfield-support.php:47
msgid "Custom Fields"
msgstr "Champs personnalisés"

#: core/class-wcfm-customfield-support.php:47
msgid ""
"You can integrate any Third Party plugin using Custom Fields, but you should "
"use the same fields name as used by Third Party plugins."
msgstr ""
"Vous pouvez intégrer n'importe quel plugin tiers en utilisant des champs "
"personnalisés, mais vous devez utiliser le même nom de champs comme utilisé "
"par les plugins tierce partie."

#: core/class-wcfm-customfield-support.php:48
#: views/products-manager/wcfm-view-products-manage-tabs.php:237
#: views/settings/wcfm-view-settings.php:420
#: views/settings/wcfm-view-settings.php:482
msgid "Enable"
msgstr "Activer"

#: core/class-wcfm-customfield-support.php:49
msgid "Block Name"
msgstr "Nom du bloc"

#: core/class-wcfm-customfield-support.php:50
msgid "Exlude Product Types"
msgstr "Type de produit à exclure"

#: core/class-wcfm-customfield-support.php:50
msgid "Choose product types for which you want to disable this field block."
msgstr ""
"Choisissez les types de produits pour lesquels vous voulez désactiver ce "
"bloc de champ."

#: core/class-wcfm-customfield-support.php:51
msgid "Visibility"
msgstr "Visibilité"

#: core/class-wcfm-customfield-support.php:51
msgid ""
"Set where and how you want to visible this custom field block in single "
"product page."
msgstr ""
"Définissez où et comment vous souhaitez afficher ce bloc de champs "
"personnalisé dans une page de produit unique."

#: core/class-wcfm-customfield-support.php:52
msgid "Fields as Group?"
msgstr "Champs en tant que groupe?"

#: core/class-wcfm-customfield-support.php:53
msgid "Group name"
msgstr "Nom du groupe"

#: core/class-wcfm-customfield-support.php:54
msgid "Fields"
msgstr "Champs"

#: core/class-wcfm-customfield-support.php:55
#: views/settings/wcfm-view-settings.php:483
msgid "Field Type"
msgstr "Type de champ"

#: core/class-wcfm-customfield-support.php:56
#: views/settings/wcfm-view-settings.php:421
#: views/settings/wcfm-view-settings.php:484
msgid "Label"
msgstr "Étiquette"

#: core/class-wcfm-customfield-support.php:57 core/class-wcfm-frontend.php:736
#: views/articles/wcfm-view-articles.php:110
#: views/articles/wcfm-view-articles.php:121
#: views/customers/wcfm-view-customers.php:59
#: views/customers/wcfm-view-customers.php:74
#: views/enquiry/wcfm-view-enquiry-form.php:56
#: views/products/wcfm-view-products.php:198
#: views/products/wcfm-view-products.php:221
#: views/products-manager/wcfm-view-products-manage-tabs.php:47
#: views/products-manager/wcfm-view-products-manage-tabs.php:139
msgid "Name"
msgstr "Nom"

#: core/class-wcfm-customfield-support.php:57
msgid ""
"This is will going to use as `meta_key` for storing this field value in "
"database."
msgstr ""
"Ceci est va utiliser comme'meta_key'pour stocker cette valeur de champ dans "
"la base de données."

#: core/class-wcfm-customfield-support.php:58
#: views/settings/wcfm-view-settings.php:485
msgid "Options"
msgstr "Options"

#: core/class-wcfm-customfield-support.php:58
msgid ""
"Insert option values | separated, leave first element empty to show as '-"
"Select-'"
msgstr ""
"Insérer des valeurs d'option | séparé, laissez le premier élément vide pour "
"afficher comme'-Select-'"

#: core/class-wcfm-customfield-support.php:59
#: views/settings/wcfm-view-settings.php:486
msgid "Help Content"
msgstr "Contenu d'Aide"

#: core/class-wcfm-customfield-support.php:60
#: views/settings/wcfm-view-settings.php:487
msgid "Required?"
msgstr "Obligatoire ?"

#: core/class-wcfm-dokan.php:124 core/class-wcfm-wcfmmarketplace.php:118
#: core/class-wcfm-wcmarketplace.php:136 core/class-wcfm-wcmarketplace.php:137
#: core/class-wcfm-wcpvendors.php:114 core/class-wcfm-wcvendors.php:151
#: views/wcfm-view-header-panels.php:41 views/wcfm-view-menu.php:68
#: views/settings/wcfm-view-settings.php:36
msgid "My Store"
msgstr "Mon magasin"

#: core/class-wcfm-dokan.php:128 core/class-wcfm-vendor-support.php:1050
#: core/class-wcfm-vendor-support.php:1058
#: core/class-wcfm-vendor-support.php:1066
#: core/class-wcfm-vendor-support.php:1077
#: core/class-wcfm-vendor-support.php:1087
#: core/class-wcfm-wcfmmarketplace.php:122
#: core/class-wcfm-wcmarketplace.php:140 core/class-wcfm-wcpvendors.php:118
#: core/class-wcfm-wcvendors.php:155
msgid "Shop"
msgstr "Boutique"

#: core/class-wcfm-dokan.php:449 core/class-wcfm-wcfmmarketplace.php:683
#: core/class-wcfm-wcmarketplace.php:683 core/class-wcfm-wcpvendors.php:532
#: core/class-wcfm-wcvendors.php:859
msgid "Total Earning"
msgstr "Total des gains"

#: core/class-wcfm-enquiry.php:118
msgid "Enquiry Dashboard"
msgstr "Tableau de bord des demandes de renseignements"

#: core/class-wcfm-enquiry.php:121
msgid "Enquiry Manager"
msgstr "Gestionnaire des demandes de renseignements"

#: core/class-wcfm-enquiry.php:287 core/class-wcfm-enquiry.php:301
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:68
msgid "Inquiries"
msgstr "Questions"

#: core/class-wcfm-enquiry.php:309 core/class-wcfm-enquiry.php:417
#: helpers/class-wcfm-install.php:317
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:183
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:190
#: views/enquiry/wcfm-view-enquiry-form.php:36
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:65
msgid "Inquiry"
msgstr "Question"

#: core/class-wcfm-enquiry.php:366 core/class-wcfm-enquiry.php:450
#: helpers/wcfm-core-functions.php:1109
#: views/enquiry/wcfm-view-enquiry-manage.php:75
#: views/enquiry/wcfm-view-enquiry-tab.php:52
#: views/enquiry/wcfm-view-enquiry.php:43
msgid "Enquiries"
msgstr "Renseignements"

#: core/class-wcfm-enquiry.php:388
#: controllers/settings/wcfm-controller-settings.php:188
#: includes/shortcodes/class-wcfm-shortcode-enquiry.php:33
#: views/enquiry/wcfm-view-enquiry-tab.php:21
#: views/settings/wcfm-view-settings.php:52
msgid "Ask a Question"
msgstr "Poser une question"

#: core/class-wcfm-enquiry.php:462 core/class-wcfm-notification.php:216
msgid "Show All"
msgstr "Afficher tout"

#: core/class-wcfm-enquiry.php:465
msgid "There is no enquiry yet!!"
msgstr "Il n’y a pas encore de demandes !!"

#: core/class-wcfm-enquiry.php:494
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:159
msgid "New Enquiry"
msgstr "Nouvelle demande de renseignement"

#: core/class-wcfm-frontend.php:271 views/dashboard/wcfm-view-dashboard.php:97
#: views/dashboard/wcfm-view-dokan-dashboard.php:123
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:126
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:144
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:148
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:130
#: views/settings/wcfm-view-settings.php:138
#: views/settings/wcfm-view-settings.php:277
msgid "Dashboard"
msgstr "Panel"

#: core/class-wcfm-frontend.php:306
#: controllers/articles/wcfm-controller-articles.php:161
#: controllers/articles/wcfm-controller-articles.php:164
#: controllers/coupons/wcfm-controller-coupons.php:98
#: controllers/coupons/wcfm-controller-coupons.php:100
#: controllers/enquiry/wcfm-controller-enquiry.php:167
#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:105
#: controllers/listings/wcfm-controller-listings.php:130
#: controllers/notice/wcfm-controller-notices.php:80
#: controllers/products/wcfm-controller-products.php:361
#: controllers/products/wcfm-controller-products.php:364
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:107
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:110
#: views/notice/wcfm-view-notice-view.php:59
msgid "Edit"
msgstr "Editer"

#: core/class-wcfm-frontend.php:310
#: controllers/articles/wcfm-controller-articles.php:162
#: controllers/articles/wcfm-controller-articles.php:165
#: controllers/enquiry/wcfm-controller-enquiry.php:170
#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:106
#: controllers/listings/wcfm-controller-listings.php:156
#: controllers/messages/wcfm-controller-messages.php:222
#: controllers/notice/wcfm-controller-notices.php:81
#: controllers/products/wcfm-controller-products.php:362
#: controllers/products/wcfm-controller-products.php:365
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:108
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:111
#: views/enquiry/wcfm-view-enquiry-manage.php:158
#: views/messages/wcfm-view-messages.php:99
msgid "Delete"
msgstr "Supprimer"

#: core/class-wcfm-frontend.php:735
#: views/products-manager/wcfm-view-products-manage-tabs.php:138
msgid "Active?"
msgstr "Actif ?"

#: core/class-wcfm-frontend.php:735
msgid "Check to associate this attribute with the product"
msgstr ""

#: core/class-wcfm-frontend.php:737
#: views/products-manager/wcfm-view-products-manage-tabs.php:140
msgid "Value(s):"
msgstr "Valeurs:"

#: core/class-wcfm-frontend.php:738
#: views/products-manager/wcfm-view-products-manage-tabs.php:141
msgid "Visible on the product page"
msgstr "Visible sur la page du produit"

#: core/class-wcfm-frontend.php:739
#: views/products-manager/wcfm-view-products-manage-tabs.php:142
msgid "Use as Variation"
msgstr "Utiliser comme variation"

#: core/class-wcfm-library.php:373
msgid "Select an option&hellip;"
msgstr ""

#: core/class-wcfm-library.php:464
msgid "Memebership"
msgstr "Adhérent"

#: core/class-wcfm-library.php:880
msgid "Processing..."
msgstr "En cours..."

#: core/class-wcfm-library.php:880
msgid "Search:"
msgstr "Recherche:"

#: core/class-wcfm-library.php:880
msgid "Show _MENU_ entries"
msgstr "Afficher _MENU_ éléments"

#: core/class-wcfm-library.php:880
msgid "Showing _START_ to _END_ of _TOTAL_ entries"
msgstr "Affichage de _START_ à _END_ sur _TOTAL_ éléments"

#: core/class-wcfm-library.php:880
msgid "Showing 0 to 0 of 0 entries"
msgstr ""
"Affichage de l’&eacute;lement 0 &agrave; 0 sur 0 &eacute;l&eacute;ments"

#: core/class-wcfm-library.php:880
msgid "(filtered _MAX_ entries of total)"
msgstr "(Filtré_MAX_entrées au total)"

#: core/class-wcfm-library.php:880
msgid "Loading..."
msgstr "Chargement..."

#: core/class-wcfm-library.php:880
msgid "No matching records found"
msgstr "Aucun enregistrements correspondants trouvés"

#: core/class-wcfm-library.php:880
msgid "No data in the table"
msgstr "Aucune donnée dans la table"

#: core/class-wcfm-library.php:880
msgid "First"
msgstr "Premier"

#: core/class-wcfm-library.php:880 core/class-wcfm-library.php:1080
#: helpers/wcfm-core-functions.php:969
msgid "Previous"
msgstr "Précédent"

#: core/class-wcfm-library.php:880 core/class-wcfm-library.php:1086
#: helpers/wcfm-core-functions.php:968
msgid "Next"
msgstr "Suivant"

#: core/class-wcfm-library.php:880
msgid "Last"
msgstr "Dernier"

#: core/class-wcfm-library.php:880
msgid "Print"
msgstr "Imprimer"

#: core/class-wcfm-library.php:880
msgid "PDF"
msgstr "PDF"

#: core/class-wcfm-library.php:880
msgid "Excel"
msgstr "Excel"

#: core/class-wcfm-library.php:880
msgid "CSV"
msgstr "CSV"

#: core/class-wcfm-library.php:984
msgid "Choose Media"
msgstr "Choisissez un média"

#: core/class-wcfm-library.php:984
msgid "Choose Image"
msgstr "Choisir une image"

#: core/class-wcfm-library.php:984
msgid "Add to Gallery"
msgstr "Ajouter à la galerie"

#: core/class-wcfm-library.php:1050
msgid "Selected:"
msgstr ""

#: core/class-wcfm-library.php:1051
msgid "Day"
msgstr "Jour"

#: core/class-wcfm-library.php:1052
msgid "Days"
msgstr "Jours"

#: core/class-wcfm-library.php:1053
msgid "Close"
msgstr "Fermer"

#: core/class-wcfm-library.php:1054
msgid "mo"
msgstr "Lun"

#: core/class-wcfm-library.php:1055
msgid "tu"
msgstr "Mar"

#: core/class-wcfm-library.php:1056
msgid "we"
msgstr "Mer"

#: core/class-wcfm-library.php:1057
msgid "th"
msgstr "Jeu"

#: core/class-wcfm-library.php:1058
msgid "fr"
msgstr "Ven"

#: core/class-wcfm-library.php:1059
msgid "sa"
msgstr "Sam"

#: core/class-wcfm-library.php:1060
msgid "su"
msgstr "Dim"

#: core/class-wcfm-library.php:1061
msgid "W"
msgstr ""

#: core/class-wcfm-library.php:1063
msgid "january"
msgstr "Janvier"

#: core/class-wcfm-library.php:1064
msgid "february"
msgstr "Février"

#: core/class-wcfm-library.php:1065
msgid "march"
msgstr "Mars"

#: core/class-wcfm-library.php:1066
msgid "april"
msgstr "Avril"

#: core/class-wcfm-library.php:1067
msgid "may"
msgstr "Mai"

#: core/class-wcfm-library.php:1068
msgid "june"
msgstr "Juin"

#: core/class-wcfm-library.php:1069
msgid "july"
msgstr "Juillet"

#: core/class-wcfm-library.php:1070
msgid "august"
msgstr "Août"

#: core/class-wcfm-library.php:1071
msgid "september"
msgstr "Septembre"

#: core/class-wcfm-library.php:1072
msgid "october"
msgstr "Octobre"

#: core/class-wcfm-library.php:1073
msgid "november"
msgstr "Novembre"

#: core/class-wcfm-library.php:1074
msgid "december"
msgstr "Décembre"

#: core/class-wcfm-library.php:1076
msgid "Shortcuts"
msgstr ""

#: core/class-wcfm-library.php:1077
msgid "Custom Values"
msgstr ""

#: core/class-wcfm-library.php:1078
msgid "Past"
msgstr ""

#: core/class-wcfm-library.php:1079
msgid "Following"
msgstr ""

#: core/class-wcfm-library.php:1081 core/class-wcfm-library.php:1087
msgid "Week"
msgstr ""

#: core/class-wcfm-library.php:1082 core/class-wcfm-library.php:1088
msgid "Month"
msgstr ""

#: core/class-wcfm-library.php:1083
msgid "This Week"
msgstr ""

#: core/class-wcfm-library.php:1084
#: includes/reports/class-wcfm-report-analytics.php:143
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:285
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:261
#: includes/reports/class-wcvendors-report-sales-by-date.php:283
#: views/enquiry/wcfm-view-enquiry.php:25
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:31
#: views/reports/wcfm-view-reports-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:31
msgid "This Month"
msgstr "Ce mois-ci"

#: core/class-wcfm-library.php:1085 core/class-wcfm-library.php:1089
#: includes/reports/class-wcfm-report-analytics.php:141
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:283
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:259
#: includes/reports/class-wcvendors-report-sales-by-date.php:281
#: views/enquiry/wcfm-view-enquiry.php:27
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:29
#: views/reports/wcfm-view-reports-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:29
msgid "Year"
msgstr "Année"

#: core/class-wcfm-library.php:1090
#, php-format
msgid "Date range should not be more than %d days"
msgstr ""

#: core/class-wcfm-library.php:1091
#, php-format
msgid "Date range should not be less than %d days"
msgstr ""

#: core/class-wcfm-library.php:1092
#, php-format
msgid "Please select a date range longer than %d days"
msgstr ""

#: core/class-wcfm-library.php:1093
msgid "Please select a date"
msgstr ""

#: core/class-wcfm-library.php:1094
#, php-format
msgid "Please select a date range less than %d days"
msgstr ""

#: core/class-wcfm-library.php:1095
#, php-format
msgid "Please select a date range between %d and %d days"
msgstr ""

#: core/class-wcfm-library.php:1096
msgid "Please select a date range"
msgstr ""

#: core/class-wcfm-library.php:1097
msgid "Time"
msgstr ""

#: core/class-wcfm-library.php:1098
msgid "Hour"
msgstr ""

#: core/class-wcfm-library.php:1099
msgid "Minute"
msgstr ""

#: core/class-wcfm-library.php:1108
msgid "Choose Data Range"
msgstr ""

#: core/class-wcfm-non-ajax.php:53
msgid "Online"
msgstr "En ligne"

#: core/class-wcfm-non-ajax.php:53
#: controllers/listings/wcfm-controller-listings.php:25
#: controllers/products/wcfm-controller-products.php:27
#: views/articles/wcfm-view-articles.php:13
#: views/listings/wcfm-view-listings.php:38
#: views/products/wcfm-view-products.php:13
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:44
msgid "Pending"
msgstr "En attente"

#: core/class-wcfm-non-ajax.php:53
#: controllers/products/wcfm-controller-products.php:26
#: views/articles/wcfm-view-articles-manage.php:374
#: views/articles/wcfm-view-articles.php:12
#: views/coupons/wcfm-view-coupons-manage.php:126
#: views/products/wcfm-view-products.php:12
#: views/products-manager/wcfm-view-products-manage.php:785
#: views/products-popup/wcfm-view-product-popup.php:289
msgid "Draft"
msgstr "Brouillon"

#: core/class-wcfm-non-ajax.php:138
msgid "No sales yet ..!!!"
msgstr "Pas encore de vente..!!!"

#: core/class-wcfm-non-ajax.php:188
msgid "View WCFM settings"
msgstr "Afficher les paramètres WCFM"

#: core/class-wcfm-non-ajax.php:188 core/class-wcfm-query.php:169
#: core/class-wcfm.php:642 helpers/class-wcfm-install.php:321
#: views/capability/wcfm-view-capability.php:150
#: views/settings/wcfm-view-dokan-settings.php:129
#: views/settings/wcfm-view-settings.php:83
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:180
#: views/settings/wcfm-view-wcmarketplace-settings.php:136
#: views/settings/wcfm-view-wcpvendors-settings.php:73
#: views/settings/wcfm-view-wcvendors-settings.php:120
msgid "Settings"
msgstr "Réglages"

#: core/class-wcfm-non-ajax.php:195 core/class-wcfm-non-ajax.php:222
msgid "Add more power to your WCFM"
msgstr "Ajouter plus de puissance à votre WCFM"

#: core/class-wcfm-non-ajax.php:195 core/class-wcfm-non-ajax.php:222
msgid "WCFM Ultimate"
msgstr "WCFM Ultime"

#: core/class-wcfm-non-ajax.php:213
msgid "View WCFM documentation"
msgstr "Afficher documentation de WCFM"

#: core/class-wcfm-non-ajax.php:213 views/settings/wcfm-view-settings.php:97
msgid "Documentation"
msgstr "Documentation"

#: core/class-wcfm-non-ajax.php:215
msgid "View WCFM Video Tutorial"
msgstr ""

#: core/class-wcfm-non-ajax.php:215 views/settings/wcfm-view-settings.php:96
msgid "Video Tutorial"
msgstr ""

#: core/class-wcfm-non-ajax.php:217
msgid "Any WC help feel free to contact us"
msgstr ""

#: core/class-wcfm-non-ajax.php:217
msgid "Customization Help"
msgstr ""

#: core/class-wcfm-non-ajax.php:277 core/class-wcfm-thirdparty-support.php:311
#: helpers/wcfm-core-functions.php:945 views/wcfm-view-menu.php:128
#: views/articles/wcfm-view-articles-manage.php:141
#: views/articles/wcfm-view-articles.php:65
#: views/coupons/wcfm-view-coupons-manage.php:77
#: views/coupons/wcfm-view-coupons.php:43
#: views/customers/wcfm-view-customers-details.php:107
#: views/customers/wcfm-view-customers-manage.php:113
#: views/customers/wcfm-view-customers.php:42
#: views/knowledgebase/wcfm-view-knowledgebase.php:38
#: views/listings/wcfm-view-listings.php:91
#: views/notice/wcfm-view-notices.php:37
#: views/products/wcfm-view-products-export.php:64
#: views/products/wcfm-view-products.php:115
#: views/products-manager/wcfm-view-products-manage.php:441
#: views/vendors/wcfm-view-vendors-manage.php:189
#: views/vendors/wcfm-view-vendors-new.php:75
#: views/vendors/wcfm-view-vendors.php:28
msgid "Add New"
msgstr "Ajouter"

#: core/class-wcfm-notification.php:62
#, php-format
msgid "A new product <b>%s</b> added by <b>%s</b>"
msgstr "Un nouveau produit <b> %s </b> ajouté par <b> %s </b>"

#: core/class-wcfm-notification.php:85
#, php-format
msgid "Product <b>%s</b> has been approved."
msgstr ""

#: core/class-wcfm-notification.php:105
#, php-format
msgid "Product <b>%s</b> awaiting for review"
msgstr ""

#: core/class-wcfm-notification.php:131
#, php-format
msgid "You have received an Order <b>#%s</b>"
msgstr "Vous avez reçu une commande <b>#%s</b>"

#: core/class-wcfm-notification.php:147
#, php-format
msgid "You have received an Order <b>#%s</b> for <b>%s</b>"
msgstr "Vous avez reçu une réservation de <b>#%s</b> pour <b>%s</b>"

#: core/class-wcfm-notification.php:196 core/class-wcfm.php:739
#: views/messages/wcfm-view-messages.php:93
msgid "Notifications"
msgstr "Notifications"

#: core/class-wcfm-notification.php:219
msgid "There is no notification yet!!"
msgstr "Il n'y a pas encore de notification !!"

#: core/class-wcfm-notification.php:362 core/class-wcfm-notification.php:377
msgid "Notification"
msgstr "Notification"

#: core/class-wcfm-notification.php:366
msgid "You have received a new notification:"
msgstr "Vous avez reçu une nouvelle notification :"

#: core/class-wcfm-notification.php:370
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:107
#, php-format
msgid "Check more details %shere%s."
msgstr "Vérifiez plus de détails %shere %s."

#: core/class-wcfm-policy.php:63 core/class-wcfm-policy.php:176
msgid "Store Policies"
msgstr ""

#: core/class-wcfm-policy.php:67 core/class-wcfm-policy.php:180
msgid "Policies Setting"
msgstr ""

#: core/class-wcfm-policy.php:79 core/class-wcfm-policy.php:191
#: core/class-wcfm-policy.php:335
msgid "Policy Tab Label"
msgstr "Label de l'onglet politiques"

#: core/class-wcfm-policy.php:80 core/class-wcfm-policy.php:192
#: core/class-wcfm-policy.php:336 core/class-wcfm-policy.php:587
#: views/settings/wcfm-view-wcvendors-settings.php:398
msgid "Shipping Policy"
msgstr "Politique de livraison"

#: core/class-wcfm-policy.php:81 core/class-wcfm-policy.php:193
#: core/class-wcfm-policy.php:337 core/class-wcfm-policy.php:592
#: views/settings/wcfm-view-wcvendors-settings.php:399
msgid "Refund Policy"
msgstr "Politique de remboursement"

#: core/class-wcfm-policy.php:82 core/class-wcfm-policy.php:194
#: core/class-wcfm-policy.php:338
msgid "Cancellation/Return/Exchange Policy"
msgstr "Retour/Annulation/Echange"

#: core/class-wcfm-policy.php:323
msgid "Product Policies"
msgstr "CGV Produit"

#: core/class-wcfm-policy.php:420
msgid "Store Polices"
msgstr "CGV Boutique"

#: core/class-wcfm-policy.php:597
msgid "Cancellation / Return / Exchange Policy"
msgstr "Retour / Annulation / Echange"

#: core/class-wcfm-query.php:116
msgid "Products Dashboard"
msgstr "Tableau de bord des produits"

#: core/class-wcfm-query.php:121
#, php-format
msgid "Product Manager -%s"
msgstr "Gestionnaire de produits -%s"

#: core/class-wcfm-query.php:121
msgid "Product Manager"
msgstr "Gestionnaire de produits"

#: core/class-wcfm-query.php:124
msgid "Products Stock Manager"
msgstr "Responsable des stocks produits"

#: core/class-wcfm-query.php:127
#: views/products/wcfm-view-products-export.php:58
#: views/products/wcfm-view-products.php:90
#: views/products/wcfm-view-products.php:95
msgid "Products Import"
msgstr "Import des produits"

#: core/class-wcfm-query.php:130
#: views/products/wcfm-view-products-export.php:33
#: views/products/wcfm-view-products.php:82
msgid "Products Export"
msgstr "Exportation de produits"

#: core/class-wcfm-query.php:133
msgid "Coupons Dashboard"
msgstr "Tableau de bord des bons de réduction"

#: core/class-wcfm-query.php:138
#, php-format
msgid "Coupon Manager -%s"
msgstr "Gestionnaire de coupon-%s"

#: core/class-wcfm-query.php:138
msgid "Coupon Manager"
msgstr "Gestionnaire de coupons"

#: core/class-wcfm-query.php:141
msgid "Orders Dashboard"
msgstr "Tableau de bord des commandes"

#: core/class-wcfm-query.php:145
#, php-format
msgid "Order Details #%s"
msgstr "Détails de commande #%s"

#: core/class-wcfm-query.php:145 views/orders/wcfm-view-orders-details.php:115
msgid "Order Details"
msgstr "Détails de la commande"

#: core/class-wcfm-query.php:148
msgid "Reports - Sales by Date"
msgstr "Rapports - ventes par Date"

#: core/class-wcfm-query.php:151
msgid "Reports - Sales by Product"
msgstr "Rapports - ventes par produit"

#: core/class-wcfm-query.php:154
msgid "Reports - Coupons by Date"
msgstr "Rapports - Coupons par Date"

#: core/class-wcfm-query.php:157
msgid "Reports - Out of Stock"
msgstr "Rapports - épuisé"

#: core/class-wcfm-query.php:160
msgid "Reports - Low in Stock"
msgstr "Stock faible"

#: core/class-wcfm-query.php:163 helpers/class-wcfm-install.php:334
#: views/settings/wcfm-view-settings.php:204
#: views/settings/wcfm-view-settings.php:210
msgid "Analytics"
msgstr "Analytics"

#: core/class-wcfm-query.php:166 core/class-wcfm.php:741
#: helpers/class-wcfm-install.php:330 views/wcfm-view-header-panels.php:57
#: views/profile/wcfm-view-profile.php:173
#: views/settings/wcfm-view-wcpvendors-settings.php:112
msgid "Profile"
msgstr "Profil"

#: core/class-wcfm-query.php:172 core/class-wcfm.php:740
#: views/wcfm-view-header-panels.php:73
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:66
#: views/knowledgebase/wcfm-view-knowledgebase.php:26
msgid "Knowledgebase"
msgstr "Base de connaissance"

#: core/class-wcfm-query.php:175
msgid "Knowledgebase Manager"
msgstr "Gestionnaire de la base de connaissances"

#: core/class-wcfm-query.php:178
msgid "Notice Dashboard"
msgstr "Tableau de bord des avis"

#: core/class-wcfm-query.php:181
msgid "Notice Manager"
msgstr "Responsable des avis"

#: core/class-wcfm-query.php:184 core/class-wcfm.php:742
msgid "Notice"
msgstr "Avis"

#: core/class-wcfm-query.php:187
msgid "Message Dashboard"
msgstr "Tableau de bord des messages"

#: core/class-wcfm-thirdparty-support.php:157
msgid "Listings Dashboard"
msgstr "Liste  Tableau de bord"

#: core/class-wcfm-thirdparty-support.php:212
#: core/class-wcfm-thirdparty-support.php:313
#: helpers/class-wcfm-setup-bak.php:950 helpers/class-wcfm-setup.php:978
#: views/listings/wcfm-view-listings.php:27
msgid "Listings"
msgstr "Annonces"

#: core/class-wcfm-thirdparty-support.php:241
msgid "Rental Product"
msgstr "Produit en Location"

#: core/class-wcfm-thirdparty-support.php:248
#: core/class-wcfm-thirdparty-support.php:276
#: core/class-wcfm-thirdparty-support.php:462
#: controllers/products/wcfm-controller-products.php:276
msgid "Auction"
msgstr "Enchère"

#: core/class-wcfm-thirdparty-support.php:264
msgid "Listing Package"
msgstr "Liste de l'abonnement"

#: core/class-wcfm-thirdparty-support.php:270
#: core/class-wcfm-thirdparty-support.php:412
#: controllers/products/wcfm-controller-products.php:278
msgid "Rental"
msgstr "Location"

#: core/class-wcfm-thirdparty-support.php:296
#: core/class-wcfm-thirdparty-support.php:310
#: core/class-wcfm-thirdparty-support.php:313
msgid "Manage Listings"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:302
msgid "Edit Listing"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:304
msgid "Add Listing"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:307
#: views/articles/wcfm-view-articles-manage.php:136
#: views/articles/wcfm-view-articles.php:60
#: views/coupons/wcfm-view-coupons-manage.php:72
#: views/coupons/wcfm-view-coupons.php:39
#: views/customers/wcfm-view-customers-details.php:96
#: views/customers/wcfm-view-customers-manage.php:106
#: views/customers/wcfm-view-customers.php:37
#: views/listings/wcfm-view-listings.php:86
#: views/orders/wcfm-view-orders-details.php:128
#: views/orders/wcfm-view-orders.php:68
#: views/products/wcfm-view-products-export.php:45
#: views/products/wcfm-view-products.php:76
#: views/products-manager/wcfm-view-products-manage.php:436
#: views/reports/wcfm-view-reports-out-of-stock.php:37
#: views/reports/wcfm-view-reports-sales-by-date.php:78
#: views/wc_bookings/wcfm-view-wcbookings-details.php:57
#: views/wc_bookings/wcfm-view-wcbookings.php:64
msgid "WP Admin View"
msgstr "Vue d'administration"

#: core/class-wcfm-thirdparty-support.php:311
#: views/listings/wcfm-view-listings.php:91
msgid "Add New Listing"
msgstr "Ajouter une nouvelle annonce"

#: core/class-wcfm-thirdparty-support.php:417
msgid "Set Price Type"
msgstr "Configurer le Type de Prix"

#: core/class-wcfm-thirdparty-support.php:417
msgid "General Pricing"
msgstr "Prix Global"

#: core/class-wcfm-thirdparty-support.php:417
msgid "Choose a price type - this controls the schema."
msgstr "Choisissez un type de prix - ceci contrôle le schéma."

#: core/class-wcfm-thirdparty-support.php:418
msgid "Hourly Price"
msgstr "Prix ​​horaire"

#: core/class-wcfm-thirdparty-support.php:418
msgid "Hourly price will be applicabe if booking or rental days min 1day"
msgstr ""
"Le prix horaire sera applicable si il y a au minimum 1 une journée de "
"réservation ou location"

#: core/class-wcfm-thirdparty-support.php:418
#: core/class-wcfm-thirdparty-support.php:419
msgid "Enter price here"
msgstr "Entrez le montant ici"

#: core/class-wcfm-thirdparty-support.php:419
msgid "General Price"
msgstr "Tarif Général"

#: core/class-wcfm-thirdparty-support.php:425
msgid "Availability"
msgstr "Disponibilité"

#: core/class-wcfm-thirdparty-support.php:430
msgid "Product Availabilities"
msgstr "Disponibilités du produit"

#: core/class-wcfm-thirdparty-support.php:430
msgid "Please select the date range to be disabled for the product."
msgstr "Sélectionnez la date à laquelle les produits sont indisponibles."

#: core/class-wcfm-thirdparty-support.php:431
#: views/coupons/wcfm-view-coupons.php:56
#: views/coupons/wcfm-view-coupons.php:66
#: views/messages/wcfm-view-messages.php:122
#: views/messages/wcfm-view-messages.php:135
#: views/products/wcfm-view-products.php:203
#: views/products/wcfm-view-products.php:226
msgid "Type"
msgstr "Type"

#: core/class-wcfm-thirdparty-support.php:431
msgid "Custom Date"
msgstr "Date personnalisée"

#: core/class-wcfm-thirdparty-support.php:432
#: views/messages/wcfm-view-messages.php:124
#: views/messages/wcfm-view-messages.php:137
#: views/products-manager/wcfm-view-products-manage-tabs.php:245
#: views/products-manager/wcfm-view-products-manage.php:472
#: views/products-popup/wcfm-view-product-popup.php:131
#: views/settings/wcfm-view-dokan-settings.php:490
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:770
#: views/settings/wcfm-view-wcmarketplace-settings.php:746
#: views/settings/wcfm-view-wcpvendors-settings.php:154
#: views/settings/wcfm-view-wcvendors-settings.php:446
msgid "From"
msgstr "Message"

#: core/class-wcfm-thirdparty-support.php:433
#: views/messages/wcfm-view-messages.php:125
#: views/messages/wcfm-view-messages.php:138
#: views/products-manager/wcfm-view-products-manage.php:473
#: views/products-popup/wcfm-view-product-popup.php:132
msgid "To"
msgstr "À"

#: core/class-wcfm-thirdparty-support.php:434
#: core/class-wcfm-wcbookings.php:187
msgid "Bookable"
msgstr "Disponible"

#: core/class-wcfm-thirdparty-support.php:434
msgid "NO"
msgstr "NON"

#: core/class-wcfm-thirdparty-support.php:467
msgid "Auction Date From"
msgstr "Date de début"

#: core/class-wcfm-thirdparty-support.php:468
msgid "Auction Date To"
msgstr "Date de fin de vente"

#: core/class-wcfm-thirdparty-support.php:500
msgid "Has Voucher"
msgstr "Carte cadeau"

#: core/class-wcfm-thirdparty-support.php:528
msgid "-- Choose Template --"
msgstr "-- Choisissez un modèle --"

#: core/class-wcfm-thirdparty-support.php:534
msgid "Voucher Template"
msgstr "Modèle de la carte cadeau"

#: core/class-wcfm-thirdparty-support.php:534
msgid "Select a voucher template to make this into a voucher product."
msgstr "Sélectionnez un modèle de bon de commande."

#: core/class-wcfm-thirdparty-support.php:646
#: core/class-wcfm-thirdparty-support.php:669
msgid "Select Delivery Time"
msgstr "Sélectionner le délai de livraison"

#: core/class-wcfm-thirdparty-support.php:738
msgid "Scheduler Config"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:775
#: core/class-wcfm-wcfmmarketplace.php:629
#: core/class-wcfm-wcmarketplace.php:484 core/class-wcfm-wcmarketplace.php:632
#: core/class-wcfm-wcpvendors.php:406 core/class-wcfm-wcpvendors.php:505
#: core/class-wcfm-wcvendors.php:636 core/class-wcfm-wcvendors.php:649
#: core/class-wcfm-wcvendors.php:814 core/class-wcfm-wcvendors.php:837
#: helpers/class-wcfm-install.php:320
#: includes/reports/class-dokan-report-sales-by-date.php:831
#: includes/reports/class-wcfm-report-sales-by-date.php:720
#: views/capability/wcfm-view-capability.php:207
#: views/customers/wcfm-view-customers-manage.php:182
#: views/orders/wcfm-view-orders-details.php:610
#: views/orders/wcfm-view-orders-details.php:860
#: views/products-manager/wcfm-view-products-manage-tabs.php:83
#: views/products-manager/wcfm-view-products-manage-tabs.php:217
#: views/profile/wcfm-view-profile.php:297
#: views/settings/wcfm-view-dokan-settings.php:341
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:690
#: views/settings/wcfm-view-wcmarketplace-settings.php:606
#: views/settings/wcfm-view-wcvendors-settings.php:351
#: views/vendors/wcfm-view-vendors-new.php:131
msgid "Shipping"
msgstr "Livraison"

#: core/class-wcfm-thirdparty-support.php:780
msgid "Shipment Origin Information"
msgstr ""

#: core/class-wcfm-vendor-support.php:141
msgid "Vendors Dashboard"
msgstr "Tableau de bord des vendeurs"

#: core/class-wcfm-vendor-support.php:144
msgid "New Vendor"
msgstr ""

#: core/class-wcfm-vendor-support.php:147
msgid "Vendors Manager"
msgstr "Gestionnaire de vendeurs"

#: core/class-wcfm-vendor-support.php:150
msgid "Vendors Commission"
msgstr "Commissions vendeurs"

#: core/class-wcfm-vendor-support.php:198
#: core/class-wcfm-vendor-support.php:211
#: views/vendors/wcfm-view-vendors-manage.php:186
#: views/vendors/wcfm-view-vendors-new.php:72
#: views/vendors/wcfm-view-vendors.php:17
msgid "Vendors"
msgstr "Vendeurs"

#: core/class-wcfm-vendor-support.php:290
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:79
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:80
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:83
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:125
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:126
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:129
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:130
#: controllers/messages/wcfm-controller-messages.php:155
#: controllers/messages/wcfm-controller-messages.php:183
#: views/enquiry/wcfm-view-enquiry.php:81
#: views/enquiry/wcfm-view-enquiry.php:93
#: views/enquiry/wcfm-view-my-account-enquiry.php:43
#: views/enquiry/wcfm-view-my-account-enquiry.php:56
#: views/products/wcfm-view-products.php:206
#: views/products/wcfm-view-products.php:229
#: views/settings/wcfm-view-dokan-settings.php:159
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:210
#: views/settings/wcfm-view-wcmarketplace-settings.php:166
#: views/settings/wcfm-view-wcvendors-settings.php:150
#: views/vendors/wcfm-view-vendors-manage.php:272
#: views/vendors/wcfm-view-vendors.php:61
#: views/vendors/wcfm-view-vendors.php:82
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:54
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:64
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:71
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:87
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:66
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:78
msgid "Store"
msgstr "Boutique"

#: core/class-wcfm-vendor-support.php:293
msgid "Package Qty"
msgstr ""

#: core/class-wcfm-vendor-support.php:338
#: core/class-wcfm-vendor-support.php:492
#: core/class-wcfm-vendor-support.php:531
#: core/class-wcfm-vendor-support.php:547
#: core/class-wcfm-vendor-support.php:576 core/class-wcfm-wcmarketplace.php:479
#: core/class-wcfm-wcpvendors.php:405 core/class-wcfm-wcvendors.php:631
#: views/orders/wcfm-view-orders.php:92 views/orders/wcfm-view-orders.php:112
#: views/settings/wcfm-view-wcpvendors-settings.php:181
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:68
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:80
msgid "Commission"
msgstr "Commission"

#: core/class-wcfm-vendor-support.php:377
#: core/class-wcfm-vendor-support.php:382
#: views/vendors/wcfm-view-vendors.php:60
#: views/vendors/wcfm-view-vendors.php:81
msgid "Vendor"
msgstr "Vendeur"

#: core/class-wcfm-vendor-support.php:497
#: core/class-wcfm-vendor-support.php:537
#: core/class-wcfm-vendor-support.php:542
#: core/class-wcfm-vendor-support.php:566
#: core/class-wcfm-vendor-support.php:571
msgid "Commission(%)"
msgstr "Commission en %"

#: core/class-wcfm-vendor-support.php:538
#: core/class-wcfm-vendor-support.php:567
msgid "Fixed (per transaction)"
msgstr "Fixé (par transaction)"

#: core/class-wcfm-vendor-support.php:543
#: core/class-wcfm-vendor-support.php:572
msgid "Fixed (per unit)"
msgstr "Fixé (par unité)"

#: core/class-wcfm-vendor-support.php:843
#: controllers/messages/wcfm-controller-messages.php:181
#: views/articles/wcfm-view-articles.php:10
#: views/listings/wcfm-view-listings.php:36
#: views/messages/wcfm-view-messages.php:106
#: views/products/wcfm-view-products.php:10
#: views/wc_bookings/wcfm-view-wcbookings.php:19
msgid "All"
msgstr "Tous"

#: core/class-wcfm-vendor-support.php:845 helpers/wcfm-core-functions.php:962
msgid "Choose Vendor ..."
msgstr "Choisir le vendeur ..."

#: core/class-wcfm-vendor-support.php:2265
msgid "Review Product"
msgstr "Avis du produit"

#: core/class-wcfm-vendor-support.php:2266 helpers/wcfm-core-functions.php:995
msgid "New Product"
msgstr "Nouveau produit"

#: core/class-wcfm-vendor-support.php:2267 helpers/wcfm-core-functions.php:996
msgid "New Category"
msgstr "Nouvelle catégorie"

#: core/class-wcfm-vendor-support.php:2274
msgid "You may manage this using WCfM Capability Controller."
msgstr ""

#: core/class-wcfm-vendor-support.php:2275
#, php-format
msgid ""
"Manage vendor backend access from <a href=\"%s\">WCfM Capability Controller</"
"a>."
msgstr ""
"Gérez l'accès au back-end du fournisseur à partir du <a href=\"%s\"> "
"contrôleur de capacité WCfM </a>."

#: core/class-wcfm-wcbookings.php:93
msgid "Bookings Dashboard"
msgstr "Tableau de bord des réservations"

#: core/class-wcfm-wcbookings.php:96
#: views/capability/wcfm-view-capability.php:347
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:99
#: views/wc_bookings/wcfm-view-wcbookings-details.php:67
#: views/wc_bookings/wcfm-view-wcbookings.php:36
msgid "Bookings List"
msgstr "Liste de réservations"

#: core/class-wcfm-wcbookings.php:99
msgid "Bookings Resources"
msgstr "Ressources de Réservations"

#: core/class-wcfm-wcbookings.php:102
msgid "Bookings Resources Manage"
msgstr "Gérer les Ressources de Réservations"

#: core/class-wcfm-wcbookings.php:105
msgid "Create Bookings"
msgstr "Créer des réservations"

#: core/class-wcfm-wcbookings.php:108
#: views/capability/wcfm-view-capability.php:348
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:114
msgid "Bookings Calendar"
msgstr "Calendrier de réservations"

#: core/class-wcfm-wcbookings.php:111
#, php-format
msgid "Booking Details #%s"
msgstr "Détails de réservation #%s"

#: core/class-wcfm-wcbookings.php:114
msgid "Bookings settings"
msgstr "Paramètres de réservations"

#: core/class-wcfm-wcbookings.php:335
msgid "Booking Options"
msgstr "Options de réservation"

#: core/class-wcfm-wcfmmarketplace.php:188
msgid "Show all"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:189
msgid "Unpaid"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:190
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:46
msgid "Requested"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:191
#: controllers/customers/wcfm-controller-customers-details.php:397
msgid "Paid"
msgstr "Payé"

#: core/class-wcfm-wcfmmarketplace.php:192
#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/customers/wcfm-controller-customers-details.php:397
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:24
#: views/withdrawal/dokan/wcfm-view-payments.php:54
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:40
#: views/withdrawal/wcfm/wcfm-view-payments.php:56
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:47
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:45
msgid "Cancelled"
msgstr "Annulé"

#: core/class-wcfm-wcfmmarketplace.php:456
#: core/class-wcfm-wcmarketplace.php:477 views/orders/wcfm-view-orders.php:88
#: views/orders/wcfm-view-orders.php:108
msgid "Fees"
msgstr "Frais"

#: core/class-wcfm-wcfmmarketplace.php:458
#: includes/reports/class-dokan-report-sales-by-date.php:814
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:541
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:537
#: includes/reports/class-wcpvendors-report-sales-by-date.php:440
#: includes/reports/class-wcvendors-report-sales-by-date.php:550
msgid "Earning"
msgstr "Revenus"

#: core/class-wcfm-wcfmmarketplace.php:613
msgid "Line Total"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:621
#: core/class-wcfm-wcmarketplace.php:490 core/class-wcfm-wcmarketplace.php:643
#: core/class-wcfm-wcpvendors.php:407 core/class-wcfm-wcpvendors.php:514
#: core/class-wcfm-wcvendors.php:642 core/class-wcfm-wcvendors.php:655
#: core/class-wcfm-wcvendors.php:825 core/class-wcfm-wcvendors.php:848
#: views/orders/wcfm-view-orders-details.php:399
#: views/orders/wcfm-view-orders-details.php:400
#: views/products-manager/wcfm-view-products-manage-tabs.php:109
msgid "Tax"
msgstr "Taxe"

#: core/class-wcfm-wcfmmarketplace.php:636
#: core/class-wcfm-wcmarketplace.php:491 core/class-wcfm-wcmarketplace.php:652
#: core/class-wcfm-wcpvendors.php:408 core/class-wcfm-wcpvendors.php:523
msgid "Shipping Tax"
msgstr "TVA livraison"

#: core/class-wcfm-wcfmmarketplace.php:645
#: core/class-wcfm-wcmarketplace.php:696 core/class-wcfm-wcpvendors.php:541
#: core/class-wcfm-wcvendors.php:869
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:67
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:79
msgid "Gross Total"
msgstr "Prix Total"

#: core/class-wcfm-wcfmmarketplace.php:662
#: views/orders/wcfm-view-orders-details.php:915
msgid "Refunded"
msgstr "Remboursé"

#: core/class-wcfm-wcfmmarketplace.php:670
#: core/class-wcfm-wcmarketplace.php:665 views/vendors/wcfm-view-vendors.php:67
#: views/vendors/wcfm-view-vendors.php:88
msgid "Total Fees"
msgstr "Total des frais"

#: core/class-wcfm-wcmarketplace.php:496 core/class-wcfm-wcpvendors.php:409
#: core/class-wcfm-wcvendors.php:662
#: views/orders/wcfm-view-orders-details.php:392
msgid "Total"
msgstr "Total"

#: core/class-wcfm-wcmarketplace.php:538 core/class-wcfm-wcmarketplace.php:626
#: core/class-wcfm-wcmarketplace.php:674 core/class-wcfm-wcmarketplace.php:688
#: controllers/orders/wcfm-controller-orders.php:210
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:279
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:261
msgid "N/A"
msgstr "N/A"

#: core/class-wcfm-wcmarketplace.php:621 core/class-wcfm-wcpvendors.php:496
#: core/class-wcfm-wcvendors.php:803
msgid "Line Commission"
msgstr "Ligne Commission"

#: core/class-wcfm-wcsubscriptions.php:55
#: core/class-wcfm-xasubscriptions.php:55
#: views/capability/wcfm-view-capability.php:375
msgid "Subscriptions"
msgstr "Inscriptions"

#: core/class-wcfm-wcsubscriptions.php:56
msgid "Variable Subscriptions"
msgstr "Abonnements variables"

#: core/class-wcfm-withdrawal.php:63
#: views/withdrawal/dokan/wcfm-view-payments.php:26
#: views/withdrawal/wcfm/wcfm-view-payments.php:28
#: views/withdrawal/wcmp/wcfm-view-payments.php:26
msgid "Payments History"
msgstr "Historique des paiements"

#: core/class-wcfm-withdrawal.php:68
#: views/withdrawal/dokan/wcfm-view-payments.php:42
#: views/withdrawal/dokan/wcfm-view-withdrawal.php:53
#: views/withdrawal/wcfm/wcfm-view-payments.php:44
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:110
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:117
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:34
#: views/withdrawal/wcmp/wcfm-view-payments.php:43
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:49
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:28
msgid "Withdrawal Request"
msgstr "Demande de retrait"

#: core/class-wcfm-withdrawal.php:72
msgid "Withdrawal Reverse"
msgstr ""

#: core/class-wcfm-withdrawal.php:76
#: views/capability/wcfm-view-capability.php:268
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:97
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:36
msgid "Transaction Details"
msgstr ""

#: core/class-wcfm-withdrawal.php:109 views/settings/wcfm-view-settings.php:345
msgid "Payments"
msgstr "Paiements"

#: core/class-wcfm-withdrawal.php:118 core/class-wcfm-withdrawal.php:129
#: core/class-wcfm.php:746 helpers/class-wcfm-install.php:332
#: includes/reports/class-dokan-report-sales-by-date.php:822
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:551
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:547
#: includes/reports/class-wcpvendors-report-sales-by-date.php:450
#: includes/reports/class-wcvendors-report-sales-by-date.php:559
#: views/capability/wcfm-view-capability.php:262
#: views/settings/wcfm-view-settings.php:351
#: views/vendors/wcfm-view-vendors.php:71
#: views/vendors/wcfm-view-vendors.php:92
#: views/withdrawal/dokan/wcfm-view-payments.php:42
#: views/withdrawal/wcfm/wcfm-view-payments.php:44
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:110
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:117
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:34
#: views/withdrawal/wcmp/wcfm-view-payments.php:43
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:49
msgid "Withdrawal"
msgstr "Retrait"

#: core/class-wcfm.php:474 core/class-wcfm.php:502
#: controllers/vendors/wcfm-controller-vendors.php:82
msgid "Disable Vendor"
msgstr ""

#: core/class-wcfm.php:612 views/capability/wcfm-view-capability.php:176
#: views/listings/wcfm-view-listings.php:105
#: views/listings/wcfm-view-listings.php:117
#: views/products/wcfm-view-products.php:37
msgid "Products"
msgstr "Produits"

#: core/class-wcfm.php:622 views/capability/wcfm-view-capability.php:324
#: views/coupons/wcfm-view-coupons.php:16
msgid "Coupons"
msgstr "Coupons"

#: core/class-wcfm.php:632 views/capability/wcfm-view-capability.php:388
#: views/customers/wcfm-view-customers-details.php:262
#: views/customers/wcfm-view-customers.php:63
#: views/customers/wcfm-view-customers.php:78
#: views/orders/wcfm-view-orders.php:30
msgid "Orders"
msgstr "Commandes"

#: core/class-wcfm.php:637 views/capability/wcfm-view-capability.php:437
msgid "Reports"
msgstr "Rapports"

#: core/class-wcfm.php:647 helpers/class-wcfm-setup-bak.php:87
#: helpers/class-wcfm-setup-bak.php:925 helpers/class-wcfm-setup.php:87
#: helpers/class-wcfm-setup.php:953 views/settings/wcfm-view-settings.php:117
#: views/settings/wcfm-view-settings.php:425
msgid "Capability"
msgstr "Capacité"

#: core/class-wcfm.php:732
msgid "Popup Add Product"
msgstr "Popup ajouter un produit"

#: core/class-wcfm.php:733 views/settings/wcfm-view-settings.php:333
msgid "Menu Manager"
msgstr ""

#: core/class-wcfm.php:734
msgid "Enquiry"
msgstr "Demande"

#: core/class-wcfm.php:735
msgid "Enquiry Tab"
msgstr "Onglet renseignements"

#: core/class-wcfm.php:735
msgid ""
"If you just want to hide Single Product page `Enquiry Tab`, but keep enable "
"`Enquiry Module` for `Catalog Mode`."
msgstr ""
"Si vous voulez simplement masquer la page de produit unique'onglet "
"d'enquête', mais gardez activé \"module d'enquête\" en \"mode catalogue\"."

#: core/class-wcfm.php:736
msgid ""
"If you disable `Enquiry Module` then `Catalog Module` will stop working "
"automatically."
msgstr ""
"Si vous désactivez le'module d'investigation'alors le'module de "
"catalogue'cessera de fonctionner automatiquement."

#: core/class-wcfm.php:737 helpers/class-wcfm-install.php:324
msgid "Article"
msgstr "Article"

#: core/class-wcfm.php:738 helpers/class-wcfm-install.php:325
#: views/enquiry/wcfm-view-enquiry.php:80
#: views/enquiry/wcfm-view-enquiry.php:92
msgid "Customer"
msgstr "Utilisateur"

#: core/class-wcfm.php:743
msgid "Policies"
msgstr "Conditions"

#: core/class-wcfm.php:744
msgid "Custom Field"
msgstr "Champ personnalisé"

#: core/class-wcfm.php:745
msgid "Sub-menu"
msgstr "Sous-menu"

#: core/class-wcfm.php:745
msgid "This will disable `Add New` sub-menus on hover."
msgstr "Cela désactive les sous-menus \"ajouter de nouveaux\" au survol."

#: core/class-wcfm.php:747
msgid "Refund"
msgstr ""

#: core/class-wcfm.php:751
msgid "BuddyPress Integration"
msgstr "Intégration BuddyPress"

#: core/class-wcfm.php:774
msgid "Base Highlighter Color"
msgstr "Couleur base surligneur"

#: core/class-wcfm.php:775
msgid "Top Bar Background Color"
msgstr "Couleur d’arrière-plan de la barre supérieure"

#: core/class-wcfm.php:776
msgid "Top Bar Text Color"
msgstr "Couleur du texte de la barre supérieure"

#: core/class-wcfm.php:777
msgid "Dashboard Background Color"
msgstr "Couleur d'arrière-plan du tableau de bord"

#: core/class-wcfm.php:778
msgid "Container Background Color"
msgstr "Couleur d'arrière-plan du conteneur"

#: core/class-wcfm.php:779
msgid "Container Head Color"
msgstr "Couleur de l'entête"

#: core/class-wcfm.php:780
msgid "Container Head Text Color"
msgstr "Couleur du texte de l'entête"

#: core/class-wcfm.php:781
msgid "Container Head Active Color"
msgstr "Couleur active de la tête du conteneur"

#: core/class-wcfm.php:782
msgid "Container Head Active Text Color"
msgstr "Couleur du texte de l'entête active"

#: core/class-wcfm.php:783
msgid "Menu Background Color"
msgstr "Couleur d’arrière-plan du menu"

#: core/class-wcfm.php:784
msgid "Menu Item Text Color"
msgstr "Couleur de texte pour le menu"

#: core/class-wcfm.php:785
msgid "Menu Active Item Background"
msgstr "Couleur de fond du menu activé"

#: core/class-wcfm.php:786
msgid "Menu Active Item Text Color"
msgstr "Couleur de texte du menu principal activé"

#: core/class-wcfm.php:787
msgid "Button Background Color"
msgstr "Couleur d’arrière plan du bouton"

#: core/class-wcfm.php:788
msgid "Button Text Color"
msgstr "Couleur du texte du bouton"

#: helpers/class-wcfm-install.php:102
msgctxt "page_slug"
msgid "store-manager"
msgstr ""

#: helpers/class-wcfm-install.php:102
#: controllers/vendors/wcfm-controller-vendors-new.php:161
msgid "Store Manager"
msgstr ""

#: helpers/class-wcfm-install.php:316
#: views/customers/wcfm-view-customers-details.php:194
#: views/customers/wcfm-view-customers-details.php:205
#: views/enquiry/wcfm-view-enquiry.php:79
#: views/enquiry/wcfm-view-enquiry.php:91
#: views/enquiry/wcfm-view-my-account-enquiry.php:42
#: views/enquiry/wcfm-view-my-account-enquiry.php:53
#: views/wc_bookings/wcfm-view-wcbookings.php:117
#: views/wc_bookings/wcfm-view-wcbookings.php:129
msgid "Product"
msgstr "Produit"

#: helpers/class-wcfm-install.php:318
#: views/customers/wcfm-view-customers-details.php:195
#: views/customers/wcfm-view-customers-details.php:206
#: views/customers/wcfm-view-customers-details.php:269
#: views/customers/wcfm-view-customers-details.php:279
#: views/orders/wcfm-view-orders.php:82 views/orders/wcfm-view-orders.php:102
#: views/wc_bookings/wcfm-view-wcbookings.php:118
#: views/wc_bookings/wcfm-view-wcbookings.php:130
msgid "Order"
msgstr "Tri"

#: helpers/class-wcfm-install.php:319
msgid "Report"
msgstr ""

#: helpers/class-wcfm-install.php:322 views/vendors/wcfm-view-vendors.php:59
#: views/vendors/wcfm-view-vendors.php:80
msgid "Verification"
msgstr ""

#: helpers/class-wcfm-install.php:323
msgid "Support Ticket"
msgstr ""

#: helpers/class-wcfm-install.php:326
msgid "Followers"
msgstr ""

#: helpers/class-wcfm-install.php:327
msgid "Coupon"
msgstr ""

#: helpers/class-wcfm-install.php:328
msgid "Noice"
msgstr ""

#: helpers/class-wcfm-install.php:329 views/settings/wcfm-view-settings.php:120
#: views/vendors/wcfm-view-vendors-manage.php:363
#: views/vendors/wcfm-view-vendors.php:62
#: views/vendors/wcfm-view-vendors.php:83
msgid "Membership"
msgstr "Adhésion"

#: helpers/class-wcfm-install.php:331
#: views/settings/wcfm-view-dokan-settings.php:252
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:378
#: views/settings/wcfm-view-wcmarketplace-settings.php:303
#: views/settings/wcfm-view-wcpvendors-settings.php:174
#: views/settings/wcfm-view-wcvendors-settings.php:246
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:55
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:65
#: views/withdrawal/wcfm/wcfm-view-payments.php:74
#: views/withdrawal/wcfm/wcfm-view-payments.php:88
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:74
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:90
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:79
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:92
msgid "Payment"
msgstr "Paiement"

#: helpers/class-wcfm-install.php:333
msgid "General"
msgstr "Général"

#: helpers/class-wcfm-install.php:335
msgid "Marketing"
msgstr ""

#: helpers/class-wcfm-install.php:336
#: views/settings/wcfm-view-dokan-settings.php:443
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:705
msgid "SEO"
msgstr "SEO"

#: helpers/class-wcfm-setup-bak.php:52 helpers/class-wcfm-setup.php:52
msgid "Introduction"
msgstr "Introduction"

#: helpers/class-wcfm-setup-bak.php:57 helpers/class-wcfm-setup.php:57
msgid "Dashboard Setup"
msgstr "Configuration du tableau de bord"

#: helpers/class-wcfm-setup-bak.php:62 helpers/class-wcfm-setup.php:62
msgid "Marketplace Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:67 helpers/class-wcfm-setup.php:67
msgid "Commission Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:72 helpers/class-wcfm-setup.php:72
msgid "Withdrawal Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:77 helpers/class-wcfm-setup.php:77
msgid "Registration Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:82 helpers/class-wcfm-setup.php:82
msgid "Style"
msgstr "Style"

#: helpers/class-wcfm-setup-bak.php:92 helpers/class-wcfm-setup.php:92
msgid "Ready!"
msgstr "Prêt !"

#: helpers/class-wcfm-setup-bak.php:120 helpers/class-wcfm-setup.php:120
msgctxt "enhanced select"
msgid "No matches found"
msgstr "Aucun résultat trouvé "

#: helpers/class-wcfm-setup-bak.php:121 helpers/class-wcfm-setup.php:121
msgctxt "enhanced select"
msgid "Loading failed"
msgstr "Échec du chargement "

#: helpers/class-wcfm-setup-bak.php:122 helpers/class-wcfm-setup.php:122
msgctxt "enhanced select"
msgid "Please enter 1 or more characters"
msgstr "Veuillez saisir 1 caractère ou plus"

#: helpers/class-wcfm-setup-bak.php:123 helpers/class-wcfm-setup.php:123
msgctxt "enhanced select"
msgid "Please enter %qty% or more characters"
msgstr "Veuillez saisir %qty% caractères ou plus"

#: helpers/class-wcfm-setup-bak.php:124 helpers/class-wcfm-setup.php:124
msgctxt "enhanced select"
msgid "Please delete 1 character"
msgstr "Veuillez supprimer 1 caractère"

#: helpers/class-wcfm-setup-bak.php:125 helpers/class-wcfm-setup.php:125
msgctxt "enhanced select"
msgid "Please delete %qty% characters"
msgstr "Veuillez supprimer %qty% caractères"

#: helpers/class-wcfm-setup-bak.php:126 helpers/class-wcfm-setup.php:126
msgctxt "enhanced select"
msgid "You can only select 1 item"
msgstr "Vous ne pouvez sélectionner qu’1 article"

#: helpers/class-wcfm-setup-bak.php:127 helpers/class-wcfm-setup.php:127
msgctxt "enhanced select"
msgid "You can only select %qty% items"
msgstr "Vous ne pouvez sélectionner que %qty% articles"

#: helpers/class-wcfm-setup-bak.php:128 helpers/class-wcfm-setup.php:128
msgctxt "enhanced select"
msgid "Loading more results&hellip;"
msgstr "Charger plus de résultats&hellip;"

#: helpers/class-wcfm-setup-bak.php:129 helpers/class-wcfm-setup.php:129
msgctxt "enhanced select"
msgid "Searching&hellip;"
msgstr "Recherche&hellip;"

#: helpers/class-wcfm-setup-bak.php:218 helpers/class-wcfm-setup.php:218
msgid "WCFM &rsaquo; Setup Wizard"
msgstr "Assistant de configuration WCFM ›"

#: helpers/class-wcfm-setup-bak.php:275 helpers/class-wcfm-setup-bak.php:386
#: helpers/class-wcfm-setup.php:276
msgid "Welcome to WooCommerce Multi-vendor Marketplace!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:276 helpers/class-wcfm-setup.php:277
msgid ""
"Thank you for choosing WCFM Marketplace! This quick setup wizard will help "
"you to configure the basic settings and you will have your marketplace ready "
"in no time."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:278 helpers/class-wcfm-setup.php:279
msgid "Let's experience the best ever WC Frontend Dashboard!!"
msgstr "Faisons l'expérience du meilleur tableau de bord de WC frontend!!"

#: helpers/class-wcfm-setup-bak.php:279 helpers/class-wcfm-setup.php:280
msgid ""
"Thank you for choosing WCFM! This quick setup wizard will help you to "
"configure the basic settings and you will have your dashboard ready in no "
"time. <strong>It’s completely optional as WCFM already auto-setup.</strong>"
msgstr ""
"Merci d'avoir choisi WCFM! Cet assistant d'installation rapide vous aidera à "
"configurer les paramètres de base et vous aurez votre tableau de bord prêt "
"en un rien de temps. <strong>Il est complètement facultatif car le WCFM est "
"déjà configuré automatiquement.</strong>"

#: helpers/class-wcfm-setup-bak.php:281 helpers/class-wcfm-setup.php:282
msgid ""
"If you don't want to go through the wizard right now, you can skip and "
"return to the WordPress dashboard. Come back anytime if you change your mind!"
msgstr ""
"Si vous ne voulez pas passer par l'assistant en ce moment, vous pouvez "
"ignorer et revenir au tableau de bord WordPress. Revenez à tout moment si "
"vous changez d'avis!"

#: helpers/class-wcfm-setup-bak.php:283 helpers/class-wcfm-setup-bak.php:390
#: helpers/class-wcfm-setup.php:284
msgid "Let's go!"
msgstr "C&rsquo;est parti&nbsp;!"

#: helpers/class-wcfm-setup-bak.php:284 helpers/class-wcfm-setup.php:285
msgid "Not right now"
msgstr "Pas maintenant"

#: helpers/class-wcfm-setup-bak.php:305 helpers/class-wcfm-setup.php:306
msgid "Dashboard setup"
msgstr "Paramétrer le Tableau de bord"

#: helpers/class-wcfm-setup-bak.php:310 helpers/class-wcfm-setup.php:311
msgid "WCFM Full View"
msgstr "Vue écran WCFM"

#: helpers/class-wcfm-setup-bak.php:311 helpers/class-wcfm-setup.php:312
msgid "Theme Header"
msgstr "En-tête du thème"

#: helpers/class-wcfm-setup-bak.php:312 helpers/class-wcfm-setup.php:313
msgid "WCFM Slick Menu"
msgstr "Menu Slick WCFM"

#: helpers/class-wcfm-setup-bak.php:313 helpers/class-wcfm-setup.php:314
msgid "WCFM Header Panel"
msgstr "Panneau d'en-tête WCFM"

#: helpers/class-wcfm-setup-bak.php:314 helpers/class-wcfm-setup.php:315
msgid "Welcome Box"
msgstr "Boîte de bienvenue"

#: helpers/class-wcfm-setup-bak.php:315 helpers/class-wcfm-setup.php:316
msgid "Category Checklist View"
msgstr "Liste de contrôle de catégorie"

#: helpers/class-wcfm-setup-bak.php:315 helpers/class-wcfm-setup.php:316
#: views/settings/wcfm-view-settings.php:160
msgid ""
"Disable this to have Product Manager Category/Custom Taxonomy Selector - "
"Flat View."
msgstr ""
"Désactivez-le pour avoir la Catégorie Gestionnaire de produits / Sélecteur "
"de taxonomie personnalisé - Vue plate."

#: helpers/class-wcfm-setup-bak.php:316 helpers/class-wcfm-setup.php:317
msgid "Quick Access"
msgstr "Accès Rapide"

#: helpers/class-wcfm-setup-bak.php:317 helpers/class-wcfm-setup.php:318
#: views/settings/wcfm-view-settings.php:156
msgid "Disable Responsive Float Menu"
msgstr "Désactiver le menu flottant responsive"

#: helpers/class-wcfm-setup-bak.php:318 helpers/class-wcfm-setup.php:319
msgid "Float Button"
msgstr "Bouton flottant"

#: helpers/class-wcfm-setup-bak.php:323 helpers/class-wcfm-setup-bak.php:426
#: helpers/class-wcfm-setup-bak.php:747 helpers/class-wcfm-setup-bak.php:863
#: helpers/class-wcfm-setup-bak.php:894 helpers/class-wcfm-setup-bak.php:963
#: helpers/class-wcfm-setup.php:324 helpers/class-wcfm-setup.php:426
#: helpers/class-wcfm-setup.php:747 helpers/class-wcfm-setup.php:891
#: helpers/class-wcfm-setup.php:922 helpers/class-wcfm-setup.php:991
msgid "Continue"
msgstr "Continuer"

#: helpers/class-wcfm-setup-bak.php:324 helpers/class-wcfm-setup-bak.php:383
#: helpers/class-wcfm-setup-bak.php:427 helpers/class-wcfm-setup-bak.php:748
#: helpers/class-wcfm-setup-bak.php:864 helpers/class-wcfm-setup-bak.php:895
#: helpers/class-wcfm-setup-bak.php:964 helpers/class-wcfm-setup.php:325
#: helpers/class-wcfm-setup.php:427 helpers/class-wcfm-setup.php:748
#: helpers/class-wcfm-setup.php:892 helpers/class-wcfm-setup.php:923
#: helpers/class-wcfm-setup.php:992
msgid "Skip this step"
msgstr "Passer cette étape"

#: helpers/class-wcfm-setup-bak.php:376
msgid "Do you want to setup a multi-vendor marketplace!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:378
msgid "Install WCFM Marketplace"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:387
#, php-format
msgid ""
"You have installed <b>%s</b> as your multi-vendor marketplace. Setup multi-"
"vendor setting from plugin setup panel."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:388
#, php-format
msgid ""
"You may switch your multi-vendor to %s for having more features and "
"flexibilities."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:393 helpers/class-wcfm-setup.php:384
msgid "Marketplace setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:520 helpers/class-wcfm-setup-bak.php:684
#: helpers/class-wcfm-setup.php:520 helpers/class-wcfm-setup.php:684
#, php-format
msgid ""
"%1$s could not be installed (%2$s). <a href=\"%3$s\">Please install it "
"manually by clicking here.</a>"
msgstr ""
"%1$s ne peut pas être installé (%2$s). <a href=\"%3$s\">Veuillez l&rsquo;"
"installer manuellement en cliquant sur ici.</a>"

#: helpers/class-wcfm-setup-bak.php:540 helpers/class-wcfm-setup-bak.php:704
#: helpers/class-wcfm-setup.php:540 helpers/class-wcfm-setup.php:704
#, php-format
msgid ""
"%1$s was installed but could not be activated. <a href=\"%2$s\">Please "
"activate it manually by clicking here.</a>"
msgstr ""
"%1$s a été installé mais ne peut pas être activé. <a href=\"%2$s\">Veuillez "
"l&rsquo;activer manuellement en cliquant sur ici.</a>"

#: helpers/class-wcfm-setup-bak.php:590 helpers/class-wcfm-setup.php:590
msgid "Setup WCFM Maketplace vendor registration:"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:592 helpers/class-wcfm-setup.php:592
msgid "Setup Registration"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:731 helpers/class-wcfm-setup.php:731
msgid "Commission setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:737 helpers/class-wcfm-setup.php:737
msgid ""
"You may setup more commission rules (By Sales Total and Product Price) from "
"setting panel."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:796 helpers/class-wcfm-setup.php:801
msgid "Withdrawal setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:882 helpers/class-wcfm-setup.php:910
#: views/settings/wcfm-view-settings.php:224
msgid "Dashboard Style"
msgstr "Style de Tableau de bord"

#: helpers/class-wcfm-setup-bak.php:930 helpers/class-wcfm-setup.php:958
#: views/capability/wcfm-view-capability.php:227
msgid "Backend Access"
msgstr "Accès backend"

#: helpers/class-wcfm-setup-bak.php:933 helpers/class-wcfm-setup.php:961
msgid "Submit Products"
msgstr "Soumettre Produits"

#: helpers/class-wcfm-setup-bak.php:934 helpers/class-wcfm-setup.php:962
#: views/capability/wcfm-view-capability.php:182
msgid "Publish Products"
msgstr "Publier les Produits"

#: helpers/class-wcfm-setup-bak.php:935 helpers/class-wcfm-setup.php:963
#: views/capability/wcfm-view-capability.php:183
msgid "Edit Live Products"
msgstr "Modifier les produits en direct"

#: helpers/class-wcfm-setup-bak.php:936 helpers/class-wcfm-setup.php:964
#: views/capability/wcfm-view-capability.php:185
msgid "Delete Products"
msgstr "Supprimer produits"

#: helpers/class-wcfm-setup-bak.php:940 helpers/class-wcfm-setup.php:968
#: views/capability/wcfm-view-capability.php:282
#: views/capability/wcfm-view-capability.php:344
msgid "Manage Bookings"
msgstr "Gérer les réservations"

#: helpers/class-wcfm-setup-bak.php:945 helpers/class-wcfm-setup.php:973
#: views/capability/wcfm-view-capability.php:294
#: views/capability/wcfm-view-capability.php:378
msgid "Manage Subscriptions"
msgstr "Gérer les abonnements"

#: helpers/class-wcfm-setup-bak.php:950 helpers/class-wcfm-setup.php:978
#: views/capability/wcfm-view-capability.php:299
msgid "by WP Job Manager."
msgstr "par WP Job Manager."

#: helpers/class-wcfm-setup-bak.php:954 helpers/class-wcfm-setup.php:982
#: views/capability/wcfm-view-capability.php:391
msgid "View Orders"
msgstr "Voir les commandes"

#: helpers/class-wcfm-setup-bak.php:955 helpers/class-wcfm-setup.php:983
#: views/capability/wcfm-view-capability.php:392
msgid "Status Update"
msgstr "Mettre à jour le statut"

#: helpers/class-wcfm-setup-bak.php:958 helpers/class-wcfm-setup.php:986
#: views/capability/wcfm-view-capability.php:440
msgid "View Reports"
msgstr "Voir Rapports"

#: helpers/class-wcfm-setup-bak.php:980 helpers/class-wcfm-setup.php:1008
msgid "We are done!"
msgstr "Nous avons fini !"

#: helpers/class-wcfm-setup-bak.php:983 helpers/class-wcfm-setup.php:1011
msgid ""
"Your marketplace is ready. It's time to experience the things more Easily "
"and Peacefully. Also you will be a bit more relax than ever before, have "
"fun!!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:985 helpers/class-wcfm-setup.php:1013
msgid ""
"Your front-end dashboard is ready. It's time to experience the things more "
"Easily and Peacefully. Also you will be a bit more relax than ever before, "
"have fun!!"
msgstr ""
"Votre tableau de bord frontal est prêt. Il est temps de vivre les choses "
"plus facilement et sereinement. Aussi vous allez être un peu plus relax que "
"jamais auparavant !!"

#: helpers/class-wcfm-setup-bak.php:990 helpers/class-wcfm-setup.php:1018
msgid "Next steps"
msgstr "Prochaines étapes"

#: helpers/class-wcfm-setup-bak.php:992 helpers/class-wcfm-setup.php:1020
msgid "Let's go to Dashboard"
msgstr "Allons sur le tableau de bord"

#: helpers/class-wcfm-setup-bak.php:996 helpers/class-wcfm-setup.php:1024
msgid "Learn more"
msgstr "En savoir plus"

#: helpers/class-wcfm-setup-bak.php:998 helpers/class-wcfm-setup.php:1026
msgid "Watch the tutorial videos"
msgstr "Regardez les vidéos du didacticiel"

#: helpers/class-wcfm-setup-bak.php:999 helpers/class-wcfm-setup.php:1027
msgid "WCFM - What & Why?"
msgstr "WCFM - Quoi et Pourquoi?"

#: helpers/class-wcfm-setup-bak.php:1000 helpers/class-wcfm-setup.php:1028
msgid "Choose your multi-vendor plugin"
msgstr "Choisissez votre plugin multifournisseur"

#: helpers/class-wcfm-setup-bak.php:1281 helpers/class-wcfm-setup.php:1322
msgid "Return to the WordPress Dashboard"
msgstr "Retour au tableau de bord WordPress"

#: helpers/class-wcfm-setup.php:377
msgid "Setup your multi-vendor marketplace in minutes!"
msgstr ""

#: helpers/class-wcfm-setup.php:379
msgid "Setup WCFM Marketplace"
msgstr ""

#: helpers/class-wcfm-setup.php:416
msgid "Single product page related products rule."
msgstr ""

#: helpers/class-wcfm-setup.php:417
msgid "No of products at Store per Page."
msgstr ""

#: helpers/class-wcfm-setup.php:879
msgid "Reverse Withdrawal setup"
msgstr ""

#: helpers/wcfm-core-functions.php:6
#, php-format
msgid ""
"WooCommerce Frontend Manager is inactive. The WooCommerce plugin must be "
"active for the WooCommerce Frontend Manager to work. Please install & "
"activate WooCommerce"
msgstr ""
"WooCommerce frontend Manager est inactif. le WooCommerce plugin doit être "
"actif pour que le gestionnaire frontend WooCommerce fonctionne. Veuillez "
"sinstaller et activer WooCommerce"

#: helpers/wcfm-core-functions.php:16
#, php-format
msgid ""
"Opps ..!!! You are using WC %s. WCFM works only with WC 3.0+. PLease upgrade "
"your WooCommerce version now to make your life easier and peaceful by using "
"WCFM."
msgstr ""
"Opps..!!! vous utilisez WC %s. WCFM fonctionne uniquement avec WC 3.0 +. "
"Veuillez mettre à jour votre version WooCommerce maintenant pour rendre "
"votre vie plus facile et paisible en utilisant WCFM."

#: helpers/wcfm-core-functions.php:44
#, php-format
msgid ""
"%s: You don't have permission to access this page. Please contact your "
"%sStore Admin%s for assistance."
msgstr ""

#: helpers/wcfm-core-functions.php:59 helpers/wcfm-core-functions.php:89
msgid ""
": Please ask your Store Admin to upgrade your dashboard to access this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:64 helpers/wcfm-core-functions.php:94
#, php-format
msgid ""
"%s: Please ask your %sStore Admin%s to upgrade your dashboard to access this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:71
msgid ""
": Upgrade your WCFM to WCFM - Ultimate to avail this feature. Disable this "
"notice from settings panel using \"Disable Ultimate Notice\" option."
msgstr ""

#: helpers/wcfm-core-functions.php:75
#, php-format
msgid ""
"%s: Upgrade your WCFM to %sWCFM - Ultimate%s to access this feature. Disable "
"this notice from settings panel using \"Disable Ultimate Notice\" option."
msgstr ""

#: helpers/wcfm-core-functions.php:101
msgid ""
": Associate your WCFM with WCFM - Groups & Staffs to avail this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:105
#, php-format
msgid ""
"%s: Associate your WCFM with %sWCFM - Groups & Staffs%s to access this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:119
msgid ": Please contact your Store Admin to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:124
#, php-format
msgid "%s: Please contact your %sStore Admin%s to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:131
msgid ": Associate your WCFM with WCFM - Analytics to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:135
#, php-format
msgid ""
"%s: Associate your WCFM with %sWCFM - Analytics%s to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:776
msgid "Please insert Article Title before submit."
msgstr "Veuillez insérer le titre de l'article avant de soumettre."

#: helpers/wcfm-core-functions.php:777
msgid "Article Successfully Saved."
msgstr "Article enregistré avec succès."

#: helpers/wcfm-core-functions.php:778
msgid "Article Successfully submitted for moderation."
msgstr "Article soumis avec succès pour modération."

#: helpers/wcfm-core-functions.php:779
msgid "Article Successfully Published."
msgstr "Article publié avec succès."

#: helpers/wcfm-core-functions.php:791
msgid "Please insert Product Title before submit."
msgstr "Veuillez insérer le titre avant de soumettre."

#: helpers/wcfm-core-functions.php:792
msgid "Product SKU must be unique."
msgstr "La référence du produit doit être unique."

#: helpers/wcfm-core-functions.php:793
msgid "Variation SKU must be unique."
msgstr "La variation de code du produit SKU doit être unique."

#: helpers/wcfm-core-functions.php:794
msgid "Product Successfully Saved."
msgstr "Produit sauvegardé avec succès."

#: helpers/wcfm-core-functions.php:795
msgid "Product Successfully submitted for moderation."
msgstr "Produit soumis avec succès pour modération."

#: helpers/wcfm-core-functions.php:796
msgid "Product Successfully Published."
msgstr "Produit publié avec succès."

#: helpers/wcfm-core-functions.php:797
msgid "Set Stock"
msgstr ""

#: helpers/wcfm-core-functions.php:798
#: views/products-manager/wcfm-view-products-manage-tabs.php:210
msgid "Increase Stock"
msgstr ""

#: helpers/wcfm-core-functions.php:799
#: views/products-manager/wcfm-view-products-manage-tabs.php:243
#: views/products-manager/wcfm-view-thirdparty-products-manage.php:294
msgid "Regular Price"
msgstr "Prix de vente"

#: helpers/wcfm-core-functions.php:800
msgid "Regular price increase by"
msgstr ""

#: helpers/wcfm-core-functions.php:801
msgid "Regular price decrease by"
msgstr ""

#: helpers/wcfm-core-functions.php:802
#: views/products-manager/wcfm-view-products-manage-tabs.php:244
#: views/products-manager/wcfm-view-products-manage.php:471
#: views/products-popup/wcfm-view-product-popup.php:130
msgid "Sale Price"
msgstr "Prix de vente"

#: helpers/wcfm-core-functions.php:803
msgid "Sale price increase by"
msgstr ""

#: helpers/wcfm-core-functions.php:804
msgid "Sale price decrease by"
msgstr ""

#: helpers/wcfm-core-functions.php:805
#: views/products-manager/wcfm-view-products-manage-tabs.php:90
#: views/products-manager/wcfm-view-products-manage-tabs.php:218
msgid "Length"
msgstr "Longueur"

#: helpers/wcfm-core-functions.php:806
#: views/products-manager/wcfm-view-products-manage-tabs.php:91
#: views/products-manager/wcfm-view-products-manage-tabs.php:219
msgid "Width"
msgstr "Largeur  "

#: helpers/wcfm-core-functions.php:807
#: views/products-manager/wcfm-view-products-manage-tabs.php:92
#: views/products-manager/wcfm-view-products-manage-tabs.php:220
msgid "Height"
msgstr "Hauteur"

#: helpers/wcfm-core-functions.php:808
#: views/products-manager/wcfm-view-products-manage-tabs.php:89
#: views/products-manager/wcfm-view-products-manage-tabs.php:221
msgid "Weight"
msgstr "Poids"

#: helpers/wcfm-core-functions.php:809
#: views/products-manager/wcfm-view-products-manage-tabs.php:52
msgid "Download Limit"
msgstr "Limite de téléchargement"

#: helpers/wcfm-core-functions.php:810
#: views/products-manager/wcfm-view-products-manage-tabs.php:53
msgid "Download Expiry"
msgstr "Expiration du téléchargement"

#: helpers/wcfm-core-functions.php:824
msgid "Please insert atleast Coupon Title before submit."
msgstr "Veuillez insérer au moins le titre du coupon avant de publier."

#: helpers/wcfm-core-functions.php:825
msgid "Coupon Successfully Saved."
msgstr "Code promo sauvegardé avec succès."

#: helpers/wcfm-core-functions.php:826
msgid "Coupon Successfully Published."
msgstr "Code promo publié avec succès."

#: helpers/wcfm-core-functions.php:838
msgid "Please insert atleast Knowledgebase Title before submit."
msgstr "Veuillez insérer au moins le titre du sujet avant de soumettre."

#: helpers/wcfm-core-functions.php:839
msgid "Knowledgebase Successfully Saved."
msgstr "Base de connaissances enregistrée avec succès."

#: helpers/wcfm-core-functions.php:840
msgid "Knowledgebase Successfully Published."
msgstr "Base de connaissances publiée avec succès."

#: helpers/wcfm-core-functions.php:852
msgid "Please insert atleast Topic Title before submit."
msgstr "Veuillez insérer au moins le titre du sujet avant de soumettre."

#: helpers/wcfm-core-functions.php:853
msgid "Topic Successfully Saved."
msgstr "Sujet sauvegardé avec succès."

#: helpers/wcfm-core-functions.php:854
msgid "Topic Successfully Published."
msgstr "Sujet publié avec succès."

#: helpers/wcfm-core-functions.php:866
msgid "Please write something before submit."
msgstr "Veuillez écrire quelque chose avant de soumettre."

#: helpers/wcfm-core-functions.php:867
msgid "Reply send failed, try again."
msgstr "L'envoi de réponse a échoué, réessayez."

#: helpers/wcfm-core-functions.php:868
msgid "Reply Successfully Send."
msgstr "Réponse envoyée avec succès."

#: helpers/wcfm-core-functions.php:880
msgid "Name is required."
msgstr "Le nom est requis."

#: helpers/wcfm-core-functions.php:881
msgid "Email is required."
msgstr "Le champ Email est requis."

#: helpers/wcfm-core-functions.php:882
msgid "Please insert your enquiry before submit."
msgstr "Merci de rédiger votre demande avant de la soumettre."

#: helpers/wcfm-core-functions.php:883
msgid "Please insert your reply before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:884
msgid "Your enquiry successfully sent."
msgstr "Votre demande a été envoyée avec succès."

#: helpers/wcfm-core-functions.php:885
msgid "Enquiry reply successfully published."
msgstr "Réponse à la demande publiée avec succès."

#: helpers/wcfm-core-functions.php:886
msgid "Your reply successfully sent."
msgstr ""

#: helpers/wcfm-core-functions.php:898
msgid "Please insert Username before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:899
msgid "Please insert Email before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:900
msgid "Please insert Store Name before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:901 helpers/wcfm-core-functions.php:918
msgid "This Username already exists."
msgstr "Ce nom d’utilisateur existe déjà."

#: helpers/wcfm-core-functions.php:902 helpers/wcfm-core-functions.php:919
msgid "This Email already exists."
msgstr "Ce courriel existe déjà."

#: helpers/wcfm-core-functions.php:903
msgid "Vendor Saving Failed."
msgstr ""

#: helpers/wcfm-core-functions.php:904
msgid "Vendor Successfully Saved."
msgstr ""

#: helpers/wcfm-core-functions.php:916
msgid "Please insert Customer Username before submit."
msgstr "Veuillez insérer le nom d'utilisateur du client avant de soumettre."

#: helpers/wcfm-core-functions.php:917
msgid "Please insert Customer Email before submit."
msgstr "Veuillez insérer le mail du client avant de soumettre."

#: helpers/wcfm-core-functions.php:920
msgid "Customer Saving Failed."
msgstr "Échec de l’enregistrement."

#: helpers/wcfm-core-functions.php:921
msgid "Customer Successfully Saved."
msgstr "Client enregistré avec succès."

#: helpers/wcfm-core-functions.php:933
msgid "Are you sure and want to approve / publish this 'Product'?"
msgstr "Êtes-vous sûr et que vous souhaitez approuver / publier ce «produit»?"

#: helpers/wcfm-core-functions.php:934
msgid ""
"Are you sure and want to delete this 'Article'?\n"
"You can't undo this action ..."
msgstr ""
"Êtes-vous sûr de vouloir supprimer cet 'Article'?\n"
"Vous ne pouvez pas annuler cette action ..."

#: helpers/wcfm-core-functions.php:935
msgid ""
"Are you sure and want to delete this 'Product'?\n"
"You can't undo this action ..."
msgstr ""
"Êtes-vous sûr de vouloir supprimer ce 'Produit'? Vous ne pouvez pas annuler "
"cette action ..."

#: helpers/wcfm-core-functions.php:936
msgid ""
"Are you sure and want to delete this 'Message'?\n"
"You can't undo this action ..."
msgstr ""
"Êtes-vous sûr de vouloir supprimer ce 'Message''? Vous ne pouvez pas annuler "
"cette action ..."

#: helpers/wcfm-core-functions.php:937
msgid ""
"Are you sure and want to delete this 'Order'?\n"
"You can't undo this action ..."
msgstr ""
"Êtes-vous sûr de vouloir supprimer cette 'commande'? Vous ne pouvez pas "
"annuler cette action ..."

#: helpers/wcfm-core-functions.php:938
msgid ""
"Are you sure and want to delete this 'Enquiry'?\n"
"You can't undo this action ..."
msgstr ""
"Êtes-vous sûr et que vous souhaitez supprimer cette 'Demande'?\n"
"Vous ne pouvez pas annuler cette action ..."

#: helpers/wcfm-core-functions.php:939
msgid ""
"Are you sure and want to delete this 'Support Ticket'?\n"
"You can't undo this action ..."
msgstr ""
"Êtes-vous sûr et que vous souhaitez supprimer ce «ticket de support»?\n"
"Vous ne pouvez pas annuler cette action ..."

#: helpers/wcfm-core-functions.php:940
msgid ""
"Are you sure and want to delete this 'Follower'?\n"
"You can't undo this action ..."
msgstr ""
"Êtes-vous sûr de vouloir supprimer ce 'Follower'?\n"
"Vous ne pouvez pas annuler cette action ..."

#: helpers/wcfm-core-functions.php:941
msgid ""
"Are you sure and want to delete this 'Following'?\n"
"You can't undo this action ..."
msgstr ""
"Êtes-vous sûr et que vous voulez supprimer ce 'suivant'?\n"
"Vous ne pouvez pas annuler cette action ..."

#: helpers/wcfm-core-functions.php:942
msgid "Are you sure and want to 'Mark as Complete' this Order?"
msgstr "Etes-vous sûr que vous voulez marquer complète cette commande ?"

#: helpers/wcfm-core-functions.php:943
msgid "Are you sure and want to 'Mark as Confirmed' this Booking?"
msgstr "Êtes-vous sûr de vouloir marquer cette réservation, comme confirmée ?"

#: helpers/wcfm-core-functions.php:944
msgid "Are you sure and want to 'Mark as Complete' this Appointment?"
msgstr "Etes-vous sûr de vouloir marquer ce rendez-vous comme 'complet' ?"

#: helpers/wcfm-core-functions.php:946
msgid "Select all"
msgstr "Tout sélectionner"

#: helpers/wcfm-core-functions.php:947
msgid "Select none"
msgstr "Ne rien sélectionner"

#: helpers/wcfm-core-functions.php:948
msgid "Any"
msgstr "Tous"

#: helpers/wcfm-core-functions.php:949
msgid "Enter a name for the new attribute term:"
msgstr "Saisissez un nom pour le nouvel attribut du terme :"

#: helpers/wcfm-core-functions.php:950
msgid ""
"Please upgrade your WC Frontend Manager to Ultimate version and avail this "
"feature."
msgstr ""
"Veuillez mettre à jour votre gestionnaire frontend WC vers la version "
"Ultimate et profiter de cette fonctionnalité."

#: helpers/wcfm-core-functions.php:951
msgid ""
"Install WC Frontend Manager Ultimate and WooCommerce PDF Invoices & Packing "
"Slips to avail this feature."
msgstr ""
"Installez WC frontend Manager Ultimate et WooCommerce PDF factures et des "
"bordereaux d'emballage pour bénéficier de cette fonctionnalité."

#: helpers/wcfm-core-functions.php:952
msgid "Please select some element first!!"
msgstr "Veuillez sélectionner quelques articles"

#: helpers/wcfm-core-functions.php:953
msgid ""
"Are you sure and want to do this?\n"
"You can't undo this action ..."
msgstr ""
"Etes-vous sûr et que vous voulez faire cela?\n"
"Vous ne pouvez pas annuler cette action..."

#: helpers/wcfm-core-functions.php:954
msgid "Are you sure and want to do this?"
msgstr "Es-tu sûr de vouloir faire ça?"

#: helpers/wcfm-core-functions.php:955
#: includes/libs/php/class-wcfm-fields.php:650
msgid "Everywhere Else"
msgstr "Partout ailleurs"

#: helpers/wcfm-core-functions.php:956 views/profile/wcfm-view-profile.php:223
#: includes/libs/php/class-wcfm-fields.php:82
#: includes/libs/php/class-wcfm-fields.php:84
#: includes/libs/php/class-wcfm-fields.php:87
#: includes/libs/php/class-wcfm-fields.php:148
#: includes/libs/php/class-wcfm-fields.php:219
#: includes/libs/php/class-wcfm-fields.php:261
#: includes/libs/php/class-wcfm-fields.php:327
#: includes/libs/php/class-wcfm-fields.php:389
#: includes/libs/php/class-wcfm-fields.php:449
#: includes/libs/php/class-wcfm-fields.php:554
#: includes/libs/php/class-wcfm-fields.php:632
#: includes/libs/php/class-wcfm-fields.php:702
#: includes/libs/php/class-wcfm-fields.php:767
#: includes/libs/php/class-wcfm-fields.php:770
#: includes/libs/php/class-wcfm-fields.php:833
#: includes/libs/php/class-wcfm-fields.php:882
#: includes/libs/php/class-wcfm-fields.php:976
msgid "This field is required."
msgstr "Ce champ est requis."

#: helpers/wcfm-core-functions.php:957
msgid "Choose "
msgstr "Choisir "

#: helpers/wcfm-core-functions.php:958
msgid "Search for a attribute ..."
msgstr "Rechercher un attribut ..."

#: helpers/wcfm-core-functions.php:959
msgid "Search for a product ..."
msgstr "Rechercher un produit ..."

#: helpers/wcfm-core-functions.php:960
msgid "Choose Categoies ..."
msgstr "Choisissez Categries..."

#: helpers/wcfm-core-functions.php:961
msgid "Choose Listings ..."
msgstr "Choisissez les listes..."

#: helpers/wcfm-core-functions.php:963
msgid "No categories"
msgstr "Aucune catégorie"

#: helpers/wcfm-core-functions.php:964
msgid "Searching ..."
msgstr "Recherche…"

#: helpers/wcfm-core-functions.php:965
msgid "No matching result found."
msgstr "Aucun résultat correspondant trouvé."

#: helpers/wcfm-core-functions.php:966
msgid "Loading ..."
msgstr "Chargement ..."

#: helpers/wcfm-core-functions.php:967
msgid "Minimum input character "
msgstr "Nombre minimum de caractères"

#: helpers/wcfm-core-functions.php:970
msgid "Add New Block"
msgstr ""

#: helpers/wcfm-core-functions.php:971
msgid "Remove Block"
msgstr ""

#: helpers/wcfm-core-functions.php:972
msgid "Toggle Block"
msgstr ""

#: helpers/wcfm-core-functions.php:973
msgid "Drag to re-arrange blocks"
msgstr ""

#: helpers/wcfm-core-functions.php:974
msgid "Please login to the site first!"
msgstr ""

#: helpers/wcfm-core-functions.php:975
msgid "Please select a shipping method"
msgstr ""

#: helpers/wcfm-core-functions.php:976
msgid "Shipping method not found"
msgstr ""

#: helpers/wcfm-core-functions.php:977
msgid "Shipping zone not found"
msgstr ""

#: helpers/wcfm-core-functions.php:978
msgid ""
"Are you sure you want to delete this 'Shipping Method'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:990
#: views/vendors/wcfm-view-vendors-manage.php:410
msgid "Direct Message"
msgstr "Message direct"

#: helpers/wcfm-core-functions.php:991
msgid "Approve Product"
msgstr "Approuver le Produit"

#: helpers/wcfm-core-functions.php:992
msgid "Status Updated"
msgstr ""

#: helpers/wcfm-core-functions.php:993
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:23
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:30
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:25
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:32
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:34
msgid "Withdrawal Requests"
msgstr ""

#: helpers/wcfm-core-functions.php:994
#: views/capability/wcfm-view-capability.php:247
msgid "Refund Requests"
msgstr ""

#: helpers/wcfm-core-functions.php:997
msgid "New Order"
msgstr "Nouvelle commande"

#: helpers/wcfm-core-functions.php:1330
msgid "Tutorial"
msgstr ""

#: views/wcfm-view-header-panels.php:46 views/wcfm-view-header-panels.php:49
msgid "Toggle Menu"
msgstr "Afficher/masquer le menu"

#: views/wcfm-view-header-panels.php:61
#: includes/shortcodes/class-wcfm-shortcode-notification.php:51
msgid "Notification Board"
msgstr "Tableau de  notifications"

#: views/wcfm-view-header-panels.php:65
#: includes/shortcodes/class-wcfm-shortcode-notification.php:55
#: views/enquiry/wcfm-view-enquiry.php:36
msgid "Enquiry Board"
msgstr "Tableau des demandes"

#: views/wcfm-view-header-panels.php:69
#: includes/shortcodes/class-wcfm-shortcode-notification.php:59
#: views/notice/wcfm-view-notices.php:26
msgid "Notice Board"
msgstr "Tableau d'affichage"

#: views/wcfm-view-header-panels.php:79 views/wcfm-view-menu.php:145
msgid "Logout"
msgstr "Deconnexion"

#: views/wcfm-view-menu.php:69 views/settings/wcfm-view-settings.php:340
msgid "Home"
msgstr "Accueil"

#: controllers/articles/wcfm-controller-articles-manage.php:37
#: controllers/coupons/wcfm-controller-coupons-manage.php:37
#: controllers/customers/wcfm-controller-customers-manage.php:37
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:34
#: controllers/knowledgebase/wcfm-controller-knowledgebase-manage.php:37
#: controllers/products-manager/wcfm-controller-products-manage.php:37
#: controllers/profile/wcfm-controller-profile.php:67
#: controllers/settings/wcfm-controller-dokan-settings.php:33
#: controllers/settings/wcfm-controller-settings.php:31
#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:40
#: controllers/settings/wcfm-controller-wcmarketplace-settings.php:33
#: controllers/settings/wcfm-controller-wcpvendors-settings.php:29
#: controllers/settings/wcfm-controller-wcvendors-settings.php:31
#: controllers/vendors/wcfm-controller-vendors-manage.php:29
#: controllers/vendors/wcfm-controller-vendors-new.php:48
#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:29
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:37
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:34
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:137
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:33
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:34
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:93
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse-actions.php:34
#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal-request.php:33
msgid "There has some error in submitted data."
msgstr "Il y a une erreur dans les données soumises."

#: controllers/articles/wcfm-controller-articles.php:138
#: controllers/listings/wcfm-controller-listings.php:24
#: controllers/products/wcfm-controller-products.php:25
#: controllers/products/wcfm-controller-products.php:222
#: views/articles/wcfm-view-articles-manage.php:117
#: views/articles/wcfm-view-articles.php:11
#: views/listings/wcfm-view-listings.php:37
#: views/products/wcfm-view-products.php:11
#: views/products-manager/wcfm-view-products-manage.php:415
msgid "Published"
msgstr "Publié"

#: controllers/articles/wcfm-controller-articles.php:158
#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:103
#: controllers/listings/wcfm-controller-listings.php:127
#: controllers/notice/wcfm-controller-notices.php:77
#: controllers/products/wcfm-controller-products.php:327
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:105
#: views/articles/wcfm-view-articles-manage.php:387
#: views/enquiry/wcfm-view-my-account-enquiry.php:60
#: views/notice/wcfm-view-notice-manage.php:59
#: views/products-manager/wcfm-view-products-manage.php:798
#: views/products-popup/wcfm-view-product-popup.php:302
msgid "View"
msgstr "Voir"

#: controllers/capability/wcfm-controller-capability.php:39
msgid "Capability saved successfully"
msgstr "Enregistré avec succès"

#: controllers/customers/wcfm-controller-customers-details.php:96
#: controllers/customers/wcfm-controller-customers-details.php:103
#: controllers/orders/wcfm-controller-dokan-orders.php:161
#: controllers/orders/wcfm-controller-dokan-orders.php:168
#: controllers/orders/wcfm-controller-orders.php:127
#: controllers/orders/wcfm-controller-orders.php:134
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:185
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:192
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:184
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:191
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:154
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:161
#: controllers/orders/wcfm-controller-wcvendors-orders.php:182
#: controllers/orders/wcfm-controller-wcvendors-orders.php:189
#, php-format
msgctxt "full name"
msgid "%1$s %2$s"
msgstr "%1$s %2$s"

#: controllers/customers/wcfm-controller-customers-details.php:107
#: controllers/customers/wcfm-controller-customers-details.php:113
#: controllers/orders/wcfm-controller-dokan-orders.php:172
#: controllers/orders/wcfm-controller-dokan-orders.php:178
#: controllers/orders/wcfm-controller-orders.php:138
#: controllers/orders/wcfm-controller-orders.php:144
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:196
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:202
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:195
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:201
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:165
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:171
#: controllers/orders/wcfm-controller-wcvendors-orders.php:193
#: controllers/orders/wcfm-controller-wcvendors-orders.php:199
msgid "Guest"
msgstr "Anonyme"

#: controllers/customers/wcfm-controller-customers-details.php:117
#: controllers/customers/wcfm-controller-customers-details.php:119
#: controllers/orders/wcfm-controller-dokan-orders.php:182
#: controllers/orders/wcfm-controller-dokan-orders.php:184
#: controllers/orders/wcfm-controller-orders.php:148
#: controllers/orders/wcfm-controller-orders.php:150
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:206
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:208
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:205
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:207
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:175
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:177
#: controllers/orders/wcfm-controller-wcvendors-orders.php:203
#: controllers/orders/wcfm-controller-wcvendors-orders.php:205
#: views/enquiry/wcfm-view-enquiry-tab.php:64
msgid "by"
msgstr "par"

#: controllers/customers/wcfm-controller-customers-details.php:140
#: controllers/orders/wcfm-controller-dokan-orders.php:205
#: controllers/orders/wcfm-controller-orders.php:171
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:244
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:237
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:233
#: controllers/orders/wcfm-controller-wcvendors-orders.php:220
#, php-format
msgid "%d item"
msgid_plural "%d items"
msgstr[0] "%d objet"
msgstr[1] "%d objets"

#: controllers/customers/wcfm-controller-customers-details.php:147
#: controllers/orders/wcfm-controller-dokan-orders.php:230
#: controllers/orders/wcfm-controller-orders.php:196
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:89
#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:139
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:132
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse.php:104
msgid "Via"
msgstr "Par"

#: controllers/customers/wcfm-controller-customers-details.php:159
#: controllers/orders/wcfm-controller-dokan-orders.php:249
#: controllers/orders/wcfm-controller-orders.php:227
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:321
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:303
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:285
#: controllers/orders/wcfm-controller-wcvendors-orders.php:334
msgid "Mark as Complete"
msgstr "Marquer comme complété"

#: controllers/customers/wcfm-controller-customers-details.php:167
#: controllers/customers/wcfm-controller-customers-details.php:171
#: controllers/orders/wcfm-controller-orders.php:236
#: views/capability/wcfm-view-capability.php:402
#: views/orders/wcfm-view-orders-details.php:147
#: views/orders/wcfm-view-orders-details.php:149
msgid "PDF Invoice"
msgstr "Facture PDF"

#: controllers/customers/wcfm-controller-customers-details.php:168
#: views/capability/wcfm-view-capability.php:408
msgid "PDF Packing Slip"
msgstr "Bon de livraison PDF"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:21
msgid "Paid & Confirmed"
msgstr "Payé et confirmé"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/customers/wcfm-controller-customers-details.php:397
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:23
msgid "Pending Confirmation"
msgstr "En attente de confirmation"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/customers/wcfm-controller-customers-details.php:397
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
msgid "Un-paid"
msgstr "Non payé"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/customers/wcfm-controller-customers-details.php:397
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:20
msgid "Complete"
msgstr "Terminé"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/customers/wcfm-controller-customers-details.php:397
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:22
msgid "Confirmed"
msgstr "Confirmé"

#: controllers/customers/wcfm-controller-customers-details.php:275
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:114
msgid "#"
msgstr "#"

#: controllers/customers/wcfm-controller-customers-details.php:289
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:128
#, php-format
msgctxt "Guest string with name from booking order in brackets"
msgid "Guest (%s)"
msgstr "Client (%s)"

#: controllers/customers/wcfm-controller-customers-details.php:357
#: controllers/customers/wcfm-controller-customers-details.php:531
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:203
msgid "Mark as Confirmed"
msgstr "Marquer comme confirmée"

#: controllers/customers/wcfm-controller-customers-details.php:400
msgid "Partial Paid"
msgstr "Paiement Partiel"

#: controllers/customers/wcfm-controller-customers-details.php:476
#, php-format
msgctxt "Guest string with name from appointment order in brackets"
msgid "Guest (%s)"
msgstr "Client (%s)"

#: controllers/customers/wcfm-controller-customers-manage.php:127
#: controllers/vendors/wcfm-controller-vendors-new.php:155
msgid "Dear"
msgstr "Cher"

#: controllers/customers/wcfm-controller-customers-manage.php:129
#: controllers/vendors/wcfm-controller-vendors-new.php:157
msgid ""
"Your account has been created as {user_role}. Follow the bellow details to "
"log into the system"
msgstr ""
"Votre compte a été créé en tant que {user_role}. Suivez les détails ci-"
"dessous pour vous connecter au système"

#: controllers/customers/wcfm-controller-customers-manage.php:131
msgid "Site"
msgstr "Site"

#: controllers/customers/wcfm-controller-customers-manage.php:133
msgid "Login"
msgstr "Connexion"

#: controllers/customers/wcfm-controller-customers-manage.php:135
#: controllers/vendors/wcfm-controller-vendors-new.php:165
#: views/profile/wcfm-view-profile.php:233
msgid "Password"
msgstr "Mot de passe"

#: controllers/customers/wcfm-controller-customers-manage.php:145
#: controllers/vendors/wcfm-controller-vendors-new.php:176
msgid "New Account"
msgstr "Nouveau Compte"

#: controllers/customers/wcfm-controller-customers-manage.php:156
#, php-format
msgid "A new customer <b>%s</b> added to the store by <b>%s</b>"
msgstr "Un nouveau <b>%s</b> ajouté au magasin par <b>%s</b>"

#: controllers/customers/wcfm-controller-customers.php:143
#: views/customers/wcfm-view-customers-manage.php:94
msgid "Manage Customer"
msgstr "Gérer le client"

#: controllers/customers/wcfm-controller-customers.php:145
#: views/capability/wcfm-view-capability.php:420
#: views/customers/wcfm-view-customers-details.php:103
#: views/customers/wcfm-view-customers-manage.php:101
msgid "Edit Customer"
msgstr "Editer client"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:100
msgid "Reply for your Inquiry"
msgstr "Réponse à votre question"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:103
#, php-format
msgid ""
"We recently have a enquiry from you regarding \"%s\". Please check below for "
"our input for the same: "
msgstr ""
"Nous avons récemment une enquête de votre part concernant \" %s\". Veuillez "
"vérifier ci-dessous pour notre entrée pour le même:"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:117
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:183
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:194
msgid "Inquiry Reply"
msgstr "Réponse à la question"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:124
#, php-format
msgid "New reply posted for Inquiry <b>%s</b>"
msgstr "Nouvelle réponse à la question <b>%s</b>"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:186
msgid ""
"You have received reply for your \"{product_title}\" inquiry. Please check "
"below for the details: "
msgstr ""
"Vous avez recu une réponse à votre question sur le produit "
"\"{product_title}\". Ci-dessous pour les déails: "

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:190
msgid "Check more details here"
msgstr "Répondre dans votre espace vendeur :"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:202
msgid "Reply to Inquiry"
msgstr "Réponse à une question"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:207
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:221
#, php-format
msgid "New reply received for Inquiry <b>%s</b>"
msgstr "Nouvelle réponse à la question <b>%s</b>"

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:46
msgid "Captcha failed, please try again."
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:123
#: views/enquiry/wcfm-view-enquiry-manage.php:97
#: views/enquiry/wcfm-view-enquiry.php:82
#: views/enquiry/wcfm-view-enquiry.php:94
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:85
#: views/orders/wcfm-view-orders.php:94 views/orders/wcfm-view-orders.php:114
#: views/vendors/wcfm-view-vendors.php:73
#: views/vendors/wcfm-view-vendors.php:94
msgid "Additional Info"
msgstr "Informations complémentaires"

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:135
msgid "New enquiry for"
msgstr "Nouvelle demande de renseignement pour"

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:138
#, php-format
msgid "You have a recent enquiry for %s."
msgstr "Vous avez une demande d'information pour %s."

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:143
#, php-format
msgid "To respond to this Enquiry, please %sClick Here%s"
msgstr "Pour répondre à cette demande, %sCliquez ici%s"

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:168
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:186
#, php-format
msgid "New Inquiry <b>%s</b> received for <b>%s</b>"
msgstr "Nouvelle question <b>%s</b> recu pour <b>%s</b>"

#: controllers/listings/wcfm-controller-listings.php:26
#: views/listings/wcfm-view-listings.php:39
msgid "Expired"
msgstr "Expiré"

#: controllers/listings/wcfm-controller-listings.php:27
#: views/articles/wcfm-view-articles-manage.php:128
#: views/articles/wcfm-view-articles-manage.php:381
#: views/listings/wcfm-view-listings.php:40
#: views/products-manager/wcfm-view-products-manage.php:426
#: views/products-manager/wcfm-view-products-manage.php:792
#: views/products-popup/wcfm-view-product-popup.php:296
msgid "Preview"
msgstr "Prévisualiser"

#: controllers/listings/wcfm-controller-listings.php:28
msgid "Pending Payment"
msgstr "Paiement en attente"

#: controllers/messages/wcfm-controller-message-sent.php:47
msgid "Message sent successfully"
msgstr "Message envoyé avec succès"

#: controllers/messages/wcfm-controller-messages.php:153
msgid "System"
msgstr "Système"

#: controllers/messages/wcfm-controller-messages.php:163
#: controllers/messages/wcfm-controller-messages.php:174
#: controllers/messages/wcfm-controller-messages.php:190
#: controllers/messages/wcfm-controller-messages.php:201
msgid "You"
msgstr "Vous"

#: controllers/messages/wcfm-controller-messages.php:213
#: controllers/messages/wcfm-controller-messages.php:215
msgid "Approve / Reject"
msgstr "Approuver / Rejeter"

#: controllers/messages/wcfm-controller-messages.php:217
#: views/messages/wcfm-view-messages.php:100
msgid "Mark Read"
msgstr "Marquer comme lu"

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:282
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:265
msgid "UNPAID"
msgstr "NON PAYÉ"

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:286
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:269
#: controllers/orders/wcfm-controller-wcvendors-orders.php:298
msgid "PAID"
msgstr "PAYÉ"

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:290
msgid "REQUESTED"
msgstr ""

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:294
msgid "CANCELLED"
msgstr ""

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:330
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:312
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:293
#: controllers/orders/wcfm-controller-wcvendors-orders.php:342
msgid "Mark Shipped"
msgstr "Marquer comme expédié"

#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:273
#: controllers/orders/wcfm-controller-wcvendors-orders.php:302
msgid "REVERSED"
msgstr "RETOURNE"

#: controllers/orders/wcfm-controller-wcvendors-orders.php:294
msgid "DUE"
msgstr "DATE D'ÉCHÉANCE"

#: controllers/products/wcfm-controller-products.php:233
#: views/products-manager/wcfm-view-products-manage-tabs.php:27
#: views/products-manager/wcfm-view-products-manage-tabs.php:250
msgid "In stock"
msgstr "En stock"

#: controllers/products/wcfm-controller-products.php:233
#: views/products-manager/wcfm-view-products-manage-tabs.php:27
#: views/products-manager/wcfm-view-products-manage-tabs.php:250
#: views/reports/wcfm-view-reports-menu.php:5
msgid "Out of stock"
msgstr "Rupture de stock"

#: controllers/products/wcfm-controller-products.php:233
#: views/products-manager/wcfm-view-products-manage-tabs.php:27
#: views/products-manager/wcfm-view-products-manage-tabs.php:250
msgid "On backorder"
msgstr "En rupture de stock"

#: controllers/products/wcfm-controller-products.php:252
#: views/capability/wcfm-view-capability.php:196
msgid "Grouped"
msgstr "Groupé"

#: controllers/products/wcfm-controller-products.php:254
msgid "External/Affiliate"
msgstr "Externe/Affiliation"

#: controllers/products/wcfm-controller-products.php:258
#: views/products/wcfm-view-products.php:161
#: views/products-manager/wcfm-view-products-manage-tabs.php:238
#: views/products-manager/wcfm-view-products-manage.php:461
#: views/products-popup/wcfm-view-product-popup.php:120
msgid "Virtual"
msgstr "Virtuel "

#: controllers/products/wcfm-controller-products.php:260
#: views/products/wcfm-view-products.php:157
#: views/products-manager/wcfm-view-products-manage-tabs.php:41
#: views/products-manager/wcfm-view-products-manage.php:462
#: views/products-popup/wcfm-view-product-popup.php:121
msgid "Downloadable"
msgstr "Téléchargeable"

#: controllers/products/wcfm-controller-products.php:262
#: views/capability/wcfm-view-capability.php:194
msgid "Simple"
msgstr "Simple"

#: controllers/products/wcfm-controller-products.php:266
#: views/capability/wcfm-view-capability.php:195
msgid "Variable"
msgstr "Variable"

#: controllers/products/wcfm-controller-products.php:268
msgid "Subscription"
msgstr "Abonnement"

#: controllers/products/wcfm-controller-products.php:270
msgid "Variable Subscription"
msgstr "Abonnement variable"

#: controllers/products/wcfm-controller-products.php:272
msgid "Listings Package"
msgstr "Forfait Inscriptions"

#: controllers/products/wcfm-controller-products.php:274
msgid "Resume Package"
msgstr "Package de CV"

#: controllers/products/wcfm-controller-products.php:280
msgid "Accommodation"
msgstr "Hébergement"

#: controllers/products/wcfm-controller-products.php:282
#: views/customers/wcfm-view-customers.php:65
#: views/customers/wcfm-view-customers.php:80
msgid "Appointment"
msgstr "Rendez-vous"

#: controllers/products/wcfm-controller-products.php:284
msgid "Bundle"
msgstr "Groupée"

#: controllers/products/wcfm-controller-products.php:286
msgid "Composite"
msgstr "Composé"

#: controllers/products/wcfm-controller-products.php:288
msgid "Lottery"
msgstr "Loterie"

#: controllers/products/wcfm-controller-products.php:322
msgid "Mark Approve / Publish"
msgstr "Marquer Approuver / Publier"

#: controllers/products/wcfm-controller-products.php:335
msgid "No Featured"
msgstr "Pas de mise en avant"

#: controllers/products/wcfm-controller-products.php:338
msgid "Mark Featured"
msgstr "Marque en mise en avant"

#: controllers/products/wcfm-controller-products.php:343
msgid ""
"Featured Product: Upgrade your WCFM to WCFM Ultimate to avail this feature."
msgstr ""
"Produit vedette : Améliorez votre WCFM à WCFM Ultimate pour bénéficier de "
"cette fonctionnalité."

#: controllers/products/wcfm-controller-products.php:352
msgid "Duplicate"
msgstr "Dupliquer"

#: controllers/products/wcfm-controller-products.php:355
msgid ""
"Duplicate Product: Upgrade your WCFM to WCFM Ultimate to avail this feature."
msgstr ""
"Produit dupliqué: mettez à jour votre WCFM à WCFM Ultimate pour bénéficier "
"de cette fonctionnalité."

#: controllers/profile/wcfm-controller-profile.php:257
msgid "Email verification code invalid."
msgstr ""

#: controllers/profile/wcfm-controller-profile.php:267
#: controllers/vendors/wcfm-controller-vendors-manage.php:49
msgid "Profile saved successfully"
msgstr "Profil enregistré avec succès"

#: controllers/settings/wcfm-controller-dokan-settings.php:93
#: controllers/settings/wcfm-controller-settings.php:232
#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:181
#: controllers/settings/wcfm-controller-wcmarketplace-settings.php:290
#: controllers/settings/wcfm-controller-wcpvendors-settings.php:80
#: controllers/settings/wcfm-controller-wcvendors-settings.php:122
msgid "Settings saved successfully"
msgstr "Paramètres enregistrés avec succès"

#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:105
#: controllers/settings/wcfm-controller-wcmarketplace-settings.php:94
msgid "Shop Slug already exists."
msgstr ""

#: controllers/settings/wcfm-controller-wcpvendors-settings.php:82
msgid "Settings failed to save"
msgstr "Les paramètres n'ont pas pu être enregistrés"

#: controllers/vendors/wcfm-controller-vendors-manage.php:81
msgid "Badges saved successfully"
msgstr "Badge sauvegardé avec succès"

#: controllers/vendors/wcfm-controller-vendors-new.php:159
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:245
msgid "Store Name"
msgstr "Nom de boutique"

#: controllers/vendors/wcfm-controller-vendors-new.php:161
msgid "Click here ..."
msgstr ""

#: controllers/vendors/wcfm-controller-vendors-new.php:163
#: views/customers/wcfm-view-customers-manage.php:131
#: views/customers/wcfm-view-customers-manage.php:133
#: views/customers/wcfm-view-customers.php:60
#: views/customers/wcfm-view-customers.php:75
#: views/vendors/wcfm-view-vendors-new.php:90
msgid "Username"
msgstr "Pseudo"

#: controllers/vendors/wcfm-controller-vendors-new.php:183
#, php-format
msgid "A new vendor <b>%s</b> added ."
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:79
msgid "Store Off-line"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:84
msgid "Active Vendor"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:98
#: views/vendors/wcfm-view-vendors-manage.php:160
msgid "Email Verified"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:100
#: views/vendors/wcfm-view-vendors-manage.php:162
msgid "Email Verification Pending"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:131
msgid "Next payment on"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:148
#: controllers/vendors/wcfm-controller-vendors.php:153
#: controllers/vendors/wcfm-controller-vendors.php:161
msgid "Expiry on"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:161
msgid "Never Expire"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:199
#: views/vendors/wcfm-view-vendors-manage.php:304
msgid "Disable Vendor Account"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:201
#: views/vendors/wcfm-view-vendors-manage.php:306
msgid "Enable Vendor Account"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:63
msgid "Booking schedule updated"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:64
msgid "Booking schedule updated."
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:70
msgid "Booking schedule updated successfully"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:72
msgid "Booking schedule updated failed!"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:573
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:220
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:205
#: includes/reports/class-wcpvendors-report-sales-by-date.php:199
#: includes/reports/class-wcvendors-report-sales-by-date.php:219
#, php-format
msgid "%s total earnings"
msgstr "%s total des gains"

#: includes/reports/class-dokan-report-sales-by-date.php:574
#: includes/reports/class-wcfm-report-analytics.php:127
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:221
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:206
#: includes/reports/class-wcvendors-report-sales-by-date.php:220
msgid ""
"This is the sum of the earned commission including shipping and taxes if "
"applicable."
msgstr ""
"Il s'agit de la somme de la commission, y compris les frais d'expédition et "
"les taxes, le cas échéant."

#: includes/reports/class-dokan-report-sales-by-date.php:580
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:229
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:214
#: includes/reports/class-wcpvendors-report-sales-by-date.php:208
#: includes/reports/class-wcvendors-report-sales-by-date.php:227
#, php-format
msgid "%s total withdrawal"
msgstr "%s retrait d'argent"

#: includes/reports/class-dokan-report-sales-by-date.php:581
msgid ""
"This is the sum of the commission withdraw including shipping and taxes if "
"applicable."
msgstr ""
"Il s'agit de la somme de la commission, y compris les frais d'expédition et "
"les taxes, le cas échéant."

#: includes/reports/class-dokan-report-sales-by-date.php:805
#: includes/reports/class-wcfm-report-sales-by-date.php:712
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:531
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:527
#: includes/reports/class-wcpvendors-report-sales-by-date.php:430
#: includes/reports/class-wcvendors-report-sales-by-date.php:541
#: views/customers/wcfm-view-customers-details.php:271
#: views/customers/wcfm-view-customers-details.php:281
#: views/orders/wcfm-view-orders.php:86 views/orders/wcfm-view-orders.php:106
#: views/vendors/wcfm-view-vendors.php:65
#: views/vendors/wcfm-view-vendors.php:86
msgid "Gross Sales"
msgstr "Montant brut des ventes"

#: includes/reports/class-dokan-report-sales-by-date.php:840
#: includes/reports/class-wcfm-report-sales-by-date.php:728
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:580
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:566
#: includes/reports/class-wcpvendors-report-sales-by-date.php:469
#: includes/reports/class-wcvendors-report-sales-by-date.php:591
msgid "Order Counts"
msgstr "Ordre Comptes"

#: includes/reports/class-dokan-report-sales-by-date.php:848
#: includes/reports/class-wcfm-report-sales-by-date.php:736
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:588
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:574
#: includes/reports/class-wcpvendors-report-sales-by-date.php:477
#: includes/reports/class-wcvendors-report-sales-by-date.php:599
msgid "Order Item Counts"
msgstr "Nombre d’éléments de commande"

#: includes/reports/class-dokan-report-sales-by-date.php:856
#: includes/reports/class-wcfm-report-sales-by-date.php:752
msgid "Coupon Amounts"
msgstr "Montants de coupon"

#: includes/reports/class-dokan-report-sales-by-date.php:864
#: includes/reports/class-wcfm-report-sales-by-date.php:760
msgid "Refund Amounts"
msgstr "Montant de remboursement"

#: includes/reports/class-dokan-report-sales-by-date.php:875
#: includes/reports/class-wcfm-report-sales-by-date.php:771
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:599
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:585
#: includes/reports/class-wcpvendors-report-sales-by-date.php:488
#: includes/reports/class-wcvendors-report-sales-by-date.php:610
msgid "Sales Report by Date"
msgstr "Rapport des ventes par Date"

#: includes/reports/class-dokan-report-sales-by-date.php:893
#: includes/reports/class-wcfm-report-analytics.php:170
#: includes/reports/class-wcfm-report-sales-by-date.php:789
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:319
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:617
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:294
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:603
#: includes/reports/class-wcpvendors-report-sales-by-date.php:506
#: includes/reports/class-wcvendors-report-sales-by-date.php:316
#: includes/reports/class-wcvendors-report-sales-by-date.php:628
#: views/articles/wcfm-view-articles.php:113
#: views/articles/wcfm-view-articles.php:124
#: views/customers/wcfm-view-customers-details.php:272
#: views/customers/wcfm-view-customers-details.php:282
#: views/enquiry/wcfm-view-enquiry.php:84
#: views/enquiry/wcfm-view-enquiry.php:96
#: views/messages/wcfm-view-messages.php:126
#: views/messages/wcfm-view-messages.php:139
#: views/orders/wcfm-view-orders.php:95 views/orders/wcfm-view-orders.php:115
#: views/products/wcfm-view-products.php:205
#: views/products/wcfm-view-products.php:228
#: views/withdrawal/dokan/wcfm-view-payments.php:71
#: views/withdrawal/dokan/wcfm-view-payments.php:80
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:57
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:67
#: views/withdrawal/wcfm/wcfm-view-payments.php:77
#: views/withdrawal/wcfm/wcfm-view-payments.php:91
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:77
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:93
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:71
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:83
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:80
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:93
#: views/withdrawal/wcmp/wcfm-view-payments.php:69
#: views/withdrawal/wcmp/wcfm-view-payments.php:80
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:61
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:70
msgid "Date"
msgstr "Date"

#: includes/reports/class-dokan-report-sales-by-date.php:902
#: includes/reports/class-wcfm-report-sales-by-date.php:798
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:626
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:612
#: includes/reports/class-wcpvendors-report-sales-by-date.php:515
#: includes/reports/class-wcvendors-report-sales-by-date.php:637
#: views/coupons/wcfm-view-coupons.php:57
#: views/coupons/wcfm-view-coupons.php:67
#: views/withdrawal/dokan/wcfm-view-payments.php:68
#: views/withdrawal/dokan/wcfm-view-payments.php:77
#: views/withdrawal/wcfm/wcfm-view-payments.php:72
#: views/withdrawal/wcfm/wcfm-view-payments.php:86
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:72
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:88
msgid "Amount"
msgstr "Montant"

#: includes/reports/class-wcfm-report-analytics.php:117
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:179
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:164
#: includes/reports/class-wcvendors-report-sales-by-date.php:201
#, php-format
msgid "%s average daily sales"
msgstr "%s de ventes journalières moyennes"

#: includes/reports/class-wcfm-report-analytics.php:121
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:183
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:168
#: includes/reports/class-wcvendors-report-sales-by-date.php:205
#, php-format
msgid "%s average monthly sales"
msgstr "%s de ventes mensuelles moyennes"

#: includes/reports/class-wcfm-report-analytics.php:126
#, php-format
msgid "%s total earned commission"
msgstr "%s total commission gagnée"

#: includes/reports/class-wcfm-report-analytics.php:142
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:284
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:260
#: includes/reports/class-wcvendors-report-sales-by-date.php:282
#: views/enquiry/wcfm-view-enquiry.php:26
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:30
#: views/reports/wcfm-view-reports-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:30
msgid "Last Month"
msgstr "Dernier mois"

#: includes/reports/class-wcfm-report-analytics.php:144
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:286
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:262
#: includes/reports/class-wcvendors-report-sales-by-date.php:284
#: views/enquiry/wcfm-view-enquiry.php:24
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:32
#: views/reports/wcfm-view-reports-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:32
msgid "Last 7 Days"
msgstr "7 Derniers Jours"

#: includes/reports/class-wcfm-report-analytics.php:176
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:325
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:300
#: includes/reports/class-wcvendors-report-sales-by-date.php:322
#: views/capability/wcfm-view-capability.php:396
msgid "Export CSV"
msgstr "Export CSV"

#: includes/reports/class-wcfm-report-analytics.php:260
msgid "Daily Views"
msgstr "Vues quotidiennes"

#: includes/reports/class-wcfm-report-analytics.php:270
#: views/dashboard/wcfm-view-dashboard.php:204
#: views/dashboard/wcfm-view-dokan-dashboard.php:208
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:211
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:229
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:232
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:215
msgid "Store Analytics"
msgstr "Nombre de visites"

#: includes/reports/class-wcfm-report-sales-by-date.php:744
msgid "Net Sales"
msgstr "Ventes Nettes"

#. translators: %s: total sales
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:189
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:174
#: includes/reports/class-wcvendors-report-sales-by-date.php:211
#, php-format
msgid "%s gross sales in this period"
msgstr "%s de ventes brut pour cette période"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:190
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:175
#: includes/reports/class-wcvendors-report-sales-by-date.php:212
msgid ""
"This is the sum of the order totals after any refunds and including shipping "
"and taxes."
msgstr ""
"Il s'agit de la somme des totaux de commande après remboursement et "
"comprenant livraison et TVA."

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:202
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:187
#, php-format
msgid "%s total admin fees"
msgstr "%s total des frais d'administration"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:203
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:188
msgid ""
"This is the sum of the admin fees including shipping and taxes if applicable."
msgstr ""
"Il s'agit de la somme de la commission, y compris les frais d'expédition et "
"les taxes, le cas échéant."

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:211
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:196
#, php-format
msgid "%s total paid fees"
msgstr "%s total frais payés"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:212
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:197
msgid ""
"This is the sum of the admin fees paid including shipping and taxes if "
"applicable."
msgstr ""
"Il s'agit de la somme de la commission, y compris les frais d'expédition et "
"les taxes, le cas échéant."

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:230
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:215
#: includes/reports/class-wcvendors-report-sales-by-date.php:228
msgid ""
"This is the sum of the commission paid including shipping and taxes if "
"applicable."
msgstr ""
"Il s'agit de la somme de la commission, y compris les frais d'expédition et "
"les taxes, le cas échéant."

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:239
#, php-format
msgid "%s total refund"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:240
msgid "This is the sum of the refunds and partial refunds."
msgstr ""

#. translators: %s: total orders
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:255
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:231
#: includes/reports/class-wcvendors-report-sales-by-date.php:243
#, php-format
msgid "%s orders placed"
msgstr "%s commandes passées"

#. translators: %s: total items
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:261
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:237
#: includes/reports/class-wcvendors-report-sales-by-date.php:249
#, php-format
msgid "%s items purchased"
msgstr "%s articles achetés"

#. translators: %s: total shipping
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:268
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:244
#: includes/reports/class-wcvendors-report-sales-by-date.php:257
#: includes/reports/class-wcvendors-report-sales-by-date.php:265
#, php-format
msgid "%s charged for shipping"
msgstr "%s facturés pour la livraison"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:541
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:537
msgid "Admin Fees"
msgstr "Frais Administrateurs"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:551
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:547
#: views/vendors/wcfm-view-vendors.php:68
#: views/vendors/wcfm-view-vendors.php:89
msgid "Paid Fees"
msgstr "Cotisations Payées"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:561
msgid "Refunds"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:571
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:557
#: includes/reports/class-wcpvendors-report-sales-by-date.php:460
#: includes/reports/class-wcvendors-report-sales-by-date.php:570
#: includes/reports/class-wcvendors-report-sales-by-date.php:581
msgid "Shipping Amounts"
msgstr "Frais d'expédition"

#: includes/shortcodes/class-wcfm-shortcode-follow.php:33
msgid "Follow Me"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:105
msgid "Manage Article"
msgstr "Gérer l'article"

#: views/articles/wcfm-view-articles-manage.php:113
msgid "Edit Article"
msgstr "Modifier Article"

#: views/articles/wcfm-view-articles-manage.php:113
msgid "Add Article"
msgstr "Ajouter l’article"

#: views/articles/wcfm-view-articles-manage.php:122
#: views/articles/wcfm-view-articles.php:112
#: views/articles/wcfm-view-articles.php:123
#: views/listings/wcfm-view-listings.php:107
#: views/listings/wcfm-view-listings.php:119
#: views/products/wcfm-view-products.php:204
#: views/products/wcfm-view-products.php:227
#: views/products-manager/wcfm-view-products-manage.php:420
msgid "Views"
msgstr "Vues"

#: views/articles/wcfm-view-articles-manage.php:141
#: views/articles/wcfm-view-articles.php:65
msgid "Add New Article"
msgstr "Ajouter un nouvel article"

#: views/articles/wcfm-view-articles-manage.php:159
msgid "Article Title"
msgstr "TItre de l’annonce "

#: views/articles/wcfm-view-articles-manage.php:167
#: views/articles/wcfm-view-articles-manage.php:262
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:89
#: views/products-manager/wcfm-view-products-manage.php:482
#: views/products-manager/wcfm-view-products-manage.php:604
#: views/products-popup/wcfm-view-product-popup.php:149
#: views/settings/wcfm-view-settings.php:512
msgid "Categories"
msgstr "Catégories"

#: views/articles/wcfm-view-articles-manage.php:212
#: views/articles/wcfm-view-articles-manage.php:310
#: views/products-manager/wcfm-view-products-manage.php:531
#: views/products-manager/wcfm-view-products-manage.php:713
#: views/products-popup/wcfm-view-product-popup.php:198
msgid "Tags"
msgstr "Étiquettes"

#: views/articles/wcfm-view-articles-manage.php:212
#: views/articles/wcfm-view-articles-manage.php:310
msgid "Separate Article Tags with commas"
msgstr "Séparer les tags avec des virgules"

#: views/articles/wcfm-view-articles-manage.php:212
#: views/articles/wcfm-view-articles-manage.php:310
#: views/products-manager/wcfm-view-products-manage.php:531
#: views/products-manager/wcfm-view-products-manage.php:713
msgid "Choose from the most used tags"
msgstr "Choisir parmi les étiquettes les plus utilisées"

#: views/articles/wcfm-view-articles-manage.php:224
#: views/articles/wcfm-view-articles-manage.php:322
#: views/products-manager/wcfm-view-products-manage.php:544
#: views/products-manager/wcfm-view-products-manage.php:726
#: views/products-popup/wcfm-view-product-popup.php:211
msgid " with commas"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:239
#: views/articles/wcfm-view-articles-manage.php:343
#: views/products-manager/wcfm-view-products-manage.php:566
#: views/products-manager/wcfm-view-products-manage.php:754
#: views/products-popup/wcfm-view-product-popup.php:264
msgid "Short Description"
msgstr "Description courte"

#: views/articles/wcfm-view-articles-manage.php:240
#: views/articles/wcfm-view-articles-manage.php:344
#: views/coupons/wcfm-view-coupons-manage.php:93
#: views/products-manager/wcfm-view-products-manage.php:567
#: views/products-manager/wcfm-view-products-manage.php:755
#: views/products-popup/wcfm-view-product-popup.php:265
msgid "Description"
msgstr "Description"

#: views/articles/wcfm-view-articles-manage.php:369
#: views/articles/wcfm-view-articles-manage.php:371
#: views/coupons/wcfm-view-coupons-manage.php:120
#: views/coupons/wcfm-view-coupons-manage.php:122
#: views/customers/wcfm-view-customers-manage.php:207
#: views/enquiry/wcfm-view-enquiry-form.php:151
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:132
#: views/notice/wcfm-view-notice-manage.php:95
#: views/products-manager/wcfm-view-products-manage.php:780
#: views/products-manager/wcfm-view-products-manage.php:782
#: views/products-popup/wcfm-view-product-popup.php:284
#: views/products-popup/wcfm-view-product-popup.php:286
#: views/vendors/wcfm-view-vendors-new.php:156
msgid "Submit"
msgstr "Soumettre"

#: views/articles/wcfm-view-articles-manage.php:369
#: views/articles/wcfm-view-articles-manage.php:371
#: views/coupons/wcfm-view-coupons-manage.php:120
#: views/coupons/wcfm-view-coupons-manage.php:122
#: views/products-manager/wcfm-view-products-manage.php:780
#: views/products-manager/wcfm-view-products-manage.php:782
#: views/products-popup/wcfm-view-product-popup.php:284
#: views/products-popup/wcfm-view-product-popup.php:286
msgid "Submit for Review"
msgstr "Soumettre à la relecture"

#: views/articles/wcfm-view-articles.php:83
msgid "Select a category"
msgstr "Choisir une catégorie"

#: views/articles/wcfm-view-articles.php:109
#: views/articles/wcfm-view-articles.php:120
#: views/products/wcfm-view-products.php:197
#: views/products/wcfm-view-products.php:220
#: views/products-manager/wcfm-view-products-manage-tabs.php:241
msgid "Image"
msgstr "Image"

#: views/articles/wcfm-view-articles.php:111
#: views/articles/wcfm-view-articles.php:122
#: views/customers/wcfm-view-customers-details.php:192
#: views/customers/wcfm-view-customers-details.php:203
#: views/customers/wcfm-view-customers-details.php:230
#: views/customers/wcfm-view-customers-details.php:241
#: views/customers/wcfm-view-customers-details.php:268
#: views/customers/wcfm-view-customers-details.php:278
#: views/listings/wcfm-view-listings.php:104
#: views/listings/wcfm-view-listings.php:116
#: views/orders/wcfm-view-orders.php:81 views/orders/wcfm-view-orders.php:101
#: views/products/wcfm-view-products.php:200
#: views/products/wcfm-view-products.php:223
#: views/products-manager/wcfm-view-products-manage-tabs.php:185
#: views/vendors/wcfm-view-vendors.php:58
#: views/vendors/wcfm-view-vendors.php:79
#: views/wc_bookings/wcfm-view-wcbookings.php:115
#: views/wc_bookings/wcfm-view-wcbookings.php:127
#: views/withdrawal/dokan/wcfm-view-payments.php:67
#: views/withdrawal/dokan/wcfm-view-payments.php:76
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:53
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:63
#: views/withdrawal/wcfm/wcfm-view-payments.php:68
#: views/withdrawal/wcfm/wcfm-view-payments.php:82
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:139
#: views/withdrawal/wcmp/wcfm-view-payments.php:63
#: views/withdrawal/wcmp/wcfm-view-payments.php:74
msgid "Status"
msgstr "Statut"

#: views/articles/wcfm-view-articles.php:114
#: views/articles/wcfm-view-articles.php:125
msgid "Author"
msgstr "Auteur"

#: views/articles/wcfm-view-articles.php:115
#: views/articles/wcfm-view-articles.php:126
#: views/customers/wcfm-view-customers-details.php:198
#: views/customers/wcfm-view-customers-details.php:209
#: views/customers/wcfm-view-customers-details.php:273
#: views/customers/wcfm-view-customers-details.php:283
#: views/customers/wcfm-view-customers.php:69
#: views/customers/wcfm-view-customers.php:84
#: views/enquiry/wcfm-view-enquiry.php:85
#: views/enquiry/wcfm-view-enquiry.php:97
#: views/enquiry/wcfm-view-my-account-enquiry.php:44
#: views/enquiry/wcfm-view-my-account-enquiry.php:59
#: views/knowledgebase/wcfm-view-knowledgebase.php:73
#: views/knowledgebase/wcfm-view-knowledgebase.php:80
#: views/messages/wcfm-view-messages.php:127
#: views/messages/wcfm-view-messages.php:140
#: views/notice/wcfm-view-notices.php:52 views/notice/wcfm-view-notices.php:58
#: views/orders/wcfm-view-orders.php:96 views/orders/wcfm-view-orders.php:116
#: views/products/wcfm-view-products.php:208
#: views/products/wcfm-view-products.php:231
#: views/reports/wcfm-view-reports-out-of-stock.php:54
#: views/reports/wcfm-view-reports-out-of-stock.php:63
#: views/wc_bookings/wcfm-view-wcbookings.php:122
#: views/wc_bookings/wcfm-view-wcbookings.php:134
msgid "Actions"
msgstr "Actions"

#: views/capability/wcfm-view-capability.php:140
#: views/settings/wcfm-view-settings.php:117
msgid "Capability Controller"
msgstr "Contrôleur de capacité"

#: views/capability/wcfm-view-capability.php:147
msgid "Capability Settings"
msgstr "Paramètres de capacité"

#: views/capability/wcfm-view-capability.php:150
msgid "Dashboard Settings"
msgstr "Paramètres du tableau de bord"

#: views/capability/wcfm-view-capability.php:166
#: views/vendors/wcfm-view-vendors-manage.php:336
msgid "Vendors Capability"
msgstr "Capacitées des vendeurs"

#: views/capability/wcfm-view-capability.php:171
msgid "Configure what to hide from all Vendors"
msgstr "Configurer ce que vous voulez cacher pour tous les vendeurs"

#: views/capability/wcfm-view-capability.php:180
msgid "Manage Products"
msgstr "Gérer les produits"

#: views/capability/wcfm-view-capability.php:181
msgid "Add Products"
msgstr "Ajouter des produits"

#: views/capability/wcfm-view-capability.php:184
msgid "Auto Publish Live Products"
msgstr "Auto publier des produits en direct"

#: views/capability/wcfm-view-capability.php:190
msgid "Types"
msgstr "Types"

#: views/capability/wcfm-view-capability.php:197
msgid "External / Affiliate"
msgstr "Prestation externe ou d'affiliation"

#: views/capability/wcfm-view-capability.php:202
msgid "Panels"
msgstr "Panneaux"

#: views/capability/wcfm-view-capability.php:206
#: views/products-manager/wcfm-view-products-manage-tabs.php:18
#: views/products-manager/wcfm-view-products-manage-tabs.php:206
msgid "Inventory"
msgstr "Inventaire"

#: views/capability/wcfm-view-capability.php:208
msgid "Taxes"
msgstr "Taxes"

#: views/capability/wcfm-view-capability.php:209
#: views/products-manager/wcfm-view-products-manage-tabs.php:265
msgid "Linked"
msgstr "Connecté"

#: views/capability/wcfm-view-capability.php:210
#: views/products-manager/wcfm-view-products-manage-tabs.php:129
#: views/products-manager/wcfm-view-products-manage-tabs.php:136
msgid "Attributes"
msgstr "Attributs"

#: views/capability/wcfm-view-capability.php:211
msgid "Advanced"
msgstr "Avancé"

#: views/capability/wcfm-view-capability.php:223
msgid "Access"
msgstr "Accès"

#: views/capability/wcfm-view-capability.php:233
msgid "Marketplace"
msgstr ""

#: views/capability/wcfm-view-capability.php:238
msgid "Show Email"
msgstr ""

#: views/capability/wcfm-view-capability.php:239
msgid "Show Phone"
msgstr ""

#: views/capability/wcfm-view-capability.php:240
msgid "Show Address"
msgstr ""

#: views/capability/wcfm-view-capability.php:241
msgid "Show Map"
msgstr ""

#: views/capability/wcfm-view-capability.php:242
msgid "Show Social"
msgstr ""

#: views/capability/wcfm-view-capability.php:243
msgid "Show Follower"
msgstr ""

#: views/capability/wcfm-view-capability.php:244
msgid "Show Policy"
msgstr ""

#: views/capability/wcfm-view-capability.php:245
msgid "Store Hours"
msgstr ""

#: views/capability/wcfm-view-capability.php:246
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:732
#: views/settings/wcfm-view-wcmarketplace-settings.php:699
msgid "Customer Support"
msgstr "Service client"

#: views/capability/wcfm-view-capability.php:248
msgid "Reviews Manage"
msgstr ""

#: views/capability/wcfm-view-capability.php:249
msgid "Ledger Book"
msgstr ""

#: views/capability/wcfm-view-capability.php:250
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:232
msgid "Video Banner"
msgstr ""

#: views/capability/wcfm-view-capability.php:251
msgid "Slider Banner"
msgstr ""

#: views/capability/wcfm-view-capability.php:266
msgid "Withdrwal Request"
msgstr ""

#: views/capability/wcfm-view-capability.php:267
#: views/withdrawal/dokan/wcfm-view-withdrawal.php:64
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:57
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:42
msgid "Transactions"
msgstr "Transactions"

#: views/capability/wcfm-view-capability.php:278
msgid "Integrations"
msgstr ""

#: views/capability/wcfm-view-capability.php:282
msgid "Install WC Bookings to enable this feature."
msgstr "Installez  WC Booking pour activer cette fonction."

#: views/capability/wcfm-view-capability.php:288
#: views/capability/wcfm-view-capability.php:361
msgid "Manage Appointments"
msgstr "Gérer les réservations"

#: views/capability/wcfm-view-capability.php:288
msgid "Install WC Appointments to enable this feature."
msgstr "Installez les rendez-vous WC pour activer cette fonctionnalité."

#: views/capability/wcfm-view-capability.php:294
msgid "Install WC Subscriptions to enable this feature."
msgstr "Installez  WC Subscriptions pour activer cette fonction."

#: views/capability/wcfm-view-capability.php:299
#: views/capability/wcfm-view-capability.php:302
msgid "Associate Listings"
msgstr "Liste des associés"

#: views/capability/wcfm-view-capability.php:302
msgid "Install WP Job Manager to enable this feature."
msgstr "Installez WP Job Manager pour activer cette fonctionnalité."

#: views/capability/wcfm-view-capability.php:311
msgid "Manage Articles"
msgstr "Gérer les articles"

#: views/capability/wcfm-view-capability.php:312
msgid "Add Articles"
msgstr "Ajouter des Articles"

#: views/capability/wcfm-view-capability.php:313
msgid "Publish Articles"
msgstr "Publier des Articles"

#: views/capability/wcfm-view-capability.php:314
msgid "Edit Live Articles"
msgstr "Editer des articles en direct"

#: views/capability/wcfm-view-capability.php:315
msgid "Auto Publish Live Articles"
msgstr "Auto publier des articles en direct"

#: views/capability/wcfm-view-capability.php:316
msgid "Delete Articles"
msgstr "Supprimer les articles"

#: views/capability/wcfm-view-capability.php:327
msgid "Manage Coupons"
msgstr "Gérer les Codes Promo"

#: views/capability/wcfm-view-capability.php:328
msgid "Add Coupons"
msgstr "Ajouter des coupons"

#: views/capability/wcfm-view-capability.php:329
msgid "Publish Coupons"
msgstr "Publier Coupons"

#: views/capability/wcfm-view-capability.php:330
msgid "Edit Live Coupons"
msgstr "Edition Rapide Promo"

#: views/capability/wcfm-view-capability.php:331
msgid "Auto Publish Live Coupons"
msgstr "Publication automatique des coupons en direct"

#: views/capability/wcfm-view-capability.php:332
msgid "Delete Coupons"
msgstr "Supprimer Promos"

#: views/capability/wcfm-view-capability.php:333
msgid "Allow Free Shipping"
msgstr "Consentire il trasporto gratuito"

#: views/capability/wcfm-view-capability.php:341
#: views/customers/wcfm-view-customers-details.php:186
#: views/customers/wcfm-view-customers.php:64
#: views/customers/wcfm-view-customers.php:79
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:24
msgid "Bookings"
msgstr "Réservations"

#: views/capability/wcfm-view-capability.php:345
msgid "Manual Booking"
msgstr "Réservation manuelle"

#: views/capability/wcfm-view-capability.php:346
msgid "Manage Resource"
msgstr "Configurer les matières"

#: views/capability/wcfm-view-capability.php:358
#: views/customers/wcfm-view-customers-details.php:224
msgid "Appointments"
msgstr "Rendez-vous"

#: views/capability/wcfm-view-capability.php:362
msgid "Manual Appointment"
msgstr "Manuel des rdv"

#: views/capability/wcfm-view-capability.php:363
msgid "Manage Staff"
msgstr "Gérer le personnel"

#: views/capability/wcfm-view-capability.php:364
msgid "Appointments List"
msgstr "Liste des rendez-vous"

#: views/capability/wcfm-view-capability.php:365
msgid "Appointments Calendar"
msgstr "Calendrier des rendez-vous"

#: views/capability/wcfm-view-capability.php:379
msgid "Subscriptions List"
msgstr "Liste d'abonnement"

#: views/capability/wcfm-view-capability.php:380
msgid "Subscription Details"
msgstr "Détails de l'abonnement"

#: views/capability/wcfm-view-capability.php:381
msgid "Subscription Status Update"
msgstr "Mise à jour du statut de l'abonnement"

#: views/capability/wcfm-view-capability.php:382
msgid "Subscription Schedule Update"
msgstr "Mise à jour du calendrier d'abonnement"

#: views/capability/wcfm-view-capability.php:394
msgid "View Comments"
msgstr "Voir les commentaires"

#: views/capability/wcfm-view-capability.php:395
msgid "Submit Comments"
msgstr "Envoyer les commentaires"

#: views/capability/wcfm-view-capability.php:406
msgid "Store Invoice"
msgstr ""

#: views/capability/wcfm-view-capability.php:407
msgid "Commission Invoice"
msgstr ""

#: views/capability/wcfm-view-capability.php:418
#: views/customers/wcfm-view-customers-details.php:100
#: views/customers/wcfm-view-customers-manage.php:110
#: views/customers/wcfm-view-customers.php:32
msgid "Manage Customers"
msgstr "Gérer Clients"

#: views/capability/wcfm-view-capability.php:419
#: views/customers/wcfm-view-customers-manage.php:101
msgid "Add Customer"
msgstr "Ajouter client"

#: views/capability/wcfm-view-capability.php:421
msgid "View Customer"
msgstr "Voir Client"

#: views/capability/wcfm-view-capability.php:422
msgid "View Customer Orders"
msgstr "Voir les Commandes Clients"

#: views/capability/wcfm-view-capability.php:423
msgid "View Customer Name"
msgstr ""

#: views/capability/wcfm-view-capability.php:424
msgid "View Customer Email"
msgstr "Afficher l'adresse mail du client"

#: views/capability/wcfm-view-capability.php:425
#: views/orders/wcfm-view-orders.php:84 views/orders/wcfm-view-orders.php:104
msgid "Billing Address"
msgstr "Adresse de facturation"

#: views/capability/wcfm-view-capability.php:426
#: views/orders/wcfm-view-orders.php:85 views/orders/wcfm-view-orders.php:105
msgid "Shipping Address"
msgstr "Adresse de livraison"

#: views/capability/wcfm-view-capability.php:430
msgid "Customer Limit"
msgstr ""

#: views/capability/wcfm-view-capability.php:454
msgid "Advanced Capability"
msgstr "Capacité avancée"

#: views/capability/wcfm-view-capability.php:471
#: views/capability/wcfm-view-capability.php:481
msgid "Shop Managers Capability"
msgstr "Capacité des gestionnaires de magasin"

#: views/capability/wcfm-view-capability.php:493
#: views/capability/wcfm-view-capability.php:503
msgid "Shop Staffs Capability"
msgstr "Capacité du personnel de magasin"

#: views/capability/wcfm-view-capability.php:512
msgid "*** Vendor Managers are treated as Shop Staff for a Vendor Store."
msgstr ""
"Les gestionnaires de fournisseurs sont traités comme des employés de magasin "
"pour un magasin fournisseur."

#: views/capability/wcfm-view-capability.php:524
#: views/profile/wcfm-view-profile.php:350
#: views/settings/wcfm-view-dokan-settings.php:511
#: views/settings/wcfm-view-settings.php:536
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:791
#: views/settings/wcfm-view-wcmarketplace-settings.php:766
#: views/settings/wcfm-view-wcpvendors-settings.php:459
#: views/settings/wcfm-view-wcvendors-settings.php:464
msgid "Save"
msgstr "Sauvegarder"

#: views/coupons/wcfm-view-coupons-manage.php:55
msgid "Manage Coupon"
msgstr "Gérer code promo"

#: views/coupons/wcfm-view-coupons-manage.php:62
msgid "Edit Coupon"
msgstr "Modifier le coupon"

#: views/coupons/wcfm-view-coupons-manage.php:62
msgid "Add Coupon"
msgstr "Ajouter un code promo"

#: views/coupons/wcfm-view-coupons-manage.php:77
#: views/coupons/wcfm-view-coupons.php:43
msgid "Add New Coupon"
msgstr "Ajouter un nouveau code promo"

#: views/coupons/wcfm-view-coupons-manage.php:92
#: views/coupons/wcfm-view-coupons.php:55
#: views/coupons/wcfm-view-coupons.php:65
msgid "Code"
msgstr "Code"

#: views/coupons/wcfm-view-coupons-manage.php:94
msgid "Discount Type"
msgstr "Type de remise"

#: views/coupons/wcfm-view-coupons-manage.php:94
msgid "Percentage discount"
msgstr "Remise en pourcentage"

#: views/coupons/wcfm-view-coupons-manage.php:94
msgid "Fixed Cart Discount"
msgstr "Remise panier fixe"

#: views/coupons/wcfm-view-coupons-manage.php:94
msgid "Fixed Product Discount"
msgstr "Remise produit fixe"

#: views/coupons/wcfm-view-coupons-manage.php:95
msgid "Coupon Amount"
msgstr "Montant du code promo"

#: views/coupons/wcfm-view-coupons-manage.php:96
msgid "Coupon expiry date"
msgstr "Date d&rsquo;expiration du code"

#: views/coupons/wcfm-view-coupons-manage.php:100
msgid "Allow free shipping"
msgstr "Permettre la livraison gratuite"

#: views/coupons/wcfm-view-coupons-manage.php:100
msgid ""
"Check this box if the coupon grants free shipping. The free shipping method "
"must be enabled and be set to require \"a valid free shipping coupon\" (see "
"the \"Free Shipping Requires\" setting)."
msgstr ""
"Cochez cette case si le coupon offre la livraison gratuite. La méthode de "
"livraison gratuite doit être activée et demande \"un bon de livraison "
"gratuite valide\" (voir les paramètres de \"Livraison gratuite \"."

#: views/coupons/wcfm-view-coupons.php:24
msgid "Coupons Listing"
msgstr "Liste de bons de réduction"

#: views/coupons/wcfm-view-coupons.php:35
#: views/listings/wcfm-view-listings.php:82
#: views/orders/wcfm-view-orders.php:64
#: views/products/wcfm-view-products.php:72
#: views/wc_bookings/wcfm-view-wcbookings.php:63
msgid "Screen Manager"
msgstr "Gestionnaire de l'écran"

#: views/coupons/wcfm-view-coupons.php:58
#: views/coupons/wcfm-view-coupons.php:68
msgid "Usage Limit"
msgstr "Limite d’utilisation"

#: views/coupons/wcfm-view-coupons.php:59
#: views/coupons/wcfm-view-coupons.php:69
msgid "Expiry date"
msgstr "Date d'expiration"

#: views/coupons/wcfm-view-coupons.php:60
#: views/coupons/wcfm-view-coupons.php:70
#: views/listings/wcfm-view-listings.php:110
#: views/listings/wcfm-view-listings.php:122
#: views/vendors/wcfm-view-vendors.php:74
#: views/vendors/wcfm-view-vendors.php:95
msgid "Action"
msgstr "Action"

#: views/customers/wcfm-view-customers-details.php:48
msgid "Customer Details"
msgstr "Détails client"

#: views/customers/wcfm-view-customers-details.php:107
#: views/customers/wcfm-view-customers-manage.php:113
#: views/customers/wcfm-view-customers.php:42
msgid "Add New Customer"
msgstr "Ajouter un nouveau client"

#: views/customers/wcfm-view-customers-details.php:128
msgid "total money spent"
msgstr "total  argent dépensé"

#: views/customers/wcfm-view-customers-details.php:140
#, php-format
msgid "<strong>%s order</strong><br />"
msgid_plural "<strong>%s orders</strong><br />"
msgstr[0] "<strong> %s commande </strong> < br/>"
msgstr[1] "<strong> %s commandes </strong> < br/>"

#: views/customers/wcfm-view-customers-details.php:142
msgid "total order placed"
msgstr "Total des commandes passées"

#: views/customers/wcfm-view-customers-details.php:160
#: views/customers/wcfm-view-customers-manage.php:137
#: views/customers/wcfm-view-customers.php:61
#: views/customers/wcfm-view-customers.php:76
#: views/enquiry/wcfm-view-enquiry-form.php:61
#: views/profile/wcfm-view-profile.php:206
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:739
#: views/settings/wcfm-view-wcmarketplace-settings.php:714
#: views/vendors/wcfm-view-vendors-manage.php:284
#: views/vendors/wcfm-view-vendors-new.php:92
msgid "Email"
msgstr "E-mail"

#: views/customers/wcfm-view-customers-details.php:161
#: views/customers/wcfm-view-customers-manage.php:138
#: views/customers/wcfm-view-customers-manage.php:167
#: views/customers/wcfm-view-customers-manage.php:185
#: views/profile/wcfm-view-profile.php:204
#: views/profile/wcfm-view-profile.php:285
#: views/profile/wcfm-view-profile.php:301
#: views/vendors/wcfm-view-vendors-manage.php:285
#: views/vendors/wcfm-view-vendors-manage.php:328
#: views/vendors/wcfm-view-vendors-new.php:93
#: views/vendors/wcfm-view-vendors-new.php:116
#: views/vendors/wcfm-view-vendors-new.php:134
msgid "First Name"
msgstr "Prénom"

#: views/customers/wcfm-view-customers-details.php:162
#: views/customers/wcfm-view-customers-manage.php:139
#: views/customers/wcfm-view-customers-manage.php:168
#: views/customers/wcfm-view-customers-manage.php:186
#: views/profile/wcfm-view-profile.php:205
#: views/profile/wcfm-view-profile.php:286
#: views/profile/wcfm-view-profile.php:302
#: views/vendors/wcfm-view-vendors-manage.php:286
#: views/vendors/wcfm-view-vendors-manage.php:329
#: views/vendors/wcfm-view-vendors-new.php:94
#: views/vendors/wcfm-view-vendors-new.php:117
#: views/vendors/wcfm-view-vendors-new.php:135
msgid "Last Name"
msgstr "Nom"

#: views/customers/wcfm-view-customers-details.php:193
#: views/customers/wcfm-view-customers-details.php:204
#: views/wc_bookings/wcfm-view-wcbookings-details.php:147
#: views/wc_bookings/wcfm-view-wcbookings.php:116
#: views/wc_bookings/wcfm-view-wcbookings.php:128
msgid "Booking"
msgstr "Réservation"

#: views/customers/wcfm-view-customers-details.php:196
#: views/customers/wcfm-view-customers-details.php:207
#: views/wc_bookings/wcfm-view-wcbookings.php:119
#: views/wc_bookings/wcfm-view-wcbookings.php:131
msgid "Start Date"
msgstr "Date de début"

#: views/customers/wcfm-view-customers-details.php:197
#: views/customers/wcfm-view-customers-details.php:208
#: views/wc_bookings/wcfm-view-wcbookings.php:120
#: views/wc_bookings/wcfm-view-wcbookings.php:132
msgid "End Date"
msgstr "Date de fin"

#: views/customers/wcfm-view-customers-details.php:270
#: views/customers/wcfm-view-customers-details.php:280
#: views/orders/wcfm-view-orders.php:83 views/orders/wcfm-view-orders.php:103
msgid "Purchased"
msgstr "Acheté"

#: views/customers/wcfm-view-customers-manage.php:159
#: views/orders/wcfm-view-orders-details.php:292
#: views/orders/wcfm-view-orders-details.php:294
#: views/orders/wcfm-view-orders-details.php:331
#: views/orders/wcfm-view-orders-details.php:333
#: views/profile/wcfm-view-profile.php:278
#: views/vendors/wcfm-view-vendors-new.php:108
msgid "Address"
msgstr "Adresse"

#: views/customers/wcfm-view-customers-manage.php:164
#: views/profile/wcfm-view-profile.php:282
#: views/vendors/wcfm-view-vendors-new.php:113
msgid "Billing"
msgstr "Facturation"

#: views/customers/wcfm-view-customers-manage.php:169
#: views/profile/wcfm-view-profile.php:238
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:738
#: views/settings/wcfm-view-wcmarketplace-settings.php:713
#: views/vendors/wcfm-view-vendors-new.php:118
msgid "Phone"
msgstr "Telephone"

#: views/customers/wcfm-view-customers-manage.php:170
#: views/customers/wcfm-view-customers-manage.php:187
#: views/profile/wcfm-view-profile.php:287
#: views/profile/wcfm-view-profile.php:303
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:740
#: views/settings/wcfm-view-wcmarketplace-settings.php:229
#: views/settings/wcfm-view-wcmarketplace-settings.php:715
#: views/settings/wcfm-view-wcvendors-settings.php:219
#: views/settings/wcfm-view-wcvendors-settings.php:408
#: views/vendors/wcfm-view-vendors-new.php:119
#: views/vendors/wcfm-view-vendors-new.php:136
msgid "Address 1"
msgstr "Adresse 1"

#: views/customers/wcfm-view-customers-manage.php:171
#: views/customers/wcfm-view-customers-manage.php:188
#: views/profile/wcfm-view-profile.php:288
#: views/profile/wcfm-view-profile.php:304
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:741
#: views/settings/wcfm-view-wcmarketplace-settings.php:230
#: views/settings/wcfm-view-wcmarketplace-settings.php:716
#: views/settings/wcfm-view-wcvendors-settings.php:220
#: views/settings/wcfm-view-wcvendors-settings.php:409
#: views/vendors/wcfm-view-vendors-new.php:120
#: views/vendors/wcfm-view-vendors-new.php:137
msgid "Address 2"
msgstr "Adresse 2"

#: views/customers/wcfm-view-customers-manage.php:172
#: views/customers/wcfm-view-customers-manage.php:189
#: views/profile/wcfm-view-profile.php:289
#: views/profile/wcfm-view-profile.php:305
#: views/settings/wcfm-view-dokan-settings.php:204
#: views/settings/wcfm-view-dokan-settings.php:416
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:308
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:742
#: views/settings/wcfm-view-wcmarketplace-settings.php:231
#: views/settings/wcfm-view-wcmarketplace-settings.php:717
#: views/settings/wcfm-view-wcvendors-settings.php:221
#: views/settings/wcfm-view-wcvendors-settings.php:382
#: views/settings/wcfm-view-wcvendors-settings.php:410
#: views/vendors/wcfm-view-vendors-new.php:121
#: views/vendors/wcfm-view-vendors-new.php:138
msgid "Country"
msgstr "Pays"

#: views/customers/wcfm-view-customers-manage.php:173
#: views/customers/wcfm-view-customers-manage.php:190
#: views/profile/wcfm-view-profile.php:290
#: views/profile/wcfm-view-profile.php:306
#: views/settings/wcfm-view-dokan-settings.php:202
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:306
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:743
#: views/settings/wcfm-view-wcmarketplace-settings.php:233
#: views/settings/wcfm-view-wcmarketplace-settings.php:718
#: views/settings/wcfm-view-wcvendors-settings.php:222
#: views/settings/wcfm-view-wcvendors-settings.php:411
#: views/vendors/wcfm-view-vendors-new.php:122
#: views/vendors/wcfm-view-vendors-new.php:139
msgid "City/Town"
msgstr "Ville/Localité"

#: views/customers/wcfm-view-customers-manage.php:174
#: views/customers/wcfm-view-customers-manage.php:191
#: views/profile/wcfm-view-profile.php:291
#: views/profile/wcfm-view-profile.php:307
#: views/settings/wcfm-view-dokan-settings.php:205
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:309
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:744
#: views/settings/wcfm-view-wcmarketplace-settings.php:232
#: views/settings/wcfm-view-wcmarketplace-settings.php:719
#: views/settings/wcfm-view-wcvendors-settings.php:223
#: views/settings/wcfm-view-wcvendors-settings.php:412
#: views/vendors/wcfm-view-vendors-new.php:123
#: views/vendors/wcfm-view-vendors-new.php:140
msgid "State/County"
msgstr "État/Pays"

#: views/customers/wcfm-view-customers-manage.php:175
#: views/customers/wcfm-view-customers-manage.php:192
#: views/profile/wcfm-view-profile.php:292
#: views/profile/wcfm-view-profile.php:308
#: views/settings/wcfm-view-dokan-settings.php:203
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:307
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:745
#: views/settings/wcfm-view-wcmarketplace-settings.php:234
#: views/settings/wcfm-view-wcmarketplace-settings.php:720
#: views/settings/wcfm-view-wcvendors-settings.php:224
#: views/settings/wcfm-view-wcvendors-settings.php:413
#: views/vendors/wcfm-view-vendors-new.php:124
#: views/vendors/wcfm-view-vendors-new.php:141
msgid "Postcode/Zip"
msgstr "Code Postal"

#: views/customers/wcfm-view-customers.php:62
#: views/customers/wcfm-view-customers.php:77
msgid "Location"
msgstr "Position"

#: views/customers/wcfm-view-customers.php:66
#: views/customers/wcfm-view-customers.php:81
msgid "Money Spent"
msgstr "Argent dépensé"

#: views/customers/wcfm-view-customers.php:67
#: views/customers/wcfm-view-customers.php:82
msgid "Last Order"
msgstr "Dernière commande"

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:48
#, php-format
msgid "Welcome to %s Dashboard"
msgstr "Bienvenue sur le tableau de bord de %s"

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:62
msgid "Last Login:"
msgstr ""

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:69
#: views/vendors/wcfm-view-vendors.php:63
#: views/vendors/wcfm-view-vendors.php:84
msgid "Product Limit Stats"
msgstr ""

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:70
#: views/vendors/wcfm-view-vendors.php:64
#: views/vendors/wcfm-view-vendors.php:85
msgid "Disk Space Usage Stats"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:115
msgid "gross sales in last 7 days"
msgstr "ventes brutes dans les 7 derniers jours"

#: views/dashboard/wcfm-view-dashboard.php:146
msgid "admin fees in last 7 days"
msgstr "frais d’administration dans les 7 derniers jours"

#: views/dashboard/wcfm-view-dashboard.php:146
msgid "commission in last 7 days"
msgstr "commission ces 7 derniers jours"

#: views/dashboard/wcfm-view-dashboard.php:154
#: views/dashboard/wcfm-view-dokan-dashboard.php:161
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:163
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:181
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:185
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:168
#, php-format
msgid "<strong>%s item</strong>"
msgid_plural "<strong>%s items</strong>"
msgstr[0] "<strong>%s objet</strong>"
msgstr[1] "<strong>%s objets</strong>"

#: views/dashboard/wcfm-view-dashboard.php:155
msgid "sold in last 7 days"
msgstr "vendu dans les 7 derniers jours"

#: views/dashboard/wcfm-view-dashboard.php:167
#: views/dashboard/wcfm-view-dokan-dashboard.php:172
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:174
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:192
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:196
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:179
#, php-format
msgid "<strong>%s order</strong>"
msgid_plural "<strong>%s orders</strong>"
msgstr[0] "<strong> %s commande </strong>"
msgstr[1] "<strong> %s commandes </strong>"

#: views/dashboard/wcfm-view-dashboard.php:168
msgid "received in last 7 days"
msgstr "reçu au cours des 7 derniers jours"

#: views/dashboard/wcfm-view-dashboard.php:228
#: views/dashboard/wcfm-view-dokan-dashboard.php:232
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:235
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:253
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:256
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:239
msgid "Product Stats"
msgstr "Statistiques de produit"

#: views/dashboard/wcfm-view-dashboard.php:250
#: views/dashboard/wcfm-view-dokan-dashboard.php:252
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:255
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:273
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:276
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:259
msgid "Store Stats"
msgstr "Stats du magasin"

#: views/dashboard/wcfm-view-dashboard.php:261
#: views/dashboard/wcfm-view-dokan-dashboard.php:264
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:267
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:285
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:271
#, php-format
msgid "%s top seller in last 7 days (sold %d)"
msgstr "%s meilleures ventes dans les 7 derniers jours (vendu %d)"

#: views/dashboard/wcfm-view-dashboard.php:274
#: views/dashboard/wcfm-view-dokan-dashboard.php:278
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:281
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:299
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:307
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:285
#, php-format
msgid "<strong>%s order</strong> - processing"
msgid_plural "<strong>%s orders</strong> - processing"
msgstr[0] "<strong>%s Commande</strong> en attente de traitement"
msgstr[1] "<strong>%s Commandes</strong> en attente de traitement"

#: views/dashboard/wcfm-view-dashboard.php:280
#, php-format
msgid "<strong>%s order</strong> - on-hold"
msgid_plural "<strong>%s orders</strong> - on-hold"
msgstr[0] "<strong>%s Commande</strong> en attente"
msgstr[1] "<strong>%s Commandes</strong> en attente"

#: views/dashboard/wcfm-view-dashboard.php:290
#: views/dashboard/wcfm-view-dokan-dashboard.php:297
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:300
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:318
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:326
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:304
#, php-format
msgid "<strong>%s product</strong> - low in stock"
msgid_plural "<strong>%s products</strong> - low in stock"
msgstr[0] "<strong>%s produit</strong> en stock faible"
msgstr[1] "<strong>%s produits</strong> en stock faible"

#: views/dashboard/wcfm-view-dashboard.php:296
#: views/dashboard/wcfm-view-dokan-dashboard.php:303
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:306
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:324
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:332
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:310
#, php-format
msgid "<strong>%s product</strong> - out of stock"
msgid_plural "<strong>%s products</strong> - out of stock"
msgstr[0] "<strong>%s produit en</strong> rupture de stock"
msgstr[1] "<strong>%s produits en</strong> rupture de stock"

#: views/dashboard/wcfm-view-dashboard.php:317
#: views/dashboard/wcfm-view-dokan-dashboard.php:324
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:328
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:346
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:353
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:331
msgid "Sales by Product"
msgstr "Ventes par produit"

#: views/dashboard/wcfm-view-dashboard.php:332
#: views/dashboard/wcfm-view-dokan-dashboard.php:340
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:344
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:362
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:369
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:347
msgid "Top Regions"
msgstr "Premières régions"

#: views/dashboard/wcfm-view-dashboard.php:352
#: views/dashboard/wcfm-view-dokan-dashboard.php:360
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:364
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:382
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:389
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:367
msgid "Latest Topics"
msgstr "Derniers sujets"

#: views/dashboard/wcfm-view-dashboard.php:374
#: views/dashboard/wcfm-view-dokan-dashboard.php:382
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:386
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:404
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:411
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:389
msgid "There is no topic yet!!"
msgstr "Il n’y a pas de sujet encore !!"

#: views/dashboard/wcfm-view-dokan-dashboard.php:143
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:145
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:163
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:167
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:150
#: views/vendors/wcfm-view-vendors-manage.php:210
msgid "gross sales in this month"
msgstr "ventes nettes de ce mois"

#: views/dashboard/wcfm-view-dokan-dashboard.php:153
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:155
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:173
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:177
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:160
#: views/vendors/wcfm-view-vendors-manage.php:228
msgid "earnings in this month"
msgstr "gain ce mois"

#: views/dashboard/wcfm-view-dokan-dashboard.php:162
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:164
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:182
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:186
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:169
#: views/vendors/wcfm-view-vendors-manage.php:257
msgid "sold in this month"
msgstr "ventes de ce mois-ci"

#: views/dashboard/wcfm-view-dokan-dashboard.php:173
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:175
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:193
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:197
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:180
msgid "received in this month"
msgstr "reçus ce mois-ci"

#: views/dashboard/wcfm-view-dokan-dashboard.php:285
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:288
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:306
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:314
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:292
#, php-format
msgid "<strong>%s product</strong> - awaiting fulfillment"
msgid_plural "<strong>%s products</strong> - awaiting fulfillment"
msgstr[0] "<strong> %s produit </strong>-en attente de réalisation"
msgstr[1] "<strong> %s produits </strong>-en attente de réalisation"

#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:155
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:173
#: views/vendors/wcfm-view-vendors-manage.php:228
msgid "admin fees in this month"
msgstr "frais d’administration du mois"

#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:296
#, php-format
msgid "%s top seller this month (sold %d)"
msgstr "%s meilleure vente du mois (%d vendus)"

#: views/enquiry/wcfm-view-enquiry-form.php:46
msgid "Your email address will not be published."
msgstr "Votre adresse email ne sera pas publiée."

#: views/enquiry/wcfm-view-enquiry-form.php:50
msgid "Your enquiry"
msgstr "Votre demande"

#: views/enquiry/wcfm-view-enquiry-manage.php:65
msgid "Manage Enquiry"
msgstr "Gérer les demandes"

#: views/enquiry/wcfm-view-enquiry-manage.php:72
msgid "Edit Enquiry"
msgstr "Modifier demande"

#: views/enquiry/wcfm-view-enquiry-manage.php:72
msgid "Add Enquiry"
msgstr "Faire une demande"

#: views/enquiry/wcfm-view-enquiry-manage.php:76
msgid "View Product"
msgstr "Voir le produit"

#: views/enquiry/wcfm-view-enquiry-manage.php:122
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:110
#: views/notice/wcfm-view-notice-view.php:104
msgid "Replies"
msgstr "Réponse"

#: views/enquiry/wcfm-view-enquiry-manage.php:175
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:157
#: views/notice/wcfm-view-notice-view.php:152
msgid "New Reply"
msgstr "Nouvelle réponse"

#: views/enquiry/wcfm-view-enquiry-manage.php:192
msgid "Stick at Product Page"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:192
msgid "Enable to stick this reply at product page"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:205
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:177
#: views/messages/wcfm-view-messages.php:77
#: views/notice/wcfm-view-notice-view.php:170
#: views/vendors/wcfm-view-vendors-manage.php:402
msgid "Send"
msgstr "Envoyer"

#: views/enquiry/wcfm-view-enquiry-tab.php:31
msgid "General Enquiries"
msgstr "Questions et demandes générales"

#: views/enquiry/wcfm-view-enquiry-tab.php:36
msgid "There are no enquiries yet."
msgstr "Il n'y a pas encore de demandes de renseignements."

#: views/enquiry/wcfm-view-enquiry-tab.php:73
msgid "Reply by"
msgstr ""

#: views/enquiry/wcfm-view-enquiry.php:78
#: views/enquiry/wcfm-view-enquiry.php:90
#: views/enquiry/wcfm-view-my-account-enquiry.php:41
#: views/enquiry/wcfm-view-my-account-enquiry.php:50
#: views/enquiry/wcfm-view-my-account-enquiry.php:65
msgid "Query"
msgstr "Requête"

#: views/enquiry/wcfm-view-enquiry.php:83
#: views/enquiry/wcfm-view-enquiry.php:95
msgid "Reply"
msgstr "Répondre"

#: views/enquiry/wcfm-view-my-account-enquiry.php:66
msgid "You do not have any enquiry yet!"
msgstr ""

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:56
msgid "Manage Knowledgebase"
msgstr "Gérer la base de connaissances"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:63
msgid "Edit Knowledgebase"
msgstr "Edition du mot clé"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:63
msgid "Add Knowledgebase"
msgstr "Ajouter la base de connaissances"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:81
#: views/knowledgebase/wcfm-view-knowledgebase.php:71
#: views/knowledgebase/wcfm-view-knowledgebase.php:78
#: views/notice/wcfm-view-notice-manage.php:78
#: views/notice/wcfm-view-notices.php:51 views/notice/wcfm-view-notices.php:57
#: views/products-manager/wcfm-view-thirdparty-products-manage.php:164
msgid "Title"
msgstr "Titre"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:82
msgid "Details"
msgstr ""

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:100
#: views/products-manager/wcfm-view-products-manage.php:619
msgid "Add new category"
msgstr "Ajouter une nouvelle catégorie"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:106
#: views/products-manager/wcfm-view-products-manage.php:625
msgid "-- Parent category --"
msgstr "-- Catégorie parente --"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:116
#: views/products-manager/wcfm-view-products-manage-tabs.php:154
#: views/products-manager/wcfm-view-products-manage.php:635
#: views/products-manager/wcfm-view-products-manage.php:696
msgid "Add"
msgstr "Ajouter"

#: views/knowledgebase/wcfm-view-knowledgebase.php:33
msgid "Guidelines for Store Users"
msgstr "Directives pour les utilisateurs des boutiques"

#: views/knowledgebase/wcfm-view-knowledgebase.php:38
msgid "Add New Knowledgebase"
msgstr "Ajouter le nouveau mot clé"

#: views/knowledgebase/wcfm-view-knowledgebase.php:57
#: views/products/wcfm-view-products.php:136
msgid "Show all category"
msgstr "Montrer toutes les catégories"

#: views/knowledgebase/wcfm-view-knowledgebase.php:72
#: views/knowledgebase/wcfm-view-knowledgebase.php:79
msgid "Category"
msgstr ""

#: views/listings/wcfm-view-listings.php:103
#: views/listings/wcfm-view-listings.php:115
msgid "Listing"
msgstr "Annonce"

#: views/messages/wcfm-view-messages.php:36
msgid "Notification Dashboard"
msgstr "Tableau de bord des notifications"

#: views/messages/wcfm-view-messages.php:48
msgid "To Store Admin"
msgstr "Vers boutique Admin"

#: views/messages/wcfm-view-messages.php:48
msgid "To Store Vendors"
msgstr "Vers boutique vendeurs"

#: views/messages/wcfm-view-messages.php:71
msgid "Direct TO:"
msgstr "Direct à :"

#: views/messages/wcfm-view-messages.php:102
msgid "Only Unread"
msgstr "Seulement non lus"

#: views/messages/wcfm-view-messages.php:103
msgid "Only Read"
msgstr "Seulement lu"

#: views/messages/wcfm-view-messages.php:120
#: views/messages/wcfm-view-messages.php:133
msgid "Select all for mark as read or delete"
msgstr ""

#: views/messages/wcfm-view-messages.php:123
#: views/messages/wcfm-view-messages.php:136
msgid "Message"
msgstr "Message"

#: views/notice/wcfm-view-notice-manage.php:47
msgid "Manage Topic"
msgstr "Gérer le sujet"

#: views/notice/wcfm-view-notice-manage.php:55
#: views/notice/wcfm-view-notice-view.php:59
msgid "Edit Topic"
msgstr "Modifier"

#: views/notice/wcfm-view-notice-manage.php:55
msgid "Add Topic"
msgstr "Créer un nouveau sujet"

#: views/notice/wcfm-view-notice-manage.php:58
#: views/notice/wcfm-view-notice-view.php:57
#: views/notice/wcfm-view-notices.php:33
msgid "Topics"
msgstr "Sujets"

#: views/notice/wcfm-view-notice-manage.php:59
msgid "View Topic"
msgstr "Voir le sujet"

#: views/notice/wcfm-view-notice-manage.php:79
msgid "Allow Reply"
msgstr "Autoriser la réponse"

#: views/notice/wcfm-view-notice-manage.php:80
msgid "Close for New Reply"
msgstr "Fermer pour nouvelle réponse"

#: views/notice/wcfm-view-notice-manage.php:81
#: views/products-manager/wcfm-view-thirdparty-products-manage.php:165
#: views/products-popup/wcfm-view-product-popup.php:251
msgid "Content"
msgstr "Contenu"

#: views/notice/wcfm-view-notice-view.php:47
msgid "Topic"
msgstr "Sujet"

#: views/notice/wcfm-view-notices.php:37
msgid "Add New Topic"
msgstr "Ajouter un nouveau sujet"

#: views/orders/wcfm-view-orders-details-fedex-dhl-express.php:49
msgid "Fedex"
msgstr "FedEx"

#: views/orders/wcfm-view-orders-details-fedex-dhl-express.php:163
msgid "DHL Express"
msgstr "DHL express"

#: views/orders/wcfm-view-orders-details.php:70
#: views/products-manager/wcfm-view-products-manage.php:355
#: views/products-manager/wcfm-view-products-manage.php:356
#: views/products-popup/wcfm-view-product-popup.php:68
#: views/products-popup/wcfm-view-product-popup.php:69
msgid "Standard"
msgstr "Standard"

#: views/orders/wcfm-view-orders-details.php:122
msgid "Order #"
msgstr "Commande #"

#: views/orders/wcfm-view-orders-details.php:137
msgid "CSV Export"
msgstr "Exporter en CSV"

#: views/orders/wcfm-view-orders-details.php:166
msgid "Order date:"
msgstr "Date de la réservation :"

#: views/orders/wcfm-view-orders-details.php:173
msgid "Order status:"
msgstr "Statut de la réservation :"

#: views/orders/wcfm-view-orders-details.php:179
msgid "Customer payment page"
msgstr "Page de paiement client"

#: views/orders/wcfm-view-orders-details.php:196
#: views/vendors/wcfm-view-vendors-manage.php:344
#: views/wc_bookings/wcfm-view-wcbookings-details.php:124
#: views/wc_bookings/wcfm-view-wcbookings-details.php:266
msgid "Update"
msgstr "Mise à jour "

#: views/orders/wcfm-view-orders-details.php:208
msgid "Customer:"
msgstr "Client :"

#: views/orders/wcfm-view-orders-details.php:216
msgid "View other orders"
msgstr "Voir les autres commandes"

#: views/orders/wcfm-view-orders-details.php:240
#, php-format
msgid "<label for=\"order_payment_via\">Payment via: </label> %s"
msgstr "<label for=\"order_payment_via\">Paiement via : </label> %s"

#: views/orders/wcfm-view-orders-details.php:259
msgid "Customer IP"
msgstr "Adresse IP du client"

#: views/orders/wcfm-view-orders-details.php:271
msgid "Billing Details"
msgstr "Détails de facturation"

#: views/orders/wcfm-view-orders-details.php:277
msgid "Shipping Details"
msgstr "Détails de la livraison"

#: views/orders/wcfm-view-orders-details.php:294
msgid "No billing address set."
msgstr "Aucune adresse de facturation définie."

#: views/orders/wcfm-view-orders-details.php:333
msgid "No shipping address set."
msgstr "Aucune adresse de livraison définie."

#: views/orders/wcfm-view-orders-details.php:358
msgid "Customer Provided Note"
msgstr "Note fournie par le client"

#: views/orders/wcfm-view-orders-details.php:380
msgid "Order Items"
msgstr "Articles de la commande"

#: views/orders/wcfm-view-orders-details.php:387
msgid "Item"
msgstr "Élément"

#: views/orders/wcfm-view-orders-details.php:389
#: views/settings/wcfm-view-dokan-settings.php:417
#: views/settings/wcfm-view-dokan-settings.php:420
msgid "Cost"
msgstr "Coût"

#: views/orders/wcfm-view-orders-details.php:390
msgid "Qty"
msgstr "Qté"

#: views/orders/wcfm-view-orders-details.php:444
msgid "SKU:"
msgstr "SKU:"

#: views/orders/wcfm-view-orders-details.php:448
msgid "Variation ID:"
msgstr "ID variation&nbsp;:"

#: views/orders/wcfm-view-orders-details.php:452
msgid "No longer exists"
msgstr "N'existe plus"

#: views/orders/wcfm-view-orders-details.php:717
#: views/withdrawal/wcmp/wcfm-view-payments.php:66
#: views/withdrawal/wcmp/wcfm-view-payments.php:77
msgid "Fee"
msgstr "Frais"

#: views/orders/wcfm-view-orders-details.php:831
msgid "Coupon(s) Used"
msgstr "Code(s) promo utilisés"

#: views/orders/wcfm-view-orders-details.php:848
msgid "This is the total discount. Discounts are defined per line item."
msgstr ""
"Ceci est le total de remise. Les remises sont définies par ligne d'article."

#: views/orders/wcfm-view-orders-details.php:848
msgid "Discount"
msgstr "Remise"

#: views/orders/wcfm-view-orders-details.php:860
msgid "This is the shipping and handling total costs for the order."
msgstr ""
"Ce sont les coûts totaux d'expédition et de manutention de la commande."

#: views/orders/wcfm-view-orders-details.php:900
msgid "Order Total"
msgstr "Total de la commande"

#: views/orders/wcfm-view-orders.php:40
msgid "Orders Listing"
msgstr "Liste des commandes"

#: views/orders/wcfm-view-orders.php:90 views/orders/wcfm-view-orders.php:110
#: views/vendors/wcfm-view-vendors.php:70
#: views/vendors/wcfm-view-vendors.php:91
msgid "Earnings"
msgstr "Gains"

#: views/products/wcfm-view-products-export.php:64
#: views/products/wcfm-view-products.php:115
#: views/products-manager/wcfm-view-products-manage.php:441
#: views/products-popup/wcfm-view-product-popup-button.php:2
msgid "Add New Product"
msgstr "Ajouter un nouveau produit"

#: views/products/wcfm-view-products.php:104
#: views/products/wcfm-view-products.php:109
msgid "Stock Manager"
msgstr "Gestionnaire des stocks"

#: views/products/wcfm-view-products.php:147
msgid "Show all product types"
msgstr "Afficher tous les types de produits"

#: views/products/wcfm-view-products.php:192
#: views/products/wcfm-view-products.php:215
msgid "Select all for bulk edit"
msgstr "Tout sélectionner pour modifier en groupe"

#: views/products/wcfm-view-products.php:199
#: views/products/wcfm-view-products.php:222
#: views/products-manager/wcfm-view-products-manage-tabs.php:23
#: views/products-manager/wcfm-view-products-manage-tabs.php:249
msgid "SKU"
msgstr "SKU"

#: views/products/wcfm-view-products.php:201
#: views/products/wcfm-view-products.php:224
#: views/products-manager/wcfm-view-products-manage-tabs.php:209
msgid "Stock"
msgstr "Stock"

#: views/products/wcfm-view-products.php:202
#: views/products/wcfm-view-products.php:225
#: views/products-manager/wcfm-view-products-manage.php:470
#: views/products-popup/wcfm-view-product-popup.php:129
msgid "Price"
msgstr "Prix"

#: views/products-manager/wcfm-view-customfield-products-manage.php:130
msgid "-Select-"
msgstr "- Sélectionner -"

#: views/products-manager/wcfm-view-epeken-products-manage.php:42
msgid "Epeken Product Config"
msgstr "Configuration produit Epeken"

#: views/products-manager/wcfm-view-products-manage-tabs.php:23
msgid ""
"SKU refers to a Stock-keeping unit, a unique identifier for each distinct "
"product and service that can be purchased."
msgstr ""
"REF fait référence à l&rsquo;Unité de Gestion de Stock, un identifiant "
"unique pour chaque produit distinct et service qui peut être vendu."

#: views/products-manager/wcfm-view-products-manage-tabs.php:24
msgid "Manage Stock?"
msgstr "Gérer le stock ?"

#: views/products-manager/wcfm-view-products-manage-tabs.php:24
msgid "Enable stock management at product level"
msgstr "Activer la gestion de stock au niveau produit"

#: views/products-manager/wcfm-view-products-manage-tabs.php:25
#: views/products-manager/wcfm-view-products-manage-tabs.php:247
msgid "Stock Qty"
msgstr "Qté stock"

#: views/products-manager/wcfm-view-products-manage-tabs.php:25
msgid ""
"Stock quantity. If this is a variable product this value will be used to "
"control stock for all variations, unless you define stock at variation level."
msgstr ""
"Quantité du stock. Si c'est un produit variable cette valeur sera utilisée "
"pour contrôler le stock pour toutes les variations, à moins que vous "
"définissiez le stock au niveau de la variation."

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
msgid "Allow Backorders?"
msgstr "Permettre des commandes de produits en rupture ?"

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
#: views/products-manager/wcfm-view-products-manage-tabs.php:248
msgid "Do not Allow"
msgstr "Ne pas autoriser"

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
#: views/products-manager/wcfm-view-products-manage-tabs.php:248
msgid "Allow, but notify customer"
msgstr "Autoriser, mais avec notification client"

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
#: views/products-manager/wcfm-view-products-manage-tabs.php:248
msgid "Allow"
msgstr "Autoriser"

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
msgid ""
"If managing stock, this controls whether or not backorders are allowed. If "
"enabled, stock quantity can go below 0."
msgstr ""
"En cas de gestion de stock, cela défini si les ruptures de stock sont "
"autorisées ou non. Si actif, la quantité de stock peut être inférieure à 0."

#: views/products-manager/wcfm-view-products-manage-tabs.php:27
#: views/products-manager/wcfm-view-products-manage-tabs.php:250
msgid "Stock status"
msgstr "État du Stock"

#: views/products-manager/wcfm-view-products-manage-tabs.php:27
msgid ""
"Controls whether or not the product is listed as \"in stock\" or \"out of "
"stock\" on the frontend."
msgstr ""
"Contrôle si la prestation est répertoriée comme \"en stock \" ou \"en "
"rupture de stock \" sur le front office."

#: views/products-manager/wcfm-view-products-manage-tabs.php:28
msgid "Sold Individually"
msgstr "Vendu individuellement"

#: views/products-manager/wcfm-view-products-manage-tabs.php:28
msgid ""
"Enable this to only allow one of this item to be bought in a single order"
msgstr ""
"Activez cette option pour autoriser uniquement l'achat de cet article dans "
"une seule commande"

#: views/products-manager/wcfm-view-products-manage-tabs.php:46
msgid "Files"
msgstr "Fichiers"

#: views/products-manager/wcfm-view-products-manage-tabs.php:48
msgid "File"
msgstr "Fichier"

#: views/products-manager/wcfm-view-products-manage-tabs.php:53
msgid "Never"
msgstr "Jamais"

#: views/products-manager/wcfm-view-products-manage-tabs.php:66
msgid "Grouped Products"
msgstr "Produits groupés"

#: views/products-manager/wcfm-view-products-manage-tabs.php:71
msgid "Grouped products"
msgstr "Produits groupés"

#: views/products-manager/wcfm-view-products-manage-tabs.php:71
msgid "This lets you choose which products are part of this group."
msgstr "Cela vous permet de choisir les produits qui font partie de ce groupe."

#: views/products-manager/wcfm-view-products-manage-tabs.php:90
msgid "Dimensions"
msgstr "Dimensions"

#: views/products-manager/wcfm-view-products-manage-tabs.php:93
msgid "Shipping class"
msgstr "Classe de livraison"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgid "Tax Status"
msgstr "État de la TVA"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgid "Taxable"
msgstr "Taxable"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgid "Shipping only"
msgstr "Expédition seulement"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgctxt "Tax status"
msgid "None"
msgstr "Aucun"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgid ""
"Define whether or not the entire product is taxable, or just the cost of "
"shipping it."
msgstr ""
"Détermine si oui ou non le produit complet est taxable ou uniquement le coût "
"de la livraison."

#: views/products-manager/wcfm-view-products-manage-tabs.php:115
msgid "Tax Class"
msgstr "Classe de taxe"

#: views/products-manager/wcfm-view-products-manage-tabs.php:115
msgid ""
"Choose a tax class for this product. Tax classes are used to apply different "
"tax rates specific to certain types of product."
msgstr ""
"Choisir une classe de TVA pour ce produit. Les classes de TVA sont utilisées "
"pour appliquer plusieurs taux de TVA spécifiques à certains types de "
"produits."

#: views/products-manager/wcfm-view-products-manage-tabs.php:140
#, php-format
msgid "Enter some text, some attributes by \"%s\" separating values."
msgstr "Entrez un texte, certains attributs par \"%s\" Séparant les valeurs."

#: views/products-manager/wcfm-view-products-manage-tabs.php:152
msgid "Add attribute"
msgstr "Ajouter un attribut"

#: views/products-manager/wcfm-view-products-manage-tabs.php:168
msgid "Variations"
msgstr "Variations"

#: views/products-manager/wcfm-view-products-manage-tabs.php:171
#, php-format
msgid ""
"Before you can add a variation you need to add some variation attributes on "
"the Attributes tab. Learn more"
msgstr ""
"Avant de pouvoir ajouter une variation vous devez ajouter des variations "
"d&rsquo;attributs dans l&rsquo;onglet <strong>Attributs</strong>."

#: views/products-manager/wcfm-view-products-manage-tabs.php:176
msgid "Default Form Values:"
msgstr "Valeurs par défaut du formulaire :"

#: views/products-manager/wcfm-view-products-manage-tabs.php:181
#: views/products-manager/wcfm-view-products-manage-tabs.php:182
msgid "Variations Bulk Options"
msgstr "Options de variations groupées"

#: views/products-manager/wcfm-view-products-manage-tabs.php:184
msgid "Choose option"
msgstr "Choisissez une option"

#: views/products-manager/wcfm-view-products-manage-tabs.php:186
msgid "Enable all Variations"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:187
msgid "Disable all Variations"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:189
msgid "Set variations \"Downloadable\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:190
msgid "Set variations \"Non-Downloadable\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:193
msgid "Set variations \"Virtual\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:194
msgid "Set variations \"Non-Virtual\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:197
msgid "Pricing"
msgstr "Tarification"

#: views/products-manager/wcfm-view-products-manage-tabs.php:198
msgid "Regular prices"
msgstr "Prix réguliers"

#: views/products-manager/wcfm-view-products-manage-tabs.php:199
msgid "Regular price increase"
msgstr "Augmenter les prix réguliers"

#: views/products-manager/wcfm-view-products-manage-tabs.php:200
msgid "Regular price decrease"
msgstr "Diminuer les prix réguliers"

#: views/products-manager/wcfm-view-products-manage-tabs.php:201
msgid "Sale prices"
msgstr "Définir les prix promo"

#: views/products-manager/wcfm-view-products-manage-tabs.php:202
msgid "Sale price increase"
msgstr "Augmentation de prix promo"

#: views/products-manager/wcfm-view-products-manage-tabs.php:203
msgid "Sale price decrease"
msgstr "Réduction du prix promo"

#: views/products-manager/wcfm-view-products-manage-tabs.php:207
msgid "ON \"Manage stock\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:208
msgid "OFF \"Manage stock\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:211
msgid "Set Status - In stock"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:212
msgid "Set Status - Out of stock"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:213
msgid "Set Status - On backorder"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:225
msgid "Downloadable products"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:226
msgid "Download limit"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:227
msgid "Download expiry"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:239
msgid "Manage Stock"
msgstr "Gestion du stock"

#: views/products-manager/wcfm-view-products-manage-tabs.php:246
#: views/products-manager/wcfm-view-products-manage.php:473
#: views/products-popup/wcfm-view-product-popup.php:132
#: views/settings/wcfm-view-dokan-settings.php:491
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:771
#: views/settings/wcfm-view-wcmarketplace-settings.php:747
#: views/settings/wcfm-view-wcpvendors-settings.php:155
#: views/settings/wcfm-view-wcvendors-settings.php:447
msgid "Upto"
msgstr "Jusqu'à"

#: views/products-manager/wcfm-view-products-manage-tabs.php:248
msgid "Backorders?"
msgstr "Commandes en réapprovisionnement ?"

#: views/products-manager/wcfm-view-products-manage-tabs.php:270
msgid "Up-sells"
msgstr "Montées en gamme"

#: views/products-manager/wcfm-view-products-manage-tabs.php:270
msgid ""
"Up-sells are products which you recommend instead of the currently viewed "
"product, for example, products that are more profitable or better quality or "
"more expensive."
msgstr ""
"Les Montées en gamme sont des produits que vous recommandez à la place de "
"ceux actuellement vus, par exemple, les produits qui sont plus profitables "
"ou de meilleure qualité ou plus chers."

#: views/products-manager/wcfm-view-products-manage-tabs.php:271
msgid "Cross-sells"
msgstr "Ventes croisées"

#: views/products-manager/wcfm-view-products-manage-tabs.php:271
msgid ""
"Cross-sells are products which you promote in the cart, based on the current "
"product."
msgstr ""
"Les Ventes Croisées sont des produits que vous mettez en avant dans la "
"panier, basés sur le produit actuel."

#: views/products-manager/wcfm-view-products-manage.php:35
msgid "You have reached product limit!"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:342
#: views/products-manager/wcfm-view-products-manage.php:354
#: views/products-popup/wcfm-view-product-popup.php:55
#: views/products-popup/wcfm-view-product-popup.php:67
msgid "Same as parent"
msgstr "Identique au parent"

#: views/products-manager/wcfm-view-products-manage.php:343
#: views/products-popup/wcfm-view-product-popup.php:56
msgid "No shipping class"
msgstr "Pas de classe de livraison"

#: views/products-manager/wcfm-view-products-manage.php:400
msgid "Manage Product"
msgstr "Gérer produit"

#: views/products-manager/wcfm-view-products-manage.php:409
msgid "Edit Product"
msgstr "Modifier le produit"

#: views/products-manager/wcfm-view-products-manage.php:409
#: views/products-popup/wcfm-view-product-popup.php:113
msgid "Add Product"
msgstr "Ajouter un produit"

#: views/products-manager/wcfm-view-products-manage.php:463
#: views/products-popup/wcfm-view-product-popup.php:122
msgid "Product Title"
msgstr "﻿Titre du produit"

#: views/products-manager/wcfm-view-products-manage.php:468
#: views/products-popup/wcfm-view-product-popup.php:127
#: views/settings/wcfm-view-settings.php:424
msgid "URL"
msgstr "URL"

#: views/products-manager/wcfm-view-products-manage.php:468
#: views/products-popup/wcfm-view-product-popup.php:127
msgid "Enter the external URL to the product."
msgstr "Saisissez l'URL externe au produit."

#: views/products-manager/wcfm-view-products-manage.php:469
#: views/products-popup/wcfm-view-product-popup.php:128
msgid "Button Text"
msgstr "Texte du bouton"

#: views/products-manager/wcfm-view-products-manage.php:469
#: views/products-popup/wcfm-view-product-popup.php:128
msgid "This text will be shown on the button linking to the external product."
msgstr "Ce texte sera affiché sur le bouton vers la prestation externe."

#: views/products-manager/wcfm-view-products-manage.php:471
#: views/products-popup/wcfm-view-product-popup.php:130
msgid "schedule"
msgstr "planification"

#: views/products-manager/wcfm-view-products-manage.php:531
#: views/products-manager/wcfm-view-products-manage.php:713
#: views/products-popup/wcfm-view-product-popup.php:198
msgid "Separate Product Tags with commas"
msgstr "Séparez les Mots-clés par des virgules"

#: views/products-manager/wcfm-view-products-manage.php:680
msgid "Add new"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:686
msgid "-- Parent taxonomy --"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:140
msgid "Yoast SEO"
msgstr "Yoast SEO"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:145
msgid "Enter a focus keyword"
msgstr "Entrer votre mot-clé cible"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:145
msgid "It should appear in title and first paragraph of the copy."
msgstr ""
"Ce mot doit apparaître dans le nom de votre produit et dans le premier "
"paragraphe de votre description pour un meilleur référencement web du "
"produit."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:146
msgid "Meta description"
msgstr "Meta description"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:146
msgid "It should not be more than 156 characters."
msgstr ""
"La meta description est une description de maximum 156 caractères destinée "
"aux moteurs de recherche (comme Google) afin qu’ils comprennent mieux ce que "
"vous vendez."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:158
msgid "Custom Tabs"
msgstr "Onglets Personnalisés"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:163
msgid "Tabs"
msgstr "Onglets"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:164
msgid "Required for tab to be visible"
msgstr "Conditions requises pour que l'onglet soit visible"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:165
msgid "HTML or Text to display ..."
msgstr "HTML ou texte à afficher..."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:178
msgid "Barcode & ISBN"
msgstr "Code à barres et ISBN"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:183
msgid "Barcode"
msgstr "Code barre"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:184
msgid "ISBN"
msgstr "ISBN"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:197
msgid "MSRP Pricing"
msgstr "Prix PDSF"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:202
msgid "MSRP Price"
msgstr "Prix PDSF"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:215
msgid "Quantities and Units"
msgstr "Quantités et unités"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:220
msgid "Deactivate Quantity Rules"
msgstr "Désactiver les règles de quantité"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:221
msgid "Override Quantity Rules"
msgstr "Substituer les règles de la quantité"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:222
msgid "Step Value"
msgstr "Valeur du pas"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:223
msgid "Minimum Quantity"
msgstr "La quantité minimale"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:224
msgid "Maximum Quantity"
msgstr "Maximum Quantity"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:225
msgid "Out of Stock Minimum"
msgstr "Rupture de stock minimum"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:226
msgid "Out of Stock Maximum"
msgstr "Rupture de stock maximum"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:227
msgid "Unit"
msgstr "Unité"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:240
msgid "Product Fees"
msgstr "Frais des produits"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:245
msgid "Fee Name"
msgstr "Nom des frais"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:245
msgid "This will be shown at the checkout description the added fee."
msgstr ""
"Les frais supplémentaires seront affichés durant le processus de commande."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:246
msgid "Fee Amount"
msgstr "Montant des frais"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:246
msgid ""
"Enter a monetary decimal without any currency symbols or thousand separator. "
"This field also accepts percentages."
msgstr ""
"Entrez une décimale monétaire sans symbole monétaire ou séparateur de "
"milliers. Ce champ accepte également les pourcentages."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:247
msgid "Multiple Fee by Quantity"
msgstr "Frais multiples par quantité"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:247
msgid ""
"Multiply the fee by the quantity of this product that is added to the cart."
msgstr ""
"Multipliez les frais par la quantité de ce produit qui est ajouté au panier."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:260
msgid "Bulk Discount"
msgstr "Remise en vrac"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:265
msgid "Bulk Discount enabled"
msgstr "Remise sur quantité activée"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:266
msgid "Bulk discount special offer text in product description"
msgstr "Texte de remise sur quantité affiché dans la description du produit"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:267
msgid "Discount Rules"
msgstr "Conditions de la remise"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:268
msgid "Quantity (min.)"
msgstr "Quantité (min)"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:269
msgid "Discount (%)"
msgstr "Rabais (%)"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:283
msgid "Role Based Price"
msgstr "Prix basé sur le rôle"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:295
msgid "Selling Price"
msgstr "Prix de vente"

#: views/products-popup/wcfm-view-product-popup.php:142
msgid "Taxonomies"
msgstr "Taxonomies"

#: views/products-popup/wcfm-view-product-popup.php:227
msgid "Image Gallery"
msgstr "Galerie d'images"

#: views/profile/wcfm-view-profile.php:180
msgid "Profile Manager"
msgstr "Gestionnaire du profil"

#: views/profile/wcfm-view-profile.php:196
msgid "Personal"
msgstr "Mes activités"

#: views/profile/wcfm-view-profile.php:203
msgid "Avatar"
msgstr "Avatar"

#: views/profile/wcfm-view-profile.php:214
msgid "Email already verified"
msgstr ""

#: views/profile/wcfm-view-profile.php:223
msgid "Email Verification Code: "
msgstr ""

#: views/profile/wcfm-view-profile.php:223
msgid "Verification Code"
msgstr ""

#: views/profile/wcfm-view-profile.php:224
msgid "Get Code"
msgstr ""

#: views/profile/wcfm-view-profile.php:233
msgid "Set New Password - Keep it blank for not to update"
msgstr ""

#: views/profile/wcfm-view-profile.php:252
msgid "Site Default"
msgstr "Site par défaut"

#: views/profile/wcfm-view-profile.php:260
msgid "Language"
msgstr "Langue"

#: views/profile/wcfm-view-profile.php:266
msgid "About"
msgstr "A propos de"

#: views/profile/wcfm-view-profile.php:300
msgid "Same as Billing"
msgstr "Identique à la facture"

#: views/profile/wcfm-view-profile.php:321
#: views/settings/wcfm-view-dokan-settings.php:141
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:192
#: views/settings/wcfm-view-wcmarketplace-settings.php:148
#: views/settings/wcfm-view-wcpvendors-settings.php:85
#: views/settings/wcfm-view-wcvendors-settings.php:132
msgid "Social"
msgstr "Social"

#: views/profile/wcfm-view-profile.php:327
msgid "Twitter"
msgstr "Twitter"

#: views/profile/wcfm-view-profile.php:328
msgid "Facebook"
msgstr "Facebook"

#: views/profile/wcfm-view-profile.php:329
msgid "Instagram"
msgstr "Instagram"

#: views/profile/wcfm-view-profile.php:330
msgid "Youtube"
msgstr "Youtube"

#: views/profile/wcfm-view-profile.php:331
msgid "Linkedin"
msgstr "LinkedIn"

#: views/profile/wcfm-view-profile.php:332
msgid "Google Plus"
msgstr "Google Plus"

#: views/profile/wcfm-view-profile.php:333
msgid "Snapchat"
msgstr "Snapchat"

#: views/profile/wcfm-view-profile.php:334
msgid "Pinterest"
msgstr "Pinterest"

#: views/reports/wcfm-html-report-sales-by-date.php:31
msgid "Custom:"
msgstr ""

#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:63
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:65
#: views/reports/wcfm-view-reports-sales-by-date.php:63
#: views/reports/wcfm-view-reports-sales-by-date.php:65
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:64
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:61
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:63
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:64
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:61
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:63
msgid "Sales BY Date"
msgstr "Ventes par date"

#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:63
#: views/reports/wcfm-view-reports-sales-by-date.php:63
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:61
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:61
#, php-format
msgctxt "start date and end date"
msgid "From %s to %s"
msgstr "De %s à %s"

#: views/reports/wcfm-view-reports-menu.php:4
msgid "Sales by date"
msgstr "Ventes par date"

#: views/reports/wcfm-view-reports-out-of-stock.php:26
msgid "Out of Stock"
msgstr "Rupture de stock"

#: views/reports/wcfm-view-reports-out-of-stock.php:50
#: views/reports/wcfm-view-reports-out-of-stock.php:59
msgid "product"
msgstr "produit"

#: views/reports/wcfm-view-reports-out-of-stock.php:51
#: views/reports/wcfm-view-reports-out-of-stock.php:60
msgid "Parent"
msgstr "Parent"

#: views/reports/wcfm-view-reports-out-of-stock.php:52
#: views/reports/wcfm-view-reports-out-of-stock.php:61
msgid "Unit in stock"
msgstr "Unité en stock"

#: views/reports/wcfm-view-reports-out-of-stock.php:53
#: views/reports/wcfm-view-reports-out-of-stock.php:62
msgid "Stock Status"
msgstr "État du stock"

#: views/settings/wcfm-view-dokan-settings.php:136
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:187
#: views/settings/wcfm-view-wcmarketplace-settings.php:143
#: views/settings/wcfm-view-wcpvendors-settings.php:80
#: views/settings/wcfm-view-wcpvendors-settings.php:103
#: views/settings/wcfm-view-wcvendors-settings.php:127
msgid "Store Settings"
msgstr "Paramètres du magasin"

#: views/settings/wcfm-view-dokan-settings.php:165
msgid "Profile Image"
msgstr "Image de profil"

#: views/settings/wcfm-view-dokan-settings.php:166
#: views/settings/wcfm-view-wcmarketplace-settings.php:207
#: views/settings/wcfm-view-wcvendors-settings.php:195
msgid "Banner"
msgstr "Bannière"

#: views/settings/wcfm-view-dokan-settings.php:167
#: views/settings/wcfm-view-wcmarketplace-settings.php:173
#: views/settings/wcfm-view-wcpvendors-settings.php:110
#: views/settings/wcfm-view-wcvendors-settings.php:157
msgid "Shop Name"
msgstr "Nom de la boutique"

#: views/settings/wcfm-view-dokan-settings.php:168
msgid "Store Product Per Page"
msgstr "Produits par page"

#: views/settings/wcfm-view-dokan-settings.php:169
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:247
#: views/settings/wcfm-view-wcvendors-settings.php:197
msgid "Store Phone"
msgstr "Téléphone de l'entreprise"

#: views/settings/wcfm-view-dokan-settings.php:170
msgid "Show email in store"
msgstr "Afficher l'email sur la boutique"

#: views/settings/wcfm-view-dokan-settings.php:171
msgid "Show tab on product single page view"
msgstr "Afficher l'onglet sur le produit vue de page unique"

#: views/settings/wcfm-view-dokan-settings.php:196
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:300
#: views/settings/wcfm-view-wcmarketplace-settings.php:225
#: views/settings/wcfm-view-wcvendors-settings.php:215
msgid "Store Address"
msgstr "Adresse de la boutique"

#: views/settings/wcfm-view-dokan-settings.php:200
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:304
msgid "Street"
msgstr "Rue"

#: views/settings/wcfm-view-dokan-settings.php:200
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:304
msgid "Street adress"
msgstr "Adresse"

#: views/settings/wcfm-view-dokan-settings.php:201
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:305
msgid "Street 2"
msgstr "Rue 2"

#: views/settings/wcfm-view-dokan-settings.php:201
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:305
msgid "Apartment, suit, unit etc. (optional)"
msgstr "Appartement, suite, unité etc. (facultatif)"

#: views/settings/wcfm-view-dokan-settings.php:202
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:306
msgid "Town / City"
msgstr "Ville"

#: views/settings/wcfm-view-dokan-settings.php:203
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:307
msgid "Postcode / Zip"
msgstr "Code Postal"

#: views/settings/wcfm-view-dokan-settings.php:211
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:319
#: views/settings/wcfm-view-wcmarketplace-settings.php:252
msgid "Store Location"
msgstr "Emplacement du magasin"

#: views/settings/wcfm-view-dokan-settings.php:259
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:387
#: views/settings/wcfm-view-wcmarketplace-settings.php:310
#: views/settings/wcfm-view-wcvendors-settings.php:252
msgid "PayPal Email"
msgstr "Courriel PayPal"

#: views/settings/wcfm-view-dokan-settings.php:260
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:388
msgid "Skrill Email"
msgstr "Email Skrill"

#: views/settings/wcfm-view-dokan-settings.php:271
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:396
msgid "Bank Details"
msgstr "Coordonnées bancaires"

#: views/settings/wcfm-view-dokan-settings.php:275
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:400
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:76
msgid "Account Name"
msgstr "Nom du compte"

#: views/settings/wcfm-view-dokan-settings.php:276
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:401
#: views/settings/wcfm-view-wcmarketplace-settings.php:312
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:77
msgid "Account Number"
msgstr "Numéro de compte"

#: views/settings/wcfm-view-dokan-settings.php:277
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:402
#: views/settings/wcfm-view-wcmarketplace-settings.php:313
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:78
msgid "Bank Name"
msgstr "Nom de la Banque"

#: views/settings/wcfm-view-dokan-settings.php:278
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:403
#: views/settings/wcfm-view-wcmarketplace-settings.php:315
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:79
msgid "Bank Address"
msgstr "Adresse de la banque"

#: views/settings/wcfm-view-dokan-settings.php:279
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:404
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:80
msgid "Routing Number"
msgstr "Numéro de routage"

#: views/settings/wcfm-view-dokan-settings.php:280
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:405
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:81
msgid "IBAN"
msgstr "IBAN"

#: views/settings/wcfm-view-dokan-settings.php:281
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:406
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:82
msgid "Swift Code"
msgstr "Swift Code"

#: views/settings/wcfm-view-dokan-settings.php:296
msgid "Stripe Connect"
msgstr "Connexion Stripe"

#: views/settings/wcfm-view-dokan-settings.php:364
msgid "If you want to use Country-State wise Shipping system then"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:374
msgid ""
"A shipping zone is a geographic region where a certain set of shipping "
"methods are offered. System will match a customer to a single zone using "
"their shipping address and present the shipping methods within that zone to "
"them."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:388
msgid "Enable Shipping"
msgstr "Activer l'expédition"

#: views/settings/wcfm-view-dokan-settings.php:415
#: views/settings/wcfm-view-wcvendors-settings.php:381
msgid "Shipping Rates"
msgstr "Frais d'Envoi"

#: views/settings/wcfm-view-dokan-settings.php:415
msgid ""
"Add the countries you deliver your products to. You can specify states as "
"well. If the shipping price is same except some countries/states, there is "
"an option Everywhere Else, you can use that."
msgstr ""
"Ajouter les pays vers lesquels vous souhaitez effectuer des livraisons. Vous "
"pouvez préciser en option des départements, provinces, états ou îles). Si le "
"prix de l'expédition est toujours le même, excepté pour certaines "
"destinations, vous avez une option <strong>\"Partout Ailleurs\"</strong>, "
"que vous pouvez utiliser."

#: views/settings/wcfm-view-dokan-settings.php:418
msgid "State Shipping Rates"
msgstr "Etat des frais de port"

#: views/settings/wcfm-view-dokan-settings.php:419
#: views/settings/wcfm-view-wcvendors-settings.php:383
msgid "State"
msgstr "État"

#: views/settings/wcfm-view-dokan-settings.php:432
msgid "Dokan Pro Shipping Settings"
msgstr "Dokan Pro Paramètres livraison"

#: views/settings/wcfm-view-dokan-settings.php:464
msgid "Dokan Pro SEO Settings"
msgstr "Dokan Pro paramètres SEO"

#: views/settings/wcfm-view-dokan-settings.php:481
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:761
#: views/settings/wcfm-view-wcmarketplace-settings.php:737
#: views/settings/wcfm-view-wcpvendors-settings.php:144
#: views/settings/wcfm-view-wcpvendors-settings.php:160
#: views/settings/wcfm-view-wcvendors-settings.php:437
msgid "Vacation Mode"
msgstr "Mode vacances"

#: views/settings/wcfm-view-dokan-settings.php:487
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:767
#: views/settings/wcfm-view-wcmarketplace-settings.php:743
#: views/settings/wcfm-view-wcpvendors-settings.php:151
#: views/settings/wcfm-view-wcvendors-settings.php:443
msgid "Enable Vacation Mode"
msgstr "Activer le mode vacances"

#: views/settings/wcfm-view-dokan-settings.php:488
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:768
#: views/settings/wcfm-view-wcmarketplace-settings.php:744
#: views/settings/wcfm-view-wcpvendors-settings.php:152
#: views/settings/wcfm-view-wcvendors-settings.php:444
msgid "Disable Purchase During Vacation"
msgstr "Désactiver l'achat pendant les vacances"

#: views/settings/wcfm-view-dokan-settings.php:489
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:769
#: views/settings/wcfm-view-wcmarketplace-settings.php:745
#: views/settings/wcfm-view-wcpvendors-settings.php:153
#: views/settings/wcfm-view-wcvendors-settings.php:445
msgid "Vacation Type"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:489
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:769
#: views/settings/wcfm-view-wcmarketplace-settings.php:745
#: views/settings/wcfm-view-wcpvendors-settings.php:153
#: views/settings/wcfm-view-wcvendors-settings.php:445
msgid "Instantly Close"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:489
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:769
#: views/settings/wcfm-view-wcmarketplace-settings.php:745
#: views/settings/wcfm-view-wcpvendors-settings.php:153
#: views/settings/wcfm-view-wcvendors-settings.php:445
msgid "Date wise close"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:492
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:772
#: views/settings/wcfm-view-wcmarketplace-settings.php:748
#: views/settings/wcfm-view-wcpvendors-settings.php:156
#: views/settings/wcfm-view-wcvendors-settings.php:448
msgid "Vacation Message"
msgstr "Message de vacances"

#: views/settings/wcfm-view-settings.php:92
msgid "WCfM Settings"
msgstr "Paramètres de WCfM"

#: views/settings/wcfm-view-settings.php:103
msgid "Bookings Global Settings"
msgstr "Paramètres globaux de réservations"

#: views/settings/wcfm-view-settings.php:111
msgid "Appointments Global Settings"
msgstr "Paramètres globaux de rendez-vous"

#: views/settings/wcfm-view-settings.php:120
msgid "Membership Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:142
msgid "WCfM Dashboard Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:146
#: views/settings/wcfm-view-wcmarketplace-settings.php:172
#: views/settings/wcfm-view-wcpvendors-settings.php:109
#: views/settings/wcfm-view-wcvendors-settings.php:156
msgid "Logo"
msgstr "Logo"

#: views/settings/wcfm-view-settings.php:147
msgid "Quick access icon"
msgstr "Accès rapide icon"

#: views/settings/wcfm-view-settings.php:148
msgid "My Store Label"
msgstr ""

#: views/settings/wcfm-view-settings.php:149
msgid "Disable Quick Access"
msgstr "Désactiver l’accès rapide"

#: views/settings/wcfm-view-settings.php:151
msgid "Disable Welcome Box"
msgstr "Désactiver la boîte de bienvenue"

#: views/settings/wcfm-view-settings.php:152
msgid "Disable WCFM Menu"
msgstr "Désactiver le Menu WCFM"

#: views/settings/wcfm-view-settings.php:153
msgid "Disable Theme Header"
msgstr "Désactiver l’en-tête"

#: views/settings/wcfm-view-settings.php:154
msgid "Disable WCFM Full View"
msgstr "Désactiver le mode plein écran WCFM"

#: views/settings/wcfm-view-settings.php:155
msgid "Disable WCFM Slick Menu"
msgstr "Désactiver WCFM Slick menu"

#: views/settings/wcfm-view-settings.php:157
msgid "Disable WCFM Header Panel"
msgstr "Désactiver le panneau en-tête WCFM"

#: views/settings/wcfm-view-settings.php:158
msgid "Disable Float Button"
msgstr "Désactiver le bouton flottant"

#: views/settings/wcfm-view-settings.php:159
msgid "Disable Ask a Question Button"
msgstr ""

#: views/settings/wcfm-view-settings.php:160
msgid "Disable Category Checklist View"
msgstr "Désactiver la liste de contrôle de catégorie"

#: views/settings/wcfm-view-settings.php:161
msgid "Disable Ultimate Notice"
msgstr "Désactiver l'avis final"

#: views/settings/wcfm-view-settings.php:175
msgid "Modules"
msgstr "Modules"

#: views/settings/wcfm-view-settings.php:179
msgid "Module Controller"
msgstr ""

#: views/settings/wcfm-view-settings.php:181
msgid "Configure what to hide from your dashboard"
msgstr "Configurer ce qu'il faut masquer à partir de votre tableau de bord"

#: views/settings/wcfm-view-settings.php:228
msgid "Dashboard Display Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:240
msgid "Reset to Default"
msgstr "Initialiser avec les valeurs par défaut"

#: views/settings/wcfm-view-settings.php:252
msgid "Dashboard Pages"
msgstr ""

#: views/settings/wcfm-view-settings.php:256
msgid "Dashboard Page/Endpoint Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:261
msgid "-- Select a page --"
msgstr "-- Sélectionnez une page --"

#: views/settings/wcfm-view-settings.php:276
msgid "Refresh Permalink"
msgstr "Actualiser le lien"

#: views/settings/wcfm-view-settings.php:276
msgid ""
"Check to refresh WCfM page permalinks. Only apply if you are getting error "
"(e.g. 404 not found) for any pages."
msgstr ""
"Cochez cette case pour actualiser les permaliens de la page WCfM. Ne "
"s'applique que si vous obtenez une erreur (par exemple, 404 non trouvé) pour "
"toutes les pages."

#: views/settings/wcfm-view-settings.php:277
msgid "This page should have shortcode - wc_frontend_manager"
msgstr "Cette page devrait avoir un shortcode-wc_frontend_manager"

#: views/settings/wcfm-view-settings.php:282
msgid "WCFM Endpoints"
msgstr "Points de terminaison"

#: views/settings/wcfm-view-settings.php:286
msgid "Dashboard End Points"
msgstr ""

#: views/settings/wcfm-view-settings.php:295
msgid "My Account End Points"
msgstr ""

#: views/settings/wcfm-view-settings.php:337
msgid "Dashboard Menu Manager"
msgstr ""

#: views/settings/wcfm-view-settings.php:418
msgid "Home Menu Label"
msgstr ""

#: views/settings/wcfm-view-settings.php:422
msgid "Icon"
msgstr ""

#: views/settings/wcfm-view-settings.php:422
msgid "Insert a valid Font-awesome icon class."
msgstr ""

#: views/settings/wcfm-view-settings.php:423
msgid "Slug"
msgstr ""

#: views/settings/wcfm-view-settings.php:426
msgid "Has New?"
msgstr ""

#: views/settings/wcfm-view-settings.php:427
#: views/settings/wcfm-view-settings.php:429
msgid "New Menu Class"
msgstr ""

#: views/settings/wcfm-view-settings.php:428
msgid "New Menu URL"
msgstr ""

#: views/settings/wcfm-view-settings.php:430
msgid "Sub Menu Capability"
msgstr ""

#: views/settings/wcfm-view-settings.php:431
msgid "Menu For"
msgstr ""

#: views/settings/wcfm-view-settings.php:431
msgid "All Users"
msgstr ""

#: views/settings/wcfm-view-settings.php:431
msgid "Only Admin"
msgstr ""

#: views/settings/wcfm-view-settings.php:431
msgid "Only Vendors"
msgstr ""

#: views/settings/wcfm-view-settings.php:432
msgid "Open in new tab?"
msgstr ""

#: views/settings/wcfm-view-settings.php:448
msgid "Email Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:452
msgid "WCfM Email Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:456
msgid "Email from name"
msgstr ""

#: views/settings/wcfm-view-settings.php:456
msgid ""
"Notification emails will be triggered with this name. By default Site Name "
"will be used"
msgstr ""

#: views/settings/wcfm-view-settings.php:457
msgid "Email from address"
msgstr ""

#: views/settings/wcfm-view-settings.php:457
msgid ""
"Notification emails will be triggered from this email address. By default "
"Site Admin Email will be used"
msgstr ""

#: views/settings/wcfm-view-settings.php:458
msgid "CC Email address"
msgstr ""

#: views/settings/wcfm-view-settings.php:458
msgid "Notification emails will be CC to this email address."
msgstr ""

#: views/settings/wcfm-view-settings.php:459
msgid "BCC Email address"
msgstr ""

#: views/settings/wcfm-view-settings.php:459
msgid "Notification emails will be BCC to this email address."
msgstr ""

#: views/settings/wcfm-view-settings.php:470
msgid "Inquiry Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:474
msgid "Inquiry Module"
msgstr ""

#: views/settings/wcfm-view-settings.php:480
msgid "Inquiry Button Label"
msgstr ""

#: views/settings/wcfm-view-settings.php:485
msgid "Insert option values | separated"
msgstr ""

#: views/settings/wcfm-view-settings.php:499
msgid "Product Type Categories"
msgstr "Catégories de produits"

#: views/settings/wcfm-view-settings.php:503
msgid "Product Type Specific Category Setup"
msgstr ""

#: views/settings/wcfm-view-settings.php:524
msgid ""
"Create group of your Store Categories as per Product Types. Product Manager "
"will work according to that."
msgstr ""
"Créez un groupe de catégories de vos magasins selon les types de produits. "
"Le chef de produit travaillera selon cela."

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:170
#, php-format
msgid "Upload a banner for your store. Banner size is (%sx%s) pixels."
msgstr ""
"Téléchargez une bannière pour votre boutique. La taille de la bannière est "
"(%sx%s) pixels."

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:215
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:216
msgid "Static Image"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:215
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:233
msgid "Slider"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:215
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:216
msgid "Video"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:228
msgid "Store Logo"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:228
msgid "Preferred  size is (125x125) pixels."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:230
msgid "Store Banner Type"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:231
msgid "Store Banner"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:232
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:241
msgid "Insert YouTube video URL."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:235
msgid "Slider Hyperlink"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:239
msgid "Store List Banner Type"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:240
msgid "Store List Banner"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:240
msgid "This Banner will be visible at Store List Page."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:241
msgid "Store List Video Banner"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:243
msgid "Mobile Banner"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:243
msgid "This Banner will be visible when someone browse store from Mobile."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:246
msgid "Store Slug"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:248
#: views/settings/wcfm-view-wcmarketplace-settings.php:175
#: views/settings/wcfm-view-wcvendors-settings.php:159
msgid "Shop Description"
msgstr "Description de votre boutique"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:248
#: views/settings/wcfm-view-wcmarketplace-settings.php:175
#: views/settings/wcfm-view-wcvendors-settings.php:159
msgid "This is displayed on your shop page."
msgstr "Ceci sera affiché sur les pages de votre boutique."

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:249
msgid "Select Shipping Countries"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:323
#: views/settings/wcfm-view-wcmarketplace-settings.php:258
msgid "Find Location"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:323
#: views/settings/wcfm-view-wcmarketplace-settings.php:258
msgid "Type an address to find"
msgstr "Saisir une adresse à rechercher"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:343
msgid "Store Visibility"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:347
msgid "Store name position at you Store Page."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:348
msgid "No of products at you Store Page."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:349
msgid "Hide Email from Store"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:350
msgid "Hide Phone from Store"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:351
msgid "Hide Address from Store"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:386
msgid "Prefered Payment Method"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:400
msgid "Your bank account name"
msgstr "Nom de votre compte en banque"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:401
msgid "Your bank account number"
msgstr "Votre numéro de compte bancaire"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:402
msgid "Name of bank"
msgstr "Nom de banque"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:403
msgid "Address of your bank"
msgstr "Adresse de votre banque"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:404
msgid "Routing number"
msgstr "Numéro de routage (USA seulement)"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:406
msgid "Swift code"
msgstr "Code Swift"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:407
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:83
msgid "IFSC Code"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:407
msgid "IFSC code"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:469
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:496
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:517
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:583
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:634
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:656
#: views/settings/wcfm-view-wcmarketplace-settings.php:382
#: views/settings/wcfm-view-wcmarketplace-settings.php:409
#: views/settings/wcfm-view-wcmarketplace-settings.php:430
#: views/settings/wcfm-view-wcmarketplace-settings.php:503
#: views/settings/wcfm-view-wcmarketplace-settings.php:550
#: views/settings/wcfm-view-wcmarketplace-settings.php:572
#: controllers/withdrawal/dokan/wcfm-controller-payments.php:90
#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:82
msgid "Stripe"
msgstr "Stripe"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:472
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:586
#: views/settings/wcfm-view-wcmarketplace-settings.php:385
#: views/settings/wcfm-view-wcmarketplace-settings.php:506
msgid "You are connected with Stripe"
msgstr "Vous êtes connecté à Stripe"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:478
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:592
#: views/settings/wcfm-view-wcmarketplace-settings.php:391
#: views/settings/wcfm-view-wcmarketplace-settings.php:512
msgid "Disconnect Stripe Account"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:499
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:520
#: views/settings/wcfm-view-wcmarketplace-settings.php:412
#: views/settings/wcfm-view-wcmarketplace-settings.php:433
msgid "Please Retry!!!"
msgstr "Veuillez réessayer !!!"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:565
msgid "Unable to disconnect your account pleease try again"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:636
#: views/settings/wcfm-view-wcmarketplace-settings.php:552
msgid "You are not connected with stripe."
msgstr "Vous n’êtes connecté à Stripe."

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:658
#: views/settings/wcfm-view-wcmarketplace-settings.php:574
msgid "Please connected with stripe again."
msgstr "Veuillez vous connecter de nouveau avec Stripe."

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:674
msgid "Stripe not setup properly, please contact your site admin."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:711
msgid "SEO Title"
msgstr "Titre SEO"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:711
msgid "SEO Title is shown as the title of your store page"
msgstr "Le titre SEO est affiché comme le titre de votre page boutique"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:712
msgid "Meta Description"
msgstr "Decription du meta"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:712
msgid ""
"The meta description is often shown as the black text under the title in a "
"search result. For this to work it has to contain the keyword that was "
"searched for and should be less than 156 chars."
msgstr ""
"La méta description est souvent représentée sous forme de texte noir sous le "
"titre dans un résultat de recherche. Pour que cela fonctionne, il doit "
"contenir le mot clé qui a été recherché et doit être inférieur à 156 "
"caractères."

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:713
msgid "Meta Keywords"
msgstr "Meta Mots-clés"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:713
msgid ""
"Insert some comma separated keywords for better ranking of your store page."
msgstr ""
"Insérez des mots-clés séparés par des virgules pour un meilleur classement "
"de votre page boutique."

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:714
msgid "Facebook Title"
msgstr "Titre Facebook"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:715
msgid "Facebook Description"
msgstr "Description Facebook"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:716
msgid "Facebook Image"
msgstr "Image Facebook"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:717
msgid "Twitter Title"
msgstr "Titre Twitter"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:718
msgid "Twitter Description"
msgstr "Description Twitter"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:719
msgid "Twitter Image"
msgstr "Image Twitter :"

#: views/settings/wcfm-view-wcmarketplace-settings.php:172
msgid "Preferred logo should be 200x200 px."
msgstr "Le logo doit être de 200x200 px."

#: views/settings/wcfm-view-wcmarketplace-settings.php:173
#: views/settings/wcfm-view-wcpvendors-settings.php:110
#: views/settings/wcfm-view-wcvendors-settings.php:157
msgid "Your shop name is public and must be unique."
msgstr "Le nom de votre boutique est public et doit être unique."

#: views/settings/wcfm-view-wcmarketplace-settings.php:174
msgid "Shop Slug"
msgstr "Slug"

#: views/settings/wcfm-view-wcmarketplace-settings.php:174
msgid "Your shop slug is public and must be unique."
msgstr "Le nom de votre boutique est public et doit être unique."

#: views/settings/wcfm-view-wcmarketplace-settings.php:201
#: views/settings/wcfm-view-wcvendors-settings.php:187
msgid "Branding"
msgstr "Image de marque"

#: views/settings/wcfm-view-wcmarketplace-settings.php:207
msgid "Preferred banner should be 1200x245 px."
msgstr "La bannière préférée devrait être 1200x245 px."

#: views/settings/wcfm-view-wcmarketplace-settings.php:209
msgid "Shop Phone"
msgstr "Téléphone de la boutique"

#: views/settings/wcfm-view-wcmarketplace-settings.php:209
msgid "Your store phone no."
msgstr "Votre numéro de téléphone"

#: views/settings/wcfm-view-wcmarketplace-settings.php:244
#: views/settings/wcfm-view-wcmarketplace-settings.php:245
#: views/settings/wcfm-view-wcpvendors-settings.php:130
#: views/settings/wcfm-view-wcpvendors-settings.php:131
msgid "Timezone"
msgstr "Fuseau horaire"

#: views/settings/wcfm-view-wcmarketplace-settings.php:244
#: views/settings/wcfm-view-wcpvendors-settings.php:130
msgid "Set the local timezone."
msgstr "Utiliser le fuseau horaire local."

#: views/settings/wcfm-view-wcmarketplace-settings.php:270
msgid "Please contact your administrator to enable Google map feature."
msgstr ""
"Veuillez contacter votre administrateur pour activer la fonctionnalité "
"Google map."

#: views/settings/wcfm-view-wcmarketplace-settings.php:278
msgid "Shop Template"
msgstr "Modèle de la boutique"

#: views/settings/wcfm-view-wcmarketplace-settings.php:311
msgid "Account Type"
msgstr "Type de compte"

#: views/settings/wcfm-view-wcmarketplace-settings.php:311
msgid "Current"
msgstr "Actuel"

#: views/settings/wcfm-view-wcmarketplace-settings.php:311
msgid "Savings"
msgstr "Épargne"

#: views/settings/wcfm-view-wcmarketplace-settings.php:314
msgid "ABA Routing Number"
msgstr "Numéro de routage - Etats-Unis facultatif"

#: views/settings/wcfm-view-wcmarketplace-settings.php:316
msgid "Destination Currency"
msgstr "Devise de la destination"

#: views/settings/wcfm-view-wcmarketplace-settings.php:317
msgid "Account IBAN"
msgstr "Coordonnée bancaire"

#: views/settings/wcfm-view-wcmarketplace-settings.php:318
msgid "Account Holder Name"
msgstr "Nom du titulaire du compte"

#: views/settings/wcfm-view-wcmarketplace-settings.php:635
#: views/settings/wcfm-view-wcmarketplace-settings.php:644
msgid "Shipping Zone"
msgstr "Zone de livraison"

#: views/settings/wcfm-view-wcmarketplace-settings.php:665
msgid "Shipping Rules"
msgstr "Règles d'expédition"

#: views/settings/wcfm-view-wcmarketplace-settings.php:684
msgid ""
"There is no shipping zone or Flat Rate shipping method not associated for "
"the zones to set shipping prices, kindly contact your Store Admin."
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:111
msgid "Vendor Email"
msgstr "Email vendeur"

#: views/settings/wcfm-view-wcpvendors-settings.php:111
msgid ""
"Enter the email for this vendor. This is the email where all notifications "
"will be send such as new orders and customer inquiries. You may enter more "
"than one email separating each with a comma."
msgstr ""
"Entrez l’email pour ce vendeur. C’est l’email où toutes les notifications "
"sont envoyées, comme les nouvelles commandes et demandes des clients. Vous "
"pouvez entrer plus d’un email séparez-les par une virgule."

#: views/settings/wcfm-view-wcpvendors-settings.php:112
msgid "Enter the profile information you would like for customer to see."
msgstr ""
"Entrez les informations de profil que vous souhaitez montrer au client."

#: views/settings/wcfm-view-wcpvendors-settings.php:180
msgid "Paypal Email"
msgstr "Email Paypal"

#: views/settings/wcfm-view-wcpvendors-settings.php:180
msgid "PayPal email account where you will receive your commission."
msgstr "Compte de messagerie PayPal où vous recevrez votre commission."

#: views/settings/wcfm-view-wcpvendors-settings.php:181
msgid ""
"Default commission you will receive per product sale. Please note product "
"level commission can override this. Check your product to confirm."
msgstr ""
"La commission par défaut que vous recevrez par produit vendu. Veuillez noter "
"que la commission de niveau de produit peut l'annuler. Vérifiez votre "
"produit pour confirmer."

#: views/settings/wcfm-view-wcvendors-settings.php:158
#: views/vendors/wcfm-view-vendors-manage.php:293
msgid "Seller Info"
msgstr "Informations sur le vendeur"

#: views/settings/wcfm-view-wcvendors-settings.php:158
msgid "This is displayed on each of your products."
msgstr "Ce message s'affiche sur chacune de vos prestations."

#: views/settings/wcfm-view-wcvendors-settings.php:196
msgid "Store Website / Blog URL"
msgstr "Lien du site web / blog de l'entreprise"

#: views/settings/wcfm-view-wcvendors-settings.php:196
msgid "Your company/blog URL here."
msgstr "Votre adresse internet entreprise/blog ici."

#: views/settings/wcfm-view-wcvendors-settings.php:197
msgid "This is your store contact number."
msgstr "Ceci est votre numéro de contact pour les clients."

#: views/settings/wcfm-view-wcvendors-settings.php:232
#: views/settings/wcfm-view-wcvendors-settings.php:420
msgid "WCV Pro Settings"
msgstr "Paramètres WCV Pro"

#: views/settings/wcfm-view-wcvendors-settings.php:252
msgid "Your PayPal address is used to send you your commission."
msgstr "Votre adresse PayPal est utilisée pour vous envoyer votre commission."

#: views/settings/wcfm-view-wcvendors-settings.php:305
msgid "Bank Payment (Mangopay)"
msgstr "Paiement bancaire (Mangopay)"

#: views/settings/wcfm-view-wcvendors-settings.php:318
msgid "CHECKING"
msgstr "VÉRIFICATION"

#: views/settings/wcfm-view-wcvendors-settings.php:318
msgid "SAVINGS"
msgstr "SAUVEGARDE"

#: views/settings/wcfm-view-wcvendors-settings.php:368
#: views/settings/wcfm-view-wcvendors-settings.php:375
msgid "Charge once per product"
msgstr "Charge une fois par produit"

#: views/settings/wcfm-view-wcvendors-settings.php:384
msgid "Postcode"
msgstr "Code Postal"

#: views/settings/wcfm-view-wcvendors-settings.php:385
msgid "Shipping Fee"
msgstr "Frais d'expédition"

#: views/settings/wcfm-view-wcvendors-settings.php:386
msgid "Override Qty"
msgstr "Remplacer qté"

#: views/settings/wcfm-view-wcvendors-settings.php:392
msgid "Min Charge Order"
msgstr "Minimum de commande"

#: views/settings/wcfm-view-wcvendors-settings.php:392
msgid "The minimum shipping fee charged for an order."
msgstr "Les frais d'expédition minimum facturés pour une commande."

#: views/settings/wcfm-view-wcvendors-settings.php:393
msgid "Max Charge Order"
msgstr "Max charge commande"

#: views/settings/wcfm-view-wcvendors-settings.php:393
msgid "The maximum shipping fee charged for an order."
msgstr "Les frais d'expédition maximum facturés pour une commande."

#: views/settings/wcfm-view-wcvendors-settings.php:394
msgid "Free Shipping Order"
msgstr "Commande de livraison gratuite"

#: views/settings/wcfm-view-wcvendors-settings.php:394
msgid ""
"Free shipping for order spends over this amount. This will override the max "
"shipping charge above."
msgstr ""
"La livraison gratuite pour l'achat dépasse ce montant. Cela annulera la "
"charge d'expédition maximale ci-dessus."

#: views/settings/wcfm-view-wcvendors-settings.php:395
msgid "Max Charge Product"
msgstr "Max charge produit"

#: views/settings/wcfm-view-wcvendors-settings.php:395
msgid "The maximum shipping charged per product no matter the quantity."
msgstr ""
"La livraison maximale facturée par produit, quelle que soit la quantité."

#: views/settings/wcfm-view-wcvendors-settings.php:396
msgid "Free Shipping Product"
msgstr "Livraison gratuite du produit"

#: views/settings/wcfm-view-wcvendors-settings.php:396
msgid ""
"Free shipping if the spend per product is over this amount. This will "
"override the max shipping charge above."
msgstr ""
"Livraison gratuite si la dépense par produit est plus de ce montant. Cela "
"remplacera les frais d'expédition Max ci-dessus."

#: views/settings/wcfm-view-wcvendors-settings.php:397
msgid "Product handling fee"
msgstr "Frais de gestion des marchandises"

#: views/settings/wcfm-view-wcvendors-settings.php:397
msgid "Leave empty to disable"
msgstr "Lasciare vuoto per disabilitare"

#: views/settings/wcfm-view-wcvendors-settings.php:397
msgid ""
"The product handling fee, this can be overridden on a per product basis. "
"Amount (5.00) or Percentage (5%)."
msgstr ""
"Des frais de gestion peuvent être fixés par prestation. Montant (5,00) ou en "
"pourcentage (5%)."

#: views/settings/wcfm-view-wcvendors-settings.php:404
msgid "From Address"
msgstr ""
"Les courriels automatiques, comme par exemple les informations "
"d'inscription, seront envoyés depuis cette adresse. Utilisez une adresse se "
"terminant par le domaine de votre site pour éviter que ces courriels ne "
"soient signalés comme des pourriels (spam)."

#: views/vendors/wcfm-view-vendors-manage.php:145
#: views/vendors/wcfm-view-vendors-new.php:62
msgid "Manage Vendor"
msgstr "Gérer le fournisseur"

#: views/vendors/wcfm-view-vendors-manage.php:189
#: views/vendors/wcfm-view-vendors-new.php:75
#: views/vendors/wcfm-view-vendors.php:28
msgid "Add New Vendor"
msgstr "Ajouter Vendeur"

#: views/vendors/wcfm-view-vendors-manage.php:241
#, php-format
msgid "%s product"
msgid_plural "<strong>%s products</strong><br />"
msgstr[0] "<strong> %s produit </strong> < br/>"
msgstr[1] "<strong> %s produits </strong> < br/>"

#: views/vendors/wcfm-view-vendors-manage.php:243
msgid "total products posted"
msgstr "total des produits affichés"

#: views/vendors/wcfm-view-vendors-manage.php:255
#, php-format
msgid "<strong>%s item</strong><br />"
msgid_plural "<strong>%s items</strong><br />"
msgstr[0] "<strong> %s Produit </strong> < br/>"
msgstr[1] "<strong> %s Produits </strong> < br/>"

#: views/vendors/wcfm-view-vendors-manage.php:279
#: views/vendors/wcfm-view-vendors-manage.php:281
msgid "Store Admin"
msgstr "Administration du magasin"

#: views/vendors/wcfm-view-vendors-manage.php:321
msgid "Profile Update"
msgstr "Mettre à jour le profil"

#: views/vendors/wcfm-view-vendors-manage.php:371
msgid "Vendor not yet subscribed for a membership!"
msgstr "Vendeur pas encore abonné !"

#: views/vendors/wcfm-view-vendors-manage.php:384
msgid "Send Message"
msgstr "Envoyer le message"

#: views/vendors/wcfm-view-vendors-new.php:69
msgid "Edit Vendor"
msgstr "Modifier Vendeur"

#: views/vendors/wcfm-view-vendors-new.php:69
msgid "Add Vendor"
msgstr "Ajouter Vendeur"

#: views/vendors/wcfm-view-vendors.php:25
msgid "Vendors Listing"
msgstr "Liste des vendeurs"

#: views/vendors/wcfm-view-vendors.php:29
msgid "Pending Vendors"
msgstr "Vendeur en attente"

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:52
#: views/wc_bookings/wcfm-view-wcbookings.php:70
msgid "Create Booking"
msgstr "Créer une réservation"

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:64
#: views/wc_bookings/wcfm-view-wcbookings-details.php:76
#: views/wc_bookings/wcfm-view-wcbookings.php:81
msgid "Create Bookable"
msgstr "Créer une réservation"

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:81
#: views/wc_bookings/wcfm-view-wcbookings-details.php:71
#: views/wc_bookings/wcfm-view-wcbookings.php:76
msgid "Manage Resources"
msgstr "Gérer les ressources"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:45
msgid "Booking Details"
msgstr "Détails de la réservation"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:52
msgid "Booking #"
msgstr "Réservation #"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:63
#: views/wc_bookings/wcfm-view-wcbookings.php:86
msgid "Calendar View"
msgstr "Vue en calendrier"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:87
msgid "Overview"
msgstr "Aperçu"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:93
msgid "Booking Created:"
msgstr "Réservation Créée :"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:98
msgid "Order Number:"
msgstr "Numéro de commande :"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:130
msgid "Confirm"
msgstr "Coonfirmer"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:131
msgid "Decline"
msgstr "Refuser"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:167
msgid "Resource:"
msgstr "Ressources :"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:346
msgid "Billing Email:"
msgstr "Email de la facturation:"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:352
msgid "Billing Phone:"
msgstr "Téléphone:"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:363
msgid "View Order"
msgstr "Voir la commande"

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:88
#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:80
msgid "PayPal"
msgstr "PayPal"

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:92
msgid "Bank Transfer"
msgstr "Virement bancaire"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:61
#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal-request.php:46
msgid "Request successfully sent"
msgstr "Demande envoyée avec succès"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:63
msgid "Something went wrong please try again later"
msgstr "Une erreur s'est produite; veuillez réessayer plus tard"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:57
msgid "Seller account balance not enough for this withdrawal."
msgstr "Pas assez sur le compte du vendeur pour ce retrait"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:106
msgid "Withdrawal Requests successfully approved."
msgstr "Demande de retrait approuvée"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:108
msgid "No withdrawals selected for approve."
msgstr "Aucune sélection"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:170
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:109
msgid "Withdrawal Requests successfully cancelled."
msgstr "Retrait annulé"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:172
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:111
msgid "No withdrawals selected for cancel."
msgstr "Aucune seléction pour l'annulation"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:77
msgid "Withdrawal Approved"
msgstr "Retrait Approuvé"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:79
#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:100
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:95
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse.php:91
msgid "Withdrawal Cancelled"
msgstr "Retrait Annulé"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:81
msgid "Withdrawal Pending"
msgstr "Retrait Approuvé"

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:98
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:93
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse.php:89
msgid "Withdrawal Completed"
msgstr "Retrait effectué"

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:102
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:97
msgid "Withdrawal Processing"
msgstr "Retrait en cours"

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:153
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:162
msgid "Auto Withdrawal"
msgstr "Retrait automatique"

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:156
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:165
msgid "By Payment Type"
msgstr "Par Type de Paiement"

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:158
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:167
msgid "By Request"
msgstr "Par demande"

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:160
msgid "Split Pay"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:83
msgid "Withdrawal Request successfully processed."
msgstr "Demande de Retrait effectuée"

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:85
msgid "Withdrawal Request processing failed, please contact Store Admin."
msgstr "Demande de Retrait en echec, contactez l'administrateur"

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:90
#, php-format
msgid "Vendor <b>%s</b> has placed a Withdrawal Request #%s."
msgstr "Le vendeur <b>%s</b> a emis une demande de retrait #%s"

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:92
msgid "Your withdrawal request successfully sent."
msgstr "Votre demande de retrait a été envoyée"

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:97
msgid "Your withdrawal request failed, please try after sometime."
msgstr "Echec de votre demande de retrait, réessayez plus tard "

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:100
msgid "No payment method selected for withdrawal commission"
msgstr "Aucun mode de paiement sélectionné pour le retrait de la commission"

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:103
msgid "No commission selected for withdrawal"
msgstr "Aucune Commission sélectionnée pour le retrait"

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:59
msgid "Withdrawal Requests successfully processed."
msgstr "Succès de la Demande de retrait "

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:61
msgid "Withdrawal Requests partially processed, check log for more details."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:64
msgid "No withdrawals selected for approval."
msgstr "Aucun Retrait sélectionné"

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse-actions.php:46
msgid "Reverse Withdrawal Requests successfully approveed."
msgstr "Demande de Retrait inverse approuvée"

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse-actions.php:48
msgid "No reverse withdrawals selected for approve."
msgstr "Aucun Retrait inverse sélectionné"

#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:84
msgid "Direct Bank Transfer"
msgstr "Virement bancaire"

#: includes/libs/php/class-wcfm-fields.php:649
#: includes/libs/php/class-wcfm-fields.php:653
msgid "-Select a location-"
msgstr "- Sélectionnez un lieu -"

#: views/withdrawal/dokan/wcfm-view-payments.php:34
#: views/withdrawal/wcfm/wcfm-view-payments.php:36
#: views/withdrawal/wcmp/wcfm-view-payments.php:34
msgid "Transactions for: "
msgstr "Transactions pour : "

#: views/withdrawal/dokan/wcfm-view-payments.php:51
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:37
#: views/withdrawal/wcfm/wcfm-view-payments.php:53
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:44
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:42
msgid "Show all .."
msgstr "Afficher tout ..."

#: views/withdrawal/dokan/wcfm-view-payments.php:52
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:38
#: views/withdrawal/wcfm/wcfm-view-payments.php:54
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:45
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:43
msgid "Approved"
msgstr "Approuvé"

#: views/withdrawal/dokan/wcfm-view-payments.php:53
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:39
#: views/withdrawal/wcfm/wcfm-view-payments.php:55
msgid "Processing"
msgstr "En cours"

#: views/withdrawal/dokan/wcfm-view-payments.php:69
#: views/withdrawal/dokan/wcfm-view-payments.php:78
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:159
#: views/withdrawal/wcmp/wcfm-view-payments.php:68
#: views/withdrawal/wcmp/wcfm-view-payments.php:79
msgid "Pay Mode"
msgstr "Mode de paiement"

#: views/withdrawal/dokan/wcfm-view-payments.php:70
#: views/withdrawal/dokan/wcfm-view-payments.php:79
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:56
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:66
#: views/withdrawal/wcfm/wcfm-view-payments.php:76
#: views/withdrawal/wcfm/wcfm-view-payments.php:90
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:76
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:92
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:70
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:82
msgid "Note"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:52
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:62
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:64
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:76
msgid "Requests"
msgstr "Demandes"

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:80
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:106
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:96
msgid "Note to Vendor(s)"
msgstr "Note au(x) Vendeur(s) "

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:87
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:113
msgid "Cancel"
msgstr "Annuler"

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:88
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:114
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:103
msgid "Approve"
msgstr "Approuvé"

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:64
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:113
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:57
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:53
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:42
msgid "Transaction History"
msgstr "Historique des transactions"

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:105
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:112
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:88
msgid "Request"
msgstr "Demande"

#: views/withdrawal/wcfm/wcfm-view-payments.php:69
#: views/withdrawal/wcfm/wcfm-view-payments.php:83
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:69
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:85
#: views/withdrawal/wcmp/wcfm-view-payments.php:64
#: views/withdrawal/wcmp/wcfm-view-payments.php:75
msgid "Transc.ID"
msgstr "Transc.ID"

#: views/withdrawal/wcfm/wcfm-view-payments.php:70
#: views/withdrawal/wcfm/wcfm-view-payments.php:84
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:70
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:86
msgid "Order IDs"
msgstr "Commande IDs"

#: views/withdrawal/wcfm/wcfm-view-payments.php:71
#: views/withdrawal/wcfm/wcfm-view-payments.php:85
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:75
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:91
#: views/withdrawal/wcmp/wcfm-view-payments.php:65
#: views/withdrawal/wcmp/wcfm-view-payments.php:76
msgid "Commission IDs"
msgstr "IDs de Commission"

#: views/withdrawal/wcfm/wcfm-view-payments.php:73
#: views/withdrawal/wcfm/wcfm-view-payments.php:87
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:73
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:89
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:78
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:91
msgid "Charges"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-payments.php:75
#: views/withdrawal/wcfm/wcfm-view-payments.php:89
msgid "Mode"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:84
msgid "Payment Received Email"
msgstr "Message pour Paiement Recu"

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:85
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:88
msgid "Transaction ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:86
msgid "Transaction Status"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:87
msgid "Request ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:89
msgid "Transaction Ref"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:90
msgid "Transfer Mode"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:104
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:43
msgid "Transaction #"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:136
msgid "Order ID(s)"
msgstr "Commande ID(s)"

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:137
msgid "Commission ID(s)"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:138
msgid "Payment Method"
msgstr "Mode de paiement"

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:169
msgid "By Split Pay"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:175
msgid "Total Amount"
msgstr "Montant Total"

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:179
msgid "Withdrawal Charges"
msgstr "Frais pour Retrait"

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:183
msgid "Paid Amount"
msgstr "Montant Payé"

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:183
msgid "Payable Amount"
msgstr "Montant Payable"

#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:35
msgid "Reverse Withdrawal"
msgstr "Retenu / retrait inverse"

#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:67
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:83
msgid "Select all to approve or cancel"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:25
msgid "Reverse Withdrawals"
msgstr "Retrait inverse"

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:32
msgid "Reverse Withdrawal Requests"
msgstr "Demandes de retrait inverse"

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:65
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:77
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:75
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:88
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:58
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:67
msgid "Order ID"
msgstr "ID de commande"

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:69
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:81
msgid "Balance"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:42
msgid "Pending Withdrawals: "
msgstr "Retraits en attente"

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:46
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:36
msgid "Threshold for withdrawals: "
msgstr "Seuil pour les remboursements: "

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:51
msgid "Reverse pay balance "
msgstr "Balance de retenus"

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:52
#, php-format
msgid "Thresold Limit: %s"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:73
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:86
msgid "Select all to send request"
msgstr "Selectionnez tous pour envoyer une demande"

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:76
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:89
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:59
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:68
msgid "Commission ID"
msgstr "ID de la Commission"

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:77
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:90
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:60
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:69
msgid "My Earnings"
msgstr "Mes revenus"

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:102
msgid ""
"Withdrawal charges will be re-calculated depending upon total withdrawal "
"amount."
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:114
msgid "Withdrawal disable due to high reverse balance."
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:117
msgid "Withdrawal disable due to low account balance."
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-payments.php:67
#: views/withdrawal/wcmp/wcfm-view-payments.php:78
msgid "Net Earnings"
msgstr "Revenus net"

#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:57
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:66
msgid "Send Request"
msgstr "Envoyer la demande"

#. Plugin Name of the plugin/theme
msgid "WooCommerce Frontend Manager"
msgstr "Gestionnaire d'interface WooCommerce"

#. Description of the plugin/theme
msgid ""
"WooCommerce is really Easy and Beautiful. We are here to make your life much "
"more Easier and Peaceful."
msgstr ""
"WooCommerce est vraiment facile et beau. Nous sommes ici pour rendre votre "
"vie beaucoup plus facile et paisible."

#. #-#-#-#-#  wc-frontend-manager-code.pot (WooCommerce Frontend Manager 4.1.0)
#. Plugin URI of the plugin/theme
#. #-#-#-#-#  wc-frontend-manager-code.pot (WooCommerce Frontend Manager 4.1.0)
#. Author URI of the plugin/theme
msgid "https://wclovers.com"
msgstr "https://gredeals.com"

#. Author of the plugin/theme
msgid "WC Lovers"
msgstr "Les amateurs de WC"
