<?php
/**
 * Vendor Gateway Manager
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Gateway manager class
 */
class Vendor_Gateway_Manager {
    
    /**
     * Available gateways
     */
    private $gateways = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_gateways();
    }
    
    /**
     * Initialize gateways
     */
    private function init_gateways() {
        $this->gateways = array(
            'paypal' => new Vendor_Gateway_Paypal(),
            'bank_transfer' => new Vendor_Gateway_Bank_Transfer()
        );
        
        // Allow plugins to add custom gateways
        $this->gateways = apply_filters('vendor_payment_gateways', $this->gateways);
    }
    
    /**
     * Get all gateways
     */
    public function get_gateways() {
        return $this->gateways;
    }
    
    /**
     * Get available gateways
     */
    public function get_available_gateways() {
        $available = array();
        
        foreach ($this->gateways as $id => $gateway) {
            if ($gateway->is_available()) {
                $available[$id] = $gateway;
            }
        }
        
        return $available;
    }
    
    /**
     * Get gateway by ID
     */
    public function get_gateway($gateway_id) {
        return isset($this->gateways[$gateway_id]) ? $this->gateways[$gateway_id] : false;
    }
    
    /**
     * Process withdrawal payment
     */
    public function process_withdrawal_payment($withdrawal_id, $gateway_id) {
        $gateway = $this->get_gateway($gateway_id);
        if (!$gateway || !$gateway->is_available()) {
            return array(
                'success' => false,
                'message' => 'Payment gateway not available'
            );
        }
        
        $withdrawal = vendor()->withdrawal->get_withdrawal($withdrawal_id);
        if (!$withdrawal) {
            return array(
                'success' => false,
                'message' => 'Withdrawal not found'
            );
        }
        
        $vendor = vendor()->vendor_manager->get_vendor($withdrawal->vendor_id);
        if (!$vendor) {
            return array(
                'success' => false,
                'message' => 'Vendor not found'
            );
        }
        
        $payment_details = maybe_unserialize($withdrawal->payment_details);
        $vendor_email = $vendor->store_email ?: get_user_by('id', $vendor->user_id)->user_email;
        
        // Process payment through gateway
        $result = $gateway->process_payment(
            $withdrawal_id,
            $vendor->id,
            $withdrawal->net_amount,
            $vendor_email,
            $payment_details
        );
        
        if ($result['success']) {
            // Update withdrawal status
            $status = isset($result['requires_manual_processing']) && $result['requires_manual_processing'] ? 'processing' : 'completed';
            
            vendor()->withdrawal->update_withdrawal_status(
                $withdrawal_id,
                $status,
                $result['message'],
                $result['transaction_id']
            );
            
            // Send notification email
            $this->send_withdrawal_notification($withdrawal, $vendor, $result);
        }
        
        return $result;
    }
    
    /**
     * Get payment methods for frontend
     */
    public function get_payment_methods() {
        $methods = array();
        
        foreach ($this->get_available_gateways() as $id => $gateway) {
            $methods[$id] = $gateway->title;
        }
        
        return $methods;
    }
    
    /**
     * Get payment fields for a gateway
     */
    public function get_payment_fields($gateway_id) {
        $gateway = $this->get_gateway($gateway_id);
        
        if (!$gateway) {
            return array();
        }
        
        return $gateway->get_payment_fields();
    }
    
    /**
     * Validate payment details
     */
    public function validate_payment_details($gateway_id, $details) {
        $gateway = $this->get_gateway($gateway_id);
        
        if (!$gateway) {
            return new WP_Error('invalid_gateway', __('Invalid payment gateway', 'vendor'));
        }
        
        return $gateway->validate_payment_details($details);
    }
    
    /**
     * Calculate withdrawal charges
     */
    public function calculate_charges($amount, $gateway_id) {
        $gateway = $this->get_gateway($gateway_id);
        
        if (!$gateway) {
            return 0;
        }
        
        // Get gateway-specific charges
        $charges = 0;
        
        if (method_exists($gateway, 'calculate_charges')) {
            $charges = $gateway->calculate_charges($amount);
        } else {
            // Default charge calculation
            $withdrawal_settings = get_option('vendor_withdrawal_settings', array());
            
            if (isset($withdrawal_settings[$gateway_id]['charges'])) {
                $charge_settings = $withdrawal_settings[$gateway_id]['charges'];
                
                if ($charge_settings['type'] === 'percentage') {
                    $charges = ($amount * $charge_settings['value']) / 100;
                } elseif ($charge_settings['type'] === 'fixed') {
                    $charges = $charge_settings['value'];
                }
            }
        }
        
        return apply_filters('vendor_withdrawal_charges', $charges, $amount, $gateway_id);
    }
    
    /**
     * Get minimum withdrawal amount for gateway
     */
    public function get_minimum_amount($gateway_id) {
        $gateway = $this->get_gateway($gateway_id);
        
        if (!$gateway) {
            return 0;
        }
        
        if (method_exists($gateway, 'get_minimum_amount')) {
            return $gateway->get_minimum_amount();
        }
        
        // Default minimum amount
        $withdrawal_settings = get_option('vendor_withdrawal_settings', array());
        
        if (isset($withdrawal_settings[$gateway_id]['minimum_amount'])) {
            return floatval($withdrawal_settings[$gateway_id]['minimum_amount']);
        }
        
        return 0;
    }
    
    /**
     * Send withdrawal notification
     */
    private function send_withdrawal_notification($withdrawal, $vendor, $result) {
        $user = get_user_by('id', $vendor->user_id);
        
        if (!$user) {
            return;
        }
        
        $subject = sprintf(__('Withdrawal Request %s', 'vendor'), $result['success'] ? 'Processed' : 'Failed');
        
        $message = sprintf(
            __('Hello %s,

Your withdrawal request of %s has been %s.

%s

Transaction ID: %s

Best regards,
%s Team', 'vendor'),
            $user->display_name,
            wc_price($withdrawal->amount),
            $result['success'] ? 'processed successfully' : 'failed',
            $result['message'],
            $result['transaction_id'] ?: 'N/A',
            get_bloginfo('name')
        );
        
        wp_mail($user->user_email, $subject, $message);
        
        // Send admin notification for manual processing
        if (isset($result['requires_manual_processing']) && $result['requires_manual_processing']) {
            $admin_email = get_option('admin_email');
            $admin_subject = sprintf(__('Manual Withdrawal Processing Required - %s', 'vendor'), $vendor->store_name);
            
            $admin_message = sprintf(
                __('A withdrawal request requires manual processing:

Vendor: %s
Amount: %s
Method: %s
Transaction ID: %s

Please process this withdrawal manually and update the status in the admin panel.

View withdrawal: %s', 'vendor'),
                $vendor->store_name,
                wc_price($withdrawal->amount),
                $withdrawal->payment_method,
                $result['transaction_id'],
                admin_url('admin.php?page=vendor-withdrawals&action=view&withdrawal_id=' . $withdrawal->id)
            );
            
            wp_mail($admin_email, $admin_subject, $admin_message);
        }
    }
    
    /**
     * Get gateway settings fields
     */
    public function get_gateway_settings_fields($gateway_id) {
        $gateway = $this->get_gateway($gateway_id);
        
        if (!$gateway || !method_exists($gateway, 'get_settings_fields')) {
            return array();
        }
        
        return $gateway->get_settings_fields();
    }
    
    /**
     * Update gateway settings
     */
    public function update_gateway_settings($gateway_id, $settings) {
        $gateway = $this->get_gateway($gateway_id);
        
        if (!$gateway || !method_exists($gateway, 'update_settings')) {
            return false;
        }
        
        $gateway->update_settings($settings);
        return true;
    }
}
