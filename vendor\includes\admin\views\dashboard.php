<?php
/**
 * Admin Dashboard View
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1><?php _e('Vendor Dashboard', 'vendor'); ?></h1>
    
    <div class="vendor-dashboard-stats">
        <div class="vendor-stats-grid">
            <!-- Vendors Stats -->
            <div class="vendor-stat-card">
                <div class="vendor-stat-icon">
                    <span class="dashicons dashicons-groups"></span>
                </div>
                <div class="vendor-stat-content">
                    <h3><?php echo esc_html($stats['total_vendors']); ?></h3>
                    <p><?php _e('Total Vendors', 'vendor'); ?></p>
                    <div class="vendor-stat-details">
                        <span class="pending"><?php printf(__('%d Pending', 'vendor'), $stats['pending_vendors']); ?></span>
                        <span class="active"><?php printf(__('%d Active', 'vendor'), $stats['active_vendors']); ?></span>
                    </div>
                </div>
            </div>
            
            <!-- Withdrawals Stats -->
            <div class="vendor-stat-card">
                <div class="vendor-stat-icon">
                    <span class="dashicons dashicons-money-alt"></span>
                </div>
                <div class="vendor-stat-content">
                    <h3><?php echo wc_price($stats['withdrawal_summary']->total_amount ?: 0); ?></h3>
                    <p><?php _e('Total Withdrawals', 'vendor'); ?></p>
                    <div class="vendor-stat-details">
                        <span class="pending"><?php echo wc_price($stats['withdrawal_summary']->pending_amount ?: 0); ?> <?php _e('Pending', 'vendor'); ?></span>
                        <span class="completed"><?php echo wc_price($stats['withdrawal_summary']->completed_amount ?: 0); ?> <?php _e('Completed', 'vendor'); ?></span>
                    </div>
                </div>
            </div>
            
            <!-- Commissions Stats -->
            <div class="vendor-stat-card">
                <div class="vendor-stat-icon">
                    <span class="dashicons dashicons-chart-line"></span>
                </div>
                <div class="vendor-stat-content">
                    <h3><?php echo wc_price($stats['commission_stats']->total_commission ?: 0); ?></h3>
                    <p><?php _e('Total Commissions', 'vendor'); ?></p>
                    <div class="vendor-stat-details">
                        <span class="pending"><?php echo wc_price($stats['commission_stats']->pending_commission ?: 0); ?> <?php _e('Pending', 'vendor'); ?></span>
                        <span class="approved"><?php echo wc_price($stats['commission_stats']->approved_commission ?: 0); ?> <?php _e('Approved', 'vendor'); ?></span>
                    </div>
                </div>
            </div>
            
            <!-- Orders Stats -->
            <div class="vendor-stat-card">
                <div class="vendor-stat-icon">
                    <span class="dashicons dashicons-cart"></span>
                </div>
                <div class="vendor-stat-content">
                    <h3><?php echo esc_html($stats['withdrawal_summary']->total_requests ?: 0); ?></h3>
                    <p><?php _e('Withdrawal Requests', 'vendor'); ?></p>
                    <div class="vendor-stat-details">
                        <a href="<?php echo admin_url('admin.php?page=vendor-withdrawals&status=pending'); ?>"><?php _e('View Pending', 'vendor'); ?></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="vendor-dashboard-content">
        <div class="vendor-dashboard-row">
            <!-- Recent Vendors -->
            <div class="vendor-dashboard-col">
                <div class="vendor-widget">
                    <div class="vendor-widget-header">
                        <h3><?php _e('Recent Vendors', 'vendor'); ?></h3>
                        <a href="<?php echo admin_url('admin.php?page=vendor-vendors'); ?>" class="button"><?php _e('View All', 'vendor'); ?></a>
                    </div>
                    <div class="vendor-widget-content">
                        <?php
                        $recent_vendors = vendor()->vendor_manager->get_vendors(array('limit' => 5));
                        if ($recent_vendors) :
                        ?>
                        <table class="vendor-table">
                            <thead>
                                <tr>
                                    <th><?php _e('Store Name', 'vendor'); ?></th>
                                    <th><?php _e('Status', 'vendor'); ?></th>
                                    <th><?php _e('Date', 'vendor'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_vendors as $vendor_item) : ?>
                                <tr>
                                    <td>
                                        <a href="<?php echo admin_url('admin.php?page=vendor-vendors&action=view&vendor_id=' . $vendor_item->id); ?>">
                                            <?php echo esc_html($vendor_item->store_name); ?>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="vendor-status vendor-status-<?php echo esc_attr($vendor_item->status); ?>">
                                            <?php echo esc_html(ucfirst($vendor_item->status)); ?>
                                        </span>
                                    </td>
                                    <td><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($vendor_item->created_at))); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <?php else : ?>
                        <p><?php _e('No vendors found.', 'vendor'); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Recent Withdrawals -->
            <div class="vendor-dashboard-col">
                <div class="vendor-widget">
                    <div class="vendor-widget-header">
                        <h3><?php _e('Recent Withdrawals', 'vendor'); ?></h3>
                        <a href="<?php echo admin_url('admin.php?page=vendor-withdrawals'); ?>" class="button"><?php _e('View All', 'vendor'); ?></a>
                    </div>
                    <div class="vendor-widget-content">
                        <?php
                        $recent_withdrawals = vendor()->withdrawal->get_all_withdrawals(array('limit' => 5));
                        if ($recent_withdrawals) :
                        ?>
                        <table class="vendor-table">
                            <thead>
                                <tr>
                                    <th><?php _e('Vendor', 'vendor'); ?></th>
                                    <th><?php _e('Amount', 'vendor'); ?></th>
                                    <th><?php _e('Status', 'vendor'); ?></th>
                                    <th><?php _e('Date', 'vendor'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_withdrawals as $withdrawal) : ?>
                                <tr>
                                    <td>
                                        <a href="<?php echo admin_url('admin.php?page=vendor-withdrawals&action=view&withdrawal_id=' . $withdrawal->id); ?>">
                                            <?php echo esc_html($withdrawal->store_name); ?>
                                        </a>
                                    </td>
                                    <td><?php echo wc_price($withdrawal->amount); ?></td>
                                    <td>
                                        <span class="vendor-status vendor-status-<?php echo esc_attr($withdrawal->status); ?>">
                                            <?php echo esc_html(ucfirst($withdrawal->status)); ?>
                                        </span>
                                    </td>
                                    <td><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($withdrawal->requested_at))); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <?php else : ?>
                        <p><?php _e('No withdrawals found.', 'vendor'); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.vendor-dashboard-stats {
    margin: 20px 0;
}

.vendor-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.vendor-stat-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.vendor-stat-icon {
    margin-right: 15px;
}

.vendor-stat-icon .dashicons {
    font-size: 40px;
    width: 40px;
    height: 40px;
    color: #0073aa;
}

.vendor-stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.vendor-stat-content p {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 14px;
}

.vendor-stat-details {
    font-size: 12px;
}

.vendor-stat-details span {
    margin-right: 10px;
}

.vendor-stat-details .pending {
    color: #f56500;
}

.vendor-stat-details .active,
.vendor-stat-details .completed,
.vendor-stat-details .approved {
    color: #46b450;
}

.vendor-dashboard-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.vendor-widget {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

.vendor-widget-header {
    background: #f9f9f9;
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.vendor-widget-header h3 {
    margin: 0;
    font-size: 16px;
}

.vendor-widget-content {
    padding: 20px;
}

.vendor-table {
    width: 100%;
    border-collapse: collapse;
}

.vendor-table th,
.vendor-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.vendor-table th {
    background: #f9f9f9;
    font-weight: bold;
}

.vendor-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.vendor-status-pending {
    background: #fff3cd;
    color: #856404;
}

.vendor-status-active,
.vendor-status-completed,
.vendor-status-approved {
    background: #d4edda;
    color: #155724;
}

.vendor-status-rejected,
.vendor-status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

@media (max-width: 768px) {
    .vendor-dashboard-row {
        grid-template-columns: 1fr;
    }
    
    .vendor-stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>
