<?php
/**
 * Vendor Commission Manager
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Commission manager class
 */
class Vendor_Commission {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Initialize
     */
    public function init() {
        // Hook into order status changes
        add_action('woocommerce_order_status_changed', array($this, 'handle_order_status_change'), 10, 3);
    }
    
    /**
     * Create commission
     */
    public function create_commission($vendor_id, $order_id, $product_id, $order_item_id, $order_item) {
        global $wpdb;
        
        $table = Vendor_Database::get_commissions_table();
        
        // Get vendor
        $vendor = vendor()->vendor_manager->get_vendor($vendor_id);
        if (!$vendor) {
            return false;
        }
        
        // Calculate amounts
        $gross_amount = $order_item->get_total();
        $commission_rate = $vendor->commission_rate;
        $commission_amount = ($gross_amount * $commission_rate) / 100;
        $admin_fee = $gross_amount - $commission_amount;
        
        // Apply filters
        $commission_amount = apply_filters('vendor_commission_amount', $commission_amount, $vendor_id, $order_id, $product_id, $gross_amount);
        $admin_fee = apply_filters('vendor_admin_fee', $admin_fee, $vendor_id, $order_id, $product_id, $gross_amount);
        
        $data = array(
            'vendor_id' => $vendor_id,
            'order_id' => $order_id,
            'product_id' => $product_id,
            'order_item_id' => $order_item_id,
            'gross_amount' => $gross_amount,
            'commission_amount' => $commission_amount,
            'commission_rate' => $commission_rate,
            'admin_fee' => $admin_fee,
            'status' => 'pending'
        );
        
        $result = $wpdb->insert($table, $data);
        
        if ($result) {
            $commission_id = $wpdb->insert_id;
            
            do_action('vendor_commission_created', $commission_id, $vendor_id, $order_id, $product_id);
            
            return $commission_id;
        }
        
        return false;
    }
    
    /**
     * Get commission
     */
    public function get_commission($commission_id) {
        global $wpdb;
        
        $table = Vendor_Database::get_commissions_table();
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table WHERE id = %d",
            $commission_id
        ));
    }
    
    /**
     * Update commission status
     */
    public function update_commission_status($commission_id, $status) {
        global $wpdb;
        
        $table = Vendor_Database::get_commissions_table();
        
        $result = $wpdb->update(
            $table,
            array('status' => $status),
            array('id' => $commission_id)
        );
        
        if ($result !== false) {
            do_action('vendor_commission_status_updated', $commission_id, $status);
            return true;
        }
        
        return false;
    }
    
    /**
     * Get vendor commissions
     */
    public function get_vendor_commissions($vendor_id, $args = array()) {
        global $wpdb;
        
        $table = Vendor_Database::get_commissions_table();
        
        $defaults = array(
            'status' => '',
            'limit' => 20,
            'offset' => 0,
            'orderby' => 'created_at',
            'order' => 'DESC',
            'date_from' => '',
            'date_to' => ''
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $where = array('vendor_id = %d');
        $values = array($vendor_id);
        
        if (!empty($args['status'])) {
            $where[] = 'status = %s';
            $values[] = $args['status'];
        }
        
        if (!empty($args['date_from'])) {
            $where[] = 'created_at >= %s';
            $values[] = $args['date_from'];
        }
        
        if (!empty($args['date_to'])) {
            $where[] = 'created_at <= %s';
            $values[] = $args['date_to'];
        }
        
        $where_clause = implode(' AND ', $where);
        
        $query = $wpdb->prepare(
            "SELECT * FROM $table WHERE $where_clause ORDER BY {$args['orderby']} {$args['order']} LIMIT %d OFFSET %d",
            array_merge($values, array($args['limit'], $args['offset']))
        );
        
        return $wpdb->get_results($query);
    }
    
    /**
     * Get vendor commission total
     */
    public function get_vendor_commission_total($vendor_id, $status = 'approved') {
        global $wpdb;
        
        $table = Vendor_Database::get_commissions_table();
        
        return $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(commission_amount) FROM $table WHERE vendor_id = %d AND status = %s",
            $vendor_id,
            $status
        ));
    }
    
    /**
     * Get vendor available balance
     */
    public function get_vendor_available_balance($vendor_id) {
        global $wpdb;
        
        $table = Vendor_Database::get_commissions_table();
        
        return $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(commission_amount) FROM $table WHERE vendor_id = %d AND status = 'approved' AND withdrawal_id IS NULL",
            $vendor_id
        ));
    }
    
    /**
     * Get vendor earnings summary
     */
    public function get_vendor_earnings_summary($vendor_id, $period = 'all') {
        global $wpdb;
        
        $table = Vendor_Database::get_commissions_table();
        
        $where = 'vendor_id = %d';
        $values = array($vendor_id);
        
        switch ($period) {
            case 'today':
                $where .= ' AND DATE(created_at) = CURDATE()';
                break;
            case 'week':
                $where .= ' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)';
                break;
            case 'month':
                $where .= ' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)';
                break;
            case 'year':
                $where .= ' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)';
                break;
        }
        
        $query = $wpdb->prepare(
            "SELECT 
                COUNT(*) as total_orders,
                SUM(gross_amount) as gross_sales,
                SUM(commission_amount) as total_commission,
                SUM(CASE WHEN status = 'approved' THEN commission_amount ELSE 0 END) as approved_commission,
                SUM(CASE WHEN status = 'pending' THEN commission_amount ELSE 0 END) as pending_commission
            FROM $table 
            WHERE $where",
            $values
        );
        
        return $wpdb->get_row($query);
    }
    
    /**
     * Handle order status change
     */
    public function handle_order_status_change($order_id, $old_status, $new_status) {
        if ($new_status === 'completed') {
            $this->approve_order_commissions($order_id);
        } elseif ($new_status === 'cancelled' || $new_status === 'refunded') {
            $this->cancel_order_commissions($order_id);
        }
    }
    
    /**
     * Approve order commissions
     */
    public function approve_order_commissions($order_id) {
        global $wpdb;
        
        $table = Vendor_Database::get_commissions_table();
        
        $wpdb->update(
            $table,
            array('status' => 'approved'),
            array('order_id' => $order_id, 'status' => 'pending')
        );
        
        do_action('vendor_order_commissions_approved', $order_id);
    }
    
    /**
     * Cancel order commissions
     */
    public function cancel_order_commissions($order_id) {
        global $wpdb;
        
        $table = Vendor_Database::get_commissions_table();
        
        $wpdb->update(
            $table,
            array('status' => 'cancelled'),
            array('order_id' => $order_id)
        );
        
        do_action('vendor_order_commissions_cancelled', $order_id);
    }
    
    /**
     * Mark commissions as withdrawn
     */
    public function mark_commissions_withdrawn($commission_ids, $withdrawal_id) {
        global $wpdb;
        
        $table = Vendor_Database::get_commissions_table();
        
        if (is_array($commission_ids)) {
            $commission_ids = implode(',', array_map('intval', $commission_ids));
        }
        
        $wpdb->query($wpdb->prepare(
            "UPDATE $table SET status = 'withdrawn', withdrawal_id = %d WHERE id IN ($commission_ids)",
            $withdrawal_id
        ));
        
        do_action('vendor_commissions_withdrawn', $commission_ids, $withdrawal_id);
    }
    
    /**
     * Get commission analytics
     */
    public function get_commission_analytics($vendor_id, $period = 'month') {
        global $wpdb;
        
        $table = Vendor_Database::get_commissions_table();
        
        $date_format = '%Y-%m-%d';
        $interval = 'DAY';
        
        switch ($period) {
            case 'week':
                $date_condition = 'created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)';
                break;
            case 'month':
                $date_condition = 'created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)';
                break;
            case 'year':
                $date_condition = 'created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)';
                $date_format = '%Y-%m';
                $interval = 'MONTH';
                break;
            default:
                $date_condition = '1=1';
        }
        
        $query = $wpdb->prepare(
            "SELECT 
                DATE_FORMAT(created_at, '$date_format') as date,
                COUNT(*) as orders_count,
                SUM(gross_amount) as gross_sales,
                SUM(commission_amount) as commission_earned
            FROM $table 
            WHERE vendor_id = %d AND $date_condition
            GROUP BY DATE_FORMAT(created_at, '$date_format')
            ORDER BY date ASC",
            $vendor_id
        );
        
        return $wpdb->get_results($query);
    }
}
