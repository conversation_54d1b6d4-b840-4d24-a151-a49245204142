<?php
/**
 * Vendor Bank Transfer Gateway
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Bank Transfer gateway class
 */
class Vendor_Gateway_Bank_Transfer {
    
    /**
     * Gateway ID
     */
    public $id = 'bank_transfer';
    
    /**
     * Gateway title
     */
    public $title = 'Bank Transfer';
    
    /**
     * Gateway description
     */
    public $description = 'Manual bank transfer payment';
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_settings();
    }
    
    /**
     * Initialize settings
     */
    private function init_settings() {
        $this->settings = get_option('vendor_bank_transfer_settings', array(
            'enabled' => 'yes',
            'minimum_amount' => 50,
            'processing_time' => '3-5 business days'
        ));
    }
    
    /**
     * Check if gateway is available
     */
    public function is_available() {
        return $this->settings['enabled'] === 'yes';
    }
    
    /**
     * Process payment (manual processing)
     */
    public function process_payment($withdrawal_id, $vendor_id, $amount, $bank_details) {
        // Bank transfer is manual, so we just mark it as pending
        // Admin will process it manually and update the status
        
        return array(
            'success' => true,
            'transaction_id' => 'bank_transfer_' . $withdrawal_id,
            'message' => 'Bank transfer request submitted. Processing time: ' . $this->settings['processing_time'],
            'requires_manual_processing' => true
        );
    }
    
    /**
     * Get payment fields for frontend
     */
    public function get_payment_fields() {
        return array(
            'bank_name' => array(
                'type' => 'text',
                'label' => __('Bank Name', 'vendor'),
                'required' => true,
                'description' => __('Enter your bank name', 'vendor')
            ),
            'account_holder_name' => array(
                'type' => 'text',
                'label' => __('Account Holder Name', 'vendor'),
                'required' => true,
                'description' => __('Enter the account holder name', 'vendor')
            ),
            'account_number' => array(
                'type' => 'text',
                'label' => __('Account Number', 'vendor'),
                'required' => true,
                'description' => __('Enter your bank account number', 'vendor')
            ),
            'routing_number' => array(
                'type' => 'text',
                'label' => __('Routing Number / IBAN', 'vendor'),
                'required' => true,
                'description' => __('Enter your routing number or IBAN', 'vendor')
            ),
            'swift_code' => array(
                'type' => 'text',
                'label' => __('SWIFT Code', 'vendor'),
                'required' => false,
                'description' => __('Enter SWIFT code for international transfers', 'vendor')
            ),
            'bank_address' => array(
                'type' => 'textarea',
                'label' => __('Bank Address', 'vendor'),
                'required' => false,
                'description' => __('Enter your bank address', 'vendor')
            )
        );
    }
    
    /**
     * Validate payment details
     */
    public function validate_payment_details($details) {
        $required_fields = array('bank_name', 'account_holder_name', 'account_number', 'routing_number');
        
        foreach ($required_fields as $field) {
            if (empty($details[$field])) {
                return new WP_Error('missing_field', sprintf(__('%s is required', 'vendor'), ucwords(str_replace('_', ' ', $field))));
            }
        }
        
        // Validate account number (basic validation)
        if (!preg_match('/^[0-9\-\s]+$/', $details['account_number'])) {
            return new WP_Error('invalid_account', __('Please enter a valid account number', 'vendor'));
        }
        
        return true;
    }
    
    /**
     * Get settings fields for admin
     */
    public function get_settings_fields() {
        return array(
            'enabled' => array(
                'title' => __('Enable Bank Transfer', 'vendor'),
                'type' => 'checkbox',
                'default' => 'yes'
            ),
            'minimum_amount' => array(
                'title' => __('Minimum Amount', 'vendor'),
                'type' => 'number',
                'default' => 50,
                'description' => __('Minimum amount for bank transfer withdrawals', 'vendor')
            ),
            'processing_time' => array(
                'title' => __('Processing Time', 'vendor'),
                'type' => 'text',
                'default' => '3-5 business days',
                'description' => __('Expected processing time for bank transfers', 'vendor')
            )
        );
    }
    
    /**
     * Update settings
     */
    public function update_settings($settings) {
        $this->settings = wp_parse_args($settings, $this->settings);
        update_option('vendor_bank_transfer_settings', $this->settings);
    }
    
    /**
     * Get minimum amount
     */
    public function get_minimum_amount() {
        return floatval($this->settings['minimum_amount']);
    }
    
    /**
     * Format bank details for display
     */
    public function format_bank_details($details) {
        $formatted = array();
        
        if (!empty($details['bank_name'])) {
            $formatted[] = __('Bank:', 'vendor') . ' ' . $details['bank_name'];
        }
        
        if (!empty($details['account_holder_name'])) {
            $formatted[] = __('Account Holder:', 'vendor') . ' ' . $details['account_holder_name'];
        }
        
        if (!empty($details['account_number'])) {
            // Mask account number for security
            $masked = substr($details['account_number'], 0, 4) . str_repeat('*', strlen($details['account_number']) - 8) . substr($details['account_number'], -4);
            $formatted[] = __('Account Number:', 'vendor') . ' ' . $masked;
        }
        
        if (!empty($details['routing_number'])) {
            $formatted[] = __('Routing/IBAN:', 'vendor') . ' ' . $details['routing_number'];
        }
        
        if (!empty($details['swift_code'])) {
            $formatted[] = __('SWIFT Code:', 'vendor') . ' ' . $details['swift_code'];
        }
        
        return implode('<br>', $formatted);
    }
}
