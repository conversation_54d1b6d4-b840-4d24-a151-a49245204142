!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e[f]=d(a[f]);b.apply(null,e)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};g("1",[],function(){var a=function(b){var c=b,d=function(){return c},e=function(a){c=a},f=function(){return a(d())};return{get:d,set:e,clone:f}};return a}),h("5",tinymce.util.Tools.resolve),g("2",["5"],function(a){return a("tinymce.PluginManager")}),g("6",[],function(){var a=function(a){return a.getParam("fullpage_hide_in_source_view")},b=function(a){return a.getParam("fullpage_default_xml_pi")},c=function(a){return a.getParam("fullpage_default_encoding")},d=function(a){return a.getParam("fullpage_default_font_family")},e=function(a){return a.getParam("fullpage_default_font_size")},f=function(a){return a.getParam("fullpage_default_text_color")},g=function(a){return a.getParam("fullpage_default_title")},h=function(a){return a.getParam("fullpage_default_doctype","<!DOCTYPE html>")};return{shouldHideInSourceView:a,getDefaultXmlPi:b,getDefaultEncoding:c,getDefaultFontFamily:d,getDefaultFontSize:e,getDefaultTextColor:f,getDefaultTitle:g,getDefaultDocType:h}}),g("8",["5"],function(a){return a("tinymce.util.Tools")}),g("9",[],function(){var a=function(a){return a.getParam("insertdatetime_dateformat",a.translate("%Y-%m-%d"))},b=function(a){return a.getParam("insertdatetime_timeformat",a.translate("%H:%M:%S"))},c=function(a){return a.getParam("insertdatetime_formats",["%H:%M:%S","%Y-%m-%d","%I:%M:%S %p","%D"])},d=function(a){var d=c(a);return d.length>0?d[0]:b(a)},e=function(a){return a.getParam("insertdatetime_element",!1)};return{getDateFormat:a,getTimeFormat:b,getFormats:c,getDefaultDateTime:d,shouldInsertTimeElement:e}}),g("7",["8","9"],function(a,b){var c="Sun Mon Tue Wed Thu Fri Sat Sun".split(" "),d="Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday".split(" "),e="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),f="January February March April May June July August September October November December".split(" "),g=function(a,b){if(a=""+a,a.length<b)for(var c=0;c<b-a.length;c++)a="0"+a;return a},h=function(a,b,h){return h=h||new Date,b=b.replace("%D","%m/%d/%Y"),b=b.replace("%r","%I:%M:%S %p"),b=b.replace("%Y",""+h.getFullYear()),b=b.replace("%y",""+h.getYear()),b=b.replace("%m",g(h.getMonth()+1,2)),b=b.replace("%d",g(h.getDate(),2)),b=b.replace("%H",""+g(h.getHours(),2)),b=b.replace("%M",""+g(h.getMinutes(),2)),b=b.replace("%S",""+g(h.getSeconds(),2)),b=b.replace("%I",""+((h.getHours()+11)%12+1)),b=b.replace("%p",""+(h.getHours()<12?"AM":"PM")),b=b.replace("%B",""+a.translate(f[h.getMonth()])),b=b.replace("%b",""+a.translate(e[h.getMonth()])),b=b.replace("%A",""+a.translate(d[h.getDay()])),b=b.replace("%a",""+a.translate(c[h.getDay()])),b=b.replace("%%","%")},i=function(a,b,c,d){var e=a.dom.create("time",{datetime:c},d);b.parentNode.insertBefore(e,b),a.dom.remove(b),a.selection.select(e,!0),a.selection.collapse(!1)},j=function(a,c){if(b.shouldInsertTimeElement(a)){var d,e=h(a,c);d=/%[HMSIp]/.test(c)?h(a,"%Y-%m-%dT%H:%M"):h(a,"%Y-%m-%d");var f=a.dom.getParent(a.selection.getStart(),"time");f?i(a,f,d,e):a.insertContent('<time datetime="'+d+'">'+e+"</time>")}else a.insertContent(h(a,c))};return{insertDateTime:j,getDateTime:h}}),g("3",["6","7"],function(a,b){var c=function(c){c.addCommand("mceInsertDate",function(){b.insertDateTime(c,a.getDateFormat())}),c.addCommand("mceInsertTime",function(){b.insertDateTime(c,a.getTimeFormat())})};return{register:c}}),g("4",["8","9","7"],function(a,b,c){var d=function(d,e){var f=b.getFormats(d);return a.map(f,function(a){return{text:c.getDateTime(d,a),onclick:function(){e.set(a),c.insertDateTime(d,a)}}})},e=function(a,e){var f=d(a,e);a.addButton("insertdatetime",{type:"splitbutton",title:"Insert date/time",menu:f,onclick:function(){var d=e.get();c.insertDateTime(a,d?d:b.getDefaultDateTime(a))}}),a.addMenuItem("insertdatetime",{icon:"date",text:"Date/time",menu:f,context:"insert"})};return{register:e}}),g("0",["1","2","3","4"],function(a,b,c,d){return b.add("insertdatetime",function(b){var e=a(null);c.register(b),d.register(b,e)}),function(){}}),d("0")()}();