msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 15:19+0000\n"
"PO-Revision-Date: 2018-09-29 14:58+0800\n"
"Language-Team: 簡体中文\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.1.1\n"
"Last-Translator: MOHO <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: core/class-wcfm-admin.php:100
msgid ""
"<h2>\n"
"\t\t\t\t\t\t\t\t\t\t           Is there anything missing in your front-end "
"dashboard !!!\n"
"\t\t\t\t\t\t\t\t               </h2>"
msgstr ""

#: core/class-wcfm-admin.php:106
msgid ""
"<p>WooCommerce Frontend Manage - Ultimate is there to fill up all those for "
"you. Store Invoice, Support Ticket, Shipment Tracking, Direct Messaging, "
"Followers, Badges, Verificaton, Product Importer, Bulk Edit and many more, "
"almost a never ending features list for you.</p>"
msgstr ""

#: core/class-wcfm-admin.php:112
msgid "WCFM U >>"
msgstr ""

#: core/class-wcfm-admin.php:148
msgid ""
"<h2>\n"
"\t\t\t\t\t\t\t\t\t\t           Now setup your vendor membership subscription "
"in minutes & it's FREE !!!\n"
"\t\t\t\t\t\t\t\t               </h2>"
msgstr ""

#: core/class-wcfm-admin.php:151
msgid ""
"<p>A simple membership plugin for offering FREE AND PREMIUM SUBSCRIPTION for "
"your multi-vendor marketplace. You may set up unlimited membership levels "
"(example: free, silver, gold etc) with different pricing plan, capabilities "
"and commission. Also you will have Pay for Product option.</p>"
msgstr ""

#: core/class-wcfm-admin.php:157
#: controllers/customers/wcfm-controller-customers-details.php:163
#: controllers/customers/wcfm-controller-customers-details.php:360
#: controllers/orders/wcfm-controller-dokan-orders.php:253
#: controllers/orders/wcfm-controller-orders.php:219
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:326
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:310
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:293
#: controllers/orders/wcfm-controller-wcvendors-orders.php:342
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:182
#: views/capability/wcfm-view-capability.php:387
msgid "View Details"
msgstr "浏览明细"

#: core/class-wcfm-admin.php:193
msgid ""
"<h2>\n"
"\t\t\t\t\t\t\t\t\t\t           Do you want to have different capabilities "
"for each membership levels !!!\n"
"\t\t\t\t\t\t\t\t               </h2>"
msgstr ""

#: core/class-wcfm-admin.php:199
msgid ""
"<p>WCFM - Groups & Staffs will empower you to set up totaly different "
"capability rules for your each membership levels very easily.</p>"
msgstr ""

#: core/class-wcfm-admin.php:205
msgid "WCFM GS >>"
msgstr ""

#: core/class-wcfm-admin.php:225 core/class-wcfm-admin.php:265
#: core/class-wcfm-admin.php:266 core/class-wcfm-admin.php:267
msgid "WCFM View"
msgstr ""

#: core/class-wcfm-admin.php:284 core/class-wcfm-admin.php:294
#: core/class-wcfm-admin.php:296 core/class-wcfm-admin.php:298
#: core/class-wcfm-wcvendors.php:196
msgid "WCFM Home"
msgstr ""

#: core/class-wcfm-admin.php:343
#, php-format
msgid ""
"WCFM totally works from front-end ... check dashboard settings %shere >>%s"
msgstr ""

#: core/class-wcfm-admin.php:346
msgid "This page should contain \"[wc_frontend_manager]\" short code"
msgstr ""

#: core/class-wcfm-admin.php:382 views/settings/wcfm-view-settings.php:277
msgid ""
"DO NOT USE WCFM DASHBOARD PAGE FOR OTHER PAGE SETTINGS, you will break your "
"site if you do."
msgstr ""

#: core/class-wcfm-ajax.php:269
#, php-format
msgid "A new %s <b>%s</b> added to the store by <b>%s</b>"
msgstr "有一个新的 %s <b>%s</b> 加入到商店，透过 <b>%s</b>"

#: core/class-wcfm-ajax.php:426 core/class-wcfm-ajax.php:459
#, php-format
msgid "<b>%s</b> order status updated to <b>%s</b> by <b>%s</b>"
msgstr "<b>%s</b> 订单状态更新为 <b>%s</b>，透过 <b>%s</b>"

#: core/class-wcfm-ajax.php:468
msgid "Order status updated."
msgstr "更新订单状态。"

#: core/class-wcfm-ajax.php:640
msgid "Email Verification Code"
msgstr ""

#: core/class-wcfm-ajax.php:641 core/class-wcfm-notification.php:342
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:99
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:180
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:135
msgid "Hi"
msgstr "您好"

#: core/class-wcfm-ajax.php:643
#, php-format
msgid "Here is your email verification code - <b>%s</b>"
msgstr ""

#: core/class-wcfm-ajax.php:644 core/class-wcfm-notification.php:349
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:106
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:187
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:143
msgid "Thank You"
msgstr "感谢您"

#: core/class-wcfm-ajax.php:654
msgid "Email verification code send to your email."
msgstr ""

#: core/class-wcfm-ajax.php:656
msgid "Email verification not working right now, please try after sometime."
msgstr ""

#: core/class-wcfm-ajax.php:680
#, php-format
msgid "<b>%s</b> (Store: <b>%s</b>) has been disabled."
msgstr ""

#: core/class-wcfm-ajax.php:684
#, php-format
msgid "Your Store: <b>%s</b> has been disabled."
msgstr ""

#: core/class-wcfm-ajax.php:687
msgid "Vendor successfully disabled."
msgstr ""

#: core/class-wcfm-ajax.php:690
msgid "Vendor can not be disabled right now, please try after sometime."
msgstr ""

#: core/class-wcfm-ajax.php:721
#, php-format
msgid "<b>%s</b> (Store: <b>%s</b>) has been enabled."
msgstr ""

#: core/class-wcfm-ajax.php:725
#, php-format
msgid "Your Store: <b>%s</b> has been enabled."
msgstr ""

#: core/class-wcfm-ajax.php:729
msgid "Vendor successfully enabled."
msgstr ""

#: core/class-wcfm-ajax.php:732
msgid "Vendor can not be enabled right now, please try after sometime."
msgstr ""

#: core/class-wcfm-article.php:71
msgid "Articles Dashboard"
msgstr "文章管理后台"

#: core/class-wcfm-article.php:74
msgid "Articles Manager"
msgstr "文章管理"

#: core/class-wcfm-article.php:118 views/articles/wcfm-view-articles.php:32
#: views/capability/wcfm-view-capability.php:302
msgid "Articles"
msgstr "文章"

#: core/class-wcfm-capability.php:292
msgid "Products Limit: "
msgstr "商品限制："

#: core/class-wcfm-capability.php:300 core/class-wcfm-capability.php:305
#: core/class-wcfm-capability.php:308 core/class-wcfm-capability.php:519
#: core/class-wcfm-capability.php:524 core/class-wcfm-capability.php:528
#: core/class-wcfm-capability.php:844 core/class-wcfm-capability.php:849
#: core/class-wcfm-capability.php:865
msgid "remaining"
msgstr "可用数量"

#: core/class-wcfm-capability.php:311 core/class-wcfm-capability.php:531
#: core/class-wcfm-capability.php:868
#: core/class-wcfm-thirdparty-support.php:380
msgid "Unlimited"
msgstr "无限制"

#: core/class-wcfm-capability.php:515
msgid "Articles Limit: "
msgstr "文章限制："

#: core/class-wcfm-capability.php:840
msgid "Customers Limit: "
msgstr "顾客限制："

#: core/class-wcfm-catalog.php:42 core/class-wcfm.php:736
#: views/capability/wcfm-view-capability.php:209
msgid "Catalog"
msgstr "目录"

#: core/class-wcfm-catalog.php:64
msgid "Catalog Mode"
msgstr "目录模式"

#: core/class-wcfm-catalog.php:69
msgid "Disable Add to Cart?"
msgstr "停用加入购物车？"

#: core/class-wcfm-catalog.php:70
msgid "Hide Price?"
msgstr "隐藏售价？"

#: core/class-wcfm-customer.php:85
msgid "Customers Dashboard"
msgstr "顾客管理后台"

#: core/class-wcfm-customer.php:88
msgid "Customers Manager"
msgstr "顾客管理"

#: core/class-wcfm-customer.php:91
msgid "Customers Details"
msgstr "顾客细节"

#: core/class-wcfm-customer.php:136
#: views/capability/wcfm-view-capability.php:409
#: views/customers/wcfm-view-customers.php:25
msgid "Customers"
msgstr "顾客"

#: core/class-wcfm-customer.php:331
msgid "New Customer"
msgstr "新顾客"

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:145
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:385
#: views/products-manager/wcfm-view-products-manage.php:390
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/products-popup/wcfm-view-product-popup.php:97
#: views/settings/wcfm-view-settings.php:498
msgid "Simple Product"
msgstr "一般商品"

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:145
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:385
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/settings/wcfm-view-settings.php:498
msgid "Variable Product"
msgstr "规格商品"

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:145
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:385
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/settings/wcfm-view-settings.php:498
msgid "Grouped Product"
msgstr "群组商品"

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:145
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:385
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/settings/wcfm-view-settings.php:498
msgid "External/Affiliate Product"
msgstr "外部商品"

#: core/class-wcfm-customfield-support.php:32
msgid "Do not show"
msgstr "不显示"

#: core/class-wcfm-customfield-support.php:38
#: core/class-wcfm-customfield-support.php:42
msgid "Product Custom Field"
msgstr "商品自订栏位"

#: core/class-wcfm-customfield-support.php:47
msgid "Custom Fields"
msgstr "自订栏位"

#: core/class-wcfm-customfield-support.php:47
msgid ""
"You can integrate any Third Party plugin using Custom Fields, but you should "
"use the same fields name as used by Third Party plugins."
msgstr ""

#: core/class-wcfm-customfield-support.php:48
#: views/products-manager/wcfm-view-products-manage-tabs.php:237
#: views/settings/wcfm-view-settings.php:414
#: views/settings/wcfm-view-settings.php:474
msgid "Enable"
msgstr "启用"

#: core/class-wcfm-customfield-support.php:49
msgid "Block Name"
msgstr ""

#: core/class-wcfm-customfield-support.php:50
msgid "Exlude Product Types"
msgstr ""

#: core/class-wcfm-customfield-support.php:50
msgid "Choose product types for which you want to disable this field block."
msgstr ""

#: core/class-wcfm-customfield-support.php:51
msgid "Visibility"
msgstr ""

#: core/class-wcfm-customfield-support.php:51
msgid ""
"Set where and how you want to visible this custom field block in single "
"product page."
msgstr ""

#: core/class-wcfm-customfield-support.php:52
msgid "Fields as Group?"
msgstr ""

#: core/class-wcfm-customfield-support.php:53
msgid "Group name"
msgstr ""

#: core/class-wcfm-customfield-support.php:54
msgid "Fields"
msgstr "栏位"

#: core/class-wcfm-customfield-support.php:55
#: views/settings/wcfm-view-settings.php:475
msgid "Field Type"
msgstr "栏位类型"

#: core/class-wcfm-customfield-support.php:56
#: views/settings/wcfm-view-settings.php:415
#: views/settings/wcfm-view-settings.php:476
msgid "Label"
msgstr "标籤"

#: core/class-wcfm-customfield-support.php:57 core/class-wcfm-frontend.php:734
#: views/articles/wcfm-view-articles.php:110
#: views/articles/wcfm-view-articles.php:121
#: views/customers/wcfm-view-customers.php:59
#: views/customers/wcfm-view-customers.php:74
#: views/enquiry/wcfm-view-enquiry-form.php:55
#: views/products/wcfm-view-products.php:198
#: views/products/wcfm-view-products.php:221
#: views/products-manager/wcfm-view-products-manage-tabs.php:139
msgid "Name"
msgstr "名称"

#: core/class-wcfm-customfield-support.php:57
msgid ""
"This is will going to use as `meta_key` for storing this field value in "
"database."
msgstr ""

#: core/class-wcfm-customfield-support.php:58
#: views/settings/wcfm-view-settings.php:477
msgid "Options"
msgstr "选项"

#: core/class-wcfm-customfield-support.php:58
msgid ""
"Insert option values | separated, leave first element empty to show as '-"
"Select-'"
msgstr ""

#: core/class-wcfm-customfield-support.php:59
#: views/settings/wcfm-view-settings.php:478
msgid "Help Content"
msgstr ""

#: core/class-wcfm-customfield-support.php:60
#: views/settings/wcfm-view-settings.php:479
msgid "Required?"
msgstr "必填？"

#: core/class-wcfm-dokan.php:124 core/class-wcfm-wcfmmarketplace.php:118
#: core/class-wcfm-wcmarketplace.php:136 core/class-wcfm-wcmarketplace.php:137
#: core/class-wcfm-wcpvendors.php:114 core/class-wcfm-wcvendors.php:151
#: views/wcfm-view-header-panels.php:41 views/wcfm-view-menu.php:67
#: views/settings/wcfm-view-settings.php:36
msgid "My Store"
msgstr "我的商店"

#: core/class-wcfm-dokan.php:128 core/class-wcfm-vendor-support.php:1044
#: core/class-wcfm-vendor-support.php:1052
#: core/class-wcfm-vendor-support.php:1060
#: core/class-wcfm-vendor-support.php:1071
#: core/class-wcfm-vendor-support.php:1081
#: core/class-wcfm-wcfmmarketplace.php:122
#: core/class-wcfm-wcmarketplace.php:140 core/class-wcfm-wcpvendors.php:118
#: core/class-wcfm-wcvendors.php:155
msgid "Shop"
msgstr "商店"

#: core/class-wcfm-dokan.php:449 core/class-wcfm-wcfmmarketplace.php:637
#: core/class-wcfm-wcmarketplace.php:683 core/class-wcfm-wcpvendors.php:532
#: core/class-wcfm-wcvendors.php:859
msgid "Total Earning"
msgstr "总货款"

#: core/class-wcfm-enquiry.php:115
msgid "Enquiry Dashboard"
msgstr "问题管理后台"

#: core/class-wcfm-enquiry.php:118
msgid "Enquiry Manager"
msgstr "问题管理"

#: core/class-wcfm-enquiry.php:283 core/class-wcfm-enquiry.php:297
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:68
msgid "Inquiries"
msgstr ""

#: core/class-wcfm-enquiry.php:305 core/class-wcfm-enquiry.php:396
#: helpers/class-wcfm-install.php:317
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:179
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:186
#: views/enquiry/wcfm-view-enquiry-form.php:41
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:65
msgid "Inquiry"
msgstr "你问我答"

#: core/class-wcfm-enquiry.php:345 core/class-wcfm-enquiry.php:429
#: helpers/wcfm-core-functions.php:1109
#: views/enquiry/wcfm-view-enquiry-manage.php:75
#: views/enquiry/wcfm-view-enquiry-tab.php:52
#: views/enquiry/wcfm-view-enquiry.php:43
msgid "Enquiries"
msgstr "你问我答"

#: core/class-wcfm-enquiry.php:367
#: controllers/settings/wcfm-controller-settings.php:185
#: includes/shortcodes/class-wcfm-shortcode-enquiry.php:33
#: views/enquiry/wcfm-view-enquiry-tab.php:21
#: views/settings/wcfm-view-settings.php:52
msgid "Ask a Question"
msgstr "你问我答"

#: core/class-wcfm-enquiry.php:441 core/class-wcfm-notification.php:209
msgid "Show All"
msgstr "显示全部"

#: core/class-wcfm-enquiry.php:444
msgid "There is no enquiry yet!!"
msgstr "目前没有问题！"

#: core/class-wcfm-enquiry.php:473
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:158
msgid "New Enquiry"
msgstr "新问题"

#: core/class-wcfm-frontend.php:271 views/dashboard/wcfm-view-dashboard.php:97
#: views/dashboard/wcfm-view-dokan-dashboard.php:123
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:126
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:144
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:148
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:130
#: views/settings/wcfm-view-settings.php:138
#: views/settings/wcfm-view-settings.php:277
msgid "Dashboard"
msgstr "管理后台"

#: core/class-wcfm-frontend.php:306
#: controllers/articles/wcfm-controller-articles.php:161
#: controllers/articles/wcfm-controller-articles.php:164
#: controllers/coupons/wcfm-controller-coupons.php:98
#: controllers/coupons/wcfm-controller-coupons.php:100
#: controllers/enquiry/wcfm-controller-enquiry.php:163
#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:105
#: controllers/listings/wcfm-controller-listings.php:130
#: controllers/notice/wcfm-controller-notices.php:80
#: controllers/products/wcfm-controller-products.php:355
#: controllers/products/wcfm-controller-products.php:358
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:107
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:110
#: views/notice/wcfm-view-notice-view.php:59
msgid "Edit"
msgstr "编辑"

#: core/class-wcfm-frontend.php:310
#: controllers/articles/wcfm-controller-articles.php:162
#: controllers/articles/wcfm-controller-articles.php:165
#: controllers/enquiry/wcfm-controller-enquiry.php:164
#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:106
#: controllers/listings/wcfm-controller-listings.php:156
#: controllers/messages/wcfm-controller-messages.php:223
#: controllers/notice/wcfm-controller-notices.php:81
#: controllers/products/wcfm-controller-products.php:356
#: controllers/products/wcfm-controller-products.php:359
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:108
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:111
msgid "Delete"
msgstr "删除"

#: core/class-wcfm-frontend.php:733
#: views/products-manager/wcfm-view-products-manage-tabs.php:138
msgid "Active?"
msgstr "启用？"

#: core/class-wcfm-frontend.php:735
#: views/products-manager/wcfm-view-products-manage-tabs.php:140
msgid "Value(s):"
msgstr "选项值："

#: core/class-wcfm-frontend.php:736
#: views/products-manager/wcfm-view-products-manage-tabs.php:141
msgid "Visible on the product page"
msgstr "显示于商品页"

#: core/class-wcfm-frontend.php:737
#: views/products-manager/wcfm-view-products-manage-tabs.php:142
msgid "Use as Variation"
msgstr "加入规格组合"

#: core/class-wcfm-library.php:369
msgid "Select an option&hellip;"
msgstr ""

#: core/class-wcfm-library.php:458
msgid "Memebership"
msgstr "会员资格"

#: core/class-wcfm-library.php:874
msgid "Processing..."
msgstr "处理中..."

#: core/class-wcfm-library.php:874
msgid "Search:"
msgstr "搜寻："

#: core/class-wcfm-library.php:874
msgid "Show _MENU_ entries"
msgstr "显示_MENU_ 个项目"

#: core/class-wcfm-library.php:874
msgid "Showing _START_ to _END_ of _TOTAL_ entries"
msgstr "显示 _START_ 到 _END_ 共 _TOTAL_ 个项目"

#: core/class-wcfm-library.php:874
msgid "Showing 0 to 0 of 0 entries"
msgstr "显示 0 到 0 共 0 个项目"

#: core/class-wcfm-library.php:874
msgid "(filtered _MAX_ entries of total)"
msgstr "(全部筛选共 _MAX_ 个项目)"

#: core/class-wcfm-library.php:874
msgid "Loading..."
msgstr "读取中..."

#: core/class-wcfm-library.php:874
msgid "No matching records found"
msgstr "没有找到符合的纪录"

#: core/class-wcfm-library.php:874
msgid "No data in the table"
msgstr "目前没有资料"

#: core/class-wcfm-library.php:874
msgid "First"
msgstr "首先"

#: core/class-wcfm-library.php:874 helpers/wcfm-core-functions.php:969
msgid "Previous"
msgstr "上一页"

#: core/class-wcfm-library.php:874 helpers/wcfm-core-functions.php:968
msgid "Next"
msgstr "下一页"

#: core/class-wcfm-library.php:874
msgid "Last"
msgstr "最近"

#: core/class-wcfm-library.php:874
msgid "Print"
msgstr "列印"

#: core/class-wcfm-library.php:874
msgid "PDF"
msgstr "PDF"

#: core/class-wcfm-library.php:874
msgid "Excel"
msgstr "Excel"

#: core/class-wcfm-library.php:874
msgid "CSV"
msgstr "CSV"

#: core/class-wcfm-library.php:978
msgid "Choose Media"
msgstr "选择媒体"

#: core/class-wcfm-library.php:978
msgid "Choose Image"
msgstr "选择图片"

#: core/class-wcfm-library.php:978
msgid "Add to Gallery"
msgstr "新增到相簿"

#: core/class-wcfm-non-ajax.php:53
msgid "Online"
msgstr "发佈中"

#: core/class-wcfm-non-ajax.php:53
#: controllers/listings/wcfm-controller-listings.php:25
#: controllers/products/wcfm-controller-products.php:27
#: views/articles/wcfm-view-articles.php:13
#: views/listings/wcfm-view-listings.php:38
#: views/products/wcfm-view-products.php:13
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:44
msgid "Pending"
msgstr "审核中"

#: core/class-wcfm-non-ajax.php:53
#: controllers/products/wcfm-controller-products.php:26
#: views/articles/wcfm-view-articles-manage.php:374
#: views/articles/wcfm-view-articles.php:12
#: views/coupons/wcfm-view-coupons-manage.php:126
#: views/products/wcfm-view-products.php:12
#: views/products-manager/wcfm-view-products-manage.php:781
#: views/products-popup/wcfm-view-product-popup.php:288
msgid "Draft"
msgstr "草稿"

#: core/class-wcfm-non-ajax.php:138
msgid "No sales yet ..!!!"
msgstr "尚未销售...！"

#: core/class-wcfm-non-ajax.php:188
msgid "View WCFM settings"
msgstr ""

#: core/class-wcfm-non-ajax.php:188 core/class-wcfm-query.php:169
#: core/class-wcfm.php:642 helpers/class-wcfm-install.php:321
#: views/capability/wcfm-view-capability.php:147
#: views/settings/wcfm-view-dokan-settings.php:129
#: views/settings/wcfm-view-settings.php:83
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:166
#: views/settings/wcfm-view-wcmarketplace-settings.php:136
#: views/settings/wcfm-view-wcpvendors-settings.php:73
#: views/settings/wcfm-view-wcvendors-settings.php:120
msgid "Settings"
msgstr "设定"

#: core/class-wcfm-non-ajax.php:195 core/class-wcfm-non-ajax.php:222
msgid "Add more power to your WCFM"
msgstr ""

#: core/class-wcfm-non-ajax.php:195 core/class-wcfm-non-ajax.php:222
msgid "WCFM Ultimate"
msgstr ""

#: core/class-wcfm-non-ajax.php:213
msgid "View WCFM documentation"
msgstr ""

#: core/class-wcfm-non-ajax.php:213 views/settings/wcfm-view-settings.php:97
msgid "Documentation"
msgstr ""

#: core/class-wcfm-non-ajax.php:215
msgid "View WCFM Video Tutorial"
msgstr ""

#: core/class-wcfm-non-ajax.php:215 views/settings/wcfm-view-settings.php:96
msgid "Video Tutorial"
msgstr ""

#: core/class-wcfm-non-ajax.php:217
msgid "Any WC help feel free to contact us"
msgstr ""

#: core/class-wcfm-non-ajax.php:217
msgid "Customization Help"
msgstr ""

#: core/class-wcfm-non-ajax.php:277 core/class-wcfm-thirdparty-support.php:311
#: helpers/wcfm-core-functions.php:945 views/wcfm-view-menu.php:127
#: views/articles/wcfm-view-articles-manage.php:141
#: views/articles/wcfm-view-articles.php:65
#: views/coupons/wcfm-view-coupons-manage.php:77
#: views/coupons/wcfm-view-coupons.php:43
#: views/customers/wcfm-view-customers-details.php:107
#: views/customers/wcfm-view-customers-manage.php:113
#: views/customers/wcfm-view-customers.php:42
#: views/knowledgebase/wcfm-view-knowledgebase.php:38
#: views/listings/wcfm-view-listings.php:91
#: views/notice/wcfm-view-notices.php:37
#: views/products/wcfm-view-products-export.php:64
#: views/products/wcfm-view-products.php:115
#: views/products-manager/wcfm-view-products-manage.php:441
#: views/vendors/wcfm-view-vendors-manage.php:189
#: views/vendors/wcfm-view-vendors-new.php:75
#: views/vendors/wcfm-view-vendors.php:35
msgid "Add New"
msgstr "新增"

#: core/class-wcfm-notification.php:59
#, php-format
msgid "A new product <b>%s</b> added by <b>%s</b>"
msgstr "商品 <b>%s</b> 由卖家 <b>%s</b> 上架"

#: core/class-wcfm-notification.php:82
#, php-format
msgid "Product <b>%s</b> has been approved."
msgstr "商品 <b>%s</b> 已开通上架。"

#: core/class-wcfm-notification.php:102
#, php-format
msgid "Product <b>%s</b> awaiting for review"
msgstr "商品 <b>%s</b> 等待审核"

#: core/class-wcfm-notification.php:126
#, php-format
msgid "You have received an Order <b>#%s</b>"
msgstr "你有新的订单<b>#%s</b>"

#: core/class-wcfm-notification.php:142
#, php-format
msgid "You have received an Order <b>#%s</b> for <b>%s</b>"
msgstr "你有新的订单 <b>#%s</b> | <b>%s</b>"

#: core/class-wcfm-notification.php:189 core/class-wcfm.php:739
#: views/messages/wcfm-view-messages.php:93
msgid "Notifications"
msgstr "讯息通知"

#: core/class-wcfm-notification.php:212
msgid "There is no notification yet!!"
msgstr "目前没有新通知！"

#: core/class-wcfm-notification.php:340 core/class-wcfm-notification.php:355
msgid "Notification"
msgstr "通知"

#: core/class-wcfm-notification.php:344
msgid "You have received a new notification:"
msgstr "您收到了一个新通知："

#: core/class-wcfm-notification.php:348
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:105
#, php-format
msgid "Check more details %shere%s."
msgstr "%s点击这裡了解更多→%s"

#: core/class-wcfm-policy.php:63 core/class-wcfm-policy.php:176
msgid "Store Policies"
msgstr "商店条款"

#: core/class-wcfm-policy.php:67 core/class-wcfm-policy.php:180
msgid "Policies Setting"
msgstr "条款设定"

#: core/class-wcfm-policy.php:79 core/class-wcfm-policy.php:191
#: core/class-wcfm-policy.php:335
msgid "Policy Tab Label"
msgstr "条款分页名称"

#: core/class-wcfm-policy.php:80 core/class-wcfm-policy.php:192
#: core/class-wcfm-policy.php:336 core/class-wcfm-policy.php:587
#: views/settings/wcfm-view-wcvendors-settings.php:398
msgid "Shipping Policy"
msgstr "配送规则"

#: core/class-wcfm-policy.php:81 core/class-wcfm-policy.php:193
#: core/class-wcfm-policy.php:337 core/class-wcfm-policy.php:592
#: views/settings/wcfm-view-wcvendors-settings.php:399
msgid "Refund Policy"
msgstr "退费规则"

#: core/class-wcfm-policy.php:82 core/class-wcfm-policy.php:194
#: core/class-wcfm-policy.php:338
msgid "Cancellation/Return/Exchange Policy"
msgstr "取消 / 转让 / 换货规则"

#: core/class-wcfm-policy.php:323
msgid "Product Policies"
msgstr "商品条款"

#: core/class-wcfm-policy.php:420
msgid "Store Polices"
msgstr "商店条款"

#: core/class-wcfm-policy.php:597
msgid "Cancellation / Return / Exchange Policy"
msgstr "取消 / 转让 / 换货规则"

#: core/class-wcfm-query.php:116
msgid "Products Dashboard"
msgstr "商品管理后台"

#: core/class-wcfm-query.php:121
#, php-format
msgid "Product Manager -%s"
msgstr "商品管理 -%s"

#: core/class-wcfm-query.php:121
msgid "Product Manager"
msgstr "商品管理"

#: core/class-wcfm-query.php:124
msgid "Products Stock Manager"
msgstr "商品库存管理"

#: core/class-wcfm-query.php:127
#: views/products/wcfm-view-products-export.php:58
#: views/products/wcfm-view-products.php:90
#: views/products/wcfm-view-products.php:95
msgid "Products Import"
msgstr "商品导入"

#: core/class-wcfm-query.php:130
#: views/products/wcfm-view-products-export.php:33
#: views/products/wcfm-view-products.php:82
msgid "Products Export"
msgstr "商品导出"

#: core/class-wcfm-query.php:133
msgid "Coupons Dashboard"
msgstr "优惠券管理后台"

#: core/class-wcfm-query.php:138
#, php-format
msgid "Coupon Manager -%s"
msgstr "优惠券管理 - %s"

#: core/class-wcfm-query.php:138
msgid "Coupon Manager"
msgstr "优惠券管理"

#: core/class-wcfm-query.php:141
msgid "Orders Dashboard"
msgstr "订单管理后台"

#: core/class-wcfm-query.php:145
#, php-format
msgid "Order Details #%s"
msgstr "订单明细 #%s"

#: core/class-wcfm-query.php:145 views/orders/wcfm-view-orders-details.php:115
msgid "Order Details"
msgstr "订单明细"

#: core/class-wcfm-query.php:148
msgid "Reports - Sales by Date"
msgstr "每日销售报表"

#: core/class-wcfm-query.php:151
msgid "Reports - Sales by Product"
msgstr "商品销售报表"

#: core/class-wcfm-query.php:154
msgid "Reports - Coupons by Date"
msgstr "每日优惠券报表"

#: core/class-wcfm-query.php:157
msgid "Reports - Out of Stock"
msgstr "已售完报表"

#: core/class-wcfm-query.php:160
msgid "Reports - Low in Stock"
msgstr "低库存报表"

#: core/class-wcfm-query.php:163 helpers/class-wcfm-install.php:334
#: views/settings/wcfm-view-settings.php:204
#: views/settings/wcfm-view-settings.php:210
msgid "Analytics"
msgstr "分析"

#: core/class-wcfm-query.php:166 core/class-wcfm.php:741
#: helpers/class-wcfm-install.php:330 views/wcfm-view-header-panels.php:57
#: views/profile/wcfm-view-profile.php:162
#: views/settings/wcfm-view-wcpvendors-settings.php:112
msgid "Profile"
msgstr "个人档案"

#: core/class-wcfm-query.php:172 core/class-wcfm.php:740
#: views/wcfm-view-header-panels.php:73
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:66
#: views/knowledgebase/wcfm-view-knowledgebase.php:26
msgid "Knowledgebase"
msgstr "知识库"

#: core/class-wcfm-query.php:175
msgid "Knowledgebase Manager"
msgstr "知识库讯息"

#: core/class-wcfm-query.php:178
msgid "Notice Dashboard"
msgstr "通知管理后台"

#: core/class-wcfm-query.php:181
msgid "Notice Manager"
msgstr "通知讯息"

#: core/class-wcfm-query.php:184 core/class-wcfm.php:742
msgid "Notice"
msgstr "通知"

#: core/class-wcfm-query.php:187
msgid "Message Dashboard"
msgstr "讯息管理后台"

#: core/class-wcfm-thirdparty-support.php:157
msgid "Listings Dashboard"
msgstr "项目管理后台"

#: core/class-wcfm-thirdparty-support.php:212
#: core/class-wcfm-thirdparty-support.php:313
#: helpers/class-wcfm-setup-bak.php:950 helpers/class-wcfm-setup.php:970
#: views/listings/wcfm-view-listings.php:27
msgid "Listings"
msgstr "项目"

#: core/class-wcfm-thirdparty-support.php:241
msgid "Rental Product"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:248
#: core/class-wcfm-thirdparty-support.php:276
#: core/class-wcfm-thirdparty-support.php:462
#: controllers/products/wcfm-controller-products.php:272
msgid "Auction"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:264
msgid "Listing Package"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:270
#: core/class-wcfm-thirdparty-support.php:412
#: controllers/products/wcfm-controller-products.php:274
msgid "Rental"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:302
msgid "Edit Listing"
msgstr "编辑项目"

#: core/class-wcfm-thirdparty-support.php:304
msgid "Add Listing"
msgstr "新增项目"

#: core/class-wcfm-thirdparty-support.php:307
#: views/articles/wcfm-view-articles-manage.php:136
#: views/articles/wcfm-view-articles.php:60
#: views/coupons/wcfm-view-coupons-manage.php:72
#: views/coupons/wcfm-view-coupons.php:39
#: views/customers/wcfm-view-customers-details.php:96
#: views/customers/wcfm-view-customers-manage.php:106
#: views/customers/wcfm-view-customers.php:37
#: views/listings/wcfm-view-listings.php:86
#: views/orders/wcfm-view-orders-details.php:128
#: views/orders/wcfm-view-orders.php:68
#: views/products/wcfm-view-products-export.php:45
#: views/products/wcfm-view-products.php:76
#: views/products-manager/wcfm-view-products-manage.php:436
#: views/reports/wcfm-view-reports-out-of-stock.php:37
#: views/reports/wcfm-view-reports-sales-by-date.php:78
#: views/wc_bookings/wcfm-view-wcbookings-details.php:56
#: views/wc_bookings/wcfm-view-wcbookings.php:64
msgid "WP Admin View"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:310
#: core/class-wcfm-thirdparty-support.php:313
msgid "Manage Listings"
msgstr "管理项目"

#: core/class-wcfm-thirdparty-support.php:311
#: views/listings/wcfm-view-listings.php:91
msgid "Add New Listing"
msgstr "新增新项目"

#: core/class-wcfm-thirdparty-support.php:417
msgid "Set Price Type"
msgstr "设定售价类型"

#: core/class-wcfm-thirdparty-support.php:417
msgid "General Pricing"
msgstr "一般售价"

#: core/class-wcfm-thirdparty-support.php:417
msgid "Choose a price type - this controls the schema."
msgstr "选择一个售价类型：这个控制模式"

#: core/class-wcfm-thirdparty-support.php:418
msgid "Hourly Price"
msgstr "小时售价"

#: core/class-wcfm-thirdparty-support.php:418
msgid "Hourly price will be applicabe if booking or rental days min 1day"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:418
#: core/class-wcfm-thirdparty-support.php:419
msgid "Enter price here"
msgstr "在这裡输入售价"

#: core/class-wcfm-thirdparty-support.php:419
msgid "General Price"
msgstr "一般售价"

#: core/class-wcfm-thirdparty-support.php:425
msgid "Availability"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:430
msgid "Product Availabilities"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:430
msgid "Please select the date range to be disabled for the product."
msgstr ""

#: core/class-wcfm-thirdparty-support.php:431
#: views/coupons/wcfm-view-coupons.php:56
#: views/coupons/wcfm-view-coupons.php:66
#: views/messages/wcfm-view-messages.php:121
#: views/messages/wcfm-view-messages.php:134
#: views/products/wcfm-view-products.php:203
#: views/products/wcfm-view-products.php:226
msgid "Type"
msgstr "类型"

#: core/class-wcfm-thirdparty-support.php:431
msgid "Custom Date"
msgstr "自订"

#: core/class-wcfm-thirdparty-support.php:432
#: views/messages/wcfm-view-messages.php:123
#: views/messages/wcfm-view-messages.php:136
#: views/products-manager/wcfm-view-products-manage-tabs.php:245
#: views/products-manager/wcfm-view-products-manage.php:472
#: views/products-popup/wcfm-view-product-popup.php:131
#: views/settings/wcfm-view-dokan-settings.php:490
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:717
#: views/settings/wcfm-view-wcmarketplace-settings.php:746
#: views/settings/wcfm-view-wcpvendors-settings.php:154
#: views/settings/wcfm-view-wcvendors-settings.php:446
msgid "From"
msgstr "从"

#: core/class-wcfm-thirdparty-support.php:433
#: views/messages/wcfm-view-messages.php:124
#: views/messages/wcfm-view-messages.php:137
#: views/products-manager/wcfm-view-products-manage.php:473
#: views/products-popup/wcfm-view-product-popup.php:132
msgid "To"
msgstr "到"

#: core/class-wcfm-thirdparty-support.php:434
#: core/class-wcfm-wcbookings.php:187
msgid "Bookable"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:434
msgid "NO"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:467
msgid "Auction Date From"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:468
msgid "Auction Date To"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:500
msgid "Has Voucher"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:528
msgid "-- Choose Template --"
msgstr "-- 选择模板 --"

#: core/class-wcfm-thirdparty-support.php:534
msgid "Voucher Template"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:534
msgid "Select a voucher template to make this into a voucher product."
msgstr ""

#: core/class-wcfm-thirdparty-support.php:646
#: core/class-wcfm-thirdparty-support.php:669
msgid "Select Delivery Time"
msgstr "选择送达时间"

#: core/class-wcfm-thirdparty-support.php:738
msgid "Scheduler Config"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:775
#: core/class-wcfm-wcfmmarketplace.php:583
#: core/class-wcfm-wcmarketplace.php:484 core/class-wcfm-wcmarketplace.php:632
#: core/class-wcfm-wcpvendors.php:406 core/class-wcfm-wcpvendors.php:505
#: core/class-wcfm-wcvendors.php:636 core/class-wcfm-wcvendors.php:649
#: core/class-wcfm-wcvendors.php:814 core/class-wcfm-wcvendors.php:837
#: helpers/class-wcfm-install.php:320
#: includes/reports/class-dokan-report-sales-by-date.php:831
#: includes/reports/class-wcfm-report-sales-by-date.php:720
#: views/capability/wcfm-view-capability.php:204
#: views/customers/wcfm-view-customers-manage.php:174
#: views/orders/wcfm-view-orders-details.php:607
#: views/orders/wcfm-view-orders-details.php:857
#: views/products-manager/wcfm-view-products-manage-tabs.php:83
#: views/products-manager/wcfm-view-products-manage-tabs.php:217
#: views/profile/wcfm-view-profile.php:286
#: views/settings/wcfm-view-dokan-settings.php:341
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:637
#: views/settings/wcfm-view-wcmarketplace-settings.php:606
#: views/settings/wcfm-view-wcvendors-settings.php:351
#: views/vendors/wcfm-view-vendors-new.php:131
msgid "Shipping"
msgstr "配送"

#: core/class-wcfm-thirdparty-support.php:780
msgid "Shipment Origin Information"
msgstr "配送原始资讯"

#: core/class-wcfm-vendor-support.php:141
msgid "Vendors Dashboard"
msgstr "卖家管理后台"

#: core/class-wcfm-vendor-support.php:144
msgid "New Vendor"
msgstr "新卖家"

#: core/class-wcfm-vendor-support.php:147
msgid "Vendors Manager"
msgstr "卖家管理"

#: core/class-wcfm-vendor-support.php:150
msgid "Vendors Commission"
msgstr "卖家款项"

#: core/class-wcfm-vendor-support.php:198
#: core/class-wcfm-vendor-support.php:211
#: views/vendors/wcfm-view-vendors-manage.php:186
#: views/vendors/wcfm-view-vendors-new.php:72
#: views/vendors/wcfm-view-vendors.php:24
msgid "Vendors"
msgstr "卖家"

#: core/class-wcfm-vendor-support.php:290
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:77
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:78
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:81
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:124
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:125
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:128
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:129
#: controllers/messages/wcfm-controller-messages.php:155
#: controllers/messages/wcfm-controller-messages.php:183
#: views/enquiry/wcfm-view-enquiry.php:91
#: views/enquiry/wcfm-view-enquiry.php:103
#: views/enquiry/wcfm-view-my-account-enquiry.php:43
#: views/enquiry/wcfm-view-my-account-enquiry.php:56
#: views/products/wcfm-view-products.php:206
#: views/products/wcfm-view-products.php:229
#: views/settings/wcfm-view-dokan-settings.php:159
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:196
#: views/settings/wcfm-view-wcmarketplace-settings.php:166
#: views/settings/wcfm-view-wcvendors-settings.php:150
#: views/vendors/wcfm-view-vendors-manage.php:272
#: views/vendors/wcfm-view-vendors.php:78
#: views/vendors/wcfm-view-vendors.php:99
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:54
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:64
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:69
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:83
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:66
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:78
msgid "Store"
msgstr "商店"

#: core/class-wcfm-vendor-support.php:293
msgid "Package Qty"
msgstr ""

#: core/class-wcfm-vendor-support.php:338
#: core/class-wcfm-vendor-support.php:492
#: core/class-wcfm-vendor-support.php:531
#: core/class-wcfm-vendor-support.php:547
#: core/class-wcfm-vendor-support.php:576
#: core/class-wcfm-wcfmmarketplace.php:440
#: core/class-wcfm-wcmarketplace.php:479 core/class-wcfm-wcpvendors.php:405
#: core/class-wcfm-wcvendors.php:631 views/orders/wcfm-view-orders.php:92
#: views/orders/wcfm-view-orders.php:112
#: views/settings/wcfm-view-wcpvendors-settings.php:181
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:68
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:80
msgid "Commission"
msgstr "货款"

#: core/class-wcfm-vendor-support.php:377
#: core/class-wcfm-vendor-support.php:382
#: views/vendors/wcfm-view-vendors.php:77
#: views/vendors/wcfm-view-vendors.php:98
msgid "Vendor"
msgstr "卖家"

#: core/class-wcfm-vendor-support.php:497
#: core/class-wcfm-vendor-support.php:537
#: core/class-wcfm-vendor-support.php:542
#: core/class-wcfm-vendor-support.php:566
#: core/class-wcfm-vendor-support.php:571
msgid "Commission(%)"
msgstr "货款(%)"

#: core/class-wcfm-vendor-support.php:538
#: core/class-wcfm-vendor-support.php:567
msgid "Fixed (per transaction)"
msgstr ""

#: core/class-wcfm-vendor-support.php:543
#: core/class-wcfm-vendor-support.php:572
msgid "Fixed (per unit)"
msgstr ""

#: core/class-wcfm-vendor-support.php:837
#: controllers/messages/wcfm-controller-messages.php:181
#: views/articles/wcfm-view-articles.php:10
#: views/listings/wcfm-view-listings.php:36
#: views/messages/wcfm-view-messages.php:105
#: views/products/wcfm-view-products.php:10
#: views/wc_bookings/wcfm-view-wcbookings.php:19
msgid "All"
msgstr "全部"

#: core/class-wcfm-vendor-support.php:839 helpers/wcfm-core-functions.php:962
msgid "Choose Vendor ..."
msgstr "选择供应商..."

#: core/class-wcfm-vendor-support.php:2254
msgid "Review Product"
msgstr "评价商品"

#: core/class-wcfm-vendor-support.php:2255 helpers/wcfm-core-functions.php:995
msgid "New Product"
msgstr "新商品"

#: core/class-wcfm-vendor-support.php:2256 helpers/wcfm-core-functions.php:996
msgid "New Category"
msgstr "新分类"

#: core/class-wcfm-vendor-support.php:2263
msgid "You may manage this using WCfM Capability Controller."
msgstr ""

#: core/class-wcfm-vendor-support.php:2264
#, php-format
msgid ""
"Manage vendor backend access from <a href=\"%s\">WCfM Capability Controller</"
"a>."
msgstr ""

#: core/class-wcfm-wcbookings.php:93
msgid "Bookings Dashboard"
msgstr ""

#: core/class-wcfm-wcbookings.php:96
#: views/capability/wcfm-view-capability.php:341
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:99
#: views/wc_bookings/wcfm-view-wcbookings-details.php:66
#: views/wc_bookings/wcfm-view-wcbookings.php:36
msgid "Bookings List"
msgstr ""

#: core/class-wcfm-wcbookings.php:99
msgid "Bookings Resources"
msgstr ""

#: core/class-wcfm-wcbookings.php:102
msgid "Bookings Resources Manage"
msgstr ""

#: core/class-wcfm-wcbookings.php:105
msgid "Create Bookings"
msgstr ""

#: core/class-wcfm-wcbookings.php:108
#: views/capability/wcfm-view-capability.php:342
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:114
msgid "Bookings Calendar"
msgstr ""

#: core/class-wcfm-wcbookings.php:111
#, php-format
msgid "Booking Details #%s"
msgstr ""

#: core/class-wcfm-wcbookings.php:114
msgid "Bookings settings"
msgstr ""

#: core/class-wcfm-wcbookings.php:328
msgid "Booking Options"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:438
#: core/class-wcfm-wcmarketplace.php:477 views/orders/wcfm-view-orders.php:88
#: views/orders/wcfm-view-orders.php:108
msgid "Fees"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:567
msgid "Line Total"
msgstr "项目统计"

#: core/class-wcfm-wcfmmarketplace.php:575
#: core/class-wcfm-wcmarketplace.php:490 core/class-wcfm-wcmarketplace.php:643
#: core/class-wcfm-wcpvendors.php:407 core/class-wcfm-wcpvendors.php:514
#: core/class-wcfm-wcvendors.php:642 core/class-wcfm-wcvendors.php:655
#: core/class-wcfm-wcvendors.php:825 core/class-wcfm-wcvendors.php:848
#: views/orders/wcfm-view-orders-details.php:396
#: views/orders/wcfm-view-orders-details.php:397
#: views/products-manager/wcfm-view-products-manage-tabs.php:109
msgid "Tax"
msgstr "税款"

#: core/class-wcfm-wcfmmarketplace.php:590
#: core/class-wcfm-wcmarketplace.php:491 core/class-wcfm-wcmarketplace.php:652
#: core/class-wcfm-wcpvendors.php:408 core/class-wcfm-wcpvendors.php:523
msgid "Shipping Tax"
msgstr "配送税款"

#: core/class-wcfm-wcfmmarketplace.php:599
#: core/class-wcfm-wcmarketplace.php:696 core/class-wcfm-wcpvendors.php:541
#: core/class-wcfm-wcvendors.php:869
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:67
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:79
msgid "Gross Total"
msgstr "销售额"

#: core/class-wcfm-wcfmmarketplace.php:616
#: views/orders/wcfm-view-orders-details.php:912
msgid "Refunded"
msgstr "已退款"

#: core/class-wcfm-wcfmmarketplace.php:624
#: core/class-wcfm-wcmarketplace.php:665 views/vendors/wcfm-view-vendors.php:84
#: views/vendors/wcfm-view-vendors.php:105
msgid "Total Fees"
msgstr "费用总计"

#: core/class-wcfm-wcmarketplace.php:496 core/class-wcfm-wcpvendors.php:409
#: core/class-wcfm-wcvendors.php:662
#: views/orders/wcfm-view-orders-details.php:389
msgid "Total"
msgstr "总计"

#: core/class-wcfm-wcmarketplace.php:538 core/class-wcfm-wcmarketplace.php:626
#: core/class-wcfm-wcmarketplace.php:674 core/class-wcfm-wcmarketplace.php:688
#: controllers/orders/wcfm-controller-orders.php:198
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:280
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:264
msgid "N/A"
msgstr ""

#: core/class-wcfm-wcmarketplace.php:621 core/class-wcfm-wcpvendors.php:496
#: core/class-wcfm-wcvendors.php:803
msgid "Line Commission"
msgstr "项目货款"

#: core/class-wcfm-wcsubscriptions.php:55
#: core/class-wcfm-xasubscriptions.php:55
#: views/capability/wcfm-view-capability.php:369
msgid "Subscriptions"
msgstr ""

#: core/class-wcfm-wcsubscriptions.php:56
msgid "Variable Subscriptions"
msgstr ""

#: core/class-wcfm-withdrawal.php:63
#: views/withdrawal/dokan/wcfm-view-payments.php:26
#: views/withdrawal/wcfm/wcfm-view-payments.php:28
#: views/withdrawal/wcmp/wcfm-view-payments.php:26
msgid "Payments History"
msgstr ""

#: core/class-wcfm-withdrawal.php:68
#: views/withdrawal/dokan/wcfm-view-payments.php:42
#: views/withdrawal/dokan/wcfm-view-withdrawal.php:53
#: views/withdrawal/wcfm/wcfm-view-payments.php:44
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:108
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:115
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:34
#: views/withdrawal/wcmp/wcfm-view-payments.php:43
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:49
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:28
msgid "Withdrawal Request"
msgstr ""

#: core/class-wcfm-withdrawal.php:72
msgid "Withdrawal Reverse"
msgstr ""

#: core/class-wcfm-withdrawal.php:76
#: views/capability/wcfm-view-capability.php:262
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:95
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:36
msgid "Transaction Details"
msgstr ""

#: core/class-wcfm-withdrawal.php:109 views/settings/wcfm-view-settings.php:345
msgid "Payments"
msgstr ""

#: core/class-wcfm-withdrawal.php:118 core/class-wcfm-withdrawal.php:129
#: core/class-wcfm.php:746 helpers/class-wcfm-install.php:332
#: includes/reports/class-dokan-report-sales-by-date.php:822
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:551
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:547
#: includes/reports/class-wcpvendors-report-sales-by-date.php:450
#: includes/reports/class-wcvendors-report-sales-by-date.php:559
#: views/capability/wcfm-view-capability.php:256
#: views/settings/wcfm-view-settings.php:351
#: views/vendors/wcfm-view-vendors.php:88
#: views/vendors/wcfm-view-vendors.php:109
#: views/withdrawal/dokan/wcfm-view-payments.php:42
#: views/withdrawal/wcfm/wcfm-view-payments.php:44
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:108
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:115
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:34
#: views/withdrawal/wcmp/wcfm-view-payments.php:43
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:49
msgid "Withdrawal"
msgstr "提款"

#: core/class-wcfm.php:474 core/class-wcfm.php:502
#: controllers/vendors/wcfm-controller-vendors.php:64
msgid "Disable Vendor"
msgstr ""

#: core/class-wcfm.php:612 views/capability/wcfm-view-capability.php:173
#: views/listings/wcfm-view-listings.php:105
#: views/listings/wcfm-view-listings.php:117
#: views/products/wcfm-view-products.php:37
msgid "Products"
msgstr "商品"

#: core/class-wcfm.php:622 views/capability/wcfm-view-capability.php:318
#: views/coupons/wcfm-view-coupons.php:16
msgid "Coupons"
msgstr "优惠券"

#: core/class-wcfm.php:632 views/capability/wcfm-view-capability.php:382
#: views/customers/wcfm-view-customers-details.php:256
#: views/customers/wcfm-view-customers.php:63
#: views/customers/wcfm-view-customers.php:78
#: views/orders/wcfm-view-orders.php:30
msgid "Orders"
msgstr "订单"

#: core/class-wcfm.php:637 views/capability/wcfm-view-capability.php:431
msgid "Reports"
msgstr "报表"

#: core/class-wcfm.php:647 helpers/class-wcfm-setup-bak.php:87
#: helpers/class-wcfm-setup-bak.php:925 helpers/class-wcfm-setup.php:87
#: helpers/class-wcfm-setup.php:945 views/settings/wcfm-view-settings.php:117
#: views/settings/wcfm-view-settings.php:419
msgid "Capability"
msgstr "权限"

#: core/class-wcfm.php:732
msgid "Popup Add Product"
msgstr "跳出新增商品"

#: core/class-wcfm.php:733 views/settings/wcfm-view-settings.php:333
msgid "Menu Manager"
msgstr "选单管理"

#: core/class-wcfm.php:734
msgid "Enquiry"
msgstr "问题"

#: core/class-wcfm.php:735
msgid "Enquiry Tab"
msgstr "问题标籤"

#: core/class-wcfm.php:735
msgid ""
"If you just want to hide Single Product page `Enquiry Tab`, but keep enable "
"`Enquiry Module` for `Catalog Mode`."
msgstr ""

#: core/class-wcfm.php:736
msgid ""
"If you disable `Enquiry Module` then `Catalog Module` will stop working "
"automatically."
msgstr ""

#: core/class-wcfm.php:737 helpers/class-wcfm-install.php:324
msgid "Article"
msgstr "文章"

#: core/class-wcfm.php:738 helpers/class-wcfm-install.php:325
#: views/enquiry/wcfm-view-enquiry.php:90
#: views/enquiry/wcfm-view-enquiry.php:102
msgid "Customer"
msgstr "顾客"

#: core/class-wcfm.php:743
msgid "Policies"
msgstr "条款"

#: core/class-wcfm.php:744
msgid "Custom Field"
msgstr "自订栏位"

#: core/class-wcfm.php:745
msgid "Sub-menu"
msgstr "子选单"

#: core/class-wcfm.php:745
msgid "This will disable `Add New` sub-menus on hover."
msgstr ""

#: core/class-wcfm.php:747
msgid "Refund"
msgstr "退款"

#: core/class-wcfm.php:751
msgid "BuddyPress Integration"
msgstr ""

#: core/class-wcfm.php:774
msgid "Base Highlighter Color"
msgstr ""

#: core/class-wcfm.php:775
msgid "Top Bar Background Color"
msgstr ""

#: core/class-wcfm.php:776
msgid "Top Bar Text Color"
msgstr ""

#: core/class-wcfm.php:777
msgid "Dashboard Background Color"
msgstr ""

#: core/class-wcfm.php:778
msgid "Container Background Color"
msgstr ""

#: core/class-wcfm.php:779
msgid "Container Head Color"
msgstr ""

#: core/class-wcfm.php:780
msgid "Container Head Text Color"
msgstr ""

#: core/class-wcfm.php:781
msgid "Container Head Active Color"
msgstr ""

#: core/class-wcfm.php:782
msgid "Container Head Active Text Color"
msgstr ""

#: core/class-wcfm.php:783
msgid "Menu Background Color"
msgstr ""

#: core/class-wcfm.php:784
msgid "Menu Item Text Color"
msgstr ""

#: core/class-wcfm.php:785
msgid "Menu Active Item Background"
msgstr ""

#: core/class-wcfm.php:786
msgid "Menu Active Item Text Color"
msgstr ""

#: core/class-wcfm.php:787
msgid "Button Background Color"
msgstr ""

#: core/class-wcfm.php:788
msgid "Button Text Color"
msgstr ""

#: helpers/class-wcfm-install.php:102
msgctxt "page_slug"
msgid "store-manager"
msgstr ""

#: helpers/class-wcfm-install.php:102
#: controllers/vendors/wcfm-controller-vendors-new.php:159
msgid "Store Manager"
msgstr "商店管理员"

#: helpers/class-wcfm-install.php:316
#: views/customers/wcfm-view-customers-details.php:188
#: views/customers/wcfm-view-customers-details.php:199
#: views/enquiry/wcfm-view-enquiry.php:89
#: views/enquiry/wcfm-view-enquiry.php:101
#: views/enquiry/wcfm-view-my-account-enquiry.php:42
#: views/enquiry/wcfm-view-my-account-enquiry.php:53
#: views/wc_bookings/wcfm-view-wcbookings.php:116
#: views/wc_bookings/wcfm-view-wcbookings.php:128
msgid "Product"
msgstr "商品"

#: helpers/class-wcfm-install.php:318
#: views/customers/wcfm-view-customers-details.php:189
#: views/customers/wcfm-view-customers-details.php:200
#: views/customers/wcfm-view-customers-details.php:263
#: views/customers/wcfm-view-customers-details.php:273
#: views/orders/wcfm-view-orders.php:82 views/orders/wcfm-view-orders.php:102
#: views/wc_bookings/wcfm-view-wcbookings.php:117
#: views/wc_bookings/wcfm-view-wcbookings.php:129
msgid "Order"
msgstr "订单"

#: helpers/class-wcfm-install.php:319
msgid "Report"
msgstr "报表"

#: helpers/class-wcfm-install.php:322 views/vendors/wcfm-view-vendors.php:76
#: views/vendors/wcfm-view-vendors.php:97
msgid "Verification"
msgstr "验证"

#: helpers/class-wcfm-install.php:323
msgid "Support Ticket"
msgstr "支援票券"

#: helpers/class-wcfm-install.php:326
msgid "Followers"
msgstr "订阅者"

#: helpers/class-wcfm-install.php:327
msgid "Coupon"
msgstr "优惠券"

#: helpers/class-wcfm-install.php:328
msgid "Noice"
msgstr "干扰"

#: helpers/class-wcfm-install.php:329 views/settings/wcfm-view-settings.php:120
#: views/vendors/wcfm-view-vendors-manage.php:363
#: views/vendors/wcfm-view-vendors.php:79
#: views/vendors/wcfm-view-vendors.php:100
msgid "Membership"
msgstr "会员"

#: helpers/class-wcfm-install.php:331
#: views/settings/wcfm-view-dokan-settings.php:252
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:326
#: views/settings/wcfm-view-wcmarketplace-settings.php:303
#: views/settings/wcfm-view-wcpvendors-settings.php:174
#: views/settings/wcfm-view-wcvendors-settings.php:246
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:55
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:65
#: views/withdrawal/wcfm/wcfm-view-payments.php:75
#: views/withdrawal/wcfm/wcfm-view-payments.php:89
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:72
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:86
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:77
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:88
msgid "Payment"
msgstr "付款"

#: helpers/class-wcfm-install.php:333
msgid "General"
msgstr "一般"

#: helpers/class-wcfm-install.php:335
msgid "Marketing"
msgstr "行销"

#: helpers/class-wcfm-install.php:336
#: views/settings/wcfm-view-dokan-settings.php:443
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:652
msgid "SEO"
msgstr "SEO 优化"

#: helpers/class-wcfm-setup-bak.php:52 helpers/class-wcfm-setup.php:52
msgid "Introduction"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:57 helpers/class-wcfm-setup.php:57
msgid "Dashboard Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:62 helpers/class-wcfm-setup.php:62
msgid "Marketplace Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:67 helpers/class-wcfm-setup.php:67
msgid "Commission Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:72 helpers/class-wcfm-setup.php:72
msgid "Withdrawal Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:77 helpers/class-wcfm-setup.php:77
msgid "Registration Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:82 helpers/class-wcfm-setup.php:82
msgid "Style"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:92 helpers/class-wcfm-setup.php:92
msgid "Ready!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:120 helpers/class-wcfm-setup.php:120
msgctxt "enhanced select"
msgid "No matches found"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:121 helpers/class-wcfm-setup.php:121
msgctxt "enhanced select"
msgid "Loading failed"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:122 helpers/class-wcfm-setup.php:122
msgctxt "enhanced select"
msgid "Please enter 1 or more characters"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:123 helpers/class-wcfm-setup.php:123
msgctxt "enhanced select"
msgid "Please enter %qty% or more characters"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:124 helpers/class-wcfm-setup.php:124
msgctxt "enhanced select"
msgid "Please delete 1 character"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:125 helpers/class-wcfm-setup.php:125
msgctxt "enhanced select"
msgid "Please delete %qty% characters"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:126 helpers/class-wcfm-setup.php:126
msgctxt "enhanced select"
msgid "You can only select 1 item"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:127 helpers/class-wcfm-setup.php:127
msgctxt "enhanced select"
msgid "You can only select %qty% items"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:128 helpers/class-wcfm-setup.php:128
msgctxt "enhanced select"
msgid "Loading more results&hellip;"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:129 helpers/class-wcfm-setup.php:129
msgctxt "enhanced select"
msgid "Searching&hellip;"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:218 helpers/class-wcfm-setup.php:218
msgid "WCFM &rsaquo; Setup Wizard"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:275 helpers/class-wcfm-setup-bak.php:386
#: helpers/class-wcfm-setup.php:275
msgid "Welcome to WooCommerce Multi-vendor Marketplace!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:276 helpers/class-wcfm-setup.php:276
msgid ""
"Thank you for choosing WCFM Marketplace! This quick setup wizard will help "
"you to configure the basic settings and you will have your marketplace ready "
"in no time."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:278 helpers/class-wcfm-setup.php:278
msgid "Let's experience the best ever WC Frontend Dashboard!!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:279 helpers/class-wcfm-setup.php:279
msgid ""
"Thank you for choosing WCFM! This quick setup wizard will help you to "
"configure the basic settings and you will have your dashboard ready in no "
"time. <strong>It’s completely optional as WCFM already auto-setup.</strong>"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:281 helpers/class-wcfm-setup.php:281
msgid ""
"If you don't want to go through the wizard right now, you can skip and "
"return to the WordPress dashboard. Come back anytime if you change your mind!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:283 helpers/class-wcfm-setup-bak.php:390
#: helpers/class-wcfm-setup.php:283
msgid "Let's go!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:284 helpers/class-wcfm-setup.php:284
msgid "Not right now"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:305 helpers/class-wcfm-setup.php:305
msgid "Dashboard setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:310 helpers/class-wcfm-setup.php:310
msgid "WCFM Full View"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:311 helpers/class-wcfm-setup.php:311
msgid "Theme Header"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:312 helpers/class-wcfm-setup.php:312
msgid "WCFM Slick Menu"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:313 helpers/class-wcfm-setup.php:313
msgid "WCFM Header Panel"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:314 helpers/class-wcfm-setup.php:314
msgid "Welcome Box"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:315 helpers/class-wcfm-setup.php:315
msgid "Category Checklist View"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:315 helpers/class-wcfm-setup.php:315
#: views/settings/wcfm-view-settings.php:160
msgid ""
"Disable this to have Product Manager Category/Custom Taxonomy Selector - "
"Flat View."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:316 helpers/class-wcfm-setup.php:316
msgid "Quick Access"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:317 helpers/class-wcfm-setup.php:317
#: views/settings/wcfm-view-settings.php:156
msgid "Disable Responsive Float Menu"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:318 helpers/class-wcfm-setup.php:318
msgid "Float Button"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:323 helpers/class-wcfm-setup-bak.php:426
#: helpers/class-wcfm-setup-bak.php:747 helpers/class-wcfm-setup-bak.php:863
#: helpers/class-wcfm-setup-bak.php:894 helpers/class-wcfm-setup-bak.php:963
#: helpers/class-wcfm-setup.php:323 helpers/class-wcfm-setup.php:421
#: helpers/class-wcfm-setup.php:742 helpers/class-wcfm-setup.php:883
#: helpers/class-wcfm-setup.php:914 helpers/class-wcfm-setup.php:983
msgid "Continue"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:324 helpers/class-wcfm-setup-bak.php:383
#: helpers/class-wcfm-setup-bak.php:427 helpers/class-wcfm-setup-bak.php:748
#: helpers/class-wcfm-setup-bak.php:864 helpers/class-wcfm-setup-bak.php:895
#: helpers/class-wcfm-setup-bak.php:964 helpers/class-wcfm-setup.php:324
#: helpers/class-wcfm-setup.php:422 helpers/class-wcfm-setup.php:743
#: helpers/class-wcfm-setup.php:884 helpers/class-wcfm-setup.php:915
#: helpers/class-wcfm-setup.php:984
msgid "Skip this step"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:376
msgid "Do you want to setup a multi-vendor marketplace!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:378
msgid "Install WCFM Marketplace"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:387
#, php-format
msgid ""
"You have installed <b>%s</b> as your multi-vendor marketplace. Setup multi-"
"vendor setting from plugin setup panel."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:388
#, php-format
msgid ""
"You may switch your multi-vendor to %s for having more features and "
"flexibilities."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:393 helpers/class-wcfm-setup.php:383
msgid "Marketplace setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:520 helpers/class-wcfm-setup-bak.php:684
#: helpers/class-wcfm-setup.php:515 helpers/class-wcfm-setup.php:679
#, php-format
msgid ""
"%1$s could not be installed (%2$s). <a href=\"%3$s\">Please install it "
"manually by clicking here.</a>"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:540 helpers/class-wcfm-setup-bak.php:704
#: helpers/class-wcfm-setup.php:535 helpers/class-wcfm-setup.php:699
#, php-format
msgid ""
"%1$s was installed but could not be activated. <a href=\"%2$s\">Please "
"activate it manually by clicking here.</a>"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:590 helpers/class-wcfm-setup.php:585
msgid "Setup WCFM Maketplace vendor registration:"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:592 helpers/class-wcfm-setup.php:587
msgid "Setup Registration"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:731 helpers/class-wcfm-setup.php:726
msgid "Commission setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:737 helpers/class-wcfm-setup.php:732
msgid ""
"You may setup more commission rules (By Sales Total and Product Price) from "
"setting panel."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:796 helpers/class-wcfm-setup.php:794
msgid "Withdrawal setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:882 helpers/class-wcfm-setup.php:902
#: views/settings/wcfm-view-settings.php:224
msgid "Dashboard Style"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:930 helpers/class-wcfm-setup.php:950
#: views/capability/wcfm-view-capability.php:224
msgid "Backend Access"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:933 helpers/class-wcfm-setup.php:953
msgid "Submit Products"
msgstr "送出商品"

#: helpers/class-wcfm-setup-bak.php:934 helpers/class-wcfm-setup.php:954
#: views/capability/wcfm-view-capability.php:179
msgid "Publish Products"
msgstr "发佈商品"

#: helpers/class-wcfm-setup-bak.php:935 helpers/class-wcfm-setup.php:955
#: views/capability/wcfm-view-capability.php:180
msgid "Edit Live Products"
msgstr "编辑公开商品"

#: helpers/class-wcfm-setup-bak.php:936 helpers/class-wcfm-setup.php:956
#: views/capability/wcfm-view-capability.php:182
msgid "Delete Products"
msgstr "删除商品"

#: helpers/class-wcfm-setup-bak.php:940 helpers/class-wcfm-setup.php:960
#: views/capability/wcfm-view-capability.php:276
#: views/capability/wcfm-view-capability.php:338
msgid "Manage Bookings"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:945 helpers/class-wcfm-setup.php:965
#: views/capability/wcfm-view-capability.php:288
#: views/capability/wcfm-view-capability.php:372
msgid "Manage Subscriptions"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:950 helpers/class-wcfm-setup.php:970
#: views/capability/wcfm-view-capability.php:293
msgid "by WP Job Manager."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:954 helpers/class-wcfm-setup.php:974
#: views/capability/wcfm-view-capability.php:385
msgid "View Orders"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:955 helpers/class-wcfm-setup.php:975
#: views/capability/wcfm-view-capability.php:386
msgid "Status Update"
msgstr "状态更新"

#: helpers/class-wcfm-setup-bak.php:958 helpers/class-wcfm-setup.php:978
#: views/capability/wcfm-view-capability.php:434
msgid "View Reports"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:980 helpers/class-wcfm-setup.php:1000
msgid "We are done!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:983 helpers/class-wcfm-setup.php:1003
msgid ""
"Your marketplace is ready. It's time to experience the things more Easily "
"and Peacefully. Also you will be a bit more relax than ever before, have "
"fun!!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:985 helpers/class-wcfm-setup.php:1005
msgid ""
"Your front-end dashboard is ready. It's time to experience the things more "
"Easily and Peacefully. Also you will be a bit more relax than ever before, "
"have fun!!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:990 helpers/class-wcfm-setup.php:1010
msgid "Next steps"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:992 helpers/class-wcfm-setup.php:1012
msgid "Let's go to Dashboard"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:996 helpers/class-wcfm-setup.php:1016
msgid "Learn more"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:998 helpers/class-wcfm-setup.php:1018
msgid "Watch the tutorial videos"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:999 helpers/class-wcfm-setup.php:1019
msgid "WCFM - What & Why?"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:1000 helpers/class-wcfm-setup.php:1020
msgid "Choose your multi-vendor plugin"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:1281 helpers/class-wcfm-setup.php:1308
msgid "Return to the WordPress Dashboard"
msgstr ""

#: helpers/class-wcfm-setup.php:376
msgid "Setup your multi-vendor marketplace in minutes!"
msgstr ""

#: helpers/class-wcfm-setup.php:378
msgid "Setup WCFM Marketplace"
msgstr ""

#: helpers/class-wcfm-setup.php:412
msgid "No of products at Store per Page."
msgstr ""

#: helpers/class-wcfm-setup.php:871
msgid "Reverse Withdrawal setup"
msgstr ""

#: helpers/wcfm-core-functions.php:6
#, php-format
msgid ""
"%sWooCommerce Frontend Manager is inactive.%s The %sWooCommerce plugin%s "
"must be active for the WooCommerce Frontend Manager to work. Please "
"%sinstall & activate WooCommerce%s"
msgstr ""

#: helpers/wcfm-core-functions.php:16
#, php-format
msgid ""
"%sOpps ..!!!%s You are using %sWC %s. WCFM works only with %sWC 3.0+%s. "
"PLease upgrade your WooCommerce version now to make your life easier and "
"peaceful by using WCFM."
msgstr ""

#: helpers/wcfm-core-functions.php:44
#, php-format
msgid ""
"%s: You don't have permission to access this page. Please contact your "
"%sStore Admin%s for assistance."
msgstr ""

#: helpers/wcfm-core-functions.php:59 helpers/wcfm-core-functions.php:89
msgid ""
": Please ask your Store Admin to upgrade your dashboard to access this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:64 helpers/wcfm-core-functions.php:94
#, php-format
msgid ""
"%s: Please ask your %sStore Admin%s to upgrade your dashboard to access this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:71
msgid ""
": Upgrade your WCFM to WCFM - Ultimate to avail this feature. Disable this "
"notice from settings panel using \"Disable Ultimate Notice\" option."
msgstr ""

#: helpers/wcfm-core-functions.php:75
#, php-format
msgid ""
"%s: Upgrade your WCFM to %sWCFM - Ultimate%s to access this feature. Disable "
"this notice from settings panel using \"Disable Ultimate Notice\" option."
msgstr ""

#: helpers/wcfm-core-functions.php:101
msgid ""
": Associate your WCFM with WCFM - Groups & Staffs to avail this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:105
#, php-format
msgid ""
"%s: Associate your WCFM with %sWCFM - Groups & Staffs%s to access this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:119
msgid ": Please contact your Store Admin to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:124
#, php-format
msgid "%s: Please contact your %sStore Admin%s to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:131
msgid ": Associate your WCFM with WCFM - Analytics to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:135
#, php-format
msgid ""
"%s: Associate your WCFM with %sWCFM - Analytics%s to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:776
msgid "Please insert Article Title before submit."
msgstr "请在送出前，输入文章标题。"

#: helpers/wcfm-core-functions.php:777
msgid "Article Successfully Saved."
msgstr "成功更新文章。"

#: helpers/wcfm-core-functions.php:778
msgid "Article Successfully submitted for moderation."
msgstr "成功送出文章做更新。"

#: helpers/wcfm-core-functions.php:779
msgid "Article Successfully Published."
msgstr "成功发表文章。"

#: helpers/wcfm-core-functions.php:791
msgid "Please insert Product Title before submit."
msgstr "请在送出前，输入商品标题。"

#: helpers/wcfm-core-functions.php:792
msgid "Product SKU must be unique."
msgstr "商品编号必须是唯一的。"

#: helpers/wcfm-core-functions.php:793
msgid "Variation SKU must be unique."
msgstr "规格商品编号必须是唯一的。"

#: helpers/wcfm-core-functions.php:794
msgid "Product Successfully Saved."
msgstr "成功更新商品。"

#: helpers/wcfm-core-functions.php:795
msgid "Product Successfully submitted for moderation."
msgstr "成功新增商品等待审核。"

#: helpers/wcfm-core-functions.php:796
msgid "Product Successfully Published."
msgstr "成功新增商品。"

#: helpers/wcfm-core-functions.php:797
msgid "Set Stock"
msgstr "设定库存"

#: helpers/wcfm-core-functions.php:798
#: views/products-manager/wcfm-view-products-manage-tabs.php:210
msgid "Increase Stock"
msgstr "增加库存数量"

#: helpers/wcfm-core-functions.php:799
#: views/products-manager/wcfm-view-products-manage-tabs.php:243
#: views/products-manager/wcfm-view-thirdparty-products-manage.php:294
msgid "Regular Price"
msgstr "固定售价"

#: helpers/wcfm-core-functions.php:800
msgid "Regular price increase by"
msgstr "提高固定售价"

#: helpers/wcfm-core-functions.php:801
msgid "Regular price decrease by"
msgstr "降低固定售价"

#: helpers/wcfm-core-functions.php:802
#: views/products-manager/wcfm-view-products-manage-tabs.php:244
#: views/products-manager/wcfm-view-products-manage.php:471
#: views/products-popup/wcfm-view-product-popup.php:130
msgid "Sale Price"
msgstr "优惠价"

#: helpers/wcfm-core-functions.php:803
msgid "Sale price increase by"
msgstr ""

#: helpers/wcfm-core-functions.php:804
msgid "Sale price decrease by"
msgstr ""

#: helpers/wcfm-core-functions.php:805
#: views/products-manager/wcfm-view-products-manage-tabs.php:90
#: views/products-manager/wcfm-view-products-manage-tabs.php:218
msgid "Length"
msgstr ""

#: helpers/wcfm-core-functions.php:806
#: views/products-manager/wcfm-view-products-manage-tabs.php:91
#: views/products-manager/wcfm-view-products-manage-tabs.php:219
msgid "Width"
msgstr ""

#: helpers/wcfm-core-functions.php:807
#: views/products-manager/wcfm-view-products-manage-tabs.php:92
#: views/products-manager/wcfm-view-products-manage-tabs.php:220
msgid "Height"
msgstr ""

#: helpers/wcfm-core-functions.php:808
#: views/products-manager/wcfm-view-products-manage-tabs.php:89
#: views/products-manager/wcfm-view-products-manage-tabs.php:221
msgid "Weight"
msgstr ""

#: helpers/wcfm-core-functions.php:809
msgid "Download Limit"
msgstr ""

#: helpers/wcfm-core-functions.php:810
msgid "Download Expiry"
msgstr ""

#: helpers/wcfm-core-functions.php:824
msgid "Please insert atleast Coupon Title before submit."
msgstr "在送出之前，请至少输入优惠券名称。"

#: helpers/wcfm-core-functions.php:825
msgid "Coupon Successfully Saved."
msgstr "成功更新优惠券。"

#: helpers/wcfm-core-functions.php:826
msgid "Coupon Successfully Published."
msgstr "成功发佈优惠券。"

#: helpers/wcfm-core-functions.php:838
msgid "Please insert atleast Knowledgebase Title before submit."
msgstr "在送出之前，请至少输入知识库名称。"

#: helpers/wcfm-core-functions.php:839
msgid "Knowledgebase Successfully Saved."
msgstr "成功储存知识库。"

#: helpers/wcfm-core-functions.php:840
msgid "Knowledgebase Successfully Published."
msgstr "成功发佈知识库。"

#: helpers/wcfm-core-functions.php:852
msgid "Please insert atleast Topic Title before submit."
msgstr "在送出之前，请至少输入主题名称。"

#: helpers/wcfm-core-functions.php:853
msgid "Topic Successfully Saved."
msgstr "成功储存主题。"

#: helpers/wcfm-core-functions.php:854
msgid "Topic Successfully Published."
msgstr "成功发佈主题。"

#: helpers/wcfm-core-functions.php:866
msgid "Please write something before submit."
msgstr "在送出前，请填写一些东西。"

#: helpers/wcfm-core-functions.php:867
msgid "Reply send failed, try again."
msgstr "送出回复失败，请再次尝试一次。"

#: helpers/wcfm-core-functions.php:868
msgid "Reply Successfully Send."
msgstr "成功送出回复。"

#: helpers/wcfm-core-functions.php:880
msgid "Name is required."
msgstr "姓名是必填的。"

#: helpers/wcfm-core-functions.php:881
msgid "Email is required."
msgstr "信箱是必填的。"

#: helpers/wcfm-core-functions.php:882
msgid "Please insert your enquiry before submit."
msgstr "在送出前，请输入您的问题。"

#: helpers/wcfm-core-functions.php:883
msgid "Please insert your reply before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:884
msgid "Your enquiry successfully sent."
msgstr "成功送出您的查询。"

#: helpers/wcfm-core-functions.php:885
msgid "Enquiry reply successfully published."
msgstr "成功发布问题回复。"

#: helpers/wcfm-core-functions.php:886
msgid "Your reply successfully sent."
msgstr ""

#: helpers/wcfm-core-functions.php:898
msgid "Please insert Username before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:899
msgid "Please insert Email before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:900
msgid "Please insert Store Name before submit."
msgstr "在送出前，请输入商店名称。"

#: helpers/wcfm-core-functions.php:901 helpers/wcfm-core-functions.php:918
msgid "This Username already exists."
msgstr "这个会员代码已经存在了。"

#: helpers/wcfm-core-functions.php:902 helpers/wcfm-core-functions.php:919
msgid "This Email already exists."
msgstr "这个信箱已经存在了。"

#: helpers/wcfm-core-functions.php:903
msgid "Vendor Saving Failed."
msgstr ""

#: helpers/wcfm-core-functions.php:904
msgid "Vendor Successfully Saved."
msgstr ""

#: helpers/wcfm-core-functions.php:916
msgid "Please insert Customer Username before submit."
msgstr "在送出之前，请输入这个顾客的会员代码。"

#: helpers/wcfm-core-functions.php:917
msgid "Please insert Customer Email before submit."
msgstr "在送出之前，请输入这个顾客的信箱。"

#: helpers/wcfm-core-functions.php:920
msgid "Customer Saving Failed."
msgstr "顾客储存失败。"

#: helpers/wcfm-core-functions.php:921
msgid "Customer Successfully Saved."
msgstr "顾客成功更新。"

#: helpers/wcfm-core-functions.php:933
msgid "Are you sure and want to approve / publish this 'Product'?"
msgstr "您确定想要许可/发佈这个商品？"

#: helpers/wcfm-core-functions.php:934
msgid ""
"Are you sure and want to delete this 'Article'?\n"
"You can't undo this action ..."
msgstr ""
"您确定想要删除这个文章？\n"
"提醒：您无法復原这个动作..."

#: helpers/wcfm-core-functions.php:935
msgid ""
"Are you sure and want to delete this 'Product'?\n"
"You can't undo this action ..."
msgstr ""
"您确定想要删除这个商品？\n"
"提醒：您无法復原这个动作..."

#: helpers/wcfm-core-functions.php:936
msgid ""
"Are you sure and want to delete this 'Message'?\n"
"You can't undo this action ..."
msgstr ""
"您确定想要删除这个资讯？\n"
"提醒：您无法復原这个动作..."

#: helpers/wcfm-core-functions.php:937
msgid ""
"Are you sure and want to delete this 'Order'?\n"
"You can't undo this action ..."
msgstr ""
"您确定想要删除这个订单？\n"
"提醒：您无法復原这个动作..."

#: helpers/wcfm-core-functions.php:938
msgid ""
"Are you sure and want to delete this 'Enquiry'?\n"
"You can't undo this action ..."
msgstr ""
"您确定想要删除这个询问？\n"
"提醒：您无法復原这个动作..."

#: helpers/wcfm-core-functions.php:939
msgid ""
"Are you sure and want to delete this 'Support Ticket'?\n"
"You can't undo this action ..."
msgstr ""
"您确定想要删除这个票券支援？\n"
"提醒：您无法復原这个动作...\v\v"

#: helpers/wcfm-core-functions.php:940
msgid ""
"Are you sure and want to delete this 'Follower'?\n"
"You can't undo this action ..."
msgstr ""
"您确定想要删除这个订阅者？\n"
"提醒：您无法復原这个动作...\v\v"

#: helpers/wcfm-core-functions.php:941
msgid ""
"Are you sure and want to delete this 'Following'?\n"
"You can't undo this action ..."
msgstr ""
"您确定想要删除这个订阅？\n"
"提醒：您无法復原这个动作...\v\v"

#: helpers/wcfm-core-functions.php:942
msgid "Are you sure and want to 'Mark as Complete' this Order?"
msgstr "你确定要将这个订单的状态，标示为完成？"

#: helpers/wcfm-core-functions.php:943
msgid "Are you sure and want to 'Mark as Confirmed' this Booking?"
msgstr ""

#: helpers/wcfm-core-functions.php:944
msgid "Are you sure and want to 'Mark as Complete' this Appointment?"
msgstr ""

#: helpers/wcfm-core-functions.php:946
msgid "Select all"
msgstr "选择全部"

#: helpers/wcfm-core-functions.php:947
msgid "Select none"
msgstr "没有选择"

#: helpers/wcfm-core-functions.php:948
msgid "Any"
msgstr "任何"

#: helpers/wcfm-core-functions.php:949
msgid "Enter a name for the new attribute term:"
msgstr ""

#: helpers/wcfm-core-functions.php:950
msgid ""
"Please upgrade your WC Frontend Manager to Ultimate version and avail this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:951
msgid ""
"Install WC Frontend Manager Ultimate and WooCommerce PDF Invoices & Packing "
"Slips to avail this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:952
msgid "Please select some element first!!"
msgstr "请先选择一些元素！！"

#: helpers/wcfm-core-functions.php:953
msgid ""
"Are you sure and want to do this?\n"
"You can't undo this action ..."
msgstr ""
"您确定想要做这个？\n"
"您无法復原这个动作..."

#: helpers/wcfm-core-functions.php:954
msgid "Are you sure and want to do this?"
msgstr "您确定想要做这个？"

#: helpers/wcfm-core-functions.php:955
#: includes/libs/php/class-wcfm-fields.php:642
msgid "Everywhere Else"
msgstr "任何地方"

#: helpers/wcfm-core-functions.php:956 views/profile/wcfm-view-profile.php:212
#: includes/libs/php/class-wcfm-fields.php:82
#: includes/libs/php/class-wcfm-fields.php:84
#: includes/libs/php/class-wcfm-fields.php:87
#: includes/libs/php/class-wcfm-fields.php:148
#: includes/libs/php/class-wcfm-fields.php:219
#: includes/libs/php/class-wcfm-fields.php:261
#: includes/libs/php/class-wcfm-fields.php:319
#: includes/libs/php/class-wcfm-fields.php:381
#: includes/libs/php/class-wcfm-fields.php:441
#: includes/libs/php/class-wcfm-fields.php:546
#: includes/libs/php/class-wcfm-fields.php:624
#: includes/libs/php/class-wcfm-fields.php:694
#: includes/libs/php/class-wcfm-fields.php:759
#: includes/libs/php/class-wcfm-fields.php:762
#: includes/libs/php/class-wcfm-fields.php:825
#: includes/libs/php/class-wcfm-fields.php:874
#: includes/libs/php/class-wcfm-fields.php:968
msgid "This field is required."
msgstr "这是栏位是必填的。"

#: helpers/wcfm-core-functions.php:957
msgid "Choose "
msgstr "选择"

#: helpers/wcfm-core-functions.php:958
msgid "Search for a attribute ..."
msgstr "搜寻属性..."

#: helpers/wcfm-core-functions.php:959
msgid "Search for a product ..."
msgstr "搜寻商品..."

#: helpers/wcfm-core-functions.php:960
msgid "Choose Categoies ..."
msgstr "选择分类..."

#: helpers/wcfm-core-functions.php:961
msgid "Choose Listings ..."
msgstr "选择项目..."

#: helpers/wcfm-core-functions.php:963
msgid "No categories"
msgstr "没有分类"

#: helpers/wcfm-core-functions.php:964
msgid "Searching ..."
msgstr "搜寻..."

#: helpers/wcfm-core-functions.php:965
msgid "No matching result found."
msgstr "没有找到符合的结果。"

#: helpers/wcfm-core-functions.php:966
msgid "Loading ..."
msgstr "读取中..."

#: helpers/wcfm-core-functions.php:967
msgid "Minimum input character "
msgstr "最小输入字元数 "

#: helpers/wcfm-core-functions.php:970
msgid "Add New Block"
msgstr "新增新的区块"

#: helpers/wcfm-core-functions.php:971
msgid "Remove Block"
msgstr "移除区块"

#: helpers/wcfm-core-functions.php:972
msgid "Toggle Block"
msgstr ""

#: helpers/wcfm-core-functions.php:973
msgid "Drag to re-arrange blocks"
msgstr ""

#: helpers/wcfm-core-functions.php:974
msgid "Please login to the site first!"
msgstr ""

#: helpers/wcfm-core-functions.php:975
msgid "Please select a shipping method"
msgstr ""

#: helpers/wcfm-core-functions.php:976
msgid "Shipping method not found"
msgstr ""

#: helpers/wcfm-core-functions.php:977
msgid "Shipping zone not found"
msgstr ""

#: helpers/wcfm-core-functions.php:978
msgid ""
"Are you sure you want to delete this 'Shipping Method'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:990
#: views/vendors/wcfm-view-vendors-manage.php:410
msgid "Direct Message"
msgstr "直接私讯"

#: helpers/wcfm-core-functions.php:991
msgid "Approve Product"
msgstr "商品等待审核"

#: helpers/wcfm-core-functions.php:992
msgid "Status Updated"
msgstr "更新状态"

#: helpers/wcfm-core-functions.php:993
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:23
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:30
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:25
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:32
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:34
msgid "Withdrawal Requests"
msgstr "提款请求"

#: helpers/wcfm-core-functions.php:994
#: views/capability/wcfm-view-capability.php:243
msgid "Refund Requests"
msgstr "退款请求"

#: helpers/wcfm-core-functions.php:997
msgid "New Order"
msgstr "新订单"

#: helpers/wcfm-core-functions.php:1318
msgid "Tutorial"
msgstr "教学"

#: views/wcfm-view-header-panels.php:46 views/wcfm-view-header-panels.php:49
msgid "Toggle Menu"
msgstr "下拉选单"

#: views/wcfm-view-header-panels.php:61
#: includes/shortcodes/class-wcfm-shortcode-notification.php:51
msgid "Notification Board"
msgstr "讯息版"

#: views/wcfm-view-header-panels.php:65
#: includes/shortcodes/class-wcfm-shortcode-notification.php:55
#: views/enquiry/wcfm-view-enquiry.php:36
msgid "Enquiry Board"
msgstr "问题版"

#: views/wcfm-view-header-panels.php:69
#: includes/shortcodes/class-wcfm-shortcode-notification.php:59
#: views/notice/wcfm-view-notices.php:26
msgid "Notice Board"
msgstr "主题版"

#: views/wcfm-view-header-panels.php:79 views/wcfm-view-menu.php:144
msgid "Logout"
msgstr "登出"

#: views/wcfm-view-menu.php:68 views/settings/wcfm-view-settings.php:340
msgid "Home"
msgstr "首页"

#: controllers/articles/wcfm-controller-articles-manage.php:37
#: controllers/coupons/wcfm-controller-coupons-manage.php:37
#: controllers/customers/wcfm-controller-customers-manage.php:37
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:34
#: controllers/knowledgebase/wcfm-controller-knowledgebase-manage.php:37
#: controllers/products-manager/wcfm-controller-products-manage.php:37
#: controllers/profile/wcfm-controller-profile.php:67
#: controllers/settings/wcfm-controller-dokan-settings.php:33
#: controllers/settings/wcfm-controller-settings.php:31
#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:40
#: controllers/settings/wcfm-controller-wcmarketplace-settings.php:33
#: controllers/settings/wcfm-controller-wcpvendors-settings.php:29
#: controllers/settings/wcfm-controller-wcvendors-settings.php:31
#: controllers/vendors/wcfm-controller-vendors-manage.php:29
#: controllers/vendors/wcfm-controller-vendors-new.php:48
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:37
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:34
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:137
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:33
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:34
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:93
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse-actions.php:34
#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal-request.php:33
msgid "There has some error in submitted data."
msgstr "在送出资料裡面有一些错误。"

#: controllers/articles/wcfm-controller-articles.php:138
#: controllers/listings/wcfm-controller-listings.php:24
#: controllers/products/wcfm-controller-products.php:25
#: controllers/products/wcfm-controller-products.php:218
#: views/articles/wcfm-view-articles-manage.php:117
#: views/articles/wcfm-view-articles.php:11
#: views/listings/wcfm-view-listings.php:37
#: views/products/wcfm-view-products.php:11
#: views/products-manager/wcfm-view-products-manage.php:415
msgid "Published"
msgstr "已发佈"

#: controllers/articles/wcfm-controller-articles.php:158
#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:103
#: controllers/listings/wcfm-controller-listings.php:127
#: controllers/notice/wcfm-controller-notices.php:77
#: controllers/products/wcfm-controller-products.php:322
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:105
#: views/articles/wcfm-view-articles-manage.php:387
#: views/notice/wcfm-view-notice-manage.php:59
#: views/products-manager/wcfm-view-products-manage.php:794
#: views/products-popup/wcfm-view-product-popup.php:301
msgid "View"
msgstr "浏览"

#: controllers/capability/wcfm-controller-capability.php:39
msgid "Capability saved successfully"
msgstr "成功更新权限"

#: controllers/customers/wcfm-controller-customers-details.php:96
#: controllers/customers/wcfm-controller-customers-details.php:103
#: controllers/orders/wcfm-controller-dokan-orders.php:161
#: controllers/orders/wcfm-controller-dokan-orders.php:168
#: controllers/orders/wcfm-controller-orders.php:115
#: controllers/orders/wcfm-controller-orders.php:122
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:186
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:193
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:187
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:194
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:158
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:165
#: controllers/orders/wcfm-controller-wcvendors-orders.php:186
#: controllers/orders/wcfm-controller-wcvendors-orders.php:193
#, php-format
msgctxt "full name"
msgid "%1$s %2$s"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:107
#: controllers/customers/wcfm-controller-customers-details.php:113
#: controllers/orders/wcfm-controller-dokan-orders.php:172
#: controllers/orders/wcfm-controller-dokan-orders.php:178
#: controllers/orders/wcfm-controller-orders.php:126
#: controllers/orders/wcfm-controller-orders.php:132
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:197
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:203
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:198
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:204
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:169
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:175
#: controllers/orders/wcfm-controller-wcvendors-orders.php:197
#: controllers/orders/wcfm-controller-wcvendors-orders.php:203
msgid "Guest"
msgstr "访客"

#: controllers/customers/wcfm-controller-customers-details.php:117
#: controllers/customers/wcfm-controller-customers-details.php:119
#: controllers/orders/wcfm-controller-dokan-orders.php:182
#: controllers/orders/wcfm-controller-dokan-orders.php:184
#: controllers/orders/wcfm-controller-orders.php:136
#: controllers/orders/wcfm-controller-orders.php:138
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:207
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:209
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:208
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:210
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:179
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:181
#: controllers/orders/wcfm-controller-wcvendors-orders.php:207
#: controllers/orders/wcfm-controller-wcvendors-orders.php:209
#: views/enquiry/wcfm-view-enquiry-tab.php:64
msgid "by"
msgstr " "

#: controllers/customers/wcfm-controller-customers-details.php:140
#: controllers/orders/wcfm-controller-dokan-orders.php:205
#: controllers/orders/wcfm-controller-orders.php:159
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:245
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:240
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:237
#: controllers/orders/wcfm-controller-wcvendors-orders.php:224
#, php-format
msgid "%d item"
msgid_plural "%d items"
msgstr[0] "%d 个商品"

#: controllers/customers/wcfm-controller-customers-details.php:147
#: controllers/orders/wcfm-controller-dokan-orders.php:230
#: controllers/orders/wcfm-controller-orders.php:184
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:89
#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:139
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:132
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse.php:104
msgid "Via"
msgstr "透过"

#: controllers/customers/wcfm-controller-customers-details.php:159
#: controllers/orders/wcfm-controller-dokan-orders.php:249
#: controllers/orders/wcfm-controller-orders.php:215
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:322
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:306
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:289
#: controllers/orders/wcfm-controller-wcvendors-orders.php:338
msgid "Mark as Complete"
msgstr "标示已完成"

#: controllers/customers/wcfm-controller-customers-details.php:167
#: controllers/customers/wcfm-controller-customers-details.php:171
#: controllers/orders/wcfm-controller-orders.php:224
#: views/capability/wcfm-view-capability.php:396
#: views/orders/wcfm-view-orders-details.php:147
#: views/orders/wcfm-view-orders-details.php:149
msgid "PDF Invoice"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:168
#: views/capability/wcfm-view-capability.php:402
msgid "PDF Packing Slip"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:21
msgid "Paid & Confirmed"
msgstr "已付款 & 已确认"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:23
msgid "Pending Confirmation"
msgstr "等待确认"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
msgid "Un-paid"
msgstr "未付款"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:24
#: views/withdrawal/dokan/wcfm-view-payments.php:54
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:40
#: views/withdrawal/wcfm/wcfm-view-payments.php:56
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:47
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:45
msgid "Cancelled"
msgstr "取消"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:20
msgid "Complete"
msgstr "完成"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:22
msgid "Confirmed"
msgstr "已确认"

#: controllers/customers/wcfm-controller-customers-details.php:275
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:90
msgid "#"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:289
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:104
#, php-format
msgctxt "Guest string with name from booking order in brackets"
msgid "Guest (%s)"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:357
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:179
msgid "Mark as Confirmed"
msgstr "标示已确认"

#: controllers/customers/wcfm-controller-customers-manage.php:127
#: controllers/vendors/wcfm-controller-vendors-new.php:153
msgid "Dear"
msgstr ""

#: controllers/customers/wcfm-controller-customers-manage.php:129
#: controllers/vendors/wcfm-controller-vendors-new.php:155
msgid ""
"Your account has been created as {user_role}. Follow the bellow details to "
"log into the system"
msgstr ""

#: controllers/customers/wcfm-controller-customers-manage.php:131
msgid "Site"
msgstr "网站"

#: controllers/customers/wcfm-controller-customers-manage.php:133
msgid "Login"
msgstr "登入"

#: controllers/customers/wcfm-controller-customers-manage.php:135
#: controllers/vendors/wcfm-controller-vendors-new.php:163
#: views/profile/wcfm-view-profile.php:222
msgid "Password"
msgstr "密码"

#: controllers/customers/wcfm-controller-customers-manage.php:145
#: controllers/vendors/wcfm-controller-vendors-new.php:174
msgid "New Account"
msgstr "新帐户"

#: controllers/customers/wcfm-controller-customers-manage.php:156
#, php-format
msgid "A new customer <b>%s</b> added to the store by <b>%s</b>"
msgstr "一个新顾客 <b>%s</b> 由 <b>%s</b> 新增到这个商店"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:98
msgid "Reply for your Inquiry"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:101
#, php-format
msgid ""
"We recently have a enquiry from you regarding \"%s\". Please check below for "
"our input for the same: "
msgstr "我们最近有一个新问题来自你的关于 \"%s\"。请检查下方我们的资讯："

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:115
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:179
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:190
msgid "Inquiry Reply"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:122
#, php-format
msgid "New reply posted for Inquiry <b>%s</b>"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:182
msgid ""
"You have received reply for your \"{product_title}\" inquiry. Please check "
"below for the details: "
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:186
msgid "Check more details here"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:198
msgid "Reply to Inquiry"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:203
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:217
#, php-format
msgid "New reply received for Inquiry <b>%s</b>"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:46
msgid "Captcha failed, please try again."
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:122
#: views/enquiry/wcfm-view-enquiry.php:92
#: views/enquiry/wcfm-view-enquiry.php:104 views/orders/wcfm-view-orders.php:94
#: views/orders/wcfm-view-orders.php:114 views/vendors/wcfm-view-vendors.php:90
#: views/vendors/wcfm-view-vendors.php:111
msgid "Additional Info"
msgstr "额外资讯"

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:134
msgid "New enquiry for"
msgstr "新问题来自"

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:137
#, php-format
msgid "You have a recent enquiry for %s."
msgstr "你有一个新问题来自 %s。"

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:142
#, php-format
msgid "To respond to this Enquiry, please %sClick Here%s"
msgstr "回复这个问题，%s请点击这裡%s"

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:167
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:185
#, php-format
msgid "New Inquiry <b>%s</b> received for <b>%s</b>"
msgstr ""

#: controllers/listings/wcfm-controller-listings.php:26
#: views/listings/wcfm-view-listings.php:39
msgid "Expired"
msgstr "已过期"

#: controllers/listings/wcfm-controller-listings.php:27
#: views/articles/wcfm-view-articles-manage.php:128
#: views/articles/wcfm-view-articles-manage.php:381
#: views/listings/wcfm-view-listings.php:40
#: views/products-manager/wcfm-view-products-manage.php:426
#: views/products-manager/wcfm-view-products-manage.php:788
#: views/products-popup/wcfm-view-product-popup.php:295
msgid "Preview"
msgstr "预览"

#: controllers/listings/wcfm-controller-listings.php:28
msgid "Pending Payment"
msgstr "等待付款"

#: controllers/messages/wcfm-controller-message-sent.php:47
msgid "Message sent successfully"
msgstr "成功发送讯息"

#: controllers/messages/wcfm-controller-messages.php:153
msgid "System"
msgstr "系统"

#: controllers/messages/wcfm-controller-messages.php:163
#: controllers/messages/wcfm-controller-messages.php:174
#: controllers/messages/wcfm-controller-messages.php:190
#: controllers/messages/wcfm-controller-messages.php:201
msgid "You"
msgstr "你"

#: controllers/messages/wcfm-controller-messages.php:213
#: controllers/messages/wcfm-controller-messages.php:215
msgid "Approve / Reject"
msgstr "许可 / 驳回"

#: controllers/messages/wcfm-controller-messages.php:217
#: views/messages/wcfm-view-messages.php:99
msgid "Mark Read"
msgstr "标示已读"

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:283
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:268
msgid "UNPAID"
msgstr "未付"

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:287
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:272
#: controllers/orders/wcfm-controller-wcvendors-orders.php:302
msgid "PAID"
msgstr "已付"

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:291
msgid "REQUESTED"
msgstr ""

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:295
msgid "CANCELLED"
msgstr ""

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:331
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:315
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:297
#: controllers/orders/wcfm-controller-wcvendors-orders.php:346
msgid "Mark Shipped"
msgstr "标示已出货"

#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:276
#: controllers/orders/wcfm-controller-wcvendors-orders.php:306
msgid "REVERSED"
msgstr "反转"

#: controllers/orders/wcfm-controller-wcvendors-orders.php:298
msgid "DUE"
msgstr "因为"

#: controllers/products/wcfm-controller-products.php:229
#: views/products-manager/wcfm-view-products-manage-tabs.php:27
#: views/products-manager/wcfm-view-products-manage-tabs.php:250
msgid "In stock"
msgstr "有库存"

#: controllers/products/wcfm-controller-products.php:229
#: views/products-manager/wcfm-view-products-manage-tabs.php:27
#: views/products-manager/wcfm-view-products-manage-tabs.php:250
#: views/reports/wcfm-view-reports-menu.php:5
msgid "Out of stock"
msgstr "已售完"

#: controllers/products/wcfm-controller-products.php:229
#: views/products-manager/wcfm-view-products-manage-tabs.php:27
#: views/products-manager/wcfm-view-products-manage-tabs.php:250
msgid "On backorder"
msgstr "缺货中"

#: controllers/products/wcfm-controller-products.php:248
#: views/capability/wcfm-view-capability.php:193
msgid "Grouped"
msgstr "群组"

#: controllers/products/wcfm-controller-products.php:250
msgid "External/Affiliate"
msgstr "外部/联盟"

#: controllers/products/wcfm-controller-products.php:254
#: views/products/wcfm-view-products.php:161
#: views/products-manager/wcfm-view-products-manage-tabs.php:238
#: views/products-manager/wcfm-view-products-manage.php:461
#: views/products-popup/wcfm-view-product-popup.php:120
msgid "Virtual"
msgstr "虚拟"

#: controllers/products/wcfm-controller-products.php:256
#: views/products/wcfm-view-products.php:157
msgid "Downloadable"
msgstr "可下载"

#: controllers/products/wcfm-controller-products.php:258
#: views/capability/wcfm-view-capability.php:191
msgid "Simple"
msgstr "一般"

#: controllers/products/wcfm-controller-products.php:262
#: views/capability/wcfm-view-capability.php:192
msgid "Variable"
msgstr "规格"

#: controllers/products/wcfm-controller-products.php:264
msgid "Subscription"
msgstr ""

#: controllers/products/wcfm-controller-products.php:266
msgid "Variable Subscription"
msgstr ""

#: controllers/products/wcfm-controller-products.php:268
msgid "Listings Package"
msgstr ""

#: controllers/products/wcfm-controller-products.php:270
msgid "Resume Package"
msgstr ""

#: controllers/products/wcfm-controller-products.php:276
msgid "Accommodation"
msgstr ""

#: controllers/products/wcfm-controller-products.php:278
#: views/customers/wcfm-view-customers.php:65
#: views/customers/wcfm-view-customers.php:80
msgid "Appointment"
msgstr ""

#: controllers/products/wcfm-controller-products.php:280
msgid "Bundle"
msgstr ""

#: controllers/products/wcfm-controller-products.php:282
msgid "Composite"
msgstr ""

#: controllers/products/wcfm-controller-products.php:284
msgid "Lottery"
msgstr ""

#: controllers/products/wcfm-controller-products.php:318
msgid "Mark Approve / Publish"
msgstr "标示许可 / 发佈"

#: controllers/products/wcfm-controller-products.php:329
msgid "No Featured"
msgstr "没有特色"

#: controllers/products/wcfm-controller-products.php:332
msgid "Mark Featured"
msgstr "标示精选"

#: controllers/products/wcfm-controller-products.php:337
msgid ""
"Featured Product: Upgrade your WCFM to WCFM Ultimate to avail this feature."
msgstr ""

#: controllers/products/wcfm-controller-products.php:346
msgid "Duplicate"
msgstr "複製"

#: controllers/products/wcfm-controller-products.php:349
msgid ""
"Duplicate Product: Upgrade your WCFM to WCFM Ultimate to avail this feature."
msgstr ""

#: controllers/profile/wcfm-controller-profile.php:252
msgid "Email verification code invalid."
msgstr ""

#: controllers/profile/wcfm-controller-profile.php:262
#: controllers/vendors/wcfm-controller-vendors-manage.php:49
msgid "Profile saved successfully"
msgstr "成功更新个人档案"

#: controllers/settings/wcfm-controller-dokan-settings.php:93
#: controllers/settings/wcfm-controller-settings.php:229
#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:161
#: controllers/settings/wcfm-controller-wcmarketplace-settings.php:290
#: controllers/settings/wcfm-controller-wcpvendors-settings.php:80
#: controllers/settings/wcfm-controller-wcvendors-settings.php:122
msgid "Settings saved successfully"
msgstr "成功更新设定"

#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:96
#: controllers/settings/wcfm-controller-wcmarketplace-settings.php:94
msgid "Shop Slug already exists."
msgstr ""

#: controllers/settings/wcfm-controller-wcpvendors-settings.php:82
msgid "Settings failed to save"
msgstr "更新设定失败"

#: controllers/vendors/wcfm-controller-vendors-manage.php:81
msgid "Badges saved successfully"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors-new.php:157
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:211
msgid "Store Name"
msgstr "商店名称"

#: controllers/vendors/wcfm-controller-vendors-new.php:159
msgid "Click here ..."
msgstr ""

#: controllers/vendors/wcfm-controller-vendors-new.php:161
#: views/customers/wcfm-view-customers-manage.php:131
#: views/customers/wcfm-view-customers-manage.php:133
#: views/customers/wcfm-view-customers.php:60
#: views/customers/wcfm-view-customers.php:75
#: views/vendors/wcfm-view-vendors-new.php:90
msgid "Username"
msgstr "会员代码"

#: controllers/vendors/wcfm-controller-vendors-new.php:181
#, php-format
msgid "A new vendor <b>%s</b> added ."
msgstr "新增了新的卖家 <b>%s</b>。"

#: controllers/vendors/wcfm-controller-vendors.php:66
msgid "Active Vendor"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:79
#: views/vendors/wcfm-view-vendors-manage.php:160
msgid "Email Verified"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:81
#: views/vendors/wcfm-view-vendors-manage.php:162
msgid "Email Verification Pending"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:112
msgid "Next payment on"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:129
#: controllers/vendors/wcfm-controller-vendors.php:134
#: controllers/vendors/wcfm-controller-vendors.php:142
msgid "Expiry on"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:142
msgid "Never Expire"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:180
#: views/vendors/wcfm-view-vendors-manage.php:304
msgid "Disable Vendor Account"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:182
#: views/vendors/wcfm-view-vendors-manage.php:306
msgid "Enable Vendor Account"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:573
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:220
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:205
#: includes/reports/class-wcpvendors-report-sales-by-date.php:199
#: includes/reports/class-wcvendors-report-sales-by-date.php:219
#, php-format
msgid "%s total earnings"
msgstr "%s 货款总计"

#: includes/reports/class-dokan-report-sales-by-date.php:574
#: includes/reports/class-wcfm-report-analytics.php:127
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:221
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:206
#: includes/reports/class-wcvendors-report-sales-by-date.php:220
msgid ""
"This is the sum of the earned commission including shipping and taxes if "
"applicable."
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:580
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:229
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:214
#: includes/reports/class-wcpvendors-report-sales-by-date.php:208
#: includes/reports/class-wcvendors-report-sales-by-date.php:227
#, php-format
msgid "%s total withdrawal"
msgstr "%s 提款总计"

#: includes/reports/class-dokan-report-sales-by-date.php:581
msgid ""
"This is the sum of the commission withdraw including shipping and taxes if "
"applicable."
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:805
#: includes/reports/class-wcfm-report-sales-by-date.php:712
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:531
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:527
#: includes/reports/class-wcpvendors-report-sales-by-date.php:430
#: includes/reports/class-wcvendors-report-sales-by-date.php:541
#: views/customers/wcfm-view-customers-details.php:265
#: views/customers/wcfm-view-customers-details.php:275
#: views/orders/wcfm-view-orders.php:86 views/orders/wcfm-view-orders.php:106
#: views/vendors/wcfm-view-vendors.php:82
#: views/vendors/wcfm-view-vendors.php:103
msgid "Gross Sales"
msgstr "销售额"

#: includes/reports/class-dokan-report-sales-by-date.php:814
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:541
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:537
#: includes/reports/class-wcpvendors-report-sales-by-date.php:440
#: includes/reports/class-wcvendors-report-sales-by-date.php:550
msgid "Earning"
msgstr "货款"

#: includes/reports/class-dokan-report-sales-by-date.php:840
#: includes/reports/class-wcfm-report-sales-by-date.php:728
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:580
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:566
#: includes/reports/class-wcpvendors-report-sales-by-date.php:469
#: includes/reports/class-wcvendors-report-sales-by-date.php:591
msgid "Order Counts"
msgstr "订单总计"

#: includes/reports/class-dokan-report-sales-by-date.php:848
#: includes/reports/class-wcfm-report-sales-by-date.php:736
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:588
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:574
#: includes/reports/class-wcpvendors-report-sales-by-date.php:477
#: includes/reports/class-wcvendors-report-sales-by-date.php:599
msgid "Order Item Counts"
msgstr "订单项目总计"

#: includes/reports/class-dokan-report-sales-by-date.php:856
#: includes/reports/class-wcfm-report-sales-by-date.php:752
msgid "Coupon Amounts"
msgstr "优惠券金额"

#: includes/reports/class-dokan-report-sales-by-date.php:864
#: includes/reports/class-wcfm-report-sales-by-date.php:760
msgid "Refund Amounts"
msgstr "退款金额"

#: includes/reports/class-dokan-report-sales-by-date.php:875
#: includes/reports/class-wcfm-report-sales-by-date.php:771
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:599
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:585
#: includes/reports/class-wcpvendors-report-sales-by-date.php:488
#: includes/reports/class-wcvendors-report-sales-by-date.php:610
msgid "Sales Report by Date"
msgstr "每日销售报告"

#: includes/reports/class-dokan-report-sales-by-date.php:893
#: includes/reports/class-wcfm-report-analytics.php:170
#: includes/reports/class-wcfm-report-sales-by-date.php:789
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:319
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:617
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:294
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:603
#: includes/reports/class-wcpvendors-report-sales-by-date.php:506
#: includes/reports/class-wcvendors-report-sales-by-date.php:316
#: includes/reports/class-wcvendors-report-sales-by-date.php:628
#: views/articles/wcfm-view-articles.php:113
#: views/articles/wcfm-view-articles.php:124
#: views/customers/wcfm-view-customers-details.php:266
#: views/customers/wcfm-view-customers-details.php:276
#: views/enquiry/wcfm-view-enquiry.php:94
#: views/enquiry/wcfm-view-enquiry.php:106
#: views/messages/wcfm-view-messages.php:125
#: views/messages/wcfm-view-messages.php:138
#: views/orders/wcfm-view-orders.php:95 views/orders/wcfm-view-orders.php:115
#: views/products/wcfm-view-products.php:205
#: views/products/wcfm-view-products.php:228
#: views/withdrawal/dokan/wcfm-view-payments.php:71
#: views/withdrawal/dokan/wcfm-view-payments.php:80
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:57
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:67
#: views/withdrawal/wcfm/wcfm-view-payments.php:78
#: views/withdrawal/wcfm/wcfm-view-payments.php:92
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:75
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:89
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:71
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:83
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:78
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:89
#: views/withdrawal/wcmp/wcfm-view-payments.php:69
#: views/withdrawal/wcmp/wcfm-view-payments.php:80
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:61
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:70
msgid "Date"
msgstr "日期"

#: includes/reports/class-dokan-report-sales-by-date.php:902
#: includes/reports/class-wcfm-report-sales-by-date.php:798
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:626
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:612
#: includes/reports/class-wcpvendors-report-sales-by-date.php:515
#: includes/reports/class-wcvendors-report-sales-by-date.php:637
#: views/coupons/wcfm-view-coupons.php:57
#: views/coupons/wcfm-view-coupons.php:67
#: views/withdrawal/dokan/wcfm-view-payments.php:68
#: views/withdrawal/dokan/wcfm-view-payments.php:77
#: views/withdrawal/wcfm/wcfm-view-payments.php:73
#: views/withdrawal/wcfm/wcfm-view-payments.php:87
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:70
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:84
msgid "Amount"
msgstr "金额"

#: includes/reports/class-wcfm-report-analytics.php:117
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:179
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:164
#: includes/reports/class-wcvendors-report-sales-by-date.php:201
#, php-format
msgid "%s average daily sales"
msgstr "%s 每日平均销售额"

#: includes/reports/class-wcfm-report-analytics.php:121
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:183
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:168
#: includes/reports/class-wcvendors-report-sales-by-date.php:205
#, php-format
msgid "%s average monthly sales"
msgstr "%s 每月平均销售额"

#: includes/reports/class-wcfm-report-analytics.php:126
#, php-format
msgid "%s total earned commission"
msgstr "%s 货款总计"

#: includes/reports/class-wcfm-report-analytics.php:141
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:283
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:259
#: includes/reports/class-wcvendors-report-sales-by-date.php:281
#: views/enquiry/wcfm-view-enquiry.php:27
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:29
#: views/reports/wcfm-view-reports-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:29
#: views/vendors/wcfm-view-vendors.php:14
msgid "Year"
msgstr "年份"

#: includes/reports/class-wcfm-report-analytics.php:142
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:284
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:260
#: includes/reports/class-wcvendors-report-sales-by-date.php:282
#: views/enquiry/wcfm-view-enquiry.php:26
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:30
#: views/reports/wcfm-view-reports-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:30
#: views/vendors/wcfm-view-vendors.php:13
msgid "Last Month"
msgstr "最近月份"

#: includes/reports/class-wcfm-report-analytics.php:143
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:285
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:261
#: includes/reports/class-wcvendors-report-sales-by-date.php:283
#: views/enquiry/wcfm-view-enquiry.php:25
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:31
#: views/reports/wcfm-view-reports-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:31
#: views/vendors/wcfm-view-vendors.php:12
msgid "This Month"
msgstr "这个月份"

#: includes/reports/class-wcfm-report-analytics.php:144
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:286
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:262
#: includes/reports/class-wcvendors-report-sales-by-date.php:284
#: views/enquiry/wcfm-view-enquiry.php:24
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:32
#: views/reports/wcfm-view-reports-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:32
#: views/vendors/wcfm-view-vendors.php:11
msgid "Last 7 Days"
msgstr "最近 7 天"

#: includes/reports/class-wcfm-report-analytics.php:176
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:325
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:300
#: includes/reports/class-wcvendors-report-sales-by-date.php:322
#: views/capability/wcfm-view-capability.php:390
msgid "Export CSV"
msgstr "导出 CSV"

#: includes/reports/class-wcfm-report-analytics.php:260
msgid "Daily Views"
msgstr "每日浏览"

#: includes/reports/class-wcfm-report-analytics.php:270
#: views/dashboard/wcfm-view-dashboard.php:204
#: views/dashboard/wcfm-view-dokan-dashboard.php:208
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:211
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:229
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:232
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:215
msgid "Store Analytics"
msgstr "商店分析"

#: includes/reports/class-wcfm-report-sales-by-date.php:744
msgid "Net Sales"
msgstr "销售淨额"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:189
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:174
#: includes/reports/class-wcvendors-report-sales-by-date.php:211
#, php-format
msgid "%s gross sales in this period"
msgstr "%s 这段时间的销售额"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:190
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:175
#: includes/reports/class-wcvendors-report-sales-by-date.php:212
msgid ""
"This is the sum of the order totals after any refunds and including shipping "
"and taxes."
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:202
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:187
#, php-format
msgid "%s total admin fees"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:203
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:188
msgid ""
"This is the sum of the admin fees including shipping and taxes if applicable."
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:211
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:196
#, php-format
msgid "%s total paid fees"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:212
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:197
msgid ""
"This is the sum of the admin fees paid including shipping and taxes if "
"applicable."
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:230
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:215
#: includes/reports/class-wcvendors-report-sales-by-date.php:228
msgid ""
"This is the sum of the commission paid including shipping and taxes if "
"applicable."
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:239
#, php-format
msgid "%s total refund"
msgstr "%s 退款总计"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:240
msgid "This is the sum of the refunds and partial refunds."
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:255
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:231
#: includes/reports/class-wcvendors-report-sales-by-date.php:243
#, php-format
msgid "%s orders placed"
msgstr "%s 订单数总计"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:261
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:237
#: includes/reports/class-wcvendors-report-sales-by-date.php:249
#, php-format
msgid "%s items purchased"
msgstr "已购买 %s 个项目"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:268
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:244
#: includes/reports/class-wcvendors-report-sales-by-date.php:257
#: includes/reports/class-wcvendors-report-sales-by-date.php:265
#, php-format
msgid "%s charged for shipping"
msgstr "%s 运费总计"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:541
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:537
msgid "Admin Fees"
msgstr "管理费用"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:551
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:547
#: views/vendors/wcfm-view-vendors.php:85
#: views/vendors/wcfm-view-vendors.php:106
msgid "Paid Fees"
msgstr "已付费用"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:561
msgid "Refunds"
msgstr "退款"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:571
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:557
#: includes/reports/class-wcpvendors-report-sales-by-date.php:460
#: includes/reports/class-wcvendors-report-sales-by-date.php:570
#: includes/reports/class-wcvendors-report-sales-by-date.php:581
msgid "Shipping Amounts"
msgstr "配送金额"

#: includes/shortcodes/class-wcfm-shortcode-follow.php:33
msgid "Follow Me"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:105
msgid "Manage Article"
msgstr "管理文章"

#: views/articles/wcfm-view-articles-manage.php:113
msgid "Edit Article"
msgstr "编辑文章"

#: views/articles/wcfm-view-articles-manage.php:113
msgid "Add Article"
msgstr "新增文章"

#: views/articles/wcfm-view-articles-manage.php:122
#: views/articles/wcfm-view-articles.php:112
#: views/articles/wcfm-view-articles.php:123
#: views/listings/wcfm-view-listings.php:107
#: views/listings/wcfm-view-listings.php:119
#: views/products/wcfm-view-products.php:204
#: views/products/wcfm-view-products.php:227
#: views/products-manager/wcfm-view-products-manage.php:420
msgid "Views"
msgstr "浏览人次"

#: views/articles/wcfm-view-articles-manage.php:141
#: views/articles/wcfm-view-articles.php:65
msgid "Add New Article"
msgstr "新增新文章"

#: views/articles/wcfm-view-articles-manage.php:159
msgid "Article Title"
msgstr "文章标题"

#: views/articles/wcfm-view-articles-manage.php:167
#: views/articles/wcfm-view-articles-manage.php:262
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:89
#: views/products-manager/wcfm-view-products-manage.php:482
#: views/products-manager/wcfm-view-products-manage.php:604
#: views/products-popup/wcfm-view-product-popup.php:149
#: views/settings/wcfm-view-settings.php:504
msgid "Categories"
msgstr "分类"

#: views/articles/wcfm-view-articles-manage.php:212
#: views/articles/wcfm-view-articles-manage.php:310
#: views/products-manager/wcfm-view-products-manage.php:531
#: views/products-manager/wcfm-view-products-manage.php:711
#: views/products-popup/wcfm-view-product-popup.php:198
msgid "Tags"
msgstr "标籤"

#: views/articles/wcfm-view-articles-manage.php:212
#: views/articles/wcfm-view-articles-manage.php:310
msgid "Separate Article Tags with commas"
msgstr "用 ',' 间隔文章标籤"

#: views/articles/wcfm-view-articles-manage.php:212
#: views/articles/wcfm-view-articles-manage.php:310
#: views/products-manager/wcfm-view-products-manage.php:531
#: views/products-manager/wcfm-view-products-manage.php:711
msgid "Choose from the most used tags"
msgstr "从这裡选择更多已使用过的标籤"

#: views/articles/wcfm-view-articles-manage.php:224
#: views/articles/wcfm-view-articles-manage.php:322
#: views/products-manager/wcfm-view-products-manage.php:544
#: views/products-manager/wcfm-view-products-manage.php:723
#: views/products-popup/wcfm-view-product-popup.php:211
msgid " with commas"
msgstr " 使用 ','"

#: views/articles/wcfm-view-articles-manage.php:239
#: views/articles/wcfm-view-articles-manage.php:343
#: views/products-manager/wcfm-view-products-manage.php:566
#: views/products-manager/wcfm-view-products-manage.php:750
#: views/products-popup/wcfm-view-product-popup.php:264
msgid "Short Description"
msgstr "特色说明"

#: views/articles/wcfm-view-articles-manage.php:240
#: views/articles/wcfm-view-articles-manage.php:344
#: views/coupons/wcfm-view-coupons-manage.php:93
#: views/products-manager/wcfm-view-products-manage.php:567
#: views/products-manager/wcfm-view-products-manage.php:751
#: views/products-popup/wcfm-view-product-popup.php:265
msgid "Description"
msgstr "完整说明"

#: views/articles/wcfm-view-articles-manage.php:369
#: views/articles/wcfm-view-articles-manage.php:371
#: views/coupons/wcfm-view-coupons-manage.php:120
#: views/coupons/wcfm-view-coupons-manage.php:122
#: views/customers/wcfm-view-customers-manage.php:199
#: views/enquiry/wcfm-view-enquiry-form.php:150
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:132
#: views/notice/wcfm-view-notice-manage.php:95
#: views/products-manager/wcfm-view-products-manage.php:776
#: views/products-manager/wcfm-view-products-manage.php:778
#: views/products-popup/wcfm-view-product-popup.php:283
#: views/products-popup/wcfm-view-product-popup.php:285
#: views/vendors/wcfm-view-vendors-new.php:156
msgid "Submit"
msgstr "送出"

#: views/articles/wcfm-view-articles-manage.php:369
#: views/articles/wcfm-view-articles-manage.php:371
#: views/coupons/wcfm-view-coupons-manage.php:120
#: views/coupons/wcfm-view-coupons-manage.php:122
#: views/products-manager/wcfm-view-products-manage.php:776
#: views/products-manager/wcfm-view-products-manage.php:778
#: views/products-popup/wcfm-view-product-popup.php:283
#: views/products-popup/wcfm-view-product-popup.php:285
msgid "Submit for Review"
msgstr "送出等待审核"

#: views/articles/wcfm-view-articles.php:83
msgid "Select a category"
msgstr "选择分类"

#: views/articles/wcfm-view-articles.php:109
#: views/articles/wcfm-view-articles.php:120
#: views/products/wcfm-view-products.php:197
#: views/products/wcfm-view-products.php:220
#: views/products-manager/wcfm-view-products-manage-tabs.php:241
msgid "Image"
msgstr "图片"

#: views/articles/wcfm-view-articles.php:111
#: views/articles/wcfm-view-articles.php:122
#: views/customers/wcfm-view-customers-details.php:186
#: views/customers/wcfm-view-customers-details.php:197
#: views/customers/wcfm-view-customers-details.php:224
#: views/customers/wcfm-view-customers-details.php:235
#: views/customers/wcfm-view-customers-details.php:262
#: views/customers/wcfm-view-customers-details.php:272
#: views/listings/wcfm-view-listings.php:104
#: views/listings/wcfm-view-listings.php:116
#: views/orders/wcfm-view-orders.php:81 views/orders/wcfm-view-orders.php:101
#: views/products/wcfm-view-products.php:200
#: views/products/wcfm-view-products.php:223
#: views/products-manager/wcfm-view-products-manage-tabs.php:185
#: views/vendors/wcfm-view-vendors.php:75
#: views/vendors/wcfm-view-vendors.php:96
#: views/wc_bookings/wcfm-view-wcbookings.php:114
#: views/wc_bookings/wcfm-view-wcbookings.php:126
#: views/withdrawal/dokan/wcfm-view-payments.php:67
#: views/withdrawal/dokan/wcfm-view-payments.php:76
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:53
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:63
#: views/withdrawal/wcfm/wcfm-view-payments.php:69
#: views/withdrawal/wcfm/wcfm-view-payments.php:83
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:137
#: views/withdrawal/wcmp/wcfm-view-payments.php:63
#: views/withdrawal/wcmp/wcfm-view-payments.php:74
msgid "Status"
msgstr "状态"

#: views/articles/wcfm-view-articles.php:114
#: views/articles/wcfm-view-articles.php:125
msgid "Author"
msgstr "作者"

#: views/articles/wcfm-view-articles.php:115
#: views/articles/wcfm-view-articles.php:126
#: views/customers/wcfm-view-customers-details.php:192
#: views/customers/wcfm-view-customers-details.php:203
#: views/customers/wcfm-view-customers-details.php:267
#: views/customers/wcfm-view-customers-details.php:277
#: views/customers/wcfm-view-customers.php:69
#: views/customers/wcfm-view-customers.php:84
#: views/enquiry/wcfm-view-enquiry.php:95
#: views/enquiry/wcfm-view-enquiry.php:107
#: views/enquiry/wcfm-view-my-account-enquiry.php:44
#: views/knowledgebase/wcfm-view-knowledgebase.php:73
#: views/knowledgebase/wcfm-view-knowledgebase.php:80
#: views/messages/wcfm-view-messages.php:126
#: views/messages/wcfm-view-messages.php:139
#: views/notice/wcfm-view-notices.php:52 views/notice/wcfm-view-notices.php:58
#: views/orders/wcfm-view-orders.php:96 views/orders/wcfm-view-orders.php:116
#: views/products/wcfm-view-products.php:208
#: views/products/wcfm-view-products.php:231
#: views/reports/wcfm-view-reports-out-of-stock.php:54
#: views/reports/wcfm-view-reports-out-of-stock.php:63
#: views/wc_bookings/wcfm-view-wcbookings.php:121
#: views/wc_bookings/wcfm-view-wcbookings.php:133
msgid "Actions"
msgstr "动作"

#: views/capability/wcfm-view-capability.php:137
#: views/settings/wcfm-view-settings.php:117
msgid "Capability Controller"
msgstr "权限管理"

#: views/capability/wcfm-view-capability.php:144
msgid "Capability Settings"
msgstr "权限设定"

#: views/capability/wcfm-view-capability.php:147
msgid "Dashboard Settings"
msgstr "管理后台设定"

#: views/capability/wcfm-view-capability.php:163
#: views/vendors/wcfm-view-vendors-manage.php:336
msgid "Vendors Capability"
msgstr "卖家权限"

#: views/capability/wcfm-view-capability.php:168
msgid "Configure what to hide from all Vendors"
msgstr "设定那些对所有供应商隐藏"

#: views/capability/wcfm-view-capability.php:177
msgid "Manage Products"
msgstr "管理商品"

#: views/capability/wcfm-view-capability.php:178
msgid "Add Products"
msgstr "新增商品"

#: views/capability/wcfm-view-capability.php:181
msgid "Auto Publish Live Products"
msgstr "自动发佈公开商品"

#: views/capability/wcfm-view-capability.php:187
msgid "Types"
msgstr "类型"

#: views/capability/wcfm-view-capability.php:194
msgid "External / Affiliate"
msgstr "外部/联盟"

#: views/capability/wcfm-view-capability.php:199
msgid "Panels"
msgstr "面板"

#: views/capability/wcfm-view-capability.php:203
#: views/products-manager/wcfm-view-products-manage-tabs.php:18
#: views/products-manager/wcfm-view-products-manage-tabs.php:206
msgid "Inventory"
msgstr "库存"

#: views/capability/wcfm-view-capability.php:205
msgid "Taxes"
msgstr "税金"

#: views/capability/wcfm-view-capability.php:206
#: views/products-manager/wcfm-view-products-manage-tabs.php:265
msgid "Linked"
msgstr "连结"

#: views/capability/wcfm-view-capability.php:207
#: views/products-manager/wcfm-view-products-manage-tabs.php:129
#: views/products-manager/wcfm-view-products-manage-tabs.php:136
msgid "Attributes"
msgstr "规格选项"

#: views/capability/wcfm-view-capability.php:208
msgid "Advanced"
msgstr "进阶"

#: views/capability/wcfm-view-capability.php:220
msgid "Access"
msgstr ""

#: views/capability/wcfm-view-capability.php:230
msgid "Marketplace"
msgstr ""

#: views/capability/wcfm-view-capability.php:235
msgid "Show Email"
msgstr ""

#: views/capability/wcfm-view-capability.php:236
msgid "Show Phone"
msgstr ""

#: views/capability/wcfm-view-capability.php:237
msgid "Show Address"
msgstr ""

#: views/capability/wcfm-view-capability.php:238
msgid "Show Map"
msgstr ""

#: views/capability/wcfm-view-capability.php:239
msgid "Show Social"
msgstr ""

#: views/capability/wcfm-view-capability.php:240
msgid "Show Follower"
msgstr ""

#: views/capability/wcfm-view-capability.php:241
msgid "Show Policy"
msgstr ""

#: views/capability/wcfm-view-capability.php:242
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:679
#: views/settings/wcfm-view-wcmarketplace-settings.php:699
msgid "Customer Support"
msgstr "顾客支援"

#: views/capability/wcfm-view-capability.php:244
msgid "Reviews Manage"
msgstr ""

#: views/capability/wcfm-view-capability.php:245
msgid "Ledger Book"
msgstr ""

#: views/capability/wcfm-view-capability.php:260
msgid "Withdrwal Request"
msgstr "提款要求"

#: views/capability/wcfm-view-capability.php:261
#: views/withdrawal/dokan/wcfm-view-withdrawal.php:64
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:57
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:42
msgid "Transactions"
msgstr "交易"

#: views/capability/wcfm-view-capability.php:272
msgid "Integrations"
msgstr ""

#: views/capability/wcfm-view-capability.php:276
msgid "Install WC Bookings to enable this feature."
msgstr ""

#: views/capability/wcfm-view-capability.php:282
#: views/capability/wcfm-view-capability.php:355
msgid "Manage Appointments"
msgstr ""

#: views/capability/wcfm-view-capability.php:282
msgid "Install WC Appointments to enable this feature."
msgstr ""

#: views/capability/wcfm-view-capability.php:288
msgid "Install WC Subscriptions to enable this feature."
msgstr ""

#: views/capability/wcfm-view-capability.php:293
#: views/capability/wcfm-view-capability.php:296
msgid "Associate Listings"
msgstr ""

#: views/capability/wcfm-view-capability.php:296
msgid "Install WP Job Manager to enable this feature."
msgstr ""

#: views/capability/wcfm-view-capability.php:305
msgid "Manage Articles"
msgstr "管理文章"

#: views/capability/wcfm-view-capability.php:306
msgid "Add Articles"
msgstr "新增文章"

#: views/capability/wcfm-view-capability.php:307
msgid "Publish Articles"
msgstr "发佈文章"

#: views/capability/wcfm-view-capability.php:308
msgid "Edit Live Articles"
msgstr "编辑公开文章"

#: views/capability/wcfm-view-capability.php:309
msgid "Auto Publish Live Articles"
msgstr "自动发佈公开文章"

#: views/capability/wcfm-view-capability.php:310
msgid "Delete Articles"
msgstr "删除文章"

#: views/capability/wcfm-view-capability.php:321
msgid "Manage Coupons"
msgstr "管理优惠券"

#: views/capability/wcfm-view-capability.php:322
msgid "Add Coupons"
msgstr "新增优惠券"

#: views/capability/wcfm-view-capability.php:323
msgid "Publish Coupons"
msgstr "发佈优惠券"

#: views/capability/wcfm-view-capability.php:324
msgid "Edit Live Coupons"
msgstr "编辑公开优惠券"

#: views/capability/wcfm-view-capability.php:325
msgid "Auto Publish Live Coupons"
msgstr "自动发佈公开优惠券"

#: views/capability/wcfm-view-capability.php:326
msgid "Delete Coupons"
msgstr "删除优惠券"

#: views/capability/wcfm-view-capability.php:327
msgid "Allow Free Shipping"
msgstr "许可宅配免运费"

#: views/capability/wcfm-view-capability.php:335
#: views/customers/wcfm-view-customers-details.php:180
#: views/customers/wcfm-view-customers.php:64
#: views/customers/wcfm-view-customers.php:79
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:24
msgid "Bookings"
msgstr ""

#: views/capability/wcfm-view-capability.php:339
msgid "Manual Booking"
msgstr ""

#: views/capability/wcfm-view-capability.php:340
msgid "Manage Resource"
msgstr ""

#: views/capability/wcfm-view-capability.php:352
#: views/customers/wcfm-view-customers-details.php:218
msgid "Appointments"
msgstr ""

#: views/capability/wcfm-view-capability.php:356
msgid "Manual Appointment"
msgstr ""

#: views/capability/wcfm-view-capability.php:357
msgid "Manage Staff"
msgstr ""

#: views/capability/wcfm-view-capability.php:358
msgid "Appointments List"
msgstr ""

#: views/capability/wcfm-view-capability.php:359
msgid "Appointments Calendar"
msgstr ""

#: views/capability/wcfm-view-capability.php:373
msgid "Subscriptions List"
msgstr ""

#: views/capability/wcfm-view-capability.php:374
msgid "Subscription Details"
msgstr ""

#: views/capability/wcfm-view-capability.php:375
msgid "Subscription Status Update"
msgstr ""

#: views/capability/wcfm-view-capability.php:376
msgid "Subscription Schedule Update"
msgstr ""

#: views/capability/wcfm-view-capability.php:388
msgid "View Comments"
msgstr "浏览留言"

#: views/capability/wcfm-view-capability.php:389
msgid "Submit Comments"
msgstr "送出留言"

#: views/capability/wcfm-view-capability.php:400
msgid "Store Invoice"
msgstr "商店发票"

#: views/capability/wcfm-view-capability.php:401
msgid "Commission Invoice"
msgstr "货款发票"

#: views/capability/wcfm-view-capability.php:412
#: views/customers/wcfm-view-customers-details.php:100
#: views/customers/wcfm-view-customers-manage.php:110
#: views/customers/wcfm-view-customers.php:32
msgid "Manage Customers"
msgstr "管理顾客"

#: views/capability/wcfm-view-capability.php:413
#: views/customers/wcfm-view-customers-manage.php:101
msgid "Add Customer"
msgstr "新增顾客"

#: views/capability/wcfm-view-capability.php:414
#: views/customers/wcfm-view-customers-details.php:103
#: views/customers/wcfm-view-customers-manage.php:101
msgid "Edit Customer"
msgstr "编辑顾客"

#: views/capability/wcfm-view-capability.php:415
msgid "View Customer"
msgstr "浏览顾客"

#: views/capability/wcfm-view-capability.php:416
msgid "View Customer Orders"
msgstr "浏览顾客订单"

#: views/capability/wcfm-view-capability.php:417
msgid "View Customer Name"
msgstr ""

#: views/capability/wcfm-view-capability.php:418
msgid "View Customer Email"
msgstr "浏览顾客信箱"

#: views/capability/wcfm-view-capability.php:419
#: views/orders/wcfm-view-orders.php:84 views/orders/wcfm-view-orders.php:104
msgid "Billing Address"
msgstr "配送地址"

#: views/capability/wcfm-view-capability.php:420
#: views/orders/wcfm-view-orders.php:85 views/orders/wcfm-view-orders.php:105
msgid "Shipping Address"
msgstr "配送地址"

#: views/capability/wcfm-view-capability.php:424
msgid "Customer Limit"
msgstr "顾客限制"

#: views/capability/wcfm-view-capability.php:448
msgid "Advanced Capability"
msgstr "进阶权限"

#: views/capability/wcfm-view-capability.php:465
#: views/capability/wcfm-view-capability.php:475
msgid "Shop Managers Capability"
msgstr "商店管理权限"

#: views/capability/wcfm-view-capability.php:487
#: views/capability/wcfm-view-capability.php:497
msgid "Shop Staffs Capability"
msgstr "商店员工权限"

#: views/capability/wcfm-view-capability.php:506
msgid "*** Vendor Managers are treated as Shop Staff for a Vendor Store."
msgstr ""

#: views/capability/wcfm-view-capability.php:518
#: views/profile/wcfm-view-profile.php:339
#: views/settings/wcfm-view-dokan-settings.php:511
#: views/settings/wcfm-view-settings.php:528
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:738
#: views/settings/wcfm-view-wcmarketplace-settings.php:766
#: views/settings/wcfm-view-wcpvendors-settings.php:459
#: views/settings/wcfm-view-wcvendors-settings.php:464
msgid "Save"
msgstr "储存"

#: views/coupons/wcfm-view-coupons-manage.php:55
msgid "Manage Coupon"
msgstr "管理优惠券"

#: views/coupons/wcfm-view-coupons-manage.php:62
msgid "Edit Coupon"
msgstr "编辑优惠券"

#: views/coupons/wcfm-view-coupons-manage.php:62
msgid "Add Coupon"
msgstr "新增优惠券"

#: views/coupons/wcfm-view-coupons-manage.php:77
#: views/coupons/wcfm-view-coupons.php:43
msgid "Add New Coupon"
msgstr "新增新优惠券"

#: views/coupons/wcfm-view-coupons-manage.php:92
#: views/coupons/wcfm-view-coupons.php:55
#: views/coupons/wcfm-view-coupons.php:65
msgid "Code"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:94
msgid "Discount Type"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:94
msgid "Percentage discount"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:94
msgid "Fixed Cart Discount"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:94
msgid "Fixed Product Discount"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:95
msgid "Coupon Amount"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:96
msgid "Coupon expiry date"
msgstr ""

#: views/coupons/wcfm-view-coupons-manage.php:100
msgid "Allow free shipping"
msgstr "许可宅配免运费"

#: views/coupons/wcfm-view-coupons-manage.php:100
msgid ""
"Check this box if the coupon grants free shipping. The free shipping method "
"must be enabled and be set to require \"a valid free shipping coupon\" (see "
"the \"Free Shipping Requires\" setting)."
msgstr ""

#: views/coupons/wcfm-view-coupons.php:24
msgid "Coupons Listing"
msgstr ""

#: views/coupons/wcfm-view-coupons.php:35
#: views/listings/wcfm-view-listings.php:82
#: views/orders/wcfm-view-orders.php:64
#: views/products/wcfm-view-products.php:72
#: views/wc_bookings/wcfm-view-wcbookings.php:63
msgid "Screen Manager"
msgstr ""

#: views/coupons/wcfm-view-coupons.php:58
#: views/coupons/wcfm-view-coupons.php:68
msgid "Usage Limit"
msgstr ""

#: views/coupons/wcfm-view-coupons.php:59
#: views/coupons/wcfm-view-coupons.php:69
msgid "Expiry date"
msgstr ""

#: views/coupons/wcfm-view-coupons.php:60
#: views/coupons/wcfm-view-coupons.php:70
#: views/listings/wcfm-view-listings.php:110
#: views/listings/wcfm-view-listings.php:122
#: views/vendors/wcfm-view-vendors.php:91
#: views/vendors/wcfm-view-vendors.php:112
msgid "Action"
msgstr ""

#: views/customers/wcfm-view-customers-details.php:48
msgid "Customer Details"
msgstr "顾客明细"

#: views/customers/wcfm-view-customers-details.php:107
#: views/customers/wcfm-view-customers-manage.php:113
#: views/customers/wcfm-view-customers.php:42
msgid "Add New Customer"
msgstr "新增新顾客"

#: views/customers/wcfm-view-customers-details.php:128
msgid "total money spent"
msgstr ""

#: views/customers/wcfm-view-customers-details.php:140
#, php-format
msgid "<strong>%s order</strong><br />"
msgid_plural "<strong>%s orders</strong><br />"
msgstr[0] "<strong>%s 订单</strong><br />"

#: views/customers/wcfm-view-customers-details.php:142
msgid "total order placed"
msgstr "总下单数"

#: views/customers/wcfm-view-customers-details.php:160
#: views/customers/wcfm-view-customers-manage.php:136
#: views/customers/wcfm-view-customers.php:61
#: views/customers/wcfm-view-customers.php:76
#: views/enquiry/wcfm-view-enquiry-form.php:60
#: views/profile/wcfm-view-profile.php:195
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:686
#: views/settings/wcfm-view-wcmarketplace-settings.php:714
#: views/vendors/wcfm-view-vendors-manage.php:284
#: views/vendors/wcfm-view-vendors-new.php:92
msgid "Email"
msgstr "信箱"

#: views/customers/wcfm-view-customers-details.php:161
#: views/customers/wcfm-view-customers-manage.php:137
#: views/customers/wcfm-view-customers-manage.php:159
#: views/customers/wcfm-view-customers-manage.php:177
#: views/profile/wcfm-view-profile.php:193
#: views/profile/wcfm-view-profile.php:274
#: views/profile/wcfm-view-profile.php:290
#: views/vendors/wcfm-view-vendors-manage.php:285
#: views/vendors/wcfm-view-vendors-manage.php:328
#: views/vendors/wcfm-view-vendors-new.php:93
#: views/vendors/wcfm-view-vendors-new.php:116
#: views/vendors/wcfm-view-vendors-new.php:134
msgid "First Name"
msgstr "姓名"

#: views/customers/wcfm-view-customers-details.php:162
#: views/customers/wcfm-view-customers-manage.php:138
#: views/customers/wcfm-view-customers-manage.php:160
#: views/customers/wcfm-view-customers-manage.php:178
#: views/profile/wcfm-view-profile.php:194
#: views/profile/wcfm-view-profile.php:275
#: views/profile/wcfm-view-profile.php:291
#: views/vendors/wcfm-view-vendors-manage.php:286
#: views/vendors/wcfm-view-vendors-manage.php:329
#: views/vendors/wcfm-view-vendors-new.php:94
#: views/vendors/wcfm-view-vendors-new.php:117
#: views/vendors/wcfm-view-vendors-new.php:135
msgid "Last Name"
msgstr "姓氏"

#: views/customers/wcfm-view-customers-details.php:187
#: views/customers/wcfm-view-customers-details.php:198
#: views/wc_bookings/wcfm-view-wcbookings-details.php:137
#: views/wc_bookings/wcfm-view-wcbookings.php:115
#: views/wc_bookings/wcfm-view-wcbookings.php:127
msgid "Booking"
msgstr ""

#: views/customers/wcfm-view-customers-details.php:190
#: views/customers/wcfm-view-customers-details.php:201
#: views/wc_bookings/wcfm-view-wcbookings.php:118
#: views/wc_bookings/wcfm-view-wcbookings.php:130
msgid "Start Date"
msgstr "开始日期"

#: views/customers/wcfm-view-customers-details.php:191
#: views/customers/wcfm-view-customers-details.php:202
#: views/wc_bookings/wcfm-view-wcbookings.php:119
#: views/wc_bookings/wcfm-view-wcbookings.php:131
msgid "End Date"
msgstr "结束日期"

#: views/customers/wcfm-view-customers-details.php:264
#: views/customers/wcfm-view-customers-details.php:274
#: views/orders/wcfm-view-orders.php:83 views/orders/wcfm-view-orders.php:103
msgid "Purchased"
msgstr "已购买"

#: views/customers/wcfm-view-customers-manage.php:94
msgid "Manage Customer"
msgstr "管理顾客"

#: views/customers/wcfm-view-customers-manage.php:151
#: views/orders/wcfm-view-orders-details.php:292
#: views/orders/wcfm-view-orders-details.php:294
#: views/orders/wcfm-view-orders-details.php:331
#: views/orders/wcfm-view-orders-details.php:333
#: views/profile/wcfm-view-profile.php:267
#: views/vendors/wcfm-view-vendors-new.php:108
msgid "Address"
msgstr "地址"

#: views/customers/wcfm-view-customers-manage.php:156
#: views/profile/wcfm-view-profile.php:271
#: views/vendors/wcfm-view-vendors-new.php:113
msgid "Billing"
msgstr "帐单"

#: views/customers/wcfm-view-customers-manage.php:161
#: views/profile/wcfm-view-profile.php:227
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:685
#: views/settings/wcfm-view-wcmarketplace-settings.php:713
#: views/vendors/wcfm-view-vendors-new.php:118
msgid "Phone"
msgstr ""

#: views/customers/wcfm-view-customers-manage.php:162
#: views/customers/wcfm-view-customers-manage.php:179
#: views/profile/wcfm-view-profile.php:276
#: views/profile/wcfm-view-profile.php:292
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:687
#: views/settings/wcfm-view-wcmarketplace-settings.php:229
#: views/settings/wcfm-view-wcmarketplace-settings.php:715
#: views/settings/wcfm-view-wcvendors-settings.php:219
#: views/settings/wcfm-view-wcvendors-settings.php:408
#: views/vendors/wcfm-view-vendors-new.php:119
#: views/vendors/wcfm-view-vendors-new.php:136
msgid "Address 1"
msgstr "地址 1"

#: views/customers/wcfm-view-customers-manage.php:163
#: views/customers/wcfm-view-customers-manage.php:180
#: views/profile/wcfm-view-profile.php:277
#: views/profile/wcfm-view-profile.php:293
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:688
#: views/settings/wcfm-view-wcmarketplace-settings.php:230
#: views/settings/wcfm-view-wcmarketplace-settings.php:716
#: views/settings/wcfm-view-wcvendors-settings.php:220
#: views/settings/wcfm-view-wcvendors-settings.php:409
#: views/vendors/wcfm-view-vendors-new.php:120
#: views/vendors/wcfm-view-vendors-new.php:137
msgid "Address 2"
msgstr "地址 2"

#: views/customers/wcfm-view-customers-manage.php:164
#: views/customers/wcfm-view-customers-manage.php:181
#: views/profile/wcfm-view-profile.php:278
#: views/profile/wcfm-view-profile.php:294
#: views/settings/wcfm-view-dokan-settings.php:204
#: views/settings/wcfm-view-dokan-settings.php:416
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:256
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:689
#: views/settings/wcfm-view-wcmarketplace-settings.php:231
#: views/settings/wcfm-view-wcmarketplace-settings.php:717
#: views/settings/wcfm-view-wcvendors-settings.php:221
#: views/settings/wcfm-view-wcvendors-settings.php:382
#: views/settings/wcfm-view-wcvendors-settings.php:410
#: views/vendors/wcfm-view-vendors-new.php:121
#: views/vendors/wcfm-view-vendors-new.php:138
msgid "Country"
msgstr "国家"

#: views/customers/wcfm-view-customers-manage.php:165
#: views/customers/wcfm-view-customers-manage.php:182
#: views/profile/wcfm-view-profile.php:279
#: views/profile/wcfm-view-profile.php:295
#: views/settings/wcfm-view-dokan-settings.php:202
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:254
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:690
#: views/settings/wcfm-view-wcmarketplace-settings.php:233
#: views/settings/wcfm-view-wcmarketplace-settings.php:718
#: views/settings/wcfm-view-wcvendors-settings.php:222
#: views/settings/wcfm-view-wcvendors-settings.php:411
#: views/vendors/wcfm-view-vendors-new.php:122
#: views/vendors/wcfm-view-vendors-new.php:139
msgid "City/Town"
msgstr "城市 / 乡镇"

#: views/customers/wcfm-view-customers-manage.php:166
#: views/customers/wcfm-view-customers-manage.php:183
#: views/profile/wcfm-view-profile.php:280
#: views/profile/wcfm-view-profile.php:296
#: views/settings/wcfm-view-dokan-settings.php:205
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:257
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:691
#: views/settings/wcfm-view-wcmarketplace-settings.php:232
#: views/settings/wcfm-view-wcmarketplace-settings.php:719
#: views/settings/wcfm-view-wcvendors-settings.php:223
#: views/settings/wcfm-view-wcvendors-settings.php:412
#: views/vendors/wcfm-view-vendors-new.php:123
#: views/vendors/wcfm-view-vendors-new.php:140
msgid "State/County"
msgstr "国家"

#: views/customers/wcfm-view-customers-manage.php:167
#: views/customers/wcfm-view-customers-manage.php:184
#: views/profile/wcfm-view-profile.php:281
#: views/profile/wcfm-view-profile.php:297
#: views/settings/wcfm-view-dokan-settings.php:203
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:255
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:692
#: views/settings/wcfm-view-wcmarketplace-settings.php:234
#: views/settings/wcfm-view-wcmarketplace-settings.php:720
#: views/settings/wcfm-view-wcvendors-settings.php:224
#: views/settings/wcfm-view-wcvendors-settings.php:413
#: views/vendors/wcfm-view-vendors-new.php:124
#: views/vendors/wcfm-view-vendors-new.php:141
msgid "Postcode/Zip"
msgstr "邮递区号"

#: views/customers/wcfm-view-customers.php:62
#: views/customers/wcfm-view-customers.php:77
msgid "Location"
msgstr "地区"

#: views/customers/wcfm-view-customers.php:66
#: views/customers/wcfm-view-customers.php:81
msgid "Money Spent"
msgstr "已付金额"

#: views/customers/wcfm-view-customers.php:67
#: views/customers/wcfm-view-customers.php:82
msgid "Last Order"
msgstr "最新订单"

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:48
#, php-format
msgid "Welcome to %s Dashboard"
msgstr "欢迎来到 %s 管理后台"

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:62
msgid "Last Login:"
msgstr "最后登入："

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:69
#: views/vendors/wcfm-view-vendors.php:80
#: views/vendors/wcfm-view-vendors.php:101
msgid "Product Limit Stats"
msgstr "商品限制统计"

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:70
#: views/vendors/wcfm-view-vendors.php:81
#: views/vendors/wcfm-view-vendors.php:102
msgid "Disk Space Usage Stats"
msgstr "空间使用统计"

#: views/dashboard/wcfm-view-dashboard.php:115
msgid "gross sales in last 7 days"
msgstr "最近七天销售额"

#: views/dashboard/wcfm-view-dashboard.php:146
msgid "admin fees in last 7 days"
msgstr "最近七天管理费用"

#: views/dashboard/wcfm-view-dashboard.php:146
msgid "commission in last 7 days"
msgstr "最近七天的款项"

#: views/dashboard/wcfm-view-dashboard.php:154
#: views/dashboard/wcfm-view-dokan-dashboard.php:161
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:163
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:181
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:185
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:168
#, php-format
msgid "<strong>%s item</strong>"
msgid_plural "<strong>%s items</strong>"
msgstr[0] ""

#: views/dashboard/wcfm-view-dashboard.php:155
msgid "sold in last 7 days"
msgstr "最近七天销售量"

#: views/dashboard/wcfm-view-dashboard.php:167
#: views/dashboard/wcfm-view-dokan-dashboard.php:172
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:174
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:192
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:196
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:179
#, php-format
msgid "<strong>%s order</strong>"
msgid_plural "<strong>%s orders</strong>"
msgstr[0] ""

#: views/dashboard/wcfm-view-dashboard.php:168
msgid "received in last 7 days"
msgstr "最近七天接单量"

#: views/dashboard/wcfm-view-dashboard.php:228
#: views/dashboard/wcfm-view-dokan-dashboard.php:232
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:235
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:253
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:256
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:239
msgid "Product Stats"
msgstr "商品统计"

#: views/dashboard/wcfm-view-dashboard.php:250
#: views/dashboard/wcfm-view-dokan-dashboard.php:252
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:255
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:273
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:276
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:259
msgid "Store Stats"
msgstr "商店统计"

#: views/dashboard/wcfm-view-dashboard.php:261
#: views/dashboard/wcfm-view-dokan-dashboard.php:264
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:267
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:285
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:271
#, php-format
msgid "%s top seller in last 7 days (sold %d)"
msgstr "%s 最近七天的热门销售 ( 销售量：%d )"

#: views/dashboard/wcfm-view-dashboard.php:274
#: views/dashboard/wcfm-view-dokan-dashboard.php:278
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:281
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:299
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:307
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:285
#, php-format
msgid "<strong>%s order</strong> - processing"
msgid_plural "<strong>%s orders</strong> - processing"
msgstr[0] "<strong>%s 订单</strong> - 处理中"

#: views/dashboard/wcfm-view-dashboard.php:280
#, php-format
msgid "<strong>%s order</strong> - on-hold"
msgid_plural "<strong>%s orders</strong> - on-hold"
msgstr[0] "<strong>%s 订单</strong> - 搁置中"

#: views/dashboard/wcfm-view-dashboard.php:290
#: views/dashboard/wcfm-view-dokan-dashboard.php:297
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:300
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:318
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:326
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:304
#, php-format
msgid "<strong>%s product</strong> - low in stock"
msgid_plural "<strong>%s products</strong> - low in stock"
msgstr[0] "<strong>%s 商品</strong> -低于库存"

#: views/dashboard/wcfm-view-dashboard.php:296
#: views/dashboard/wcfm-view-dokan-dashboard.php:303
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:306
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:324
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:332
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:310
#, php-format
msgid "<strong>%s product</strong> - out of stock"
msgid_plural "<strong>%s products</strong> - out of stock"
msgstr[0] "<strong>%s 商品</strong> - 已售完"

#: views/dashboard/wcfm-view-dashboard.php:317
#: views/dashboard/wcfm-view-dokan-dashboard.php:324
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:328
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:346
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:353
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:331
msgid "Sales by Product"
msgstr "商品销售"

#: views/dashboard/wcfm-view-dashboard.php:332
#: views/dashboard/wcfm-view-dokan-dashboard.php:340
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:344
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:362
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:369
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:347
msgid "Top Regions"
msgstr "热门地区"

#: views/dashboard/wcfm-view-dashboard.php:352
#: views/dashboard/wcfm-view-dokan-dashboard.php:360
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:364
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:382
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:389
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:367
msgid "Latest Topics"
msgstr "最新主题"

#: views/dashboard/wcfm-view-dashboard.php:374
#: views/dashboard/wcfm-view-dokan-dashboard.php:382
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:386
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:404
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:411
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:389
msgid "There is no topic yet!!"
msgstr "目前没有主题！"

#: views/dashboard/wcfm-view-dokan-dashboard.php:143
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:145
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:163
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:167
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:150
#: views/vendors/wcfm-view-vendors-manage.php:210
msgid "gross sales in this month"
msgstr "本月销售额"

#: views/dashboard/wcfm-view-dokan-dashboard.php:153
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:155
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:173
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:177
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:160
#: views/vendors/wcfm-view-vendors-manage.php:228
msgid "earnings in this month"
msgstr "本月款项"

#: views/dashboard/wcfm-view-dokan-dashboard.php:162
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:164
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:182
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:186
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:169
#: views/vendors/wcfm-view-vendors-manage.php:257
msgid "sold in this month"
msgstr "本月销售量"

#: views/dashboard/wcfm-view-dokan-dashboard.php:173
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:175
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:193
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:197
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:180
msgid "received in this month"
msgstr "本月接单量"

#: views/dashboard/wcfm-view-dokan-dashboard.php:285
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:288
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:306
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:314
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:292
#, php-format
msgid "<strong>%s product</strong> - awaiting fulfillment"
msgid_plural "<strong>%s products</strong> - awaiting fulfillment"
msgstr[0] "<strong>%s 商品</strong> - 等待完成"

#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:155
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:173
#: views/vendors/wcfm-view-vendors-manage.php:228
msgid "admin fees in this month"
msgstr "本月管理费用"

#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:296
#, php-format
msgid "%s top seller this month (sold %d)"
msgstr "%s 本月热门销售 ( 销售量：%d )"

#: views/enquiry/wcfm-view-enquiry-form.php:45
msgid "Your email address will not be published."
msgstr "您的信箱不会被公开。"

#: views/enquiry/wcfm-view-enquiry-form.php:49
msgid "Your enquiry"
msgstr "您的问题"

#: views/enquiry/wcfm-view-enquiry-manage.php:65
msgid "Manage Enquiry"
msgstr "管理问题"

#: views/enquiry/wcfm-view-enquiry-manage.php:72
msgid "Edit Enquiry"
msgstr "编辑问题"

#: views/enquiry/wcfm-view-enquiry-manage.php:72
msgid "Add Enquiry"
msgstr "新增问题"

#: views/enquiry/wcfm-view-enquiry-manage.php:76
msgid "View Product"
msgstr "浏览商品"

#: views/enquiry/wcfm-view-enquiry-manage.php:169
msgid "Stick at Product Page"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:169
msgid "Enable to stick this reply at product page"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:182
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:162
#: views/messages/wcfm-view-messages.php:77
#: views/notice/wcfm-view-notice-view.php:170
#: views/vendors/wcfm-view-vendors-manage.php:402
msgid "Send"
msgstr "送出"

#: views/enquiry/wcfm-view-enquiry-tab.php:31
msgid "General Enquiries"
msgstr "一般问题"

#: views/enquiry/wcfm-view-enquiry-tab.php:36
msgid "There are no enquiries yet."
msgstr "目前没有问题。"

#: views/enquiry/wcfm-view-enquiry-tab.php:73
msgid "Reply by"
msgstr ""

#: views/enquiry/wcfm-view-enquiry.php:88
#: views/enquiry/wcfm-view-enquiry.php:100
#: views/enquiry/wcfm-view-my-account-enquiry.php:41
#: views/enquiry/wcfm-view-my-account-enquiry.php:50
#: views/enquiry/wcfm-view-my-account-enquiry.php:65
msgid "Query"
msgstr "问题"

#: views/enquiry/wcfm-view-enquiry.php:93
#: views/enquiry/wcfm-view-enquiry.php:105
msgid "Reply"
msgstr "回复"

#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:95
#: views/notice/wcfm-view-notice-view.php:104
msgid "Replies"
msgstr "回复数"

#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:142
#: views/notice/wcfm-view-notice-view.php:152
msgid "New Reply"
msgstr "新回复"

#: views/enquiry/wcfm-view-my-account-enquiry.php:66
msgid "You do not have any enquiry yet!"
msgstr ""

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:56
msgid "Manage Knowledgebase"
msgstr "管理知识库"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:63
msgid "Edit Knowledgebase"
msgstr "编辑知识库"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:63
msgid "Add Knowledgebase"
msgstr "新增知识库"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:81
#: views/knowledgebase/wcfm-view-knowledgebase.php:71
#: views/knowledgebase/wcfm-view-knowledgebase.php:78
#: views/notice/wcfm-view-notice-manage.php:78
#: views/notice/wcfm-view-notices.php:51 views/notice/wcfm-view-notices.php:57
#: views/products-manager/wcfm-view-thirdparty-products-manage.php:164
msgid "Title"
msgstr "标题"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:82
msgid "Details"
msgstr "明细"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:100
#: views/products-manager/wcfm-view-products-manage.php:619
msgid "Add new category"
msgstr "新增新分类"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:106
#: views/products-manager/wcfm-view-products-manage.php:625
msgid "-- Parent category --"
msgstr "-- 上层分类 --"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:116
#: views/products-manager/wcfm-view-products-manage-tabs.php:154
#: views/products-manager/wcfm-view-products-manage.php:635
#: views/products-manager/wcfm-view-products-manage.php:695
msgid "Add"
msgstr "新增"

#: views/knowledgebase/wcfm-view-knowledgebase.php:33
msgid "Guidelines for Store Users"
msgstr "商店卖家新手指南"

#: views/knowledgebase/wcfm-view-knowledgebase.php:38
msgid "Add New Knowledgebase"
msgstr "新增新知识库"

#: views/knowledgebase/wcfm-view-knowledgebase.php:57
#: views/products/wcfm-view-products.php:136
msgid "Show all category"
msgstr "显示全部分类"

#: views/knowledgebase/wcfm-view-knowledgebase.php:72
#: views/knowledgebase/wcfm-view-knowledgebase.php:79
msgid "Category"
msgstr "分类"

#: views/listings/wcfm-view-listings.php:103
#: views/listings/wcfm-view-listings.php:115
msgid "Listing"
msgstr "项目"

#: views/messages/wcfm-view-messages.php:36
msgid "Notification Dashboard"
msgstr "通知讯息管理后台"

#: views/messages/wcfm-view-messages.php:48
msgid "To Store Admin"
msgstr "给商店管理员"

#: views/messages/wcfm-view-messages.php:48
msgid "To Store Vendors"
msgstr "给商店供应商"

#: views/messages/wcfm-view-messages.php:71
msgid "Direct TO:"
msgstr "寄送到："

#: views/messages/wcfm-view-messages.php:101
msgid "Only Unread"
msgstr "限未读"

#: views/messages/wcfm-view-messages.php:102
msgid "Only Read"
msgstr "限已读"

#: views/messages/wcfm-view-messages.php:119
#: views/messages/wcfm-view-messages.php:132
msgid "Select all for mark as read"
msgstr "选择全部标示已读"

#: views/messages/wcfm-view-messages.php:122
#: views/messages/wcfm-view-messages.php:135
msgid "Message"
msgstr "讯息"

#: views/notice/wcfm-view-notice-manage.php:47
msgid "Manage Topic"
msgstr "讯息主题"

#: views/notice/wcfm-view-notice-manage.php:55
#: views/notice/wcfm-view-notice-view.php:59
msgid "Edit Topic"
msgstr "编辑主题"

#: views/notice/wcfm-view-notice-manage.php:55
msgid "Add Topic"
msgstr "新增主题"

#: views/notice/wcfm-view-notice-manage.php:58
#: views/notice/wcfm-view-notice-view.php:57
#: views/notice/wcfm-view-notices.php:33
msgid "Topics"
msgstr "主题"

#: views/notice/wcfm-view-notice-manage.php:59
msgid "View Topic"
msgstr "浏览主题"

#: views/notice/wcfm-view-notice-manage.php:79
msgid "Allow Reply"
msgstr "许可回复"

#: views/notice/wcfm-view-notice-manage.php:80
msgid "Close for New Reply"
msgstr "关闭新回复"

#: views/notice/wcfm-view-notice-manage.php:81
#: views/products-manager/wcfm-view-thirdparty-products-manage.php:165
#: views/products-popup/wcfm-view-product-popup.php:251
msgid "Content"
msgstr "内容"

#: views/notice/wcfm-view-notice-view.php:47
msgid "Topic"
msgstr "主题"

#: views/notice/wcfm-view-notices.php:37
msgid "Add New Topic"
msgstr "新增主题"

#: views/orders/wcfm-view-orders-details-fedex-dhl-express.php:49
msgid "Fedex"
msgstr ""

#: views/orders/wcfm-view-orders-details-fedex-dhl-express.php:163
msgid "DHL Express"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:70
#: views/products-manager/wcfm-view-products-manage.php:355
#: views/products-manager/wcfm-view-products-manage.php:356
#: views/products-popup/wcfm-view-product-popup.php:68
#: views/products-popup/wcfm-view-product-popup.php:69
msgid "Standard"
msgstr "标准"

#: views/orders/wcfm-view-orders-details.php:122
msgid "Order #"
msgstr "订单 #"

#: views/orders/wcfm-view-orders-details.php:137
msgid "CSV Export"
msgstr "CSV 导出"

#: views/orders/wcfm-view-orders-details.php:166
msgid "Order date:"
msgstr "订单日期："

#: views/orders/wcfm-view-orders-details.php:173
msgid "Order status:"
msgstr "订单状态："

#: views/orders/wcfm-view-orders-details.php:179
msgid "Customer payment page"
msgstr "顾客付款页面"

#: views/orders/wcfm-view-orders-details.php:196
#: views/vendors/wcfm-view-vendors-manage.php:344
#: views/wc_bookings/wcfm-view-wcbookings-details.php:123
msgid "Update"
msgstr "更新"

#: views/orders/wcfm-view-orders-details.php:208
msgid "Customer:"
msgstr "顾客："

#: views/orders/wcfm-view-orders-details.php:216
msgid "View other orders"
msgstr "浏览其他订单"

#: views/orders/wcfm-view-orders-details.php:240
#, php-format
msgid "<label for=\"order_payment_via\">Payment via: </label> %s"
msgstr "<label for=\"order_payment_via\">付款方式：</label> %s"

#: views/orders/wcfm-view-orders-details.php:259
msgid "Customer IP"
msgstr "顾客 IP"

#: views/orders/wcfm-view-orders-details.php:271
msgid "Billing Details"
msgstr "帐单明细"

#: views/orders/wcfm-view-orders-details.php:277
msgid "Shipping Details"
msgstr "配送明细"

#: views/orders/wcfm-view-orders-details.php:294
msgid "No billing address set."
msgstr "没有帐单地址。"

#: views/orders/wcfm-view-orders-details.php:333
msgid "No shipping address set."
msgstr "没有宅配地址。"

#: views/orders/wcfm-view-orders-details.php:358
msgid "Customer Provided Note"
msgstr "顾客备注"

#: views/orders/wcfm-view-orders-details.php:377
msgid "Order Items"
msgstr "订单项目"

#: views/orders/wcfm-view-orders-details.php:384
msgid "Item"
msgstr "项目"

#: views/orders/wcfm-view-orders-details.php:386
#: views/settings/wcfm-view-dokan-settings.php:417
#: views/settings/wcfm-view-dokan-settings.php:420
msgid "Cost"
msgstr "金额"

#: views/orders/wcfm-view-orders-details.php:387
msgid "Qty"
msgstr "数量"

#: views/orders/wcfm-view-orders-details.php:441
msgid "SKU:"
msgstr "SKU："

#: views/orders/wcfm-view-orders-details.php:445
msgid "Variation ID:"
msgstr "规格商品 ID："

#: views/orders/wcfm-view-orders-details.php:449
msgid "No longer exists"
msgstr "不存在"

#: views/orders/wcfm-view-orders-details.php:714
#: views/withdrawal/wcmp/wcfm-view-payments.php:66
#: views/withdrawal/wcmp/wcfm-view-payments.php:77
msgid "Fee"
msgstr "费用"

#: views/orders/wcfm-view-orders-details.php:828
msgid "Coupon(s) Used"
msgstr ""

#: views/orders/wcfm-view-orders-details.php:845
msgid "This is the total discount. Discounts are defined per line item."
msgstr ""

#: views/orders/wcfm-view-orders-details.php:845
msgid "Discount"
msgstr "折扣"

#: views/orders/wcfm-view-orders-details.php:857
msgid "This is the shipping and handling total costs for the order."
msgstr ""

#: views/orders/wcfm-view-orders-details.php:897
msgid "Order Total"
msgstr "订单总计"

#: views/orders/wcfm-view-orders.php:40
msgid "Orders Listing"
msgstr "订单列表"

#: views/orders/wcfm-view-orders.php:46
msgid "Show all dates"
msgstr "显示所有日期"

#: views/orders/wcfm-view-orders.php:90 views/orders/wcfm-view-orders.php:110
#: views/vendors/wcfm-view-vendors.php:87
#: views/vendors/wcfm-view-vendors.php:108
msgid "Earnings"
msgstr "货款"

#: views/products/wcfm-view-products-export.php:64
#: views/products/wcfm-view-products.php:115
#: views/products-manager/wcfm-view-products-manage.php:441
#: views/products-popup/wcfm-view-product-popup-button.php:2
msgid "Add New Product"
msgstr "新增新的商品"

#: views/products/wcfm-view-products.php:104
#: views/products/wcfm-view-products.php:109
msgid "Stock Manager"
msgstr "库存管理"

#: views/products/wcfm-view-products.php:147
msgid "Show all product types"
msgstr "显示全部商品类型"

#: views/products/wcfm-view-products.php:192
#: views/products/wcfm-view-products.php:215
msgid "Select all for bulk edit"
msgstr "选择全部批量编辑"

#: views/products/wcfm-view-products.php:199
#: views/products/wcfm-view-products.php:222
#: views/products-manager/wcfm-view-products-manage-tabs.php:23
#: views/products-manager/wcfm-view-products-manage-tabs.php:249
msgid "SKU"
msgstr "SKU"

#: views/products/wcfm-view-products.php:201
#: views/products/wcfm-view-products.php:224
#: views/products-manager/wcfm-view-products-manage-tabs.php:209
msgid "Stock"
msgstr "库存"

#: views/products/wcfm-view-products.php:202
#: views/products/wcfm-view-products.php:225
#: views/products-manager/wcfm-view-products-manage.php:470
#: views/products-popup/wcfm-view-product-popup.php:129
msgid "Price"
msgstr "售价"

#: views/products-manager/wcfm-view-customfield-products-manage.php:130
msgid "-Select-"
msgstr "-选择-"

#: views/products-manager/wcfm-view-epeken-products-manage.php:42
msgid "Epeken Product Config"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:23
msgid ""
"SKU refers to a Stock-keeping unit, a unique identifier for each distinct "
"product and service that can be purchased."
msgstr ""
"在连锁零售门店中有时称单品为一个 SKU ( 中文译为最小存货单位，英文全称为 "
"Stock Keeping Unit，简称 SKU，定义为保存库存控制的最小可用单位，例如纺织品中"
"一个 SKU 通常表示规格、颜色、款式 )。"

#: views/products-manager/wcfm-view-products-manage-tabs.php:24
msgid "Manage Stock?"
msgstr "管理库存？"

#: views/products-manager/wcfm-view-products-manage-tabs.php:24
msgid "Enable stock management at product level"
msgstr "启用商品的库存管理功能"

#: views/products-manager/wcfm-view-products-manage-tabs.php:25
#: views/products-manager/wcfm-view-products-manage-tabs.php:247
msgid "Stock Qty"
msgstr "库存数量"

#: views/products-manager/wcfm-view-products-manage-tabs.php:25
msgid ""
"Stock quantity. If this is a variable product this value will be used to "
"control stock for all variations, unless you define stock at variation level."
msgstr ""
"如果您新增的是规格商品，则这个库存数量的值，将自动设定到规格商品每个组合选项"
"的库存数量，当然您可以在规格商品的每个组合选项，设定个别的库存数量。"

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
msgid "Allow Backorders?"
msgstr "允许延期交货？"

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
#: views/products-manager/wcfm-view-products-manage-tabs.php:248
msgid "Do not Allow"
msgstr "不允许"

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
#: views/products-manager/wcfm-view-products-manage-tabs.php:248
msgid "Allow, but notify customer"
msgstr "允许，但通知顾客"

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
#: views/products-manager/wcfm-view-products-manage-tabs.php:248
msgid "Allow"
msgstr "允许"

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
msgid ""
"If managing stock, this controls whether or not backorders are allowed. If "
"enabled, stock quantity can go below 0."
msgstr "如果管理库存，允许延期交货是否被允许。如果启用，库存数量可以是 0。"

#: views/products-manager/wcfm-view-products-manage-tabs.php:27
#: views/products-manager/wcfm-view-products-manage-tabs.php:250
msgid "Stock status"
msgstr "库存状态"

#: views/products-manager/wcfm-view-products-manage-tabs.php:27
msgid ""
"Controls whether or not the product is listed as \"in stock\" or \"out of "
"stock\" on the frontend."
msgstr "在卖家管理后台控制显示列出商品是有库存，还是已售完状态。"

#: views/products-manager/wcfm-view-products-manage-tabs.php:28
msgid "Sold Individually"
msgstr "单件出售"

#: views/products-manager/wcfm-view-products-manage-tabs.php:28
msgid ""
"Enable this to only allow one of this item to be bought in a single order"
msgstr "在单一订单中限制这个项目只能购买一个"

#: views/products-manager/wcfm-view-products-manage-tabs.php:66
msgid "Grouped Products"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:71
msgid "Grouped products"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:71
msgid "This lets you choose which products are part of this group."
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:90
msgid "Dimensions"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:93
msgid "Shipping class"
msgstr "配送类别"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgid "Tax Status"
msgstr "税款类别"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgid "Taxable"
msgstr "应税"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgid "Shipping only"
msgstr "限配送"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgctxt "Tax status"
msgid "None"
msgstr "免税"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgid ""
"Define whether or not the entire product is taxable, or just the cost of "
"shipping it."
msgstr ""
"目前超级商城的免税商品为：金饰/未经加工之农产品/部份类别杂誌/教科书等。免税营"
"业税的相关规定及详情请至财政部税务网站查询确认。"

#: views/products-manager/wcfm-view-products-manage-tabs.php:115
msgid "Tax Class"
msgstr "税制"

#: views/products-manager/wcfm-view-products-manage-tabs.php:115
msgid ""
"Choose a tax class for this product. Tax classes are used to apply different "
"tax rates specific to certain types of product."
msgstr "选择商品的税制，税制是根据商品不同的税款利率。"

#: views/products-manager/wcfm-view-products-manage-tabs.php:140
#, php-format
msgid "Enter some text, some attributes by \"%s\" separating values."
msgstr "输入规格选项名称，用逗号 \"%s\" 间隔。例如：大,中,小"

#: views/products-manager/wcfm-view-products-manage-tabs.php:152
msgid "Add attribute"
msgstr "新增规格"

#: views/products-manager/wcfm-view-products-manage-tabs.php:168
msgid "Variations"
msgstr "规格组合"

#: views/products-manager/wcfm-view-products-manage-tabs.php:171
#, php-format
msgid ""
"Before you can add a variation you need to add some variation attributes on "
"the Attributes tab. %sLearn more%s"
msgstr ""
"在您新增商品的规格组合选项之前，您必须在规格选项区，新增商品的规格选项。"

#: views/products-manager/wcfm-view-products-manage-tabs.php:176
msgid "Default Form Values:"
msgstr "预设选项值："

#: views/products-manager/wcfm-view-products-manage-tabs.php:181
#: views/products-manager/wcfm-view-products-manage-tabs.php:182
msgid "Variations Bulk Options"
msgstr "规格组合批量选项"

#: views/products-manager/wcfm-view-products-manage-tabs.php:184
msgid "Choose option"
msgstr "选择选项"

#: views/products-manager/wcfm-view-products-manage-tabs.php:186
msgid "Enable all Variations"
msgstr "启用全部规格组合选项"

#: views/products-manager/wcfm-view-products-manage-tabs.php:187
msgid "Disable all Variations"
msgstr "停用全部规格组合选项"

#: views/products-manager/wcfm-view-products-manage-tabs.php:189
msgid "Set variations \"Downloadable\""
msgstr "设定规格组合选项 \"可下载\""

#: views/products-manager/wcfm-view-products-manage-tabs.php:190
msgid "Set variations \"Non-Downloadable\""
msgstr "设定规格组合选项 \"不可下载\""

#: views/products-manager/wcfm-view-products-manage-tabs.php:193
msgid "Set variations \"Virtual\""
msgstr "设定规格组合选项 \"虚拟商品\""

#: views/products-manager/wcfm-view-products-manage-tabs.php:194
msgid "Set variations \"Non-Virtual\""
msgstr "设定规格组合选项 \"非虚拟商品\""

#: views/products-manager/wcfm-view-products-manage-tabs.php:197
msgid "Pricing"
msgstr "售价"

#: views/products-manager/wcfm-view-products-manage-tabs.php:198
msgid "Regular prices"
msgstr "固定售价"

#: views/products-manager/wcfm-view-products-manage-tabs.php:199
msgid "Regular price increase"
msgstr "提高固定售价"

#: views/products-manager/wcfm-view-products-manage-tabs.php:200
msgid "Regular price decrease"
msgstr "降低固定售价"

#: views/products-manager/wcfm-view-products-manage-tabs.php:201
msgid "Sale prices"
msgstr "优惠价"

#: views/products-manager/wcfm-view-products-manage-tabs.php:202
msgid "Sale price increase"
msgstr "提高优惠价"

#: views/products-manager/wcfm-view-products-manage-tabs.php:203
msgid "Sale price decrease"
msgstr "降低优惠价"

#: views/products-manager/wcfm-view-products-manage-tabs.php:207
msgid "ON \"Manage stock\""
msgstr "启用 \"管理库存\""

#: views/products-manager/wcfm-view-products-manage-tabs.php:208
msgid "OFF \"Manage stock\""
msgstr "停用 \"管理库存\""

#: views/products-manager/wcfm-view-products-manage-tabs.php:211
msgid "Set Status - In stock"
msgstr "设定状态 \"目前有库存\""

#: views/products-manager/wcfm-view-products-manage-tabs.php:212
msgid "Set Status - Out of stock"
msgstr "设定状态 \"已售完\""

#: views/products-manager/wcfm-view-products-manage-tabs.php:213
msgid "Set Status - On backorder"
msgstr "设定状态 \"延后交货\""

#: views/products-manager/wcfm-view-products-manage-tabs.php:225
msgid "Downloadable products"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:226
msgid "Download limit"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:227
msgid "Download expiry"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:239
msgid "Manage Stock"
msgstr "管理库存"

#: views/products-manager/wcfm-view-products-manage-tabs.php:246
#: views/products-manager/wcfm-view-products-manage.php:473
#: views/products-popup/wcfm-view-product-popup.php:132
#: views/settings/wcfm-view-dokan-settings.php:491
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:718
#: views/settings/wcfm-view-wcmarketplace-settings.php:747
#: views/settings/wcfm-view-wcpvendors-settings.php:155
#: views/settings/wcfm-view-wcvendors-settings.php:447
msgid "Upto"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:248
msgid "Backorders?"
msgstr "延期交货？"

#: views/products-manager/wcfm-view-products-manage-tabs.php:270
msgid "Up-sells"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:270
msgid ""
"Up-sells are products which you recommend instead of the currently viewed "
"product, for example, products that are more profitable or better quality or "
"more expensive."
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:271
msgid "Cross-sells"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:271
msgid ""
"Cross-sells are products which you promote in the cart, based on the current "
"product."
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:35
msgid "You have reached product limit!"
msgstr "您已超过商品数量上限！"

#: views/products-manager/wcfm-view-products-manage.php:342
#: views/products-manager/wcfm-view-products-manage.php:354
#: views/products-popup/wcfm-view-product-popup.php:55
#: views/products-popup/wcfm-view-product-popup.php:67
msgid "Same as parent"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:343
#: views/products-popup/wcfm-view-product-popup.php:56
msgid "No shipping class"
msgstr "没有宅配类别"

#: views/products-manager/wcfm-view-products-manage.php:400
msgid "Manage Product"
msgstr "管理商品"

#: views/products-manager/wcfm-view-products-manage.php:409
msgid "Edit Product"
msgstr "编辑商品"

#: views/products-manager/wcfm-view-products-manage.php:409
#: views/products-popup/wcfm-view-product-popup.php:113
msgid "Add Product"
msgstr "新增商品"

#: views/products-manager/wcfm-view-products-manage.php:463
#: views/products-popup/wcfm-view-product-popup.php:122
msgid "Product Title"
msgstr "商品名称"

#: views/products-manager/wcfm-view-products-manage.php:468
#: views/products-popup/wcfm-view-product-popup.php:127
#: views/settings/wcfm-view-settings.php:418
msgid "URL"
msgstr "网址"

#: views/products-manager/wcfm-view-products-manage.php:468
#: views/products-popup/wcfm-view-product-popup.php:127
msgid "Enter the external URL to the product."
msgstr "输入这个商品的外部网址。"

#: views/products-manager/wcfm-view-products-manage.php:469
#: views/products-popup/wcfm-view-product-popup.php:128
msgid "Button Text"
msgstr "按钮文字"

#: views/products-manager/wcfm-view-products-manage.php:469
#: views/products-popup/wcfm-view-product-popup.php:128
msgid "This text will be shown on the button linking to the external product."
msgstr "这个文字会被显示在外部商品的按钮连结上。"

#: views/products-manager/wcfm-view-products-manage.php:471
#: views/products-popup/wcfm-view-product-popup.php:130
msgid "schedule"
msgstr "时间表"

#: views/products-manager/wcfm-view-products-manage.php:531
#: views/products-manager/wcfm-view-products-manage.php:711
#: views/products-popup/wcfm-view-product-popup.php:198
msgid "Separate Product Tags with commas"
msgstr "用 ',' 间隔商品标籤"

#: views/products-manager/wcfm-view-products-manage.php:679
msgid "Add new"
msgstr "新增卖家"

#: views/products-manager/wcfm-view-products-manage.php:685
msgid "-- Parent taxonomy --"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:140
msgid "Yoast SEO"
msgstr "SEO 优化"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:145
msgid "Enter a focus keyword"
msgstr "输入重点关键字"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:145
msgid "It should appear in title and first paragraph of the copy."
msgstr "这将会出现在标题和第一段。"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:146
msgid "Meta description"
msgstr "资料描述"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:146
msgid "It should not be more than 156 characters."
msgstr "这不能超过 156 个字元。"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:158
msgid "Custom Tabs"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:163
msgid "Tabs"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:164
msgid "Required for tab to be visible"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:165
msgid "HTML or Text to display ..."
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:178
msgid "Barcode & ISBN"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:183
msgid "Barcode"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:184
msgid "ISBN"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:197
msgid "MSRP Pricing"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:202
msgid "MSRP Price"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:215
msgid "Quantities and Units"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:220
msgid "Deactivate Quantity Rules"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:221
msgid "Override Quantity Rules"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:222
msgid "Step Value"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:223
msgid "Minimum Quantity"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:224
msgid "Maximum Quantity"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:225
msgid "Out of Stock Minimum"
msgstr "最低已售完"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:226
msgid "Out of Stock Maximum"
msgstr "最高已售完"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:227
msgid "Unit"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:240
msgid "Product Fees"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:245
msgid "Fee Name"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:245
msgid "This will be shown at the checkout description the added fee."
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:246
msgid "Fee Amount"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:246
msgid ""
"Enter a monetary decimal without any currency symbols or thousand separator. "
"This field also accepts percentages."
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:247
msgid "Multiple Fee by Quantity"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:247
msgid ""
"Multiply the fee by the quantity of this product that is added to the cart."
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:260
msgid "Bulk Discount"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:265
msgid "Bulk Discount enabled"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:266
msgid "Bulk discount special offer text in product description"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:267
msgid "Discount Rules"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:268
msgid "Quantity (min.)"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:269
msgid "Discount (%)"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:283
msgid "Role Based Price"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:295
msgid "Selling Price"
msgstr ""

#: views/products-popup/wcfm-view-product-popup.php:142
msgid "Taxonomies"
msgstr "分类"

#: views/products-popup/wcfm-view-product-popup.php:227
msgid "Image Gallery"
msgstr "图片相簿"

#: views/profile/wcfm-view-profile.php:169
msgid "Profile Manager"
msgstr ""

#: views/profile/wcfm-view-profile.php:185
msgid "Personal"
msgstr ""

#: views/profile/wcfm-view-profile.php:192
msgid "Avatar"
msgstr ""

#: views/profile/wcfm-view-profile.php:203
msgid "Email already verified"
msgstr ""

#: views/profile/wcfm-view-profile.php:212
msgid "Email Verification Code: "
msgstr ""

#: views/profile/wcfm-view-profile.php:212
msgid "Verification Code"
msgstr ""

#: views/profile/wcfm-view-profile.php:213
msgid "Get Code"
msgstr ""

#: views/profile/wcfm-view-profile.php:222
msgid "Set New Password - Keep it blank for not to update"
msgstr ""

#: views/profile/wcfm-view-profile.php:241
msgid "Site Default"
msgstr ""

#: views/profile/wcfm-view-profile.php:249
msgid "Language"
msgstr ""

#: views/profile/wcfm-view-profile.php:255
msgid "About"
msgstr ""

#: views/profile/wcfm-view-profile.php:289
msgid "Same as Billing"
msgstr ""

#: views/profile/wcfm-view-profile.php:310
#: views/settings/wcfm-view-dokan-settings.php:141
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:178
#: views/settings/wcfm-view-wcmarketplace-settings.php:148
#: views/settings/wcfm-view-wcpvendors-settings.php:85
#: views/settings/wcfm-view-wcvendors-settings.php:132
msgid "Social"
msgstr ""

#: views/profile/wcfm-view-profile.php:316
msgid "Twitter"
msgstr "Twitter"

#: views/profile/wcfm-view-profile.php:317
msgid "Facebook"
msgstr ""

#: views/profile/wcfm-view-profile.php:318
msgid "Instagram"
msgstr ""

#: views/profile/wcfm-view-profile.php:319
msgid "Youtube"
msgstr ""

#: views/profile/wcfm-view-profile.php:320
msgid "Linkedin"
msgstr ""

#: views/profile/wcfm-view-profile.php:321
msgid "Google Plus"
msgstr ""

#: views/profile/wcfm-view-profile.php:322
msgid "Snapchat"
msgstr ""

#: views/profile/wcfm-view-profile.php:323
msgid "Pinterest"
msgstr ""

#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:63
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:65
#: views/reports/wcfm-view-reports-sales-by-date.php:63
#: views/reports/wcfm-view-reports-sales-by-date.php:65
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:64
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:61
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:63
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:64
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:61
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:63
msgid "Sales BY Date"
msgstr "日期销售"

#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:63
#: views/reports/wcfm-view-reports-sales-by-date.php:63
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:61
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:61
#, php-format
msgctxt "start date and end date"
msgid "From %s to %s"
msgstr "从 %s 到 %s"

#: views/reports/wcfm-view-reports-menu.php:4
msgid "Sales by date"
msgstr "日期销售"

#: views/reports/wcfm-view-reports-out-of-stock.php:26
msgid "Out of Stock"
msgstr "已售完"

#: views/reports/wcfm-view-reports-out-of-stock.php:50
#: views/reports/wcfm-view-reports-out-of-stock.php:59
msgid "product"
msgstr "商品"

#: views/reports/wcfm-view-reports-out-of-stock.php:51
#: views/reports/wcfm-view-reports-out-of-stock.php:60
msgid "Parent"
msgstr ""

#: views/reports/wcfm-view-reports-out-of-stock.php:52
#: views/reports/wcfm-view-reports-out-of-stock.php:61
msgid "Unit in stock"
msgstr ""

#: views/reports/wcfm-view-reports-out-of-stock.php:53
#: views/reports/wcfm-view-reports-out-of-stock.php:62
msgid "Stock Status"
msgstr "库存状态"

#: views/settings/wcfm-view-dokan-settings.php:136
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:173
#: views/settings/wcfm-view-wcmarketplace-settings.php:143
#: views/settings/wcfm-view-wcpvendors-settings.php:80
#: views/settings/wcfm-view-wcpvendors-settings.php:103
#: views/settings/wcfm-view-wcvendors-settings.php:127
msgid "Store Settings"
msgstr "商店设定"

#: views/settings/wcfm-view-dokan-settings.php:165
msgid "Profile Image"
msgstr "个人档案图片"

#: views/settings/wcfm-view-dokan-settings.php:166
#: views/settings/wcfm-view-wcmarketplace-settings.php:207
#: views/settings/wcfm-view-wcvendors-settings.php:195
msgid "Banner"
msgstr "商店横幅图片"

#: views/settings/wcfm-view-dokan-settings.php:167
#: views/settings/wcfm-view-wcmarketplace-settings.php:173
#: views/settings/wcfm-view-wcpvendors-settings.php:110
#: views/settings/wcfm-view-wcvendors-settings.php:157
msgid "Shop Name"
msgstr "商店名称"

#: views/settings/wcfm-view-dokan-settings.php:168
msgid "Store Product Per Page"
msgstr "每页商店商品"

#: views/settings/wcfm-view-dokan-settings.php:169
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:213
#: views/settings/wcfm-view-wcvendors-settings.php:197
msgid "Store Phone"
msgstr "商店电话"

#: views/settings/wcfm-view-dokan-settings.php:170
msgid "Show email in store"
msgstr "在商店显示信箱"

#: views/settings/wcfm-view-dokan-settings.php:171
msgid "Show tab on product single page view"
msgstr "在单一商品页显示分页标籤"

#: views/settings/wcfm-view-dokan-settings.php:196
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:248
#: views/settings/wcfm-view-wcmarketplace-settings.php:225
#: views/settings/wcfm-view-wcvendors-settings.php:215
msgid "Store Address"
msgstr "商店地址"

#: views/settings/wcfm-view-dokan-settings.php:200
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:252
msgid "Street"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:200
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:252
msgid "Street adress"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:201
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:253
msgid "Street 2"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:201
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:253
msgid "Apartment, suit, unit etc. (optional)"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:202
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:254
msgid "Town / City"
msgstr "乡镇 / 城市"

#: views/settings/wcfm-view-dokan-settings.php:203
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:255
msgid "Postcode / Zip"
msgstr "邮递区号"

#: views/settings/wcfm-view-dokan-settings.php:211
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:267
#: views/settings/wcfm-view-wcmarketplace-settings.php:252
msgid "Store Location"
msgstr "商店地区"

#: views/settings/wcfm-view-dokan-settings.php:259
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:334
#: views/settings/wcfm-view-wcmarketplace-settings.php:310
#: views/settings/wcfm-view-wcvendors-settings.php:252
msgid "PayPal Email"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:260
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:335
msgid "Skrill Email"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:271
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:343
msgid "Bank Details"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:275
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:347
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:76
msgid "Account Name"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:276
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:348
#: views/settings/wcfm-view-wcmarketplace-settings.php:312
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:77
msgid "Account Number"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:277
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:349
#: views/settings/wcfm-view-wcmarketplace-settings.php:313
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:78
msgid "Bank Name"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:278
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:350
#: views/settings/wcfm-view-wcmarketplace-settings.php:315
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:79
msgid "Bank Address"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:279
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:351
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:80
msgid "Routing Number"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:280
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:352
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:81
msgid "IBAN"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:281
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:353
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:82
msgid "Swift Code"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:296
msgid "Stripe Connect"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:364
msgid "If you want to use Country-State wise Shipping system then"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:374
msgid ""
"A shipping zone is a geographic region where a certain set of shipping "
"methods are offered. System will match a customer to a single zone using "
"their shipping address and present the shipping methods within that zone to "
"them."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:388
msgid "Enable Shipping"
msgstr "启用宅配"

#: views/settings/wcfm-view-dokan-settings.php:415
#: views/settings/wcfm-view-wcvendors-settings.php:381
msgid "Shipping Rates"
msgstr "出货价格"

#: views/settings/wcfm-view-dokan-settings.php:415
msgid ""
"Add the countries you deliver your products to. You can specify states as "
"well. If the shipping price is same except some countries/states, there is "
"an option Everywhere Else, you can use that."
msgstr "退费规则"

#: views/settings/wcfm-view-dokan-settings.php:418
msgid "State Shipping Rates"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:419
#: views/settings/wcfm-view-wcvendors-settings.php:383
msgid "State"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:432
msgid "Dokan Pro Shipping Settings"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:464
msgid "Dokan Pro SEO Settings"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:481
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:708
#: views/settings/wcfm-view-wcmarketplace-settings.php:737
#: views/settings/wcfm-view-wcpvendors-settings.php:144
#: views/settings/wcfm-view-wcpvendors-settings.php:160
#: views/settings/wcfm-view-wcvendors-settings.php:437
msgid "Vacation Mode"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:487
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:714
#: views/settings/wcfm-view-wcmarketplace-settings.php:743
#: views/settings/wcfm-view-wcpvendors-settings.php:151
#: views/settings/wcfm-view-wcvendors-settings.php:443
msgid "Enable Vacation Mode"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:488
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:715
#: views/settings/wcfm-view-wcmarketplace-settings.php:744
#: views/settings/wcfm-view-wcpvendors-settings.php:152
#: views/settings/wcfm-view-wcvendors-settings.php:444
msgid "Disable Purchase During Vacation"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:489
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:716
#: views/settings/wcfm-view-wcmarketplace-settings.php:745
#: views/settings/wcfm-view-wcpvendors-settings.php:153
#: views/settings/wcfm-view-wcvendors-settings.php:445
msgid "Vacation Type"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:489
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:716
#: views/settings/wcfm-view-wcmarketplace-settings.php:745
#: views/settings/wcfm-view-wcpvendors-settings.php:153
#: views/settings/wcfm-view-wcvendors-settings.php:445
msgid "Instantly Close"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:489
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:716
#: views/settings/wcfm-view-wcmarketplace-settings.php:745
#: views/settings/wcfm-view-wcpvendors-settings.php:153
#: views/settings/wcfm-view-wcvendors-settings.php:445
msgid "Date wise close"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:492
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:719
#: views/settings/wcfm-view-wcmarketplace-settings.php:748
#: views/settings/wcfm-view-wcpvendors-settings.php:156
#: views/settings/wcfm-view-wcvendors-settings.php:448
msgid "Vacation Message"
msgstr ""

#: views/settings/wcfm-view-settings.php:92
msgid "WCfM Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:103
msgid "Bookings Global Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:111
msgid "Appointments Global Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:120
msgid "Membership Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:142
msgid "WCfM Dashboard Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:146
#: views/settings/wcfm-view-wcmarketplace-settings.php:172
#: views/settings/wcfm-view-wcpvendors-settings.php:109
#: views/settings/wcfm-view-wcvendors-settings.php:156
msgid "Logo"
msgstr "商店大头照"

#: views/settings/wcfm-view-settings.php:147
msgid "Quick access icon"
msgstr ""

#: views/settings/wcfm-view-settings.php:148
msgid "My Store Label"
msgstr ""

#: views/settings/wcfm-view-settings.php:149
msgid "Disable Quick Access"
msgstr ""

#: views/settings/wcfm-view-settings.php:151
msgid "Disable Welcome Box"
msgstr ""

#: views/settings/wcfm-view-settings.php:152
msgid "Disable WCFM Menu"
msgstr ""

#: views/settings/wcfm-view-settings.php:153
msgid "Disable Theme Header"
msgstr ""

#: views/settings/wcfm-view-settings.php:154
msgid "Disable WCFM Full View"
msgstr ""

#: views/settings/wcfm-view-settings.php:155
msgid "Disable WCFM Slick Menu"
msgstr ""

#: views/settings/wcfm-view-settings.php:157
msgid "Disable WCFM Header Panel"
msgstr ""

#: views/settings/wcfm-view-settings.php:158
msgid "Disable Float Button"
msgstr ""

#: views/settings/wcfm-view-settings.php:159
msgid "Disable Ask a Question Button"
msgstr ""

#: views/settings/wcfm-view-settings.php:160
msgid "Disable Category Checklist View"
msgstr ""

#: views/settings/wcfm-view-settings.php:161
msgid "Disable Ultimate Notice"
msgstr ""

#: views/settings/wcfm-view-settings.php:175
msgid "Modules"
msgstr ""

#: views/settings/wcfm-view-settings.php:179
msgid "Module Controller"
msgstr ""

#: views/settings/wcfm-view-settings.php:181
msgid "Configure what to hide from your dashboard"
msgstr ""

#: views/settings/wcfm-view-settings.php:228
msgid "Dashboard Display Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:240
msgid "Reset to Default"
msgstr ""

#: views/settings/wcfm-view-settings.php:252
msgid "Dashboard Pages"
msgstr ""

#: views/settings/wcfm-view-settings.php:256
msgid "Dashboard Page/Endpoint Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:261
msgid "-- Select a page --"
msgstr "-- 选择页面 --"

#: views/settings/wcfm-view-settings.php:276
msgid "Refresh Permalink"
msgstr ""

#: views/settings/wcfm-view-settings.php:276
msgid ""
"Check to refresh WCfM page permalinks. Only apply if you are getting error "
"(e.g. 404 not found) for any pages."
msgstr ""

#: views/settings/wcfm-view-settings.php:277
msgid "This page should have shortcode - wc_frontend_manager"
msgstr ""

#: views/settings/wcfm-view-settings.php:282
msgid "WCFM Endpoints"
msgstr ""

#: views/settings/wcfm-view-settings.php:286
msgid "Dashboard End Points"
msgstr ""

#: views/settings/wcfm-view-settings.php:295
msgid "My Account End Points"
msgstr ""

#: views/settings/wcfm-view-settings.php:337
msgid "Dashboard Menu Manager"
msgstr ""

#: views/settings/wcfm-view-settings.php:412
msgid "Home Menu Label"
msgstr ""

#: views/settings/wcfm-view-settings.php:416
msgid "Icon"
msgstr ""

#: views/settings/wcfm-view-settings.php:416
msgid "Insert a valid Font-awesome icon class."
msgstr ""

#: views/settings/wcfm-view-settings.php:417
msgid "Slug"
msgstr ""

#: views/settings/wcfm-view-settings.php:420
msgid "Has New?"
msgstr ""

#: views/settings/wcfm-view-settings.php:421
#: views/settings/wcfm-view-settings.php:423
msgid "New Menu Class"
msgstr ""

#: views/settings/wcfm-view-settings.php:422
msgid "New Menu URL"
msgstr ""

#: views/settings/wcfm-view-settings.php:424
msgid "Sub Menu Capability"
msgstr ""

#: views/settings/wcfm-view-settings.php:425
msgid "Menu For"
msgstr ""

#: views/settings/wcfm-view-settings.php:425
msgid "All Users"
msgstr ""

#: views/settings/wcfm-view-settings.php:425
msgid "Only Admin"
msgstr ""

#: views/settings/wcfm-view-settings.php:425
msgid "Only Vendors"
msgstr ""

#: views/settings/wcfm-view-settings.php:426
msgid "Open in new tab?"
msgstr ""

#: views/settings/wcfm-view-settings.php:440
msgid "Email From"
msgstr ""

#: views/settings/wcfm-view-settings.php:444
msgid "WCfM Email Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:448
msgid "Email from name"
msgstr ""

#: views/settings/wcfm-view-settings.php:448
msgid ""
"Notification emails will be triggered with this name. By default Site Name "
"will be used"
msgstr ""

#: views/settings/wcfm-view-settings.php:449
msgid "Email from address"
msgstr ""

#: views/settings/wcfm-view-settings.php:449
msgid ""
"Notification emails will be triggered from this email address. By default "
"Site Admin Email will be used"
msgstr ""

#: views/settings/wcfm-view-settings.php:450
msgid "CC Email address"
msgstr ""

#: views/settings/wcfm-view-settings.php:450
msgid "Notification emails will be CC to this email address."
msgstr ""

#: views/settings/wcfm-view-settings.php:451
msgid "BCC Email address"
msgstr ""

#: views/settings/wcfm-view-settings.php:451
msgid "Notification emails will be BCC to this email address."
msgstr ""

#: views/settings/wcfm-view-settings.php:462
msgid "Inquiry Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:466
msgid "Inquiry Module"
msgstr ""

#: views/settings/wcfm-view-settings.php:472
msgid "Inquiry Button Label"
msgstr ""

#: views/settings/wcfm-view-settings.php:477
msgid "Insert option values | separated"
msgstr ""

#: views/settings/wcfm-view-settings.php:491
msgid "Product Type Categories"
msgstr ""

#: views/settings/wcfm-view-settings.php:495
msgid "Product Type Specific Category Setup"
msgstr ""

#: views/settings/wcfm-view-settings.php:516
msgid ""
"Create group of your Store Categories as per Product Types. Product Manager "
"will work according to that."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:156
#, php-format
msgid "Upload a banner for your store. Banner size is (%sx%s) pixels."
msgstr "上传商店的封面照。图片建议尺寸 %sx%s 像素px。"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:202
msgid "Store Logo"
msgstr "商店大头照"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:202
msgid "Preferred  size is (125x125) pixels."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:203
msgid "Store List Banner"
msgstr "商店列表封面照"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:203
msgid "This Banner will be visible at Store List Page."
msgstr "这个封面照会显示在商店列表网页。"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:204
msgid "Store Banner Type"
msgstr "商店封面照类型"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:204
msgid "Static Image"
msgstr "静态图片"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:204
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:207
msgid "Slider"
msgstr "幻灯片"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:204
msgid "Video"
msgstr "影片"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:205
msgid "Store Banner"
msgstr "商店封面照"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:206
msgid "Video Banner"
msgstr "影片封面照"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:206
msgid "Insert YouTube video URL."
msgstr "插入 Youtube 影片网址。"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:212
msgid "Store Slug"
msgstr "商店 Slug"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:214
#: views/settings/wcfm-view-wcmarketplace-settings.php:175
#: views/settings/wcfm-view-wcvendors-settings.php:159
msgid "Shop Description"
msgstr "商店描述"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:214
#: views/settings/wcfm-view-wcmarketplace-settings.php:175
#: views/settings/wcfm-view-wcvendors-settings.php:159
msgid "This is displayed on your shop page."
msgstr "这将会显示在您的商店页"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:271
#: views/settings/wcfm-view-wcmarketplace-settings.php:258
msgid "Find Location"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:271
#: views/settings/wcfm-view-wcmarketplace-settings.php:258
msgid "Type an address to find"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:291
msgid "Store Visibility"
msgstr "商店显示方式"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:295
msgid "Store name position at you Store Page."
msgstr "商店页的商店名称位置"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:296
msgid "No of products at you Store Page."
msgstr "在商店页一次显示多少商品数量。"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:297
msgid "Hide Email from Store"
msgstr "在商店页隐藏信箱"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:298
msgid "Hide Phone from Store"
msgstr "在商店页隐藏电话"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:299
msgid "Hide Address from Store"
msgstr "在商店页隐藏地址"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:333
msgid "Prefered Payment Method"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:347
msgid "Your bank account name"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:348
msgid "Your bank account number"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:349
msgid "Name of bank"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:350
msgid "Address of your bank"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:351
msgid "Routing number"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:353
msgid "Swift code"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:354
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:83
msgid "IFSC Code"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:354
msgid "IFSC code"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:416
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:443
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:464
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:530
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:581
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:603
#: views/settings/wcfm-view-wcmarketplace-settings.php:382
#: views/settings/wcfm-view-wcmarketplace-settings.php:409
#: views/settings/wcfm-view-wcmarketplace-settings.php:430
#: views/settings/wcfm-view-wcmarketplace-settings.php:503
#: views/settings/wcfm-view-wcmarketplace-settings.php:550
#: views/settings/wcfm-view-wcmarketplace-settings.php:572
#: controllers/withdrawal/dokan/wcfm-controller-payments.php:90
#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:82
msgid "Stripe"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:419
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:533
#: views/settings/wcfm-view-wcmarketplace-settings.php:385
#: views/settings/wcfm-view-wcmarketplace-settings.php:506
msgid "You are connected with Stripe"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:425
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:539
#: views/settings/wcfm-view-wcmarketplace-settings.php:391
#: views/settings/wcfm-view-wcmarketplace-settings.php:512
msgid "Disconnect Stripe Account"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:446
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:467
#: views/settings/wcfm-view-wcmarketplace-settings.php:412
#: views/settings/wcfm-view-wcmarketplace-settings.php:433
msgid "Please Retry!!!"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:512
msgid "Unable to disconnect your account pleease try again"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:583
#: views/settings/wcfm-view-wcmarketplace-settings.php:552
msgid "You are not connected with stripe."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:605
#: views/settings/wcfm-view-wcmarketplace-settings.php:574
msgid "Please connected with stripe again."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:621
msgid "Stripe not setup properly, please contact your site admin."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:658
msgid "SEO Title"
msgstr "SEO 标题"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:658
msgid "SEO Title is shown as the title of your store page"
msgstr "SEO 标题会设定为商店页的网页名称"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:659
msgid "Meta Description"
msgstr "资料描述"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:659
msgid ""
"The meta description is often shown as the black text under the title in a "
"search result. For this to work it has to contain the keyword that was "
"searched for and should be less than 156 chars."
msgstr ""
"资料描述通常显示为搜索结果中标题下的黑色文字。因此，它必须包含搜索到的关键"
"字，同时必须少于 156 个字元。"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:660
msgid "Meta Keywords"
msgstr "资料关键字"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:660
msgid ""
"Insert some comma separated keywords for better ranking of your store page."
msgstr "用逗号 ',' 分隔，插入关键字，进行商店页面的 SEO 优化排名。"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:661
msgid "Facebook Title"
msgstr "Facebook 标题"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:662
msgid "Facebook Description"
msgstr "Facebook 描述"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:663
msgid "Facebook Image"
msgstr "Facebook 图片"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:664
msgid "Twitter Title"
msgstr "Twitter 标题"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:665
msgid "Twitter Description"
msgstr "Twitter 描述"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:666
msgid "Twitter Image"
msgstr "Twitter 图片"

#: views/settings/wcfm-view-wcmarketplace-settings.php:172
msgid "Preferred logo should be 200x200 px."
msgstr "建议使用圆形  LOGO 去背 PNG 图档尺寸 200x200 px(像素)。"

#: views/settings/wcfm-view-wcmarketplace-settings.php:173
#: views/settings/wcfm-view-wcpvendors-settings.php:110
#: views/settings/wcfm-view-wcvendors-settings.php:157
msgid "Your shop name is public and must be unique."
msgstr "您的商店名称是公开，且必须是唯一的。"

#: views/settings/wcfm-view-wcmarketplace-settings.php:174
msgid "Shop Slug"
msgstr "商店代码"

#: views/settings/wcfm-view-wcmarketplace-settings.php:174
msgid "Your shop slug is public and must be unique."
msgstr "您的商店代码公开，且必须是唯一的。"

#: views/settings/wcfm-view-wcmarketplace-settings.php:201
#: views/settings/wcfm-view-wcvendors-settings.php:187
msgid "Branding"
msgstr "品牌"

#: views/settings/wcfm-view-wcmarketplace-settings.php:207
msgid "Preferred banner should be 1200x245 px."
msgstr "建议 Banner 尺寸 1200x245 px(像素)。"

#: views/settings/wcfm-view-wcmarketplace-settings.php:209
msgid "Shop Phone"
msgstr "商店电话"

#: views/settings/wcfm-view-wcmarketplace-settings.php:209
msgid "Your store phone no."
msgstr "您的商店电话号码"

#: views/settings/wcfm-view-wcmarketplace-settings.php:244
#: views/settings/wcfm-view-wcmarketplace-settings.php:245
#: views/settings/wcfm-view-wcpvendors-settings.php:130
#: views/settings/wcfm-view-wcpvendors-settings.php:131
msgid "Timezone"
msgstr "时区"

#: views/settings/wcfm-view-wcmarketplace-settings.php:244
#: views/settings/wcfm-view-wcpvendors-settings.php:130
msgid "Set the local timezone."
msgstr "设定在地时间"

#: views/settings/wcfm-view-wcmarketplace-settings.php:270
msgid "Please contact your administrator to enable Google map feature."
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:278
msgid "Shop Template"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:311
msgid "Account Type"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:311
msgid "Current"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:311
msgid "Savings"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:314
msgid "ABA Routing Number"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:316
msgid "Destination Currency"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:317
msgid "Account IBAN"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:318
msgid "Account Holder Name"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:635
#: views/settings/wcfm-view-wcmarketplace-settings.php:644
msgid "Shipping Zone"
msgstr "出货地区"

#: views/settings/wcfm-view-wcmarketplace-settings.php:665
msgid "Shipping Rules"
msgstr "出货规则"

#: views/settings/wcfm-view-wcmarketplace-settings.php:684
msgid ""
"There is no shipping zone or Flat Rate shipping method not associated for "
"the zones to set shipping prices, kindly contact your Store Admin."
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:111
msgid "Vendor Email"
msgstr "卖家信箱"

#: views/settings/wcfm-view-wcpvendors-settings.php:111
msgid ""
"Enter the email for this vendor. This is the email where all notifications "
"will be send such as new orders and customer inquiries. You may enter more "
"than one email separating each with a comma."
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:112
msgid "Enter the profile information you would like for customer to see."
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:180
msgid "Paypal Email"
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:180
msgid "PayPal email account where you will receive your commission."
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:181
msgid ""
"Default commission you will receive per product sale. Please note product "
"level commission can override this. Check your product to confirm."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:158
#: views/vendors/wcfm-view-vendors-manage.php:293
msgid "Seller Info"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:158
msgid "This is displayed on each of your products."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:196
msgid "Store Website / Blog URL"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:196
msgid "Your company/blog URL here."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:197
msgid "This is your store contact number."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:232
#: views/settings/wcfm-view-wcvendors-settings.php:420
msgid "WCV Pro Settings"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:252
msgid "Your PayPal address is used to send you your commission."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:305
msgid "Bank Payment (Mangopay)"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:318
msgid "CHECKING"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:318
msgid "SAVINGS"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:368
#: views/settings/wcfm-view-wcvendors-settings.php:375
msgid "Charge once per product"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:384
msgid "Postcode"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:385
msgid "Shipping Fee"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:386
msgid "Override Qty"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:392
msgid "Min Charge Order"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:392
msgid "The minimum shipping fee charged for an order."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:393
msgid "Max Charge Order"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:393
msgid "The maximum shipping fee charged for an order."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:394
msgid "Free Shipping Order"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:394
msgid ""
"Free shipping for order spends over this amount. This will override the max "
"shipping charge above."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:395
msgid "Max Charge Product"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:395
msgid "The maximum shipping charged per product no matter the quantity."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:396
msgid "Free Shipping Product"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:396
msgid ""
"Free shipping if the spend per product is over this amount. This will "
"override the max shipping charge above."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:397
msgid "Product handling fee"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:397
msgid "Leave empty to disable"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:397
msgid ""
"The product handling fee, this can be overridden on a per product basis. "
"Amount (5.00) or Percentage (5%)."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:404
msgid "From Address"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:145
#: views/vendors/wcfm-view-vendors-new.php:62
msgid "Manage Vendor"
msgstr "管理供应商"

#: views/vendors/wcfm-view-vendors-manage.php:189
#: views/vendors/wcfm-view-vendors-new.php:75
#: views/vendors/wcfm-view-vendors.php:35
msgid "Add New Vendor"
msgstr "新增新的卖家"

#: views/vendors/wcfm-view-vendors-manage.php:241
#, php-format
msgid "<strong>%s product</strong><br />"
msgid_plural "<strong>%s products</strong><br />"
msgstr[0] "<strong>%s 商品</strong><br />"

#: views/vendors/wcfm-view-vendors-manage.php:243
msgid "total products posted"
msgstr "总发佈商品数"

#: views/vendors/wcfm-view-vendors-manage.php:255
#, php-format
msgid "<strong>%s item</strong><br />"
msgid_plural "<strong>%s items</strong><br />"
msgstr[0] "<strong>%s 项目</strong><br />"

#: views/vendors/wcfm-view-vendors-manage.php:279
#: views/vendors/wcfm-view-vendors-manage.php:281
msgid "Store Admin"
msgstr "商店管理员"

#: views/vendors/wcfm-view-vendors-manage.php:321
msgid "Profile Update"
msgstr "更新个人档案"

#: views/vendors/wcfm-view-vendors-manage.php:371
msgid "Vendor not yet subscribed for a membership!"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:384
msgid "Send Message"
msgstr "送出讯息"

#: views/vendors/wcfm-view-vendors-new.php:69
msgid "Edit Vendor"
msgstr ""

#: views/vendors/wcfm-view-vendors-new.php:69
msgid "Add Vendor"
msgstr ""

#: views/vendors/wcfm-view-vendors.php:32
msgid "Vendors Listing"
msgstr "卖家列表"

#: views/vendors/wcfm-view-vendors.php:36
msgid "Pending Vendors"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:52
#: views/wc_bookings/wcfm-view-wcbookings.php:70
msgid "Create Booking"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:64
#: views/wc_bookings/wcfm-view-wcbookings-details.php:75
#: views/wc_bookings/wcfm-view-wcbookings.php:81
msgid "Create Bookable"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:81
#: views/wc_bookings/wcfm-view-wcbookings-details.php:70
#: views/wc_bookings/wcfm-view-wcbookings.php:76
msgid "Manage Resources"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:44
msgid "Booking Details"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:51
msgid "Booking #"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:62
#: views/wc_bookings/wcfm-view-wcbookings.php:86
msgid "Calendar View"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:86
msgid "Overview"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:92
msgid "Booking Created:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:97
msgid "Order Number:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:157
msgid "Resource:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:256
msgid "Billing Email:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:262
msgid "Billing Phone:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:273
msgid "View Order"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:88
#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:80
msgid "PayPal"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:92
msgid "Bank Transfer"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:61
#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal-request.php:46
msgid "Request successfully sent"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:63
msgid "Something went wrong please try again later"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:57
msgid "Seller account balance not enough for this withdrawal."
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:106
msgid "Withdrawal Requests successfully approved."
msgstr "成功许可提领请求。"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:108
msgid "No withdrawals selected for approve."
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:170
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:109
msgid "Withdrawal Requests successfully cancelled."
msgstr "成功取消提款请求。"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:172
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:111
msgid "No withdrawals selected for cancel."
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:77
msgid "Withdrawal Approved"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:79
#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:100
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:95
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse.php:91
msgid "Withdrawal Cancelled"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:81
msgid "Withdrawal Pending"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:98
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:93
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse.php:89
msgid "Withdrawal Completed"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:102
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:97
msgid "Withdrawal Processing"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:153
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:160
msgid "Auto Withdrawal"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:156
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:163
msgid "By Payment Type"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:158
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:165
msgid "By Request"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:83
msgid "Withdrawal Request successfully processed."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:85
msgid "Withdrawal Request processing failed, please contact Store Admin."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:90
#, php-format
msgid "Vendor <b>%s</b> has placed a Withdrawal Request #%s."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:92
msgid "Your withdrawal request successfully sent."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:97
msgid "Your withdrawal request failed, please try after sometime."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:100
msgid "No payment method selected for withdrawal commission"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:103
msgid "No commission selected for withdrawal"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:59
msgid "Withdrawal Requests successfully processed."
msgstr "成功执行提领请求。"

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:61
msgid "Withdrawal Requests partially processed, check log for more details."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:64
msgid "No withdrawals selected for approval."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse-actions.php:46
msgid "Reverse Withdrawal Requests successfully approveed."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse-actions.php:48
msgid "No reverse withdrawals selected for approve."
msgstr ""

#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:84
msgid "Direct Bank Transfer"
msgstr ""

#: includes/libs/php/class-wcfm-fields.php:641
#: includes/libs/php/class-wcfm-fields.php:645
msgid "-Select a location-"
msgstr "-选择地区-"

#: views/withdrawal/dokan/wcfm-view-payments.php:34
#: views/withdrawal/wcfm/wcfm-view-payments.php:36
#: views/withdrawal/wcmp/wcfm-view-payments.php:34
msgid "Transactions for: "
msgstr ""

#: views/withdrawal/dokan/wcfm-view-payments.php:51
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:37
#: views/withdrawal/wcfm/wcfm-view-payments.php:53
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:44
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:42
msgid "Show all .."
msgstr "显示全部"

#: views/withdrawal/dokan/wcfm-view-payments.php:52
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:38
#: views/withdrawal/wcfm/wcfm-view-payments.php:54
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:45
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:43
msgid "Approved"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-payments.php:53
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:39
#: views/withdrawal/wcfm/wcfm-view-payments.php:55
msgid "Processing"
msgstr "处理中"

#: views/withdrawal/dokan/wcfm-view-payments.php:69
#: views/withdrawal/dokan/wcfm-view-payments.php:78
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:157
#: views/withdrawal/wcmp/wcfm-view-payments.php:68
#: views/withdrawal/wcmp/wcfm-view-payments.php:79
msgid "Pay Mode"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-payments.php:70
#: views/withdrawal/dokan/wcfm-view-payments.php:79
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:56
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:66
#: views/withdrawal/wcfm/wcfm-view-payments.php:77
#: views/withdrawal/wcfm/wcfm-view-payments.php:91
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:74
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:88
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:70
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:82
msgid "Note"
msgstr "备注"

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:52
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:62
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:66
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:80
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:64
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:76
msgid "Requests"
msgstr "请求"

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:80
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:102
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:96
msgid "Note to Vendor(s)"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:87
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:109
msgid "Cancel"
msgstr "取消"

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:88
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:110
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:103
msgid "Approve"
msgstr "许可"

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:64
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:111
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:57
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:53
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:42
msgid "Transaction History"
msgstr "交易历史纪录"

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:105
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:108
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:88
msgid "Request"
msgstr "请求"

#: views/withdrawal/wcfm/wcfm-view-payments.php:70
#: views/withdrawal/wcfm/wcfm-view-payments.php:84
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:67
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:81
#: views/withdrawal/wcmp/wcfm-view-payments.php:64
#: views/withdrawal/wcmp/wcfm-view-payments.php:75
msgid "Transc.ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-payments.php:71
#: views/withdrawal/wcfm/wcfm-view-payments.php:85
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:68
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:82
msgid "Order IDs"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-payments.php:72
#: views/withdrawal/wcfm/wcfm-view-payments.php:86
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:73
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:87
#: views/withdrawal/wcmp/wcfm-view-payments.php:65
#: views/withdrawal/wcmp/wcfm-view-payments.php:76
msgid "Commission IDs"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-payments.php:74
#: views/withdrawal/wcfm/wcfm-view-payments.php:88
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:71
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:85
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:76
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:87
msgid "Charges"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-payments.php:76
#: views/withdrawal/wcfm/wcfm-view-payments.php:90
msgid "Mode"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:84
msgid "Payment Received Email"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:85
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:88
msgid "Transaction ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:86
msgid "Transaction Status"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:87
msgid "Request ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:102
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:43
msgid "Transaction #"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:134
msgid "Order ID(s)"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:135
msgid "Commission ID(s)"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:136
msgid "Payment Method"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:171
msgid "Total Amount"
msgstr "金额总计"

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:175
msgid "Withdrawal Charges"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:179
msgid "Paid Amount"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:179
msgid "Payable Amount"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:35
msgid "Reverse Withdrawal"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:46
msgid "Requested"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:25
msgid "Reverse Withdrawals"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:32
msgid "Reverse Withdrawal Requests"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:65
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:77
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:73
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:84
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:58
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:67
msgid "Order ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:69
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:81
msgid "Balance"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:42
msgid "Pending Withdrawals: "
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:46
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:36
msgid "Threshold for withdrawals: "
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:51
msgid "Reverse pay balance "
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:52
#, php-format
msgid "Thresold Limit: %s"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:72
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:83
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:57
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:66
msgid "Send Request"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:74
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:85
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:59
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:68
msgid "Commission ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:75
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:86
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:60
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:69
msgid "My Earnings"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:98
msgid ""
"Withdrawal charges will be re-calculated depending upon total withdrawal "
"amount."
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:110
msgid "Withdrawal disable due to high reverse balance."
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:113
msgid "Withdrawal disable due to low account balance."
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-payments.php:67
#: views/withdrawal/wcmp/wcfm-view-payments.php:78
msgid "Net Earnings"
msgstr ""

#. Name of the plugin
msgid "WooCommerce Frontend Manager"
msgstr ""

#. Description of the plugin
msgid ""
"WooCommerce is really Easy and Beautiful. We are here to make your life much "
"more Easier and Peaceful."
msgstr ""

#. Author URI of the plugin
msgid "https://wclovers.com"
msgstr ""

#. Author of the plugin
msgid "WC Lovers"
msgstr ""
