/*! DO NOT EDIT THIS FILE. This file is a auto generated on 2023-03-25 */
jQuery(document).ready(function(e){$reference_type="",$status_type="",$wcfm_ledger_table=e("#wcfm-ledger").DataTable({processing:!0,serverSide:!0,aFilter:!1,bFilter:!1,responsive:!0,deferRender:!0,scrollY:500,scrollCollapse:!0,scroller:!0,language:e.parseJSON(dataTables_language),columns:[{responsivePriority:1},{responsivePriority:2},{responsivePriority:4},{responsivePriority:1},{responsivePriority:1},{responsivePriority:3}],columnDefs:[{targets:0,orderable:!1},{targets:1,orderable:!1},{targets:2,orderable:!1},{targets:3,orderable:!1},{targets:4,orderable:!1},{targets:4,orderable:!1}],ajax:{type:"POST",url:wcfm_params.ajax_url,data:function(e){e.action="wcfm_ajax_controller",e.controller="wcfm-ledger",e.status_type=$status_type,e.type=$reference_type,e.wcfm_ajax_nonce=wcfm_params.wcfm_ajax_nonce},complete:function(){initiateTip(),e(document.body).trigger("updated_wcfm-ledger")}}}),0<e("#dropdown_status_type").length&&e("#dropdown_status_type").on("change",function(){$status_type=e("#dropdown_status_type").val(),$wcfm_ledger_table.ajax.reload()}),0<e("#dropdown_reference_type").length&&e("#dropdown_reference_type").on("change",function(){$reference_type=e("#dropdown_reference_type").val(),$wcfm_ledger_table.ajax.reload()}),0<e(".wcfm_filters_wrap").length&&e(".wcfm_filters_wrap").css("display","inline-block")});