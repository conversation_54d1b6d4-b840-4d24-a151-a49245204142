/*! DO NOT EDIT THIS FILE. This file is a auto generated on 2023-03-25 */
function wcfm_refund_requests_form_validate(e){return $is_valid=!0,jQuery(".wcfm-message").html("").removeClass("wcfm-success").removeClass("wcfm-error").slideUp(),0==jQuery.trim(e.find("#wcfm_refund_reason").val()).length&&($is_valid=!1,e.find(".wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfm_refund_requests_messages.no_refund_reason).addClass("wcfm-error").slideDown()),$is_valid}function wcfm_refund_requests_form_submit(s){var e;($is_valid=wcfm_refund_requests_form_validate(s))&&(s.block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),e={action:"wcfm_ajax_controller",controller:"wcfm-refund-tab",wcfm_refund_tab_form:s.serialize(),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce,status:"submit"},jQuery.post(wcfm_params.ajax_url,e,function(e){e&&(($response_json=jQuery.parseJSON(e)).status?(wcfm_notification_sound.play(),s.find(".wcfm-message").html('<span class="wcicon-status-completed"></span>'+$response_json.message).addClass("wcfm-success").slideDown("slow"),setTimeout(function(){jQuery(".refund_form_wrapper_hide").slideUp("slow"),$refund_form_show=!1,s.find("#wcfm_refund_reason").val("")},2e3),$wcfm_refund_submited=!0):s.find(".wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+$response_json.message).addClass("wcfm-error").slideDown(),s.unblock())}))}$wcfm_refund_submited=!1,jQuery(document).ready(function(n){function s(e,s,r,t){$item_id=e.data("item"),$commission_id=e.data("commission");e={action:"wcfmmp_refund_requests_form_html",item_id:$item_id,order_id:s,commission_id:$commission_id,customer_refund:t,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:e,success:function(e){jQuery.colorbox({html:e,width:$large_popup_width,height:"70%",onComplete:function(){0<jQuery(".anr_captcha_field").length&&"undefined"!=typeof grecaptcha&&wcfm_refund_anr_onloadCallback(),n(".wcfm_refund_input_qty").each(function(){n(this).change(function(){$item_id=n(this).data("item"),$input_qty=n(this).val(),$order_line_item=n(".order_line_item_"+$item_id),$item_cost=$order_line_item.find(".wcfm_refund_input_total").data("item_cost"),$max_cost=$order_line_item.find(".wcfm_refund_input_total").data("max_total"),($total_cost=$item_cost*$input_qty)>$max_cost&&($total_cost=$max_cost),$order_line_item.find(".wcfm_refund_input_total").val($total_cost),$order_line_item.find(".wcfm_refund_input_tax").each(function(){$tax_cost=n(this).data("item_tax"),$max_tax=n(this).data("max_tax"),($total_tax=$tax_cost*$input_qty)>$max_tax&&($total_tax=$max_tax),n(this).val($total_tax)})})}),jQuery("#wcfm_refund_request").change(function(){"full"==($wcfm_refund_request=n(this).val())?n(".wcfm_refund_input_ele,.wcfm_refund_items_ele").addClass("wcfm_custom_hide"):n(".wcfm_refund_input_ele,.wcfm_refund_items_ele").removeClass("wcfm_custom_hide")}).change(),jQuery("#wcfm_refund_requests_submit_button").click(function(e){return e.preventDefault(),jQuery("#wcfm_refund_form_wrapper").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),jQuery(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp(),0==jQuery("#wcfm_refund_reason").val().length?(jQuery("#wcfm_refund_requests_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfm_refund_requests_messages.no_refund_reason).addClass("wcfm-error").slideDown(),jQuery("#wcfm_refund_form_wrapper").unblock()):(e={action:"wcfm_ajax_controller",controller:"wcfm-refund-requests-form",wcfm_refund_requests_form:jQuery("#wcfm_refund_requests_form").serialize(),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce},jQuery.post(wcfm_params.ajax_url,e,function(e){e&&(jQueryresponse_json=jQuery.parseJSON(e),jQuery(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp(),wcfm_notification_sound.play(),jQueryresponse_json.status?(jQuery("#wcfm_refund_requests_form .wcfm-message").html('<span class="wcicon-status-completed"></span>'+jQueryresponse_json.message).addClass("wcfm-success").slideDown(),jQuery("#wcfm_refund_requests_submit_button").hide(),r?$wcfm_orders_table.ajax.reload():window.location=window.location.href,setTimeout(function(){jQuery.colorbox.remove()},2e3)):jQuery("#wcfm_refund_requests_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+jQueryresponse_json.message).addClass("wcfm-error").slideDown(),0<n(".wcfm_gglcptch_wrapper").length&&"undefined"!=typeof grecaptcha&&grecaptcha.reset(),jQuery("#wcfm_refund_form_wrapper").unblock())})),!1})}})}})}$refund_form_show=!1,n(document.body).on("updated_wcfm-orders",function(){n(".wcfmmp_order_refund_request").each(function(){n(this).click(function(e){e.preventDefault(),$order_id=n(this).data("order"),s(n(this),$order_id,!0,"no")})})}),n(".wcfm-refund-action").each(function(){n(this).click(function(e){e.preventDefault(),$order_id=n(this).attr("href"),s(n(this),$order_id,!1,"yes")})}),n(".add_refund").click(function(){$refund_form_show=$refund_form_show?(n(".refund_form_wrapper_hide").slideUp("slow"),!1):(n(".refund_form_wrapper_hide").slideDown("slow"),!0)}),n("#wcfm_refund_requests_submit_button").click(function(e){e.preventDefault(),$wcfm_refund_submited=!1,wcfm_refund_requests_form_submit(n(this).parent().parent())})});