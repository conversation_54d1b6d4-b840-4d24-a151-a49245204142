/*! DO NOT EDIT THIS FILE. This file is a auto generated on 2023-03-25 */
jQuery(document).ready(function(n){var a=geocoder=marker=map_marker=infowindow="";function m(o,e,t,a){a&&("google"==wcfm_maps.lib?document.getElementById("wcfmmp_user_location").value=o:(n("#wcfmmp_user_location").val(o),n("#leaflet_wcfmmp_user_location").find(".search-input").val(o))),document.getElementById("wcfmmp_user_location_lat").value=e,document.getElementById("wcfmmp_user_location_lng").value=t,n(document.body).trigger("update_checkout")}function r(o,e,t){google.maps.event.addListener(e,"click",function(){o.setContent(t),o.open(a,e)})}function c(){navigator.geolocation.getCurrentPosition(function(t){$current_location_fetched=!0,console.log(t.coords.latitude,t.coords.longitude),"google"==wcfm_maps.lib?geocoder.geocode({location:{lat:t.coords.latitude,lng:t.coords.longitude}},function(o,e){"OK"===e&&(m(o[0].formatted_address,t.coords.latitude,t.coords.longitude,!0),e=new google.maps.LatLng(t.coords.latitude,t.coords.longitude),marker.setPosition(e),marker.setVisible(!0),infowindow.setContent(o[0].formatted_address),infowindow.open(a,marker),r(infowindow,marker,o[0].formatted_address))}):n.get("https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat="+t.coords.latitude+"&lon="+t.coords.longitude,function(o){m(o.address.road,t.coords.latitude,t.coords.longitude,!0),map_marker.bindPopup(o.address.road).openPopup()})})}$wcfmmp_user_location_lat=jQuery("#wcfmmp_user_location_lat").val(),$wcfmmp_user_location_lng=jQuery("#wcfmmp_user_location_lng").val(),0<jQuery("#wcfmmp_user_location_lat").length&&setTimeout(function(){var o,e,t;"google"==wcfm_maps.lib?(n("#wcfmmp_user_location").parent().append('<i class="wcfmmmp_locate_icon" style="background-image: url('+wcfmmp_checkout_map_options.locate_svg+')"></i>'),o=new google.maps.LatLng(wcfmmp_checkout_map_options.default_lat,wcfmmp_checkout_map_options.default_lng,13),a=new google.maps.Map(document.getElementById("wcfmmp-user-locaton-map"),{center:o,mapTypeId:google.maps.MapTypeId.ROADMAP,zoom:parseInt(wcfmmp_checkout_map_options.default_zoom)}),t={url:wcfmmp_checkout_map_options.store_icon,scaledSize:new google.maps.Size(wcfmmp_checkout_map_options.icon_width,wcfmmp_checkout_map_options.icon_height)},marker=new google.maps.Marker({map:a,position:o,animation:google.maps.Animation.DROP,icon:t,draggable:!0}),o=document.getElementById("wcfmmp_user_location"),geocoder=new google.maps.Geocoder,(e=new google.maps.places.Autocomplete(o)).bindTo("bounds",a),infowindow=new google.maps.InfoWindow,e.addListener("place_changed",function(){infowindow.close(),marker.setVisible(!1);var o=e.getPlace();o.geometry?(o.geometry.viewport?a.fitBounds(o.geometry.viewport):(a.setCenter(o.geometry.location),a.setZoom(parseInt(wcfmmp_checkout_map_options.default_zoom))),marker.setPosition(o.geometry.location),marker.setVisible(!0),m(o.formatted_address,o.geometry.location.lat(),o.geometry.location.lng(),!1),infowindow.setContent(o.formatted_address),infowindow.open(a,marker),r(infowindow,marker,o.formatted_address)):window.alert("Autocomplete returned place contains no geometry")}),google.maps.event.addListener(marker,"dragend",function(){geocoder.geocode({latLng:marker.getPosition()},function(o,e){e==google.maps.GeocoderStatus.OK&&o[0]&&(m(o[0].formatted_address,marker.getPosition().lat(),marker.getPosition().lng(),!0),infowindow.setContent(o[0].formatted_address),infowindow.open(a,marker),r(infowindow,marker,o[0].formatted_address))})})):(n("#wcfmmp_user_location").replaceWith('<div id="leaflet_wcfmmp_user_location"></div><input type="hidden" class="wcfm_custom_hide" name="wcfmmp_user_location" id="wcfmmp_user_location" />'),(map_marker=$wcfmmp_user_location_lat&&$wcfmmp_user_location_lng?((a=new L.Map("wcfmmp-user-locaton-map",{zoom:parseInt(wcfmmp_checkout_map_options.default_zoom),center:new L.latLng([$wcfmmp_user_location_lat,$wcfmmp_user_location_lng])})).addLayer(new L.TileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png")),L.marker([$wcfmmp_user_location_lat,$wcfmmp_user_location_lng],{draggable:"true"}).addTo(a).on("click",function(){window.open("https://www.openstreetmap.org/?mlat="+$wcfmmp_user_location_lat+"&mlon="+$wcfmmp_user_location_lng+"#map=14/"+$wcfmmp_user_location_lat+"/"+$wcfmmp_user_location_lng,"_blank")})):((a=new L.Map("wcfmmp-user-locaton-map",{zoom:parseInt(wcfmmp_checkout_map_options.default_zoom),center:new L.latLng([wcfmmp_checkout_map_options.default_lat,wcfmmp_checkout_map_options.default_lng])})).addLayer(new L.TileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png")),L.marker([0,0],{draggable:"true"}))).on("dragend",function(o){var e=map_marker.getLatLng(),t="http://nominatim.openstreetmap.org/reverse?format=json&lat="+e.lat+"&lon="+e.lng+"&zoom=18&addressdetails=1",a="";n.getJSON(t).done(function(o){o.address.road?a+=o.address.road+", ":o.address.pedestrian?a+=o.address.pedestrian+", ":a="",o.address.house_number&&(a+=o.address.house_number+", "),o.address.city_district&&(a+=o.address.city_district+", "),o.address.city&&(a+=o.address.city+", "),o.address.postcode&&(a+=o.address.postcode),m(a,e.lat,e.lng,!0);o=a;map_marker.bindPopup(o).openPopup()})}),t=new L.Control.Search({container:"leaflet_wcfmmp_user_location",url:"https://nominatim.openstreetmap.org/search?format=json&q={s}",jsonpParam:"json_callback",propertyName:"display_name",propertyLoc:["lat","lon"],marker:map_marker,moveToLocation:function(o,e,t){m(e,o.lat,o.lng,!0),t.setView(o,parseInt(wcfmmp_checkout_map_options.default_zoom))},initial:!1,collapsed:!1,autoType:!1,minLength:2}),a.addControl(t),setTimeout(function(){a.invalidateSize()},3e3)),navigator.geolocation&&(n(".wcfmmmp_locate_icon").on("click",function(){c()}),c())},1e3)});