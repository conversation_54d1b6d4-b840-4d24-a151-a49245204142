/*! DO NOT EDIT THIS FILE. This file is a auto generated on 2023-03-25 */
jQuery(document).ready(function(n){function t(){var e=280;n(".wcfmmp-single-store").hasClass("coloum-2")&&(n(".wcfmmp-single-store .store-footer").each(function(){n(this).outerHeight()>e&&(e=n(this).outerHeight())}),n(".wcfmmp-single-store .store-footer").css("height",e)),n(".wcfmmp-store-lists-sorting #wcfmmp_store_orderby").on("change",function(){n(this).parent().submit()})}$current_location_fetched=!1,n("#wcfmmp-stores-lists").parent().hasClass("col-md-8")&&(n("#wcfmmp-stores-lists").parent().removeClass("col-md-8"),n("#wcfmmp-stores-lists").parent().addClass("col-md-12")),n("#wcfmmp-stores-lists").parent().hasClass("col-md-9")&&(n("#wcfmmp-stores-lists").parent().removeClass("col-md-9"),n("#wcfmmp-stores-lists").parent().addClass("col-md-12")),n("#wcfmmp-stores-lists").parent().hasClass("col-sm-8")&&(n("#wcfmmp-stores-lists").parent().removeClass("col-sm-8"),n("#wcfmmp-stores-lists").parent().addClass("col-md-12")),n("#wcfmmp-stores-lists").parent().hasClass("col-sm-9")&&(n("#wcfmmp-stores-lists").parent().removeClass("col-sm-9"),n("#wcfmmp-stores-lists").parent().addClass("col-md-12")),n("#wcfmmp-stores-lists").parent().removeClass("col-sm-push-3"),n("#wcfmmp-stores-lists").parent().removeClass("col-sm-push-4"),n("#wcfmmp-stores-lists").parent().removeClass("col-md-push-3"),n("#wcfmmp-stores-lists").parent().removeClass("col-md-push-4"),0<n(".left_sidebar").length&&768<n(window).width()&&($left_sidebar_height=n(".left_sidebar").outerHeight(),$right_side_height=n(".right_side").outerHeight(),$left_sidebar_height<$right_side_height)&&n(".left_sidebar").css("height",$right_side_height+50),setTimeout(function(){t()},200),0<n("#wcfmmp_store_country").length&&n("#wcfmmp_store_country").select2({allowClear:!0,placeholder:wcfmmp_store_list_messages.choose_location+" ..."}),0<n("#wcfmmp_store_category").length&&n("#wcfmmp_store_category").select2({allowClear:!0,placeholder:wcfmmp_store_list_messages.choose_category+" ..."}),0<n(".wcfm-custom-search-select-field").length&&n(".wcfm-custom-search-select-field").each(function(){$title=n(this).data("title"),n(this).select2({allowClear:!0,placeholder:$title+" ..."})});var s,e,o,a,r,c=n(".wcfmmp-store-search-form"),m=null;function i(){data={action:"wcfmmp_stores_list_search",pagination_base:c.find("#pagination_base").val(),paged:c.find("#wcfm_paged").val(),per_row:$per_row,per_page:$per_page,includes:$includes,excludes:$excludes,orderby:n("#wcfmmp_store_orderby").val(),has_orderby:$has_orderby,has_product:$has_product,sidebar:$sidebar,theme:$theme,search_term:n(".wcfmmp-store-search").val(),wcfmmp_store_category:n("#wcfmmp_store_category").val(),search_data:jQuery(".wcfmmp-store-search-form").serialize(),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce,_wpnonce:c.find("#nonce").val()},m&&clearTimeout(m),s&&s.abort(),m=setTimeout(function(){n(".wcfmmp-stores-listing").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),s=n.post(wcfm_params.ajax_url,data,function(e){e.success&&(n(".wcfmmp-stores-listing").unblock(),e=e.data,n("#wcfmmp-stores-wrap").html(n(e).find(".wcfmmp-stores-content")),d(),initiateTip(),n(".wcfm_catalog_enquiry").each(function(){n(this).hasClass("wcfm_login_popup")?jQuery(".wcfm_login_popup").each(function(){jQuery(this).click(function(e){e.preventDefault(),jQuerylogin_popup=jQuery(this),jQuery("body").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});e={action:"wcfm_login_popup_form",wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};return jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:e,success:function(e){jQuery.colorbox({html:e,width:$popup_width,onComplete:function(){jQuery("#wcfm_login_popup_button").click(function(){var e;$wcfm_is_valid_form=!0,jQuery("#wcfm_login_popup_form").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),jQuery(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp(),0==jQuery("input[name=wcfm_login_popup_username]").val().length?(jQuery("#wcfm_login_popup_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfm_login_messages.no_username).addClass("wcfm-error").slideDown(),wcfm_notification_sound.play(),jQuery("#wcfm_login_popup_form").unblock()):0==jQuery("input[name=wcfm_login_popup_password]").val().length?(jQuery("#wcfm_login_popup_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfm_login_messages.no_password).addClass("wcfm-error").slideDown(),wcfm_notification_sound.play(),jQuery("#wcfm_login_popup_form").unblock()):(jQuery(document.body).trigger("wcfm_form_validate",jQuery("#wcfm_login_popup_form")),$wcfm_is_valid_form?(jQuery("#wcfm_login_popup_button").hide(),e={action:"wcfm_login_popup_submit",wcfm_login_popup_form:jQuery("#wcfm_login_popup_form").serialize(),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce},jQuery.post(wcfm_params.ajax_url,e,function(e){e&&(jQueryresponse_json=jQuery.parseJSON(e),wcfm_notification_sound.play(),jQuery(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp(),jQueryresponse_json.status?(jQuery("#wcfm_login_popup_form .wcfm-message").html('<span class="wcicon-status-completed"></span>'+jQueryresponse_json.message).addClass("wcfm-success").slideDown(),window.location=window.location.href):(jQuery("#wcfm_login_popup_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+jQueryresponse_json.message).addClass("wcfm-error").slideDown(),jQuery("#wcfm_login_popup_button").show(),jQuery("#wcfm_login_popup_form").unblock()))})):(wcfm_notification_sound.play(),jQuery("#wcfm_login_popup_form").unblock()))})}}),jQuery("body").unblock()}}),!1})}):n(this).off("click").on("click",function(e){e.preventDefault(),$store=n(this).data("store"),$product=n(this).data("product"),n.colorbox({inline:!0,href:"#enquiry_form_wrapper",width:$popup_width,onComplete:function(){n("#wcfm_enquiry_form").find("#enquiry_vendor_id").val($store),n("#wcfm_enquiry_form").find("#enquiry_product_id").val($product),0<jQuery(".anr_captcha_field").length&&"undefined"!=typeof grecaptcha&&($wcfm_anr_loaded?grecaptcha.reset():wcfm_anr_onloadCallback(),$wcfm_anr_loaded=!0)}})})}),setTimeout(function(){t()},200))})},500)}$wcfm_anr_loaded=!1,0<n(".wcfmmp-store-search-form").length&&(!wcfmmp_store_list_options.is_geolocate||0!=n("#wcfmmp_radius_addr").length&&navigator.geolocation||i(),c.on("keyup",".wcfm-search-field",function(){i()}),c.on("keyup","#search",function(){i()}),n(".wcfm-search-field").on("input",function(e){i()}),n("#search").on("input",function(e){i()}),n(".wcfm-custom-search-input-field").on("input",function(e){i()}),c.on("change","#wcfmmp_store_category",function(){i()}),c.on("change",".wcfm-custom-search-select-field",function(){i()}),n(document.body).on("wcfm_store_list_country_changed",function(e){i()}),c.on("change","#wcfmmp_store_state",function(){i()}),c.on("keyup","#wcfmmp_store_state",function(){i()}),r="",0<n("#wcfmmp_radius_addr").length)&&(e=parseInt(wcfmmp_store_list_options.max_radius),_=document.getElementById("wcfmmp_radius_addr"),"google"==wcfm_maps.lib?(o=new google.maps.Geocoder,(a=new google.maps.places.Autocomplete(_)).addListener("place_changed",function(){var e=a.getPlace();n("#wcfmmp_radius_lat").val(e.geometry.location.lat()),n("#wcfmmp_radius_lng").val(e.geometry.location.lng()),i()}),n("#wcfmmp_radius_addr").blur(function(){0==n(this).val().length&&(n("#wcfmmp_radius_lat").val(""),n("#wcfmmp_radius_lng").val(""),i())})):r=new L.Control.Search({container:"wcfm_radius_filter_container",url:"https://nominatim.openstreetmap.org/search?format=json&q={s}",jsonpParam:"json_callback",propertyName:"display_name",propertyLoc:["lat","lon"],marker:L.marker([0,0]),moveToLocation:function(e,t,s){n("#wcfmmp_radius_lat").val(e.lat),n("#wcfmmp_radius_lng").val(e.lng),i()},initial:!1,collapsed:!1,autoType:!1,minLength:2}),n("#wcfmmp_radius_range").on("input",function(){n(".wcfmmp_radius_range_cur").html(this.value+wcfmmp_store_list_options.radius_unit),wcfmmp_store_list_options.is_rtl?n(".wcfmmp_radius_range_cur").css("right",this.value/e*n(".wcfm_radius_slidecontainer").outerWidth()-7.5+"px"):n(".wcfmmp_radius_range_cur").css("left",this.value/e*n(".wcfm_radius_slidecontainer").outerWidth()-7.5+"px"),($wcfmmp_radius_lat=n("#wcfmmp_radius_lat").val())&&setTimeout(function(){i()},100)}),wcfmmp_store_list_options.is_rtl?n(".wcfmmp_radius_range_cur").css("right",n("#wcfmmp_radius_range").val()/e*n(".wcfm_radius_slidecontainer").outerWidth()-7.5+"px"):n(".wcfmmp_radius_range_cur").css("left",n("#wcfmmp_radius_range").val()/e*n(".wcfm_radius_slidecontainer").outerWidth()-7.5+"px"),navigator.geolocation)&&(n(".wcfmmmp_locate_icon").on("click",function(){navigator.geolocation.getCurrentPosition(function(s){$current_location_fetched=!0,"google"==wcfm_maps.lib?o.geocode({location:{lat:s.coords.latitude,lng:s.coords.longitude}},function(e,t){"OK"===t&&(n("#wcfmmp_radius_addr").val(e[0].formatted_address),n("#wcfmmp_radius_lat").val(s.coords.latitude),n("#wcfmmp_radius_lng").val(s.coords.longitude),i())}):n.get("https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat="+s.coords.latitude+"&lon="+s.coords.longitude,function(e){n("input.search-input.wcfmmp-radius-addr").val(e.display_name),n("#wcfmmp_radius_lat").val(s.coords.latitude),n("#wcfmmp_radius_lng").val(s.coords.longitude),i()})})}),wcfmmp_store_list_options.is_geolocate)&&(lat=n("#wcfmmp_radius_lat").val(),lng=n("#wcfmmp_radius_lng").val(),lat||lng||n(".wcfmmmp_locate_icon").click());var l,_,p,f=n(".wcfmmp-store-search-form"),u="";function d(){var e;0<n(".wcfmmp-store-list-map").length&&(g(),e={action:"wcfmmp_stores_list_map_markers",pagination_base:c.find("#pagination_base").val(),paged:c.find("#wcfm_paged").val(),per_row:$per_row,per_page:$per_page,includes:$includes,excludes:$excludes,has_product:$has_product,has_orderby:$has_orderby,sidebar:$sidebar,theme:$theme,search_term:n(".wcfmmp-store-search").val(),wcfmmp_store_category:n("#wcfmmp_store_category").val(),wcfmmp_store_country:n("#wcfmmp_store_country").val(),wcfmmp_store_state:n("#wcfmmp_store_state").val(),search_data:jQuery(".wcfmmp-store-search-form").serialize(),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce},s=n.post(wcfm_params.ajax_url,e,function(e){e.success&&(e=e.data,w(n.parseJSON(e)))}))}({init:function(){f.on("change","select#wcfmmp_store_country",this.state_select)},state_select:function(){var e=wc_country_select_params.countries.replace(/&quot;/g,'"'),e=n.parseJSON(e),t=n("#wcfmmp_store_state"),s=t.val(),o=n(this).val();t.data("required");if(e[o])if(n.isEmptyObject(e[o]))t.is("select")&&n("select#wcfmmp_store_state").replaceWith('<input type="text" class="wcfm-text wcfm_ele" name="wcfmmp_store_state" id="wcfmmp_store_state" placeholder="'+wcfmmp_store_list_messages.choose_state+' ..." />'),s?n("#wcfmmp_store_state").val(s):n("#wcfmmp_store_state").val("");else{var a,r,c=u="",m=e[o];for(a in m)m.hasOwnProperty(a)&&(c=c+'<option value="'+a+'"'+r+">"+m[a]+"</option>");t.is("select")&&n("select#wcfmmp_store_state").html('<option value="">'+wcfmmp_store_list_messages.choose_state+" ...</option>"+c),t.is("input")&&(n("input#wcfmmp_store_state").replaceWith('<select class="wcfm-select wcfm_ele" name="wcfmmp_store_state" id="wcfmmp_store_state"></select>'),n("select#wcfmmp_store_state").html('<option value="">'+wcfmmp_store_list_messages.choose_state+" ...</option>"+c))}else t.is("select")&&n("select#wcfmmp_store_state").replaceWith('<input type="text" class="wcfm-text wcfm_ele" name="wcfmmp_store_state" id="wcfmmp_store_state" placeholder="'+wcfmmp_store_list_messages.choose_state+' ..." />'),n("#wcfmmp_store_state").val(u),"N/A"==n("#wcfmmp_store_state").val()&&n("#wcfmmp_store_state").val("");n(document.body).trigger("wcfm_store_list_country_changed")}}).init();{function w(o){var c,m,e;$icon_width=parseInt(wcfmmp_store_list_options.icon_width),$icon_height=parseInt(wcfmmp_store_list_options.icon_height),"google"==wcfm_maps.lib?(c=new google.maps.LatLngBounds,m=new google.maps.InfoWindow,n.each(o,function(e,t){var s,o=new google.maps.LatLng(t.lat,t.lang),a=(c.extend(o),{url:t.icon,scaledSize:new google.maps.Size($icon_width,$icon_height)}),o=new google.maps.Marker({position:o,map:p,animation:google.maps.Animation.DROP,title:t.name,icon:a,zIndex:e}),r=t.info_window_content;google.maps.event.addListener(o,"click",(s=o,function(){m.setContent(r),m.open(p,s)})),p.setCenter(o.getPosition()),l.push(o)}),wcfmmp_store_list_options.is_cluster&&(e=wcfmmp_store_list_options.cluster_image,markerClusterer&&markerClusterer.clearMarkers(),markerClusterer=new MarkerClusterer(p,l,{imagePath:e})),$auto_zoom&&0<o.length&&p.fitBounds(c)):(markersGroup&&markersGroup.clearLayers(),n.each(o,function(e,t){var s=L.icon({iconUrl:t.icon,iconSize:[$icon_width,$icon_height]}),s=L.marker([t.lat,t.lang],{icon:s}).bindPopup(t.info_window_content);l.push(s),markersGroup=L.featureGroup(l).addTo(p),$auto_zoom&&0<o.length&&setTimeout(function(){p.fitBounds(markersGroup.getBounds())},1e3)}))}function g(){if("google"==wcfm_maps.lib)for(var e=0;e<l.length;e++)l[e].setMap(null);l=[]}0<n(".wcfmmp-store-list-map").length&&(n(".wcfmmp-store-list-map").css("height",n(".wcfmmp-store-list-map").outerWidth()/2),l=[],p=markerClusterer=markersGroup="",_=wcfmmp_store_list_options.is_poi?[]:[{featureType:"poi",elementType:"labels",stylers:[{visibility:"off"}]}],"google"==wcfm_maps.lib?(_={zoom:$map_zoom,center:new google.maps.LatLng(wcfmmp_store_list_options.default_lat,wcfmmp_store_list_options.default_lng,13),mapTypeId:wcfm_maps.map_type,styles:_},p=new google.maps.Map(document.getElementById("wcfmmp-store-list-map"),_)):(p=L.map("wcfmmp-store-list-map",{center:[wcfmmp_store_list_options.default_lat,wcfmmp_store_list_options.default_lng],minZoom:2,zoom:$map_zoom,zoomAnimation:!1}),wcfmmp_store_list_options.is_allow_scroll_zoom||p.scrollWheelZoom.disable(),L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{subdomains:["a","b","c"]}).addTo(p),r&&0<n("#wcfmmp_radius_addr").length&&(n("#wcfmmp_radius_addr").remove(),p.addControl(r),$wcfmmp_radius_addr=n("#wcfm_radius_filter_container").find(".search-input").addClass("wcfmmp-radius-addr").attr("id","wcfmmp_radius_addr").css("float","none").attr("placeholder",wcfmmp_store_list_options.search_location),$lat=n("#wcfmmp_radius_lat").val(),$lng=n("#wcfmmp_radius_lng").val(),$lat)&&$lng&&n.get("https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat="+$lat+"&lon="+$lng,function(e){$wcfmmp_radius_addr.val(e.display_name)})),wcfmmp_store_list_options.is_geolocate&&$current_location_fetched||d())}});