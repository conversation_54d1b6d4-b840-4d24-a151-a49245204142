﻿=== WCFM - Frontend Manager for WooCommerce along with Bookings Subscription Listings Compatible ===
Contributors: wclovers
Tags: woocommerce marketplace, multivendor marketplace, multi vendor, product vendors, wp user frontend, product vendor dashboard, ecommerce, vendor shop manager, woocommerce frontend shop manager, wcvendors, woocommerce frontend manager, wc frontend manager, woocommerce frontend shop manager, shop manager, ecommerce, e-commerce, woocommerce live manager
Donate link: https://www.paypal.me/wclovers/25usd
Requires at least: 4.4
Tested up to: 6.8
WC requires at least: 3.0
WC tested up to: 9.9.0
Requires PHP: 5.6
Stable tag: 6.7.19
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Vendor frontend store/shop manager for WC Marketplace, WC Vendors, WC Product Vendors & Dokan with Bookings, Listings & Subscriptions compatible. Best Frontend Product Manager for WooCommerce with Orders, Coupons, Bookings, Reports - view and manage from your live site front end.

== Description ==

### Best multi vendor frontend store manager for WordPress and WooCommerce

WCFM is the smartest and most featured frontend vendor store/shop manager on WordPress, powered by WooCommerce. It works as vendors' frontend dashboard for most popular woocommerce multi vendor marketplace plugins along with WooCommerce Bookings, Appointments, Rental & Bookings System, Subscriptions compatibility. Import / Export, Knowledgebase, Notification, Direct Messaging and PDF Invoice are some of the many colorful feathers of the wings. We offer you a panoply of cutting-edge solution. 

> Explore WCFM -
> [WCFM Demo](http://wcfmmp.wcfmdemos.com/my-account/) - [Overview](https://www.youtube.com/watch?v=Nozi3VHVD6U) - [Guidelines](https://wclovers.com/blog/woocommerce-frontend-manager/)
>
> FREE Modules -
> [WCFM - Marketplace](https://wordpress.org/plugins/wc-multivendor-marketplace/) - [Demo](http://wcfmmp.wcfmdemos.com/my-account/) - [Documentation](https://wclovers.com/knowledgebase_category/wcfm-marketplace/)
> [WCFM - Membership](https://wordpress.org/plugins/wc-multivendor-membership/) - [Demo](http://wcfmmp.wcfmdemos.com/vendor-membership/) - [Documentation](https://wclovers.com/knowledgebase/wcfm-membership/)
>
> Premium addons also add many more feathers into the frontend wing - 
> [WCFM - Ultimate](https://wclovers.com/product/woocommerce-frontend-manager-ultimate/) - [Overview](https://youtu.be/gitvRdLMTjw) - [Guidelines](https://wclovers.com/blog/why-woocommerce-frontend-manager-ultimate/)
> [WCFM - Groups & Staffs](https://wclovers.com/product/woocommerce-frontend-manager-groups-staffs/) - [Demo](http://wcfmgs.wcfmdemos.com/my-account/) - [Documentation](https://wclovers.com/knowledgebase/wcfm-groups-staffs/)
> [WCFM - Delivery](https://wclovers.com/product/woocommerce-frontend-manager-delivery/) - [Demo](http://wcfmmp.wcfmdemos.com/my-account/) - [Documentation](https://wclovers.com/knowledgebase/wcfm-marketplace-delivery/)
> [WCFM - Affiliate](https://wclovers.com/product/woocommerce-frontend-manager-affiliate/) - [Documentation](https://docs.wclovers.com/wcfm-affiliate/)
> [WCFM - Analytics](https://wclovers.com/product/woocommerce-frontend-manager-analytics/) - [Demo](https://wcfmmp.wcfmdemos.com/my-account/) - [Documentation](https://wclovers.com/knowledgebase/wcfm-analytics/)
> [WCFM - Product HUB](https://wclovers.com/product/woocommerce-frontend-manager-product-hub/) - [Demo](https://wcfmmp.wcfmdemos.com/my-account/) - [Documentation](https://wclovers.com/knowledgebase/)

🎉 Let your vendors give quick real-time support via [Live Chat](https://docs.wclovers.com/live-chat/)

On any days, we provide <strong>6 hours</strong> turnaround time to reply every query, even in our busiest schedule. All you need to do is to reach us either via our [support forum](https://wclovers.com/forums) or [WordPress.org](https://wordpress.org/support/plugin/wc-frontend-manager)

= Marketplace Compatibility =

- WCFM has own multi-vendor module - <strong>WooCommerce Multivendor Marketplace</strong> - [Documentation](https://wclovers.com/blog/woocommerce-multivendor-marketplace-wcfm-marketplace/) - [Demo](http://wcfmmp.wcfmdemos.com/my-account/)

Beside this it's compatible with WordPress-WooCommerce other most popular multivendor marketplace plugins as well, e.g Dokan, WC Vendors etc ..

= Booking Compatibility =

Exclusively compatible with <strong>WooCommerce Bookings</strong> -

- <strong>WC Bookings</strong> - [Overview](https://youtu.be/PqSm6jO6bPY) - [Documentation](https://wclovers.com/documentation/wcfm-wc-booking/) - [Demo](http://wcbooking.wcfmdemos.com/my-account/)

[youtube https://www.youtube.com/watch?v=sw4RTICctTc]

= WCFM (Membership) Pay for Product Option =

This will allow you to setup a system where vendor has to pay before adding a product -

[youtube https://youtu.be/WwFHorx93Fw]

> Read full feature list here -
>
> [WCFM - how this will going to change your marketplace?](https://wclovers.com/blog/woocommerce-frontend-manager/)

= Exclusive Plugin Support =

🔖 WCFM already compatible with most popular WooCommerce plugins. Check here for [compatible plugins](https://wclovers.com/wcfm-compatible-plugins/)

... You will also have support of <strong>Custom Fields</strong> and full flexibility to enhance WCfM in your own way. Check our [Developer's Guide](https://wclovers.com/documentation/developers-guide/).

= Exclusive Theme Support =

WCFM is fully compatible with any WordPress-WooCommerce themes. You will also have styling option to make WCFM dashboard more look alike your site appearnce. Now we are added exclusive support with the most popular themes.

= Quick view - what will be on your dashboard? =

- Create and Manage Simple, Variable, Grouped, External, Subscription, Bookable, Accommodation, Auction, Rental, Job Package and Resume Package Products
- Product Enquiry Module
- Support TIcket Module
- Vendor Followers Module
- Email Verification Module
- Shipment Tracking Module
- Vendor PDF Invoices & Packing Slick (Ultimate)
- Bulk Stock manager Module (Ultimate)
- Vendor verification Module (Ultimate)
- Vendor Groups & Staffs
- Message / Chat board
- Notification Module
- Vendor Article Module
- Vendor Customer Module
- Knowledgebase Module
- Product, gallery limit
- Category restriction
- Featured and Duplicate Product
- Vendor - Store Admin Direct messaging (Chat) system (Ultimate)
- WP Job Manager listings associate with products (Ultimate)
- Product <strong>Featured image</strong> and image gallery
- Product <strong>Category</strong> and <strong>Tag</strong> manage
- Product <strong>Custom Taxonomy</strong> (e.g. Brand) manage
- Product <strong>Stock</strong> manage
- Product <strong>Sale price</strong> control and scheduling
- Product <strong>Shipping</strong> manage
- Product <strong>Tax</strong> manage
- Product <strong>Attributes</strong>
- Product <strong>Advanced</strong> options
- Downloadable and Virtual product (Ultimate)
- Download limit restriction (Ultimate)
- View and manage <strong>Coupons</strong>
- Create and Edit <strong>Coupons</strong> 
- Coupon <strong>Usage Restriction</strong> (Ultimate)
- Coupon <strong>User limit restrictions</strong> (Ultimate)
- View and Manage <strong>Orders</strong>
- View full <strong>Order Details</strong> with tax and shipping info
- Update <strong>Order Status</strong> (Ultimate)
- Add <strong>Order Note</strong> (Ultimate)
- Full featured <strong>WC Reports</strong>
- Reports by <strong>Sales Date</strong>
- Reports of <strong>Out of Stock</strong> product
- Reports by <strong>Product</strong> (Ultimate)
- Reports by <strong>Coupon Usage</strong> (Ultimate)
- Reports of <strong>Low Stock</strong> Product (Ultimate)
- Delete Product, Coupon and Order (Ultimate)
- Manage product from anywhere of your Store using <strong>Quick Edit</strong> (Ultimate)

= Translations =

- Potuguese (Thanks to Rafael Sartori)
- Russian   (Thanks to Alexey Seregin)
- German    (Thanks to Ciao)
- Spanish   (Thanks to Felipe @sophivorus)
- Czech     (Thanks to Fany VanDaal)
- Arbic
- Bulgarian

= Feedback = 

All we want is love. We are extremely responsive about support requests - so if you face a problem or find any bugs, shoot us a mail or post it in the support forum, and we will respond within 6-12 hours(during business days). If you get the impulse to rate the plugin low because it is not working as it should, please do wait for our response because the root cause of the problem may be something else. 

It is extremely disheartening when trigger happy users downrate a plugin for no fault of the plugin. 

Feel free to reach us either via our [support forum](https://wclovers.com/forums) or [WordPress.org](https://wordpress.org/support/plugin/wc-frontend-manager), happy to serve anything you looking for. 

Really proud to serve and enhance [WooCommerce](http://woocommerce.com).

Be with us ... Team [WC Lovers](https://wclovers.com)

== Installation ==

= Minimum Requirements =

* WordPress 4.7 or greater
* WooCommerce 3.0 or greater
* PHP version 5.2.4 or greater
* MySQL version 5.0 or greater

= Automatic installation =

Automatic installation is the easiest option as WordPress handles the file transfers itself and you don't need to leave your web browser. To do an automatic install of WooCommerce Frontend Manager, log in to your WordPress dashboard, navigate to the Plugins menu and click Add New.

In the search field type "WooCommerce Frontend Manager" and click Search Plugins. Once you've found our eCommerce plugin you can view details about it such as the point release, rating and description. Most importantly of course, you can install it by simply clicking "Install Now".

= Manual installation =

The manual installation method involves downloading our eCommerce plugin and uploading it to your webserver via your favourite FTP application. The WordPress codex contains [instructions on how to do this here](https://codex.wordpress.org/Managing_Plugins#Manual_Plugin_Installation).

== Frequently Asked Questions ==

= WooCommerce Frontend Manager pages are not found, 404 error ?

Login to your WP dashboard and navigate to Settings -> Permalink, click on "Save Changes". Now everything alright.

= Where can I find WooCommerce Frontend Manager documentation and user guides? =

For quick reference refer to our [user guide](https://wclovers.com/documentation)

= Where can I get support or talk to other users? =

If you get stuck, you can ask for help in the [WCFM Plugin Forum](https://wordpress.org/support/plugin/wc-frontend-manager).

For help with WCFM Ultimate from wclovers.com, use [our helpdesk](https://wclovers.com/forums).

= Will WooCommerce Frontend Manager work with my theme? =

Yes; WooCommerce Frontend Manager will work with any theme, but may require some styling to make it match nicely.

= Does it compatible with other WooCommerce extensions? =

We are working hard to add support of all major WC-extensions. If you are looking for anything right now then feel free to contact us at '<EMAIL>' or [our helpdesk](https://wclovers.com/forums)

= Does it work with Marketplace-Vendor plugins? =

Fully compatible with all major multi-vendor marketplace plugins - <strong>WCFM Marketplace</storng>, <strong>WC Product Vendors</storng>, <strong>Dokan Lite & Dokan Pro</storng> and <strong>WC Vendors</storng>.

== Screenshots ==

1. WCFM Dashboard
2. WCFM Products Dashboard
3. WCFM Orders Dashboard
4. WCFM PDF Invoice
5. WCFM Diverse of Reports
6. WCFM Product Manager
7. WCFM Groups & Staffs
8. WCFM Bookings Calendar
9. WCFM Bookings Dashboard
10. WCFM Bookable Product Manager
11. WCFM Analytics Dashboard
12. WCFM Coupon Manager
13. WCFM Coupons Dashboard
14. WCFM Settings Manager
15. WCFM Profile Manager
16. WCFM Order Details
17. WCFM Reports by Product
18. WCFM Importer / Exporter
19. WCFM Bookings / Appointments Dashboard
20. WCFM Message Board
21. WCFM Knowledgebase
22. WCFM Subscription Product Manager
23. WCFM Quick Product Manager
24. WCFM Dashboard - Tablet view
25. WCFM Dashboard - Mobile view

== Changelog ==

= 6.7.19 =
*Updated - 14/06/2025*

* Enhance - WooCommerce 9.9+ compatibility check added
* Enhance - Compatibility with German Market version 3.47
* Fixed   - Store Manager dashboard settings update issue for vendors

= 6.7.18 =
*Updated - 28/05/2025*

* Fixed - DataTables warning: table id=wcfm-* - invalid JSON response issue fixed

= 6.7.17 =
*Updated - 26/05/2025*

* Enhance - WooCommerce 9.8+ compatibility check added
* Fixed   - Add 'wcfm_allow_setup_page_access' filter and nonce check to fix missing authorization vulnerability in setup page (reported by Brian Sans-Souci (liardom) - Wordfence)

= 6.7.16 =
*Updated - 09/03/2025*

* Enhance - WooCommerce 9.7+ compatibility check added
* Fixed   - Stripe vendor connect issue for cross border countries. Now vendor needs to choose from a list of supported countries.
* Fixed   - Minor bug fixes

= 6.7.15 =
*Updated - 12/01/2025*

* Enhance - WordPress 6.7+ compatibility check added
* Enhance - WooCommerce 9.5+ compatibility check added

= 6.7.14 =
*Updated - 02/10/2024*

* Fixed    – Stripe Connect Issue for vendors where platform country is CANADA

= 6.7.13 =
*Updated - 23/09/2024*

* Enhanced – Stripe Split Payment connect settings now use the store location address as prefilled data.
* Fixed    – Vulnerability in the Customer Manage Section: An unauthorized user can change a customer's email, as reported by Wesley (Wcraft). Credit goes to Wesley for identifying this issue.
* Fixed    – Stripe Split Payment Issue with Cross-Border Payments: If the platform account and connected account(s) are not from the same country, Stripe payments fail when using "direct charge" mode.
* Fixed    – Issue related to report section Out of stock database query
* Fixed    – Minor typo fix, language pot file updated

= 6.7.12 =
*Updated - 23/06/2024*

* Fixed    – PHP Notice: Function ID was called incorrectly. Order properties should not be accessed directly.
* Fixed    - Replaced strlen() with mb_strlen() to support UTF-8 characters for menu labels in store-manager dashboard.
* Enhanced – wcfm_get_customers_orders_stat() function to accurately calculate the total amount spent by vendors. This update resolves previous inaccuracies in displaying vendor-specific order statistics.

= 6.7.11 =
*Updated - 07/06/2024*

* Enhanced – WordPress 6.5+ compatibility added
* Enhanced – WooCommerce 8.9+ compatibility added

= 6.7.10 =
*Updated - 23/03/2024*

* Enhanced – WooCommerce 8.7+ compatibility added
* Fixed    - Menu Manager Label changed after settings update

= 6.7.9 =
*Updated - 10/03/2024*

* Fixed    - Vulnerablity to Cross Site Scripting (XSS) issue fixed
* Fixed    - WooCommerce Checkout page stuck when using Stripe Split Pay
* Fixed    - Minor bug fixes

= 6.7.8 =
*Updated - 20/02/2024*

* Enhanced – WooCommerce 8.6+ compatibility added
* Enhanced – jQuery select2 library updated to version 4.0.13
* Fixed    - Order details page error when using WooCommerce 8.6.0+
* Fixed    - Product manage page attribute Select All/None issue
* Fixed    - Bulk action select all checkbox issue for Enquiry, Message, Withdrawal

= 6.7.7 =
*Updated - 11/01/2024*

* Fixed   - Resolve fatal error in vendor dashboard payment settings due to missing admin stripe credentials

= 6.7.6 =
*Updated - 03/01/2024*

* Fixed   - Vendor can not see withdrawal issue resolved

= 6.7.5 =
*Updated - 02/01/2024*

* Enhance - WooCommerce 8.4+ compatibility check added
* Enhance - Stripe Connect implemented with Stripe Accounts API instead of the deprecated OAuth module
* Fixed   - Order details gross total issue fixed

= 6.7.4 =
*Updated - 29/11/2023*

* Enhance - WordPress 6.4+ compatibility check added
* Fixed   - Order table ajax error issue resolved

= 6.7.3 =
*Updated - 23/11/2023*

* Enhance - WooCommerce 8.3+ compatibility check added
* Enhance - WooComerce HPOS compatibility added

= 6.7.2 =
*Updated - 04/10/2023*

* Enhance - WordPress 6.3+ compatibility check added
* Enhance - WooCommerce 8.1+ compatibility check added
* Enhance - Enquiry bulk delete feature added
* Fixed   - Compatibility with the Product Size Charts Plugin for WooCommerce(*******) plugin
* Fixed   - Compatibility with the Tiered Pricing Table for WooCommerce (Premium)(5.5.1) plugin
* Fixed   - Translation & minor other fixes

= 6.7.1 =
*Updated - 16/07/2023*

* Enhance - WordPress 6.2+ compatibility check added
* Enhance - WooCommerce 7.8+ compatibility check added

= 6.7.0 =
*Updated - 06/04/2023*

* Feature - Store multiple branch support added (WCFM - Ultimate needed)
* Fixed   - Minor bug fixes

= 6.6.9 =
*Updated - 27/03/2023*

* Fixed – Location tab not showing for vendor if WCFM – Marketplace version 3.5.10

= 6.6.8 =
*Updated - 26/03/2023*

* Enhance - WooCommerce 7.5+ compatibility check added
* Enhance - Manual approval by admin for withdrawal request added (filter: "wcfm_allow_withdrawal_requests_manually_approve")
* Fixed   - Block theme error fixed
* Fixed   - Other minor fixes

= 6.6.7 =
*Updated - 14/01/2023*

* Enhance - WooCommerce 7.3+ compatibility check added
* Fixed   - Elementor Errors resolved
* Fixed   - WPML conflict issues resolved
* Fixed   - Minor JavaScript errors resolved
* Fixed   - PHP 8.0+ some issues resolved

= 6.6.6 =
*Updated - 29/10/2022*

* Enhance - WooCommerce 7.0+ compatibility check added
* Enhance - PHP 8.0+ compatibility added

= 6.6.5 =
*Updated - 18/07/2022*

* Enhance - WooCommerce 6.7+ compatibility check added
* Fixed   - WPML email translation issue resolved

= 6.6.4 =
*Updated - 22/05/2022*

* Enhance - WooCommerce 6.5+ compatibility check added
* Fixed   - WCFM APP store order status update issue resolved

= 6.6.3 =
*Updated - 16/04/2022*

* Enhance - WooCommerce 6.4+ compatibility check added
* Fixed   - WCFM APP store list not visible issue resolved

= 6.6.2 =
*Updated - 19/02/2022*

* Enhance - WordPress 5.9+ compatibility check added
* Enhance - WooCommerce 6.2+ compatibility check added
* Enhance - Some queries modified to prevent SQL Injection

= 6.6.1 =
*Updated - 21/11/2021*

* Fixed   - Membership subscription payment processing issue resolved

= 6.6.0 =
*Updated - 19/11/2021*

* Enhance - Ajax functions nonce check and user permission check added
* Fixed   - Admin area PHP error issue resolved

= 6.5.13 =
*Updated - 14/11/2021*

* Enhance - WooCommerce 5.9+ compatibility check added
* Fixed   - Some security issues resolved

= 6.5.12 =
*Updated - 10/10/2021*

* Enhance - Some new filters added
* Fixed   - Vendor reports "last month" and "current mmonth" query issue resolved
* Fixed   - Few security issues resolved

= 6.5.11 =
*Updated - 26/09/2021*

* Enhance - WordPress 5.8+ compatibility check added
* Enhance - WooCommerce 5.7+ compatibility check added
* Fixed   - Enquiry/Inquiry not deleting issue resolved

= 6.5.10 =
*Updated - 24/07/2021*

* Enhance - WordPress 5.8+ compatibility check added
* Enhance - WooCommerce 5.5+ compatibility check added
* Enhance - Many security check improved

= 6.5.9 =
*Updated - 13/06/2021*

* Enhance - WooCommerce 5.4+ compatibility check added
* Enhance - Dashboard setting form security check improved

= 6.5.8 =
*Updated - 24/04/2021*

* Enhance - WooCommerce 5.2+ compatibility check added
* Fixed   - wcfm-script-core.js "file" undefined issue resolved
* Fixed   - Store setting dulicate slug error not showing issue resolved 
* Fixed   - Product Custom attributes with "double quotes" variation not properly saving issue resolved
* Fixed   - Downloadable product multiple file upload issue resolved 

= 6.5.7 =
*Updated - 28/03/2021*

* Enhance - WordPress 5.7+ compatibility check added
* Enhance - WooCommerce 5.1+ compatibility check added

= 6.5.6 =
*Updated - 06/01/2021*

* Enhance - WordPress 5.6+ compatibility check added
* Enhance - WooCommerce 4.8+ compatibility check added
* Enhance - Facebook for WooCommerce 2.2+ compatibility added

= 6.5.5 =
*Updated - 08/11/2020*

* Feature - Facebook store sync with vendor store option added - [Documentation](https://docs.wclovers.com/facebook-for-marketplace/)
* Enhance - WooCommerce 4.6+ compatibility check added
* Enhance - WCFM login popup credential check improved
* Enhance - Vendor store location setting current location detect option added
* Enhance - Add product "Verification Product Limit" reached message display improved
* Fixed   - Variation stock status not properly sync with main product stock ststus issue resolved 
* Fixed   - Vendor shipping setting auto-reset issue resolved
* Fixed   - Product "Menu Order" not properly saving issue resolved

= 6.5.4 =
*Updated - 28/08/2020*

* Fixed   - Notice/Announcement notification not triggering issue resolved

= 6.5.3 =
*Updated - 28/08/2020*

* Enhance - WooCommerce 4.4+ compatibility check added
* Enhance - Country Based Restrictions for WooCommerce compatibility added
* Enhance - Product custom fields "Multi Select" field type support added
* Enhance - Booking list data export option added
* Tweak   - Dashboard pages setting drop-downs changed to choosen for better performance
* Fixed   - Store Followers capability not working issue resolved
* Fixed   - WooCommerce Appointments calendar display issue resolved
* Fixed   - ShipStation order sync Tax, Shipping cost not working as per vendor issue resolved
* Fixed   - ShipStation order sync order note and status not properly updating issue resolved
* Fixed   - "Category wise attributes" previously selected attributes not reset on category change issue resolved
* Fixed   - WooCommerce - PDF Vouchers "Generate Code" not working from "Popup Add Product" issue resolved 
* Fixed   - During add product WooCommerce Product Add-ons and WooCommerce Tab Manager conflict issue resolved
* Fixed   - Vendor coupon "Exclude Products" not saving issue resolved

= 6.5.2 =
*Updated - 02/08/2020*

* Feature - ShipStation integration for vendors (works only for WCFM Marketplace, WCFM Ultimate required) - [Documentation](https://docs.wclovers.com/shipstation/)
* Enhance - Vendor Profile ACF user type fields support added (WCFM Ultimate required)
* Enhance - Product custom fields exclude product type condition "Virtual Product" support added
* Enhance - WooCommerce 4.3+ compatibility added
* Tweak   - YiTH Request a Quote one vendor's products at a time restriction added
* Tweak   - PW Gift Cards redeem only for the issuer vendor's products restriction added 
* Tweak   - ELEX Shipping addons compatibility revoked, instead recommend to use [WooCommerce Multi-Vendor Support for ELEX Shipping Plugins](https://elextensions.com/plugin/woocommerce-multi-vendor-add-on-for-elex-shipping-plugins/)
* Fixed   - Category wise attributes not working properly for category limit "1" issue resolved
* Fixed   - Product type categories not saving issue resolved
* Fixed   - WooCommerce PDF Vouchers - Start Date, End Date, Expiry Date, Primary Vendor, Vednor Logo not properly updating issue resoved
* Fixed   - WPML translated product overrides original product values issue resolved
* Fixed   - Bulk Stock Manager page filters are not working issue resolved

= 6.5.1 =
*Updated - 09/06/2020*

* Enhance - WooCommerce 4.2+ compatibility added
* Enhance - "Filter Products by Store" widget added for WooCommerce product archive pages to filter products by vendor
* Enhance - YiTH Auction Premium 1.4+ compatibility added
* Enhance - Auction bid delete option added
* Enhance - Vendor order list search enahnced by order meta
* Enhance - Vendor order support added for Manual booking associate with existing order
* Enhance - WCFM Delivery - delivery time slots WPML compatibility added
* Enahnce - Checkout delivery location field compatibility added for "Force shipping to the customer billing address" setting option
* Enahnce - YiTH Request a Quote Premium "accepted quote" pay page shipping compatibility added
* Enahnce - YiTH Request a Quote Premium "accepted quote" vendor order compatibility added
* Fixed   - Vendor product variation export issue resolved
* Fixed   - Store list page map pointer not visible at proper position issue resolved
* Fixed   - Store list page map pointer position change on map zoom change issue resolved
* Fixed   - Trashed orders visible to vendors issue resolved 
* Fixed   - Germanized for WooCommerce variable product edit issue resolved
* Fixed   - Admin bar quick menu links giving 404 issue resolved

= 6.5.0 =
*Updated - 13/05/2020*

* Enhance - Page load performence improved
* Enhance - Store List search by "City" and "Zipcode" option added - [Documentation](https://docs.wclovers.com/store-list/)
* Enhance - Products Custom Taxonomy add/access capability option added (WCFM Ultimate required)
* Enhance - Edit order item wise cost and quantity edit option added (WCFM Ultimate required)
* Enhance - Vendors capability check improved
* Enhance - Vendor additional info edit "File/Image" type fields support added
* Enhance - Category wise Attributes WPML compatibility added
* Enhance - Store setup responsive and RTL CSS improved
* Fixed   - FooEvents - vendors are able to see all event tickets issue resolved
* Fixed   - Product manager on category change "variation" tab broken issue resolved
* Fixed   - Coupon "Draft" button save issue resolved

= 6.4.9 =
*Updated - 08/05/2020*

* Enhance - WooCommerce 4.1 compatibility added
* Enhance - Delivery Time module compatibility added - [WCFM Delivery addon required](https://wclovers.com/product/woocommerce-frontend-manager-delivery/) - [Documentation](https://docs.wclovers.com/delivery-time/)
* Enhance - WooCommerce Bookings "Global Availability" vendor wise compatibility added (WCFM Ultimate required)
* Enhance - Dashboard fields display improved
* Enhance - Dashboard menu RTL responsive display improved
* Enhance - German Market "Age Restrction" module support added
* Enhance - German Market product manage fields not visible for "Variable" product issue resolved
* Fixed   - Germanized for WooCommerce "Delivery Time" field edit issue resolved

= 6.4.8 =
*Updated - 17/04/2020*

* Feature - YiTH Request a Quote Premium compatibility added (WCFM Ultimate Required)
* Enhance - WooCommerce German Market product fields compatibility added
* Enhance - Pending approval product "Reject with Reason" option added
* Enhance - WooCommerce Simple Auction Relist and Auction Fail vendor email added
* Enhance - Membership module option added - Manager from WCFM Admin Setting -> Modules
* Fixed   - Reverse withdrawal screen "select2" library missing issue resolved 

= 6.4.7 =
*Updated - 06/04/2020*

* Enhance - Variable products "Create variations from all attributes" option added
* Enhance - WooCommerce Measurement Price Calculator version 3.17+ compatibility added
* Enhance - Listing "approve" option added for Admin
* Enhance - Product object checking improved
* Enhance - Draft Listing "continue submission" option added
* Enhance - [wcfm_policy] - short code added, works for single product and vendor's store page
* Enhance - Dashboard products list sort by "View Count" option added
* Enhance - FooEvents Ticket "Resend" option added 
* Tweak   - Dashboard page "Float Button" by default disabled for mobile and tablet view
* Fixed   - Product description image upload from external url not visible issue resolved 
* Fixed   - Sales reports previous year graph display issue resolved
* Fixed   - Dashborad mobile view "My Store" not clickable issue resolved
* Fixed   - Group archive showing all vendors issue resolved
* Fixed   - Inquiry and Support email quotes display with slash issue resolved 

= 6.4.6 =
*Updated - 15/03/2020*

* Feature - Post Expirator compatibility added
* Enhance - WooCommerce 4.0+ compatibility added
* Enhance - WP 5.4+ compatibility added
* Enhance - WCFM Marketplace Admin order list "vendor/store filter" option added
* Tweak   - Includes Emogrifier composer package instead of including into includes/libraries
* Fixed   - Accommodation product cost rule edit PHP warning issue resolved
* Fixed   - PW Gift Cards UTF8 search issue resolved
* Fixed   - FooEvents ticket logo not showing in ticket issue resolved
* Fixed   - Without policy content "Policies" heading visible under order details page issue resolved
* Fixed   - Without "policy" and "customer support" content headings visible under store invoice issue resolved
* Fixed   - Inquiry, Support Ticket and Refund popup form "Submit" button multiple click issue resolved
* Fixed   - Vendors are able to manage restricted capabilities for their staff issue resolved
* Fixed   - WooCommerce Additional Variation Images edit issue resolved

= 6.4.5 =
*Updated - 07/03/2020*

* Feature - PW WooCommerce Gift Cards compatibility added
* Enhance - WooCommerce Product attributes "Custom Order" support added
* Enhance - Store Manager page Divi Builder support added
* Enhance - FooEvent Check-in APP Rest API compatibility added
* Fixed   - Accommodation product cost rule save issue resolved
* Fixed   - RnB Rental & Booking "Quote Details" page error issue resolved

= 6.4.4 =
*Updated - 18/02/2020*

* Enhance - Messages direct reply option added
* Enhance - Notificaton email content capability check added
* Enhance - WCFM - Affiliate notification compatibility added
* Enhance - Inquiry and Support Tickets "My Account" view improved
* Enhance - Firebase Chat conversation email format improved
* Enhance - Firebase Chat History and Offline messages delete option added
* Enhance - Bookable and Appointment products order commission invoice improved
* Enhance - Dashbord mobile view display improved
* Enhance - Few missing strings added to translation file

= 6.4.3 =
*Updated - 10/02/2020*

* Enhance - Vendor location setting "Leaflet" map library support added
* Enhance - Product image gallery "unlimited" image upload support added  
* Fixed   - Mobile view product image gallery upload issue resolved

= 6.4.2 =
*Updated - 08/02/2020*

* Feature - Firebase chat - Offline Messages and Chat History view added (WCFM Ultimate required)
* Enhance - Reports data generation performence improved
* Enhance - Vendor "Store Invoice" setting manage option added under Admin's vendor manage
* Enhance - "wcfm_store_info", "wcfm_chat_now" and "wcfm_follow" short codes will work for any vendor created post page
* Dev     - WCFM Marketplace Stripe "Express Connect API" support added, enbale by return TRUE to this filter - wcfm_is_allow_stripe_express_api
* Fixed   - Firebase chat - Chat copy email not sending issue resolved
* Fixed   - Vendor verification not opening issue resolved
* Fixed   - Auction and Appointment product manage tabs not properly option issue resolved 
* Fixed   - Popup add product display issue resolved
* Fixed   - OpenStreetMap load over HTTPS/SSL issue resolved
* Fixed   - Report graph point showing long decimal numbers issue resolved
* Fixed   - Product "quick edit" not saving issue resolved

= 6.4.1 =
*Updated - 25/01/2020*

* Enhance - WooCommerce 3.9+ compatibility added
* Enhance - Vendor orders liting page search by "Order ID" option added
* Enhance - Whole order shippment tracking update option added under order details 
* Enhance - WC Appointments calendar display improved
* Enhance - Product manager "Attributes" selector view simplified
* Enhance - Inquiry and Support response email content improved
* Enhance - Product CSV Import as Admin "Store" column support added to import products for specific store/vendor
* Enhance - Schedule order email "Store Invoice" generate and attach with mail support added
* Enhance - Vendor coupon apply validation added
* Enhance - Fancy Product Designer template builder display improved
* Enhance - Store reference added with vendor's order note
* Enhance - WoodMart theme 360 degree image and variation image gallery compatibility added 
* Enhance - Advanced Product Size Charts for WooCommerce plugin compatibility added
* Enhance - Germanized for WooCommerce 3.1+ compatibility added
* Enhance - WooCommerce Bookings "Resources" delete and filter by store/vendor option added
* Enhance - WCFM Marketplace "Group Archive" page map support added
* Dev     - Order item policies show by vendor filter "wcfm_is_allow_order_item_policies_by_vendor" added, return TRUE
* Dev     - Order policies disable for emails filter "wcfm_is_allow_order_item_policies_by_vendor" added, return FALSE
* Dev     - Order policies disable for invoice filter "wcfm_is_allow_policy_under_order_invoice" added, return FALSE
* Fixed   - Variation "sales schdule" capability not working issue resolved
* Fixed   - Store "Default Banner" and "Default Logo" saving and loading issue resolved
* Fixed   - Non-vendor products author reset to current user on update issue resolved
* Fixed   - Firebase chat box load issue for non-logged in users issue resolved
* Fixed   - WooCommerce PDF Voucher "Recipient Email" label not saving issue resolved
* Fixed   - Electro theme and Fancy Product Designer conflict issue resolved

= 6.4.0 =
*Updated - 18/01/2020*

* Feature - License Manager for WooCommerce compatibility added (WCFM Ultimate required)
* Enhance - WooCommerce Quick View Pro compatibility added
* Enhance - ELEX Role based Price compatibility added
* Enhance - WP Offload Media Pro compatibility added
* Enhance - GEO my WP Leaflet map library support added
* Enhance - Dashboard logout url change to site's frontend url
* Fixed   - GEO Location Map center setup setting not loading issue resolved

= 6.3.8 =
*Updated - 16/01/2020*

* Enhance - WP Offload Media support added
* Fixed   - Vennor location setting Google map not loading issue resolved

= 6.3.7 =
*Updated - 11/01/2020*

* Feature - Open Street Map (Free map library) support added
* Feature - Firebase Free Chat API support added - [Documentation](https://docs.wclovers.com/live-chat/)
* Enhance - Fancy Product Designer 4.2+ compatibility added
* Enhance - WooCommerce Multilingual 4.7+ compatibility added
* Enhance - Listing "Add Product" option "Limit and Space" capability check added  
* Fixed   - Accommodation product "Has Resource" and "Has Persons" opton not saving issue resolved

= 6.3.6 =
*Updated - 21/12/2019*

* Enhance - WooCommerce PDF Invoice & Packing Slips plugin 2.4+ compatibility added
* Enhance - WooCommerce Measurement and Price calculator 3.17+ compatibility added
* Enhance - FooEvents PDF Tickets 1.7+ compatibility added
* Enhance - FooEvents "Add event to EventBrite" option support added
* Enhance - Bookable product date range cost rule "equals" option support added
* Enhance - Vendor registration custom fields "multi-select" type field support added
* Enhance - WooCommerce Tiered Table Price Premium compatibility added
* Tweak   - On Booking cancel "Admin Calcel Email" send to vednors, previously "Customer Cancel Email" send to vendors
* Fixed   - Product "excerpt" and "description" fields custom validation not working issue resolved
* Fixed   - WooCOmmerce Sequential Order Numbers not visible every where issue resolved 
* Fixed   - WooCommerce Product Badges not reset issue resolved
* Fixed   - Add announcement page showing "Invalid Topic" issue resolved
* Fixed   - Product custom validation message visible shipment tracking update form issue resolved
* Fixed   - Wholesale products not visible to non-wholesale users issue resolved

= 6.3.5 =
*Updated - 29/11/2019*

* Fixed   - Admin's post count showing "0" issue resolved
* Fixed   - Article/Post Yoast SEO meta save issue resolved

= 6.3.4 =
*Updated - 21/11/2019*

* Enhance - WordPress 5.3 compatibility added
* Enhance - WooCommerce 3.8 compatibility added
* Enhance - WooCommerce Tiered Table Price plugin compatibility added
* Enhance - WooCommerce Deposit remaining payment invoice generate compatibility added
* Enhance - Vendor product import new category create and assign to products support added
* Enhance - Article/Post Yoast SEO meta support added 
* Enhance - Product and Article All in One SEO meta title and description support added
* Enhance - Product and Article Rank Math SEO focus keyword and meta description support added
* Enhance - WPML translated products quick edit view restricted to edit meta information
* Enhance - Product bulk delete option added
* Enhance - FooEvent PDF ticket download option added
* Fixed   - FooEvent ticket barcode not rendering issue resolved
* Fixed   - Admin's product count showing "0" issue resolved
* Fixed   - Variation "enable/disable" not working issue resolved
* Fixed   - Product "Off Line" bit reset issue resolved
* Fixed   - RnB Rental & Booking Product inventory update issue resolved
* Fixed   - On membership cancel archived/draft products able to re-publish issue resolved
* Fixed   - Vendors able to import more than product limit capability issue resolved
* Fixed   - Without product limit vendor able to import product issue resolved
* Fixed   - Auction product manager tab not visible properly issue resolved
* Fixed   - Analytics view broken due to deleted products issue resolved
* Fixed   - Notification sound file load issue resolved

= 6.3.3 =
*Updated - 22/10/2019*

* Enhance - WooCommerce Appointments 4.7+ version compatibility added
* Enhance - WooCommerce Simple Auction "Sealed Bid?" support added
* Enhance - WooCommerce Deposit "Payment Plan" support added
* Enhance - Follow button ask for login to non-logged in user option added
* Enhance - Customer manage "company field" added
* Fixed   - Vendors not able to see "Auction Products" bids issue resolved

= 6.3.2 =
*Updated - 03/10/2019*

* Enhance - WCFM Marketplace Withdrawal, Withdrawal Request, Reverse Withdrawal screens "date range" filter compatibility added
* Enhance - Toolset Maps 1.8+ version compatibility added
* Enhance - Product and Vendor Registration custom filed "HTML Block" field type support added
* Fixed   - Product "File/Image" type custom field "required" check not working issue resolved
* Fixed   - Custom taxonomies not de-associated from product issue resolved
* Fixed   - Live Chatbox not closing issue resolved

= 6.3.1 =
*Updated - 22/09/2019*

* Feature - WCFM Marketplace Wirecard (Moip) payment gateway (customer & vendor split payment) compatibility added
* Enhance - WCFM Marketplace Subscription Renewal order "Admin Mode Commission" support added
* Enhance - WP User Avatar compatibility - Shop Staff, Affiliate User, Delivery Person not able to upload "Profile Image" taken care of
* Enhance - WCFM Marketplace Store SEO WPML compatibility added
* Enhance - WCFM Marketplace - "Sold by Label" applied for all dashboard modules vendor/store display
* Enhance - WCFM Marketplace - "wp-admin" articles, coupons, orders, bookings, appointments, subscriptions listing "Store/Vendor" reference added
* Enhance - WC Appointments "two way" Gcal sync compatibility added for vendors
* Fixed   - WC Appointments "Restricted Days" update issue resolved
* Fixed   - Affiliate Vendor not able to access "products" and "coupons" menu issue resolved

= 6.3.0 =
*Updated - 16/09/2019*

* Feature - Vendors may apply to become affiliate option added ([WCFM Affiliate addon require](https://docs.wclovers.com/wcfm-affiliate/#vendor-as-affiliate))
* Feature - Affiliate commission calculate on vendor's commission compatibility added - [Documentation](https://docs.wclovers.com/wcfm-affiliate/#affiliate-commission-calculation-mode)
* Enhance - Membership subscription using Stripe SCA compatibility added
* Enhance - Product Tags input as "drop-down" option added, vendors allowed to choose from pre-defined tags
* Enhance - Vendor and Affiliate Registration custom fields WPML compatibility added, you may create different type of fields for different languages
* Enhance - WooCommerce Appointments 4.6 calender view compatibility added
* Enhance - WCFM Marketplace new store order email "Group Manager" added in CC (WCFM - Group & Staff addon require)

= 6.2.10 =
*Updated - 09/09/2019*

* Enhance - WCFM Marketplace Stripe Split pay "3D Secure and SCA" compatibility added
* Enhance - Enfold Theme compatibility added - Media Library load, jQuery UI load, Checkbox display etc ... 
* Fixed   - TAX disable order details page PHP warning issue resolved
* Fixed   - Product Custom Validation for "Excerpt" and "Descripton" not working issue resolved

= 6.2.9 =
*Updated - 03/09/2019*

* Feature - WooCommerce Dynamic Pricing compatibility added (WCFM Ultimate require)
* Feature - MSRP for WooCommerce (Algoritmika) compatibility added (WCFM Ultimate require)
* Feature - Cost of Goods for WooCommerce (Algoritmika) compatibility added (WCFM Ultimate require)
* Enhance - WCFM Marketplace Store Rank Math SEO compatibility added
* Enhance - WCFM Marketplace Offline and disable sotre products auto-archived option added
* Enhance - WCFM Marketplace Notification message sync with BuddyPress messages option added
* Enhance - WooCommerce Measurement and Calculator 3.15 version compatibility added
* Enhance - End points slug edit fields custom validation added
* Enhance - WC Bookings "Global Availability Rules" 1.15 version compatibility added
* Enhance - Archived products can be directly accessible by its URL
* Enhance - WCFM Product Bulk Update status change "Archived" status added
* Enhance - Support Ticket "delete" option added for Admin and Vendors
* Enhance - Order details mobile display improved
* Enhance - Product duplicate SKU check improved 
* Tweak   - Product "Inqueries" display order by change latest first
* Tweak   - ACF and Toolset product custom fields allowed to edit for WPML translated products
* Tweak   - WCFM Marketplace "Payment Setting" new tab created under WCFM Admin Setting, previously payment setup comes under "Withdrawal Setting" tab
* Fixed   - Wholesale price rule editable for WPML translated products issue resolved
* Fixed   - Vendor policy setting fields showing HTML content when "Rich Editor" capability disable issue resolved 

= 6.2.8 =
*Updated - 24/08/2019*

* Enhance - WC 3.7 "Product Importer" compatibility added
* Enhance - Product custom field "textarea" display improved
* Enhance - Booking and Appointment confirmation order, Renewal Order WCFM Marketplace Stripe Split Pay compatibility added
* Enhance - Orders list page page in "Mobile View" address display improved
* Enhance - Store Invoice generate as per site's current language compatibility added
* Enhance - WPML translated products "policy" and "advance" options edit option enabled
* Enhance - WPML translated products "wholesale price" edit option disabled 
* Tweak   - WPML translated products meta edit blocked, previously worked as per site's default language
* Enhance - Manual Booking page "Add New Custom" option added for all multi-vendor plugins, previously only work for WCFM Marketplace
* Fixed   - WCFM Marketplace manual add vendor issue resolved
* Fixed   - WCFM Marketplace Vendor preferred payment method not saveing when "Direct PayPal" addon enabled issue resolved
* Fixed   - Cost of Good for WooCommerce empty label visible under invoice and packing slip issue resolved

= 6.2.7 =
*Updated - 13/08/2019*

* Enhance - WCFM Marketplace Vendor Commission Tax deduction compatibility added
* Enhance - WooCommerce Variation Swatches compatibility added (WCFM Ultimate require) 
* Enhance - WC 3.7 compatibility added
* Enhance - WC Bookings 1.15.0 compatibility added
* Enhance - [WooCommerce Quote Request](https://woocommercequoteplugin.com/) plugin's compatibility added 
* Tweak   - "Disable Responsive Float Menu" is now deprecated
* Fixed   - Store Manager dashboard mobile menu scrolling issue resolved
* Fixed   - Vendors' inquiry and Support notification not working sue to Admin notification OFF issue resolved

= 6.2.6 =
*Updated - 05/08/2019*

* Fixed   - Product Featured image not uploading issue resolved 
* Fixed   - Installation Setup screen not set issue resolved
* Fixed   - Archived products visible under "Store Manager" dashboard "All" products list issue resolved 
* Fixed   - Vendors' inquiry and Support notification not working sue to Admin notification OFF issue resolved

= 6.2.5 =
*Updated - 31/07/2019*

* Feature - Product and Article "Featured Image" edit option added
* Feature - Product "Archive" option added
* Feature - Customer "Order Add Note" option added under order details (WCFM Ultimate require)
* Feature - Vendor "Order Add Note" "Attachment" add option added
* Enhance - WPML translated product's meta fields restricted for editing
* Enhance - Wholesale "Quantity Based Rule" support added
* Enhance - WCFM Marketplace Store setup custom color setting compatibility added
* Fixed   - WCFM - Delivery order assign email notification to delivery person not working issue resolved
* Fixed   - WooCommerce Measurement addon data dave issue resolved
* Fixed   - Mobile view ashboard restricted pages menu not collapse issue resolved
* Fixed   - Verification Product limit check applyiing on Admin issue resolved

= 6.2.4 =
*Updated - 19/07/2019*

* Enhance - Sales by Date and Sales by Vendor reports "Print" option added
* Enhance - New Announcement notification for vendors added
* Enhance - WCFM Marketplace vendor orders list "product" filter added
* Enhance - WCFM Marketplace scheduled "Data Cleanup" compatibility added
* Fixed   - Products/Articles publish redirect on Edit Live Products/Articles capability disable issue resolved 
* Fixed   - Manual Appointment "addons" not working issue resolved
* Fixed   - BuddyPress vendor's profile page capability check issue resolved
* Fixed   - Vacation mode check issue resolved for few themes
* Fixed   - Capabiliy page PHP warning issue resolved
* Fixed   - Dashboard Products listing data overlapping issue resolved
* Fixed   - Single product page Inquiry button RTL display issue resolved

= 6.2.3 =
*Updated - 06/07/2019*

* Enhance - WP-Stateless – Google Cloud Storage - custom bucket support added
* Enhance - Customer delete option added
* Fixed   - Product manager custom validation check issue resolved

= 6.2.2 =
*Updated - 03/07/2019*

* Enhance - Coupon, Bulk Stock Manager, Product Import and more module option added 
* Enhance - WilCity theme JS conflict taken care for Store Manager page
* Fixed   - Vendor capability "OFF" product data reset issue resolved 
* Fixed   - Inquiry and Support notification setting not working issue resolved

= 6.2.1 =
*Updated - 26/06/2019*

* Fixed   - Admin "add new vendor" page not loading issue resolved
* Fixed   - Invoice currency display issue resolved

= 6.2.0 =
*Updated - 24/06/2019*

* Feature - WCFM Marketplace Periodic Withdrawal compatibility added
* Enhance - Downloadable products "woocommerce_uploads" directory compatibility added
* Enhance - Store Manager restricted content vendor message shown as per their membership level
* Enhance - Manual Booking and Manual Appointment "Add Customer" option added
* Enhance - WooCommerce PDF Voucher product wise "Voucher Price" option added
* Enhance - "Mark Featured" option added under "Edit Product" screen
* Enhance - Admin Vendor list "Membership" filter option added
* Enhance - Store Invoice download option added under Admin's order details
* Enhance - WCFM Marketplace Store Policies and Customer Support setting option added under Admin's Vendor Manager
* Enhance - WCFM Marketplace Store SEO and Social setting option added under Admin's Vendor Manager
* Fixed   - WC Product Vendors customer list not visible issue resolved
* Fixed   - Customer manage address "State/County Field" load issue resolved
* Fixed   - Profile address "State/County Field" load issue resolved
* Fixed   - Vendor able to see other vendor's order note issue resolved
* Fixed   - WCFM - Category Hierarchy addon "Allowed Categories" capability check issue resolved

= 6.1.6 =
*Updated - 11/06/2019*

* Enhance - YiTH Quick Product view "Catalog Mode" compatibility added
* Enhance - Flatsome Quick Product view "Catalog Mode" compatibility added
* Fixed   - WCFM Installer load issue resolved
* Fixed   - WCFM Marketplace vendors able to see other vendors' order note issue resolved 

= 6.1.5 =
*Updated - 09/06/2019*

* Enhance - Inquiry and Support reply "Attachment" support added
* Enhance - Inquiry Button position setting added
* Enhance - Inquiry and Chat Now button display together improved
* Enhance - New capability options added
* Enhance - French Translation update
* Tweak   - Product "Inquiry" button available though vendor's Inquiry Capability "OFF"
* Tweak   - "Disable Ask a Question Button" setting option removed, will work as per "Inquiry Module" setting
* Fixed   - Uncategorized category exclude filter issue resolved
* Fixed   - Vendor customer "Total Spent" amount display issue resolved

= 6.1.4 =
*Updated - 04/06/2019*

* Enhance - Woocommerce Product Availability Scheduler plugin's compatibility added
* Enhance - Support Ticket reference details added under ticket manage screen
* Enhance - Inquiry and Support customer linked to customer details page for better reference
* Enhance - WCFM Marketplace vendor manager "Commission & Withdrawal" and "Store Hours & Vacation" separate setting section added
* Enhance - Other vendors' Inquiry, Support Ticket access by changing ID at browser bar restriction check added
* Enhance - Article, Coupon and Customer vendor association option added under Admin Dashboard
* Enhance - WCFM Marketplace "Product Multivendor" global and group wise capability option added
* Enhance - Inquiry manager separate capability option added
* Tweak   - Product Multivendor "Product Title" edit disabled
* Fixed   - "Add to My Store" catalog page endpoint 404 issue resolved
* Fixed   - Vendor's order details "Admin Feee" twice visibility issue resolved
* Fixed   - Admin vendor manager update store SEO setting reset issue resolved
* Fixed   - Listing Inquiry manage page broken issue resolved
* Fixed   - Withdrawal and Payment page "Default Status" filter not working issue resolved
* Fixed   - Some type errors issue resolved

= 6.1.3 =
*Updated - 31/05/2019*

* Enhance - FooEvents Check-in App Compatibility added (WCFM Ultimate Require)
* Enhance - WooCommerce Bookings 1.14.2 new capability compatibility added (please refresh vendor capability after update) 
* Enhance - Vendor media bulk delete option added
* Enhance - WC Appointments 4.5 calendar display compatibility added
* Enhance - WC Product Addon and Fancy Product Designer capability option added
* Enhance - Inquiry only for logged in user setting option added
* Enhance - WCFM Marketplace Store and Store List page "Inquiry" custom button label compatibility added
* Enhance - Commission details added under Admin Order Details page
* Enhance - Booking resouce available quantity input option added under product manager
* Tweak   - All Vendors "Followers" manage page added under Admin dashboard, previously only vendors allow to manage own followers
* Fixed   - WCFM Marketplace Admin Total Fee display negative issue resolved
* Fixed   - Notification display slash(\) issue resolved
* Fixed   - Dashboard product list search pagination issue resolved

= 6.1.2 =
*Updated - 24/05/2019*

* Feature - WCFM Affiliate registration compatibility added - [Documentation](https://docs.wclovers.com/wcfm-affiliate/)
* Feature - WCFM Marketplec Membership subscription invoice option compatibility added, attach to "Welcome Email" (WCFM Ultimate require)
* Feature - WCFM Membership subscription cost "Tax" support compatibility added (setup Tax under WCFM Membership Setting -> Tax Setting)
* Enhance - Inquiry manage display improved
* Fixed   - Dashboard "Welcome Box" Staff name replaced by vendor name issue resolved

= 6.1.1 =
*Updated - 15/05/2019*

* Feature - FooEvents Tickets listing added (WCFM Ultmate require)
* Enhance - Vendor coupon "Include Products" option added
* Enhance - "Add Article" disable due to "low space" capability checking added 
* Enhance - Admin customers list store filter added
* Enhance - Admin vendors list store orders section quick reference link added
* Enhance - Vednor profile additional information added under Admin Vendor Manager -> Profile Section
* Enhance - Multi-input block remove confirmation added
* Enhance - Listings "Mark Fill" inside WCFM dashboard option added
* Enhance - Article new category add option added
* Tweak   - Booking date range filter work with booking start date, previously worked with booking created date
* Tweak   - Appointment date range filter work with appointment start date, previously worked with booking created date
* Fixed   - Product page custom field display issue resolved

= 6.1.0 =
*Updated - 07/05/2019*

* Feature - Category wise Attributes mapping option added (WCFM Ultimate require) - [Documentation](https://docs.wclovers.com/attributes/#category-attributes-mapping)
* Enhance - WCFM Marketplace vendor's sales report "tax" added 
* Fixed   - Product Manager Catalog visibility twice display issue resolved
* Fixed   - Dashboard vendors list page not loading issue resolved
* Fixed   - WCFM Marketplace store list search result verified badge not display issue resolved 

= 6.0.5 =
*Updated - 05/05/2019*

* Feature - WooCommerce Min/Max Quantities compatibility added (WCFM Ultimate require)
* Feature - WooCommerce 360 Images compatibility added (WCFM Ultimate require)
* Feature - WooCommerce Product Badge Manager compatibility added (WCFM Ultimate require)
* Enahnce - WC Appointment 4.4 calendar compatibility added
* Enahnce - WC Appointment calendar staff filter added
* Enahnce - WC Booking 1.14 manual booking compatibility added
* Enahnce - WCFM all integrated plugins "global and group wise" capability option added
* Enhance - Customer export option added
* Enhance - Job Listing dashboard "WP Job Manager Applications" column added
* Enhance - Customer list search enahnced to billing fields
* Enahnce - Bulk stock manager screen layout improved 
* Tweak   - Verification Auth library load restricted only for Profile page
* Tweak   - WCFM Marketplace "vacation mode" message display at store header, previously visible with individual products
* Tweak   - "woocommerce_order_details_after_order_table" hook enabled by default
* Fixed   - WCFM Marketplace "vacation mode" widget product list issue rsolved
* Fixed   - "Subscriptions" endpoint 404 issue resolved
* Fixed   - WC Product Vendors customers not visible issue resolved
* Fixed   - WCFM Marketplace vendor's store page analytics data update issue resolved
* Fixed   - Chat.js and color picker library conflict issue resolved
* Fixed   - Follower update issue resolved

= 6.0.4 =
*Updated - 22/04/2019*

* Enahnce - WCFM Marketplace Stripe split pay API refund compatibility added
* Enahnce - WCFM Marketplace order sync with WC edit order refund compatibility added
* Enahnce - WCFM Marketplace order sync with order item add from WC edit order compatibility added
* Enahnce - WCFM Marketplace order sync with order item edit from WC edit order compatibility added
* Enahnce - WCFM Marketplace order sync with order item delete from WC edit order compatibility added

= 6.0.3 =
*Updated - 21/04/2019*

* Enahnce - WC 3.6 compatibility added
* Enhance - WooCommerce Wholesale Prices compatibility added (WCFM Ultimate required)
* Enhance - Local pickup orders shipping address hidden from order listing and order details
* Enhance - Registration and profile password field strength check added
* Enhance - Custom order status compatibility added
* Enhance - WC Appointments "two way" GCal sync compatibility added
* Enhance - PDF Invoice and Packing Slip special charater and advance font support added
* Enhance - Variable PDF Voucher compatibility added
* Enhance - Used PDF Voucher download option added
* Enhance - WC Duplicate product custom taxonomies support added
* Enhance - WPML translated product custom taxonomies support added
* Enhance - WCFM Marketplace Vendor "order details items" WPML compatibility added
* Enhance - WCFM login popup "registration" link added
* Tweak   - Order edit "applied discount" apply on total order cost, perviously applied on each item
* Fixed   - Orders dashboard 404 issue resolved
* Fixed   - Vendor's customer load issue resolved
* Fixed   - Dashboard listing pages "next" button not working issue resolved
* Fixed   - Catalog mode product archive "Hide Price" and "Disable Add to Cart" condition check issue resolved

= 6.0.2 =
*Updated - 15/04/2019*

* Enahnce - Dashboard Products listing SKU, Price order by option added
* Enahnce - Subscription renewal order "Store Invoice" support added
* Enahnce - My Account order list "Store Invoice" download option added
* Enahnce - WCFM Marketplace Store new order email "Store Invoice" attachment added
* Enahnce - WCFM Marketplace Store page "About" and "Policy" tab enable/disable store setting option added
* Enahnce - WCFM Marketplace Admin dashboard "Vendor Listing" page store address and contact information added
* Enahnce - Booking calendar responsive display improved
* Fixed   - Article ACF image/file upload issue resolved

= 6.0.1 =
*Updated - 06/04/2019*

* Enahnce - Product gallery image sorting option added
* Enahnce - Manual order multiple item compatibility added (WCFM Ultimate require)
* Enahnce - WC Appointments 4.4+ compatibility added
* Tweak   - Order export as PDF orientation change to landscape
* Fixed   - Variation attribuutes with quotes (') issue resolved
* Fixed   - Vendor Withdrawal request page access issue resolved
* Fixed   - WCFM Marketplace vendor dashbord "awaiting fulfillment" count display issue resolved

= 6.0.0 =
*Updated - 01/04/2019*

* Feature - WCFM APP compatibility - https://docs.wclovers.com/wcfm-app/
* Enahnce - WCFM - Affiliate addon compatibility - https://wclovers.com/product/woocommerce-frontend-manager-affiliate/
* Enahnce - WC Advanced Product Labels compatibility added (WCFM Ultimate require)
* Fixed   - WCFM Marketplace Add New vendor data save issue resolved
* Fixed   - Enquiry count not updating issue resolved

= 5.4.7 =
*Updated - 26/03/2019*

* Enahnce - WC Simple Auction "Relist" compatibility added
* Enahnce - Product Bundle Min/Max extension compatibility added (WCFM - Product HUB require)
* Enahnce - WCFM Marketplace Vendor "Free shipping coupon" compatibility added
* Enahnce - WCFM Marketplace Shipping calculation "item cost including tax" option supported
* Enahnce - Vendor's multiple appointments in single order compatibility added
* Fixed   - WC Appointment addons save issue resolved

= 5.4.6 =
*Updated - 22/03/2019*

* Feature - WCFM Marketplace Store List Radius Search option added (https://docs.wclovers.com/store-list/)
* Feature - Fancy Product Designer compatibility added (WCFM Ultimate require)
* Enhance - Store commission invoice download option added for Admin (Only for WCFM Marketplace)
* Enhance - Vacation message and Store invoice content WPML compatibility added 
* Enhance - WCFM Marketplace store slug base and sold by label WPML compatibility added
* Enhance - WCFM Marketplace withdrawal/payment invoice updated
* Fixed   - Store Map Location save issue resolved
* Fixed   - Enquiry, Support time display issue resolved

= 5.4.5 =
*Updated - 15/03/2019*

* Enhance - Inquiry and Support customer notification store email "reply-to" reference added
* Enhance - Inquiry and Support vendor notification customer email "reply-to" reference added
* Enhance - Chart time display localization support added
* Enhance - Dashboard endpoints WPML compatibility added
* Enhance - MY Account endpoints WPML compatibility added
* Enhance - Variable product import issue resolved (WCFM Ultimate require)
* Enhance - Dashbord mobile view Sticky header option added 
* Enhance - Direct Message to all vendors email notification optiion added
* Enhance - WCFM Marketplace Netgsm plugin compaibility added for SMS notification
* Tweak   - Product vendor de-select option added
* Tweak   - Inquiry and Support admin reply represent using Site Logo and Title
* Tweak   - Product approve by user data stored with product meta - _wcfm_product_approved_by
* Fixed   - Mobile view help tooltip display issue resolved
* Fixed   - Admin add new vendor "Store Description" save issue resolved
* Fixed   - Vendor profile custom field edit issue resolved
* Fixed   - Dashboard few missing icons added

= 5.4.4 =
*Updated - 09/03/2019*

* Enhance - Listing mark featured option added
* Enhance - Large number of categories load memory compatibility added (WCFM Category Hierarchy addon required)
* Enhance - WCFM Marketplace new store page compatibility added
* Enhance - WCFM Marketplace Order Sync condition check improved

= 5.4.3 =
*Updated - 27/02/2019*

* Tweak   - Font Awesome old version compatibility added (Please refresh site caches after update)
* Enhance - WCFM Marketplace withdrawal invoice download option added (WCFM Ultimate require)
* Enhance - Sales report graph date localization support added

= 5.4.2 =
*Updated - 25/02/2019*

* Feature - WCFM Marketplace sales by vendor report added
* Enahnce - WCFM menu manager WPML compatibility added 
* Enahnce - Order shipping package sold by label capability label display support added
* Fixed   - Dokan vendor's sales report display issue resolved
* Fixed   - WCFM menu manager icon not updating issue resolved

= 5.4.1 =
*Updated - 23/02/2019*

* Feature - WooCommerce Measurement Price Calculator plugin compatibility added (WCFM Ultimate required)
* Fixed   - Booking dashboard endpoint broken issue resolved
* Fixed   - Chart date label display issue resolved
* Fixed   - ACF date save issue resolved
* Fixed   - Vendor limit stat display issue resolved
* Fixed   - Admin dashboard commission display issue resolved
* Fixed   - Vendor dashboard "Sales by Product" display issue resolved
* Fixed   - My account action icon display issue resolved
* Fixed   - RTL dashboard toggle issue resolved
* Fixed   - Checkbox checked display issue resolved
* Fixed   - Withdrawal, Payment, Refund screen date display issue resolved

= 5.4.0 =
*Updated - 17/02/2019*

* Feature - FooEvents (https://www.fooevents.com/) compatibility added (WCFM Ultimate required)
* Enhance - Dashboard icon library upgrade to Font Awesome 5 (Free) - Please clear your caches after update
* Enhance - Dashboard date display WP date-time and timezone setting used
* Enhance - WCFM - Delivery SMS notification added

= 5.3.3 =
*Updated - 14/02/2019*

* Enhance - WC Bookings 1.13+ compatibility added
* Enhance - RnB Rental & Booking multiple categories select option added
* Enhance - Toolset Maps 1.7+ compatibility added
* Enhance - Vendor list drop-down Store Name added
* Enhance - WCFM Marketplace admin vendor manager store description edit option added
* Tweak   - Enquiry and Support vendor reply author shown as Store logo and Store name (previously it was vendor personal name and profile image) 
* Tweak   - Dashboard stats and graph color changed
* Fixed   - Local pickup orders shipment tracking display issue resolved

= 5.3.2 =
*Updated - 03/02/2019*

* Enhance - Sold By group wise capability option added
* Enhance - Enquiry only with login option added - enable using filter "wcfm_is_allow_enquiry_with_login"
* Enhance - Vendor coupon only for vendor products condition added without WCFM Ultimate
* Tweak   - Vendor notification board additional columns removed
* Tweak   - Product and article short description editor simplified
* Tweak   - Enquiry and Support reply editor simplified   
* Fixed   - Enquiry and Support form recaptcha issue resolved
* Fixed   - ACF Image, File, Map field save issue resolved 

= 5.3.1 =
*Updated - 20/01/2019*

* Fixed    - JetPack lazy load conflict issue resolved
* Fixed    - Variation attributes quote parsing issue resolved
* Fixed    - GEO my WP product title conflict issue resolved

= 5.3.0 =
*Updated - 15/01/2019*

* Feature  - WCFM - Delivery addon compatibility added - [WCFM Delivery](https://wclovers.com/product/woocommerce-frontend-manager-delivery/)
* Enhance  - Enquiry and Support customer email view capability check added

= 5.2.9 =
*Updated - 10/01/2019*

* Enhance  - Listing page vendor filter option added
* Enhance  - Listing page store info visible support added
* Enhance  - Listing page enquiry button support added
* Fixed    - WPML translated product generation issue resolved
* Fixed    - Dashboard Checkbox wrapping issue resolved (theme fix)
* Fixed    - Email subject HTML character display issue resolved

= 5.2.8 =
*Updated - 07/01/2019* 

* Enhance  - Shipment processing time displayed in single product page and order shipping
* Enhance  - Subscription order indicator added
* Enhance  - Renewal order shipment tracking handler added
* Enhance  - Order item meta display improved
* Enhance  - File/image type custom field open with popup option added
* Enhance  - Profile additional info files open with popup option added
* Fixed    - Bookable product block cost update issue resolved 
* Fixed    - Manager WP backend view check issue resolved
* Fixed    - WCFM custom field display quote slash issue resolved

= 5.2.7 =
*Updated - 28/12/2018* 

* Enhance  - WCFM Marketplace auto-withdrawal request compatibility added
* Enhance  - WCFM Marketplace vendor registration OTP verification compatibility added (Indian Users - https://wordpress.org/plugins/sms-alert/, Others - https://woocommerce.com/products/twilio-sms-notifications/)
* Enhance  - Registration form file upload compatibility added
* Enhance  - Welcome email WPML compatibility added
* Enhance  - Thank you page content WPML compatibility added
* Enhance  - Orders table purchased item meta display added
* Enhance  - Orders shipping address display condition check added
* Enhance  - Dokan-Woo Wallet compatibility added
* Enhance  - RTL CSS improved
* Tweak    - Manual vendor email auto-verified
* Fixed    - Variable product update duplicate SKU error issue resolved

= 5.2.6 =
*Updated - 23/12/2018* 

* Tweak       - WCFM Marketplace vendor's orders list change to merged view (previously it was itemized)

= 5.2.5 =
*Updated - 18/12/2018* 

* Feature     - WCFM Marketplace Store orders manage option added under Admin dashboard vendor manager
* Feature     - WCFM Marketplace Store endpoints translation option added
* Feature     - WCFM Analytics custom taxonomy compatibility added
* Enhance     - Store invoice and email order item meta displayed
* Enhance     - WCFM Ultimate chat module multi-lingual compatibility added
* Enhance     - WCFM Ultimate chat button - vendor online indicator added
* Enhance     - WCFM Ultimate chat button short code support added - wcfm_chat_now
* Enhance     - WCFM Analytics date range filter compatibility added
* Enhance     - ACF and ACF Pro map field support added
* Enhance     - ACF time and date-time field type support added
* Enhance     - Customer manager shipping address "same as billing" option added
* Enhance     - Article category allowed limit capability added
* Enhance     - Product custom taxonomies allowed limit capability added
* Fixed       - WPML and Polylang conflict check issue resolved
* Fixed       - Notification access capability check issue resolved
* Fixed       - Dashboard "the_title" filter changed other post lables issue resolved
* Fixed       - ACF date picker field issue resolved
* Fixed       - ACF select field index issue resolved
* Fixed       - WC Vendors Pro vendor shipping cap check issue resolved

= 5.2.4 =
*Updated - 13/12/2018* 

* Feature     - [WCFM Marketplace - REST API](https://wordpress.org/plugins/wcfm-marketplace-rest-api/) compatibility added
* Enhance     - WC Geo Locate used for generating store and product analytics
* Enhance     - WCFM Marketplace store slug duplicate check added

= 5.2.3 =
*Updated - 09/12/2018* 

* Feature     - WCFM Marketplace order edit compatibility added
* Enhance     - WCFM Marketplace Manual Order discount option supported
* Enhance     - WCFM Marketplace store coupons "Show on store" conditional option added
* Enhance     - Group Buy Deals product compatibility added through [WCFM - Product HUB](https://wclovers.com/product/woocommerce-frontend-manager-product-hub/)
* Fixed       - WC Product Addons and WC Bookings dependency issue resolved
* Fixed       - Dokan and WCMp orders dashboard PHP warning issue resolved
* Fixed       - Order details coupon link display issue resolved

= 5.2.2 =
*Updated - 05/12/2018* 

* Feature     - WCFM Marketplace vendor shipping admin side edit option compatibility added
* Feature     - WCFM Marketplace store list new template compatibility added
* Feature     - Store policies WPML compatibility added
* Fixed       - Product sales date format issue resolved

= 5.2.1 =
*Updated - 02/12/2018* 

* Feature     - WCFM Marketplace vendor add new order compatibility added
* Enhance     - Product custom taxonomy limit capability limit check added added
* Enhance     - Shipment Tracking edit optiion added
* Fixed       - Popup product category selection issue resolved
* Fixed       - Coupon expiry date locale format issue resolved

= 5.2.0 =
*Updated - 26/11/2018* 

* Feature     - WCFM Marketplace weight based vendor shipping compatibility added - [Documentation](https://wclovers.com/knowledgebase/wcfm-marketplace-store-shipping/)

= 5.1.11 =
*Updated - 22/11/2018*

* Fixed     - Inquiry form visible under page footer issue resolved

= 5.1.10 =
*Updated - 21/11/2018*

* Enhance   - WCFM Coupons dashboard store filter added
* Enhance   - WCFM Coupons dashboard store column added
* Enhance   - WCFM Inquiry form load enhanced
* Tweak     - Order status change to complete on shipment receive - enable using filter "wcfm_is_allow_order_complete_on_receive" 
* Fixed     - Multiple item from same store multiple store invoice attachment issue resolved
* Fixed     - Vendor's Appointment GCal connect issue resolved
* Fixed     - Vendor support undefined variable issue resolved
* Fixed     - Redirect function missing argument issue resolved (Theme Fix)
* Fixed     - Coupons include-exclude products title display issue resolved
* Fixed     - Coupon update vendor de-select issue resolved

= 5.1.9 =
*Updated - 18/11/2018*

* Feature   - WCFM Marketplace Store list page inquiry button support added
* Enhance   - WCFM Knowledgebase and Notice archive option added
* Enhance   - WCFM Appointment dashboard staff filter added
* Enhance   - WCFM Appointment dashboard staff column added
* Enhance   - WCFM Appointment calendar appointment details popup added
* Fixed     - Order dashboard screen manager issue resolved
* Fixed     - Auction type product stock issue resolved
* Fixed     - Withdrawal request approve button missing issue resolved

= 5.1.8 =
*Updated - 15/11/2018*

* Feature   - WCFM Ultimate Chat Module compatibility added - [Documentation](https://wclovers.com/knowledgebase/wcfm-marketplace-chat-module/)
* Enhance   - Dokan Moip payment gateway compatibility added
* Enhance   - More capability options added - Attributes, Custom Fields, Order Delete, View Commission
* Enhance   - Gallery Images custom validation option added
* Enhance   - Inquiry additional information column added at my-account Inquiry Dashboard
* Enhance   - WC Product Addons 3.0 compatibility added
* Enhance   - Product taxonomies column added under WCFM Products dashboard
* Enhance   - Product taxonomies filter added under WCFM Products dashboard
* Enhance   - Orders dashboard hidden column added for export - without format for acounting purpose

= 5.1.7 =
*Updated - 31/10/2018*

* Enhance   - Inquiry additional info display at inquiry manage page
* Enhance   - Inquiry form date picker additional field compatibility added
* Enhance   - Inquiry response delete option added
* Enhance   - Inquiry meta and response deleted with inquiry delete
* Enhance   - Single page multiple inquiry button compatibility added
* Enhance   - Booking confirm-decline button added
* Enhance   - Booking start and end date update validation added
* Enhance   - Booking start and end date update customer notification added
* Fixed     - Vendor dashboard notice count display issue resolved
* Fixed     - Missing strings added for translation

= 5.1.6 =
*Updated - 26/10/2018*

* Fixed     - Minor bug fixes

= 5.1.5 =
*Updated - 24/10/2018*

* Feature   - WCFM Marketplace store list video banner type support added 
* Feature   - WC Bookings schedule date edit option added
* Enhance   - WC Subscriptions related orders section added in subscription manage page
* Enhance   - Customer email view capability check added
* Enhance   - WC Simple Auctions "Do not show auctions on shop page" compatibility added 
* Enhance   - WCFM Product Importer WooCommerce 3.5 compatibility added
* Enhance   - Global scripts and styles minification added
* Enhance   - WCFM Marketplace video and slider banner type capability option added
* Enhance   - Bookings, Appointments and Subscriptions dashboard date range filter added
* Tweak     - Dashboard date filters change into date range picker
* Fixed     - Vendor verification admin notification not sending issue resolved
* Fixed     - Products List search duplicate entry show issue resolved
* Fixed     - Product popup policy editors not responding issues resolved 
* Fixed     - Yoast meta key word update issue resolved

= 5.1.4 =
*Updated - 10/10/2018*

* Feature   - WCFM Marketplace Store opening/closing hours compatibility added - [Documentation](https://wclovers.com/knowledgebase/wcfm-marketplace-store-hours/)
* Feature   - Profile additional information fields edit opton added
* Fixed     - Social Profile update issue resolved

= 5.1.3 =
*Updated - 04/10/2018*

* Feature   - WCFM Marketplace WooCommerce POS compatibility added
* Feature   - WCFM Marketplace Store New Order WC Emails template compatibility added
* Fixed     - Minor CSS issues resolved

= 5.1.2 =
*Updated - 01/10/2018*

* Feature   - WCFM Marketplace SMS notification compatibility added
* Feature   - WCFM Marketplace media manager added
* Enhance   - WCFM Marketplace vendor coupon products auto-assign option added
* Enhance   - Notification Messages bulk delete option added
* Enhance   - External product view count added on button click 
* Fixed     - Coupons manage collapsible height sync issue resolved
* Fixed     - Vacation mode shop page add to cart button hide issue resolved
* Fixed     - WC Vendors Pro capability setting check issue resolved 

= 5.1.1 =
*Updated - 24/09/2018*

* Feature   - WCFM Marketplace Stripe Split Pay compatiblity added
* Feature   - WCFM Marketplace Notification manager compatiblity added

= 5.1.0 =
*Release Date - 21/09/2018*

* Feature   - Store inquery button short code support added
* Feature   - Store Follow short code support added
* Feature   - WCFM Marketplace store inquiry short code added - [Documentation](https://wclovers.com/knowledgebase/wcfm-marketplace-widgets-short-codes/)
* Feature   - WCFM Marketplace store follow short code added (available only with WCFM Ultimate) - "[wcfm_follow store=""]"
* Enhance   - WooCommerce Rental & Bookings System - Quote request message send option added
* Fixed     - WooCommerce Rental & Bookings System - Quote page double view issue resolved
* Fixed     - Minor CSS issues resolved

= 5.0.10 =
*Release Date - 16/09/2018*

* Feature   - WCFM Marketplace vendor list widget compatibility added
* Feature   - Store invoice download option added under vendor's orders page
* Feature   - Store invoice advanced currency symbol support option added
* Enhance   - Store invoice advanced characters (Chinese, Korian, Japanese etc ..) support option added
* Enhance   - WCFM Marketplace Store Invoice vendor's shipping details added
* Enhance   - WCFM Marketplace commission invoice shipping and refund details added
* Enhance   - Product Quick and Bulk edit visibility controller added
* Enhance   - WCFM Dashboard list pages restrict hidden columns export
* Enhance   - Translations updated
* Fixed     - Inquiry and Support Popup reCaptcha issue for loggedin users resolved
* Fixed     - Minor CSS issues resolved

= 5.0.9 =
* Enhance    - My Account endpoint slug edit setting option added (WCFM Admin Setting -> Dashboard Pages -> My Account End Points)
* Fixed     - My Account inquiry link broken issue resolved
* Fixed     - Select2 library load issue resolved (for some specific themes)

= 5.0.8 =
* Feature   - WCFM Inquiry module extended for threded reply
* Feature   - WCFM Marketplace Customer refund request option added
* Enhance    - WCFM Marketplace vendors group list page design sync with store list page 
* Enhance    - Users are now allowed to manage their inquiries from My Account page
* Enhance    - WCFM Marketplace new booking and appointment store vendor email notification added 
* Enhance    - WCFM Menu Manager menu for option added - All User or Only Admin or Only Vendor
* Enhance    - WCFM Menu Manager menu open in new tab option added
* Tweak     - Product Attachment delete on product delete function added - have to enable by filter "wcfm_is_allow_delete_post_media" 
* Tweak     - WCFM list pages default row length change to 20 (previously it was 10)
* Fixed     - Variable Product edit blank variations issue resolved
* Fixed     - Product sales schdule date saving issue resolved
* Fixed     - Some CSS issue resolved

= 5.0.7 =
* Feature   - WCFM Marketplace Store List separate banner option added
* Feature   - WCFM Marketplace Vendor store no of products per page option added
* Enhance   - WCFM Marketplace Store Banner width-height setting option added
* Enhance   - Pending Vendors link added under WCFM Vendor Dashboard
* Enhance   - Product taxonomy dropdown view sub-category support added
* Enhance   - Support Ticket form Google Captcha support added
* Fixed     - Group product child title change issue resolved 
* Fixed     - WP Hide attachment upload issue resolved
* Fixed     - Amazon S3 storage vendor disk usage calculation issue resolved
* Fixed     - Some CSS issue resolved

= 5.0.6 =
* Feature   - WCFM Marketplace Slider store banner support added
* Feature   - WCFM Marketplace  Video store banner support added
* Fixed     - Vendor last login time display as per timezone issue resolved 
* Fixed     - WCFM Marketplace Store slug update error issue resolved
* Fixed     - Some CSS issue resolved

= 5.0.5 =
* Feature   - Product Visibility controller option added
* Feature   - Custom taxonomy "Add new" option added
* Enhance   - Variations bulk actions translation support added
* Enhance   - WCFM Marketplace Vendor commission order sync with WC order item delete
* Enhance   - WCFM Marketplace Vendor commission order sync with WC order delete
* Enhance   - WC Product Vendors "pending vednor" restriction check added
* Fixed     - WC Box Office checkout issue resolved 
* Fixed     - Some CSS isuue resolved
* Fixed     - Some missing strings added for translation

= 5.0.4 =
* Enhance   - Toolset profile field vendor specific customer data flexibility filter added - wcfm_toolset_profile_field_meta
* Enhance   - WCFM Shipment Tracking form override option added 
* Enhance   - WC Appointment lead and schedule duration options synched
* Enhance   - SEO capability option added
* Enhance   - WC Simple Auction "Auto Proxy" setting compatibility added
* Enhance   - WCFM Marketplace GEO my WP compatibility added
* Fixed     - WCFM Marketplace Store analytics issue resolved 
* Fixed     - Vendor Export capability issue resolved
* Fixed     - Vendor Import capability issue resolved
* Fixed     - WCFM Email constant already defined PHP notie issue resolved
* Fixed     - Notification email hyperlink escape issue resolved

= 5.0.3 =
* Enhance   - WCFM Marketplace Vendor shipping zone delete confirmation added
* Enhance   - WCFM Marketplace Vendor country shipping disable notice option added 
* Tweak     - Admin report data cache load disabled
* Fixed     - Coupon list expiry date display issue resolved
* Fixed     - Expired isting not visible in all listing issue resolved
* Fixed     - WCFM Marketplace Cancel withdrawal not available for withdrawal issue resolved
* Fixed     - WCFM Marketplace Cancel withdrawal multiple vendor notification issue resolved

= 5.0.2 =
* Enhance   - WCFM Marketplace frontend "Add Vendor" option added for Admin
* Enhance   - WCFM Membership plan commission support added

= 5.0.1 =
* Feature   - WCFM Marketplace - Single Product Multivendor compatibility added - [Documentation](https://wclovers.com/knowledgebase/wcfm-marketplace-single-product-multi-vendor/)
* Enhance   - Jetpack image lazy load compatibility added
* Enhance   - WCFM Marketplace Store description support added
* Fixed     - WCFM Marketplace vendor capability check issue resolved

= 5.0.0 =
* Feature   - [WCFM - Marketplace](https://wordpress.org/plugins/wc-multivendor-marketplace/) integrated version

= 4.2.5 =
* Fixed   - Article excerpt and description save issue resolved
* Fixed   - Customer Toolset field save issue resolved
* Fixed   - WC marketplace setting layout broken due to Address Capability OFF issue resolved
* Fixed   - Manual Booking creation page hangs issue resolved
* Fixed   - Dokan withdrawal cancel page hangs issue resolved
* Fixed   - On save product save JS error issue resolved

= 4.2.4 =
* Feature - Variations bulk action more options added
* Enahnce - Product approval vendor notification added
* Enahnce - ACF field category type check enhance upto fifth hierarchy 
* Tweak   - Store invoice now generated per vendor instead of per item
* Tweak   - Product review option WC review status check added
* Tweak   - Dashboard all editor changed tp WP_Editor
* Tweak   - Product Type Toolset field mapping set by field id instead of slug 
* Fixed   - Product quick edit out of stock issue resolved
* Fixed   - Cancellation policy update issue resolved
* Fixed   - Numeric value 0 update issue resolved
* Fixed   - Vendors can see other users attachments issue resolved
* Fixed   - WC Product Vendors manage vendor page broken issue resolved

= 4.2.3 =
* Feature - Dokan Admin withdrawal request manage screen added
* Feature - Add/Edit Listing page wrap under WCfM Dashboard template
* Feature - Articles Toolset field support added
* Feature - Articles ACF and ACF Pro field support added
* Feature - Inquiry button lable edit option added
* Enahnce - Store Invoice, Commission Invoice and Packing Slip template override under theme option added
* Enahnce - Store Invoice vendor email and phone included
* Enahnce - Packing Slip store policies included
* Enahnce - Product quick edit capabillity check added
* Enahnce - Product quick edit custom validation check added
* Enahnce - Knowledge base categories translation support added 
* Enhance - GEO my WP capability option (Location) added
* Enhance - After vendor login WCfM dashboard redirect disable filter added - wcfm_is_allow_login_redirect
* Enhance - WC Tab Manager latest version compatibility added
* Enhance - Admin bar quick WCfM Menu formatted menu support added
* Enhance - Menu Manager module option added
* Enhance - WCfM dashboard error/warning message auto-hide option added
* Tweak   - Listing add product replace with WCfM Popup Add Product
* Tweak   - Commission Invoice customer email, phone and shipping address replaced by Vendor store address, email and phone
* Tweak   - Product from validation remove for Draft save
* Tweak   - On duplicate SKU found product status change to Draft
* Tweak   - Downloadable file default required check remove
* Tweak   - WCfM Menu Manager default dashboard can not be deleted, only disable option available
* Tweak   - WCfM Dashboard responsive display modified
* Fixed   - WPML other language admin setting update permalink broken issue resolved
* Fixed   - WPML vendor product translation process timeout issue resolved
* Fixed   - Store Stock custom validation check issue resolved
* Fixed   - WCFM Menu Manager vendor menu broken issue resolved
* Fixed   - BuddyPress member menu vertical display issue resolved
* Fixed   - Some CSS conflict issues resolved

= 4.2.2 =
* Enahnce - WCfM Menu Manager WPML compatibility added
* Enahnce - WCfM Menu 'HOME' label edit option added
* Enahnce - WCfM topbar 'My Store' label edit option added 
* Enahnce - WCFM Menu Manager capability and module preference compatibility added
* Fixed   - WCFM Menu Manager vendor menu broken issue resolved

= 4.2.0 =
* Feature - WCfM Policies module added - [Documentation](https://wclovers.com/knowledgebase/wcfm-store-policies/)
* Feature - WCfM Menu Manager added ( WCfM Dashboard -> Admin Setting -> Menu Manager Tab )
* Enahnce - Order, Booking, Appointment, Subscription status update notification added
* Enahnce - WCfM Store Invoice polices added
* Enahnce - WCfM Store Invoice vednor digital signature support added 
* Enahnce - WCfM new welcome box RTL support added
* Enahnce - WCfM Membership free plan expiry limit compatibility added
* Tweak   - WC Pre-defined attributes are now available in WCfM Free
* Tweak   - Sales scheduling now available in WCfM Free 
* Tweak   - On delete Order status will change to trash from now
* Tweak   - On delete Product can be hold at trash by filter - wcfm_is_allow_product_delete 
* Tweak   - On focus textaea hieght toogle property removed
* Fixed   - Product Popup Address Geocoder map intialize issue resolved
* Fixed   - Multi-lingual product limit count issue resolved
* Fixed   - Knowledgebase title link to restricted page for vendors issue resolved
* Fixed   - On Draft product new product notification to admin issue resolved
* Fixed   - Minor CSS fixes

= 4.1.9 =
* Feature - WCfM Vednor Store Invoice Module added - [Documentation](https://wclovers.com/knowledgebase/wcfm-store-invoice/)
* Enahnce - Withdrawal request capability controller added
* Enahnce - Staff list store info column added
* Enahnce - Knowledge base list catagory column added
* Enahnce - Capability controller module preference check added
* Fixed   - WC Vendors 2.0.10 WCV_Orders class missing issue resolved
* Fixed   - Dokan Terms & Conditions setting save issue resolved
* Fixed   - Dokan store location search CSS issue resolved
* Fixed   - WC Marketplace timezone setting update issue resolved

= 4.1.8 =
* Feature - Disk space usage limit capability option added
* Feature - WCfM Knowledgebase category option added
* Feature - WCfM Profile manage password change option added
* Enhance - WCfM Product custom field display new option "After Title" and "After Price" added
* Enhance - WCfM Knowledgebase view pop-up added
* Enhance - WCfM Orders table billing and shipping address column added
* Enhance - WCfM product import vendor capability status check added
* Enhance - Auction product type virtual and downloadable option support added
* Enhance - Vendor specific Toolset field info visible at WCfM vendor manage page
* Enhance - Vendor vacation message display capability checking added
* Enhance - WCMp 3.1 compatibility (integrated Stripe connect) added
* Tweak   - Vendor setting page profile setting tabs move to header
* Tweak   - WCfM Module Controler added - Shipment Tracking, Vendor Vacation
* Tweak   - WCfM Notice rename to Announcement
* Fixed   - Some minor CSS issues resolved

= 4.1.7 =
* Feature - Vendor specific capability controller added
* Enhance - Vendors listing dashboard re-designed
* Enhance - Inqury notification vendor capability checking added
* Enhance - Support notification vendor capability checking added
* Enhance - Upload type custom field display modified
* Tweak   - Now WCfM screen templates can be override to theme for edit
* Tweak   - On product publish it will no more redirect to product view page, instead stay on same page in edit mode
* Tweak   - On article publish it will no more redirect to article view page, instead stay on same page in edit mode
* Tweak   - WCMp Table rate shipping setting fields change to number type
* Tweak   - Shipping setting cost fields changeto number type 
* Fixed   - Social verification profile save issue resolved
* Fixed   - PDF Voucher date and location save/edit issue resolved 
* Fixed   - Variation attributes character display issue resolved
* Fixed   - Some minor CSS issues resolved

= 4.1.6 =
* Feature - CC and BCC email option added for notifications
* Enhance - Dokan vendor status restriction check added
* Enhance - Dokan vendor publish product restriction check added (May disable using filter "wcfm_is_allow_respect_dokan_publish_product_settings")
* Enhance - Inquiry button custom style setting support added
* Enhance - Inquiry button short code "label" parameter added to define own button label
* Enhance - Inquiry button short code "background" parameter added to define own button background color
* Enhance - Inquiry button short code "color" parameter added to define own button color 
* Enhance - Vendor Manager Toolset 3.0 compatibility added
* Tweak   - WCfM Dashboard base color changed
* Tweak   - WC Marketplace product policy pre-filled with vendor policy content
* Fixed   - WCfM empty custom field visibility issue resolved
* Fixed   - My account double Logout display issue resolved
* Fixed   - PDF Voucher product manager empty field visible issue resolved
* Fixed   - Some minor CSS issues resolved

= 4.1.5 =
* Feature - WC Warrenty compatibility added
* Feature - WC Waitlists compatibility added
* Feature - Appointment Calendar appointment details pop-up added
* Enhance - Toolset 3.0 compatibility added
* Enhance - Email header content type set to send emails at HTML mode 
* Enhance - Missing strings added for trnslation
* Tweak   - WCfM dashboard editors Text Mode disabled
* Tweak   - Variable Virtual product is now part of WCfM Free
* Tweak   - WCfM view mode change
* Fixed   - Toolset date field save issue resolved
* Fixed   - WCfM 'wcfm_hide_admin_bar_prefs' method missing issue resolved 
* Fixed   - Bookings and Appointments dashboard undefined variable PHP warning issue resolved
* Fixed   - Some minor CSS issues resolved

= 4.1.4 =
* Feature - Pay for Product limit module compatibility added  - [Documentation](https://youtu.be/WwFHorx93Fw)
* Feature - WCfM Product custom fields are now dragable to re-arrange
* Feature - WCfM Enquiry custom fields are now dragable to re-arrange
* Enhance - Product custom taxonomy validation option added
* Enhance - WC Vendors and WC Marketplace product specific commission manage option added
* Enhance - WC Marketplace product specific policies manage option added
* Enhance - Dokan withdrawal note added in payment list display
* Enhance - Product custom fields WPML string translation compatibility added
* Enhance - Enquiry custom fields WPML string translation compatibility added
* Tweak   - TinyMCE editors context menu disabled to enable spell suggestion
* Tweak   - Dokan withdrawal restricted without vendor's payment setting 
* Fixed   - Mobile menu item click issue resolved
* Fixed   - In mobile product attributes active double click issue resolved
* Fixed   - Code resend to verified email address issue resolved 
* Fixed   - Vendor verification custom identity fields JS error due to special characters issue resolved
* Fixed   - Gallery image capability setting not working issue resolved
* Fixed   - Dokan subscription remaining product limit check issue resolved 
* Fixed   - Dokan shipping zone setting version check issue resolved
* Fixed   - Coupon type display issue resolved
* Fixed   - Dummy image icon missing CSS issue resolved
* Fixed   - Other minor CSS issue resolved

= 4.1.3 =
* Feature - WCfM Enquiry custom field support added 
* Feature - wcfm_enquiry -> short code added to add "Ask a Question" button in widgets 
* Feature - Dokan Pro zone wise shipping support added 
* Feature - WC Product Vendor -> Marketplace Stripe Connect support added
* Feature - WCMp transaction details page added
* Feature - Email from name and address setting option added
* Feature - Article, Customer, Staff limit capability option added
* Feature - Article category capability option added
* Enhance - My Following list vendors store link added
* Enhance - Gallery image remove X icon added
* Tweak   - Vendor customer list now cotains own and order customers both
* Tweak   - "Ask a Question" button placed outside Enquiry tab
* Fixed   - New vendor sales by product stats disaplay using admin products issue resolved
* Fixed   - WCMp withdrawal transaction status update display issue resolved
* Fixed   - Product popup empty content due to theme collapse issue resolved 
* Fixed   - Vendor Customers page throwing error issue resolved 
* Fixed   - Email verification code visible with message issue resolved

= 4.1.2 =
* Feature - Vendor Followers module added
* Feature - Vendor disable option added
* Feature - Vendor email confirmation option added
* Feature - Vacation mode date wise option added
* Feature - ACF Pro User, Time & Date-Time field type support added
* Enhance - ACF Pro category wise field group support added for WCfM product popup
* Enhance - Wp admin top bar disable for Vendors
* Tweak   - Social seeting now available with WCfM Free
* Tweak   - Admin-Vendor review product notification conditional
* Tweak   - Admin-Vendor new product notification conditional
* Tweak   - Admin-Vendor new order notification conditional
* Fixed   - WCfM emails twice footer text issue resolved

= 4.1.1 =
* Feature - WC 4.0 compatibility added
* Feature - Subscriptions for WooCommerce (Free Plugin only) compatibility added
* Feature - Dokan Subscription compatibility added
* Feature - Shipment mark received admin and vendor notification added
* Enhance - Epeken All Kurir Plugin compatibility improved
* Enhance - Bulk product edit improved
* Enhance - WCfM notification email message type added
* Enhance - WCfM dashboard notification sound manage filter "wcfm_is_allow_notification_sound" added
* Enhance - WCfM dashboard notification sound change filter "wcfm_notification_sound" added
* Tweak   - Vendors are now allowed to view customers for their orders
* Fixed   - Bulk product edit shipping class missing issue resolved 
* Fixed   - Category archive product delete issue resolved
* Fixed   - WCfM product popup float button issue resolved 
* Fixed   - WCfM setting page break due to Dokan Pro JS - conflict issue resolved
* Fixed   - WCfM multiple notification issue resolved

= 4.1.0 =
* Feature - Dashboard popup add product option added 
* Feature - WooCommerce Tabs Manager compatibility added 
* Feature - Epeken All Kurir Plugin compatibility added
* Feature - Subscriptions for WooCommerce (Free Plugin only) compatibility added
* Enhance - WCfM all dashboard notification will have email notifications as well
* Enhance - WC Vendors Pro 1.5.0 compatibility added
* Enhance - WC Vendors 2.0 vendor bank details support added
* Enhance - Shipment Tracking customer email notification added
* Enhance - Shipment Tracking admin desktop notification added
* Tweak   - WCfM screens slugs chnage to without wcfm- suffix
* Tweak   - Downlodable product now available in WCfM FREE
* Tweak   - Image Gallery (4 images) now available in WCfM FREE
* Fixed   - WP Editor text mode data save issue resolved 
* Fixed   - WC Marketplace commission withdrawal duplicate transaction issue resolved
* Fixed   - WC Marketplace Stripe disconnect issue resolved
* Fixed   - WCfM-WPML pages setting update issues resolved 

= 4.0.10 =
* Enhance - WC Vendors 2.0 compatibility added

= 4.0.9 =
* Feature - WCfM dashboard new mobile menu added
* Enhance - My Listings package group capability support added
* Enhance - Geo my WP product default location by vendor store location compatibility added
* Enhance - Select2 localization support added
* Enhance - WCfM dashboard date filters format sync with WP date format
* Fixed   - WC custom product attributes character issue resolved
* Fixed   - WP user avatar sync issue resolved
* Fixed   - Subscription manage screen custom permalink issue resolved
* Fixed   - Product dashboard product count by status issue resolved
* Fixed   - Knowledgebase display order issue resolved
* Fixed   - WC Marketplace refund police settings edit issue resolved
* Fixed   - WPML translated products edit URL issue resolved
* Fixed   - WCfM page settings reset due to WPML resolved
* Fixed   - WPML terms restore filter argument missing issue resolved
* Fixed   - Some missing strings added for translation

= 4.0.8 =
* Feature - Product field specific capability options added
* Feature - Featured product limit capability option added
* Feature - Subscriptions capability options added
* Feature - Subscription dashboard screen manager added
* Enhance - GMW product default address set by vendor store location
* Tweak   - WP Editor used as product manager default editor
* Tweak   - WP Editor used as article manager default editor
* Tweak   - WP Editor used as knowledgebase manager default editor
* Tweak   - Sub-category allow / block depending upon main category
* Tweak   - Profile shipping address same as shipping option added
* Fixed   - Order item attributes display as slug issue resolved 
* Fixed   - Vendor verification popup ID proof missing issue resolved
* Fixed   - WC Simple Auction "Buy Now" option missing issue resolved
* Fixed   - YiTH Auction Premium product data edit issue resolved
* Fixed   - Auction products tab cut-off issue resolved
* Fixed   - Mobile view popup display issue resolved

= 4.0.7 =
* Feature - WC Subscriptions front-end dashboard (vendor wise as well) added
* Feature - WC Subscriptions front-end manage page added
* Feature - WCfM product custom field product type wise condition added
* Feature - GEO my WP 3.0 compatibility added
* Feature - WC Appointment Google Calendar Sync option added
* Feature - Vendor specific Appointment Gcal sync integrated
* Feature - WCfM dashboard profile avatar sync with BuddyPress profile avatar 
* Enhance - WC Appointments 3.8 quantity fields support added
* Enhance - Products, Articles, Listings fetch performence enhanced
* Enhance - Vendors drop-down search improved
* Fixed   - On product type change Virtual options visibility issue resolved 
* Fixed   - Message board display error due to deleted vendors resolved
* Fixed   - Vendor product count PHP memory issue resolved 
* Fixed   - WC Marketplace single product multiple seller association issue resolved
* Fixed   - Dokan is_vendor check for multi-site issue resolved 
* Fixed   - Dokan vendors orders page PHP notice issue resolved
* Fixed   - Dokan stripe connect button missing issue resolved

= 4.0.6 =
* Feature - WP date format support added for report graph dates
* Feature - WC PDF Voucher generate voucher code option added
* Feature - WC PDF Voucher variable product support added
* Enhance - Downloadable option enabled for Bookable and Appointable product type
* Enhance - WCfM shipment tracking on complete "wcfm_after_order_mark_shipped" action hook added (Parameters -> $order_id, $order_item_id, $tracking_code, $tracking_url)
* Tweak   - Downloadable file required set instead of downloadable file name 
* Tweak   - WCfM product manager tax fields filter changed to "wcfm_product_simple_fields_tax"
* Tweak   - WC core JS file missing error exception handler added 
* Fixed   - Booking, Accommodation, Appointment product zero cost rule issue resolved
* Fixed   - Booking, Accommodation, Appointment empty availability rule issue resolved
* Fixed   - YiTH Auction date from / to display issue resolved 
* Fixed   - DHL FedEX / UPS shipping label generation issue resolved
* Fixed   - WCfM attributes limit override for all issue resolved
* Fixed   - Dokan Stripe conect active check issue resolved
* Fixed   - My Account support ticket label broken link issue resolved

= 4.0.5 =
* Feature - WCfM Product Manager custom validaton rule settings added (WCfM Ultimate Admin Settings -> Product Custom Validation)
* Feature - Unverified vendors special product limit restriction option added - [Documentation](https://wclovers.com/knowledgebase/wcfm-vendor-verification/)
* Enhance - WCfM admin vendor manage page -> verification section added 
* Enhance - Vendor verification identity proofs manage option
* Enhance - WCfM Shipment Tracking now possible by Admin and Shop Manager
* Enhance - Dokan Single product multi-vendor module compatibility added
* Enhance - WC Bookings "Restrict Days" 1.10.7 backward compatibility added
* Enhance - WC Appointments 3.7 compatibility added
* Enhance - WC Appointments missing translations added
* Enhance - Dokan & WCfM both dashboard usage compatibility added
* Fixed   - Products for Listings JS error issue resolved
* Fixed   - WCfM -> WC Vendors Pro settings page broken issue resolved 
* Fixed   - WC Marketplace store address state field uneditable issue resolved
* Fixed   - Dokan store address state field uneditable issue resolved
* Fixed   - Dokan disable shiping checkbox save issue resolved
* Fixed   - WC Product Add-ons translation issue resolved
* Fixed   - Product CSV import page CSS issues resolved (Update color setting to best view)
* Fixed   - WCfM dashboard header and menu RTL display issue resolved

= 4.0.4 =
* Feature - WCfM Support Ticket module added - [Documentation](https://wclovers.com/knowledgebase/wcfm-support-ticket-module/)
* Enhance - WC attribute delimiter change compatibillity added
* Enhance - WCfM Dashboard load perfermence improved
* Fixed   - WC Reports calculation order count issue resolved
* Fixed   - Dokan vendor report sales calculaton cancelled order exclusion issue resolved 
* Fixed   - WCfM enquiry form freezing issue resolved
* Fixed   - WC Appointment calendar broken issue resolved
* Fixed   - Dokan disable shiping checkbox save issue resolved

= 4.0.3 =
* Feature - WCfM Support Ticket module added - [Documentation](https://wclovers.com/knowledgebase/wcfm-support-ticket-module/)
* Enhance - WC attribute delimiter change compatibillity added
* Fixed   - WC Reports calculation order count issue resolved
* Fixed   - Dokan vendor report sales calculaton cancelled order exclusion issue resolved 
* Fixed   - WCfM enquiry form freezing issue resolved
* Fixed   - WC Appointment calendar broken issue resolved
* Fixed   - Dokan disable shiping checkbox save issue resolved

= 4.0.2 =
* Feature - Dokan Pro Reviews module compatibility added
* Feature - Marketica theme vendor account nav-menu compatibility added
* Enhance - WCfM emails are now wrap with WC email format 
* Enhance - WCfM custom attributes rendering change to support attribute specific controllers
* Enhance - Variation Per product shipping multiple rules support added
* Fixed   - Dokan setting checkbox save issue resolved

= 4.0.1 =
* Feature - Dokan Stripe Connect compatibility added
* Feature - WCfM enquiry form Google reCaptcha support added
* Enhance - WCfM custom field drop-down empty option support added
* Fixed   - No menu header panel toggle menu visible issue resolved
* Fixed   - WC depricated method notice issue resolved
* Fixed   - Without label fields required display issue resolved

= 4.0.0 =
* Feature - WCfM Dashboard new menu style (Refresh style settings to get the right view)
* Feature - WooCommerce PDF Vouchers compatibility added 
* Feature - WP-Stateless – Google Cloud Storage compatibility added
* Feature - WooCommerce SKU Generatore compatibility added
* Feature - Product quick approve action icon added at Product dashboard 
* Feature - MyListing theme Products for Listings compatibility added
* Enhance - Order Status update order note added for log
* Enhance - Menu toggler option added
* Fixed   - WP loop back failed issue resolved 
* Fixed   - WooCommerce Germanized "delivery time" error resolved

= 3.5.10 =
* Enhance - WCfM Dashboard new menu style

= 3.5.9 =
* Feature - WooCommerce Deposits compatibility added
* Feature - Editor type WCFM custom field added
* Enhance - "wcfm_is_allow_edit_specific_products" product capability controller filter added
* Enhance - Dokan Pro coupon show in store option added
* Fixed   - Add product capability restrict message display issue resolved 

= 3.5.8 =
* Feature - Add new product admin  notificaton added
* Feature - Toolset Colorpicker, Checkboxes field support added
* Enhance - "wcfm_order_status_display" filter added to change order table status display
* Enhance - Order details display for vendors' deleted product re-generated
* Enhance - Coupons capability re-defined
* Fixed   - WC Vendors deleted product's order gross total "0" issue resolved
* Fixed   - WC Vendors Pro product shipping rate reset issue resolved
* Fixed   - Gallery image save with WP Hide issue resolved
* Fixed   - Toolset profile and Customer manager repetitive field issue resolved   
* Fixed   - Low stock report action icon repeatation issue resolved
* Fixed   - Enquiry Tab "Submit" not working issue resolved
* Fixed   - Enqueries display CSS issue resolved
* Fixed   - Order details item discount price display CSS issue resolved
* Fixed   - Without edit product capability vendors' draft/pending product edit issue resolved

= 3.5.7 =
* Enhance - WC 3.3 Stock controller compatibility added
* Enhance - Deleted products order details supported
* Enahnce - Vendor verification pop-up changed to colorbox
* Fixed   - Catalog enquiryy popup missing issue resolved
* Fixed   - WC Vendors registration profile info page redirect issue resolved
* Fixed   - Stock report pagination issue resolved
* Fixed   - All attributes selected on active issue resolved

= 3.5.6 =
* Fixed   - Vendor "submit for review" product save issue resolved
* Fixed   - Downloadable product hash save issue resolved

= 3.5.5 =
* Feature - WCFM custom fields front-end automatic visibility option added - [Documentation](https://wclovers.com/knowledgebase/wcfm-custom-fields/)
* Enhance - Now vendors are allowed to add embed code to their product description
* Enhance - Now Shipment Tracking widget available under Order Details screen 
* Enhance - Multi-site installation multi-Vendor compatibility added
* Enhance - WCFM form custom validation option added using "wcfm_form_custom_validation" filter (Developer friendly) 
* Tweaks  - Product review email added as backup option (only works if multi-vendor email failed)
* Fixed   - Order details page "Order Again" button visibility issue resolved
* Fixed   - Dokan Multi-vendor shipment tracking issue resolved
* Fixed   - Order details empty billing shipping table issue resolved

= 3.5.4 =
* Feature - Vendor custom badgae support added - [Documentation](https://wclovers.com/knowledgebase/wcfm-vendor-badges/)
* Enhance - WC Marketplace 3.0 vendor store location settings added

= 3.5.3 =
* Feature - Vendor custom badgae support added - [Documentation](https://wclovers.com/knowledgebase/wcfm-vendor-badges/)
* Enhance - Attributes on active auto-selection property added
* Enhance - Notificaton dashboard vendor name display improved
* Enhance - WCFM dashboard page conflict with other pages setting handler added
* Fixed   - Vendor and customer details page php error issue resolved 
* Fixed   - Dashboard z-index issue resolved
* Fixed   - New variation sales schedule calendar issue resolved

= 3.5.2 =
* Enhance - Variations sales price schedule option added
* Enhance - Toolset fields data display added in customer manager page
* Enhance - Toolset fields data display added in Vendor manager page
* Fixed   - Auction product shiping tracking option unavailable issue resolved
* Fixed   - Shop Manager wp admin view icon visible issue resolved

= 3.5.1 =
* Feature - WCFM Customer Toolset fields compatibility added
* Feature - Vendor wp-admin backend controller now in WCfM Free
* Enhance - WC Vendors Pro flat rate shipping support added 
* Fixed   - ACF multi-select field save issue resolved
* Fixed   - Group Manager capability reset to default shop manager issue resolved

= 3.5.0 =
* Feature - WCFM CRM (customer) module introduced
* Feature - Yoast SEO Premium compatibility added
* Feature - WooCommerce Lottery compatibility added
* Enhance - Vendor manage profile update form validation added
* Enhance - WooCommerce Checkout Field Manager additional fields displayed at order details page
* Tweaks  - On disable advanced block capability - Product Review always set ON
* Tweaks  - On disable downloadable block capability - admin added downloadable files untouched
* Tweaks  - On disable gallery image capability - admin added gallery images untouched
* Tweaks  - Product import from server file option removed for vendor users
* Fixed   - Gallery image bilk upload limit capability violation issue resolved
* Fixed   - Sales chart error due to deleted products issue resolved

= 3.4.7 =
* Feature - WooCommerce PDF Product Voucher compatibility added
* Feature - Add new category section under Product Manager screen
* Feature - WC Variation Swatches custom attribute types support added
* Enhance - Text type pre-defined attributes support added
* Enhance - Select type attributes "Select all" and "Select none" option added
* Enhance - Vendor Manage profile update section added
* Fixed   - WPML translation vendor assignment issue resolved
* Fixed   - WC Order deprecated function issue resolved

= 3.4.6 =
* Feature - Article Module added - now you may allow your vendors to write their own articles
* Feature - WCFM Product Manager Attributes block re-defined
* Enhance - Enquiry Tab - WooCommerce Tab Manager compatibility added
* Enhance - Choose from the most used tags option added 
* Enhance - Bookings Listings guest user name displayed
* Enhance - Appointments Listings guest user name displayed
* Tweaks  - WCFM enquiry customer email added to "reply-to"
* Tweaks  - Dokan admin content restriction issue taken care off
* Fixed   - Product sales report character issue resolved
* Fixed   - Products dashboard total product count issue resolved

= 3.4.5 =
* Feature - "wcfm_notifications" shorcode added - now you can add WCFM dashbaord header panel notification icons anywhere in your site
* Enhance - WCFM Dashboard load as per user locale
* Enhance - Notification message delete option added of Admin
* Enhance - Edit publish product auto-publish capability option added
* Enhance - WCFM product manager before save filter added "wcfm_product_content_before_save"
* Enhance - WCFM translated product before save filter added "wcfm_translated_product_content_before_save"
* Enhance - Bookings general option fields filter added "wcfm_wcbokings_general_fields"
* Enhance - WCFM editor content before save filter added "wcfm_editor_content_before_save"
* Fixed   - New Booking multiple notification issue resolved
* Fixed   - New Appointment multiple notification issue resolved
* Fixed   - Appointments filter list others staff visible issue resolved
* Fixed   - Appointment product delete issue resolved
* Fixed   - Category/Taxonomy checklist collapser issue resolved
* Fixed   - Some missing strings added for translation

= 3.4.4 =
* Enhance - WCFM Dashboard date display alinged with WP date settings
* Enahnce - WP User Avatar vendors media upload capability conflict resolved 
* Enhance - Multi-currency order listings support added
* Enhance - Vendor unpaid order details view restriction added
* Enhance - Vendor sales report shipping controller filter (wcfm_sales_report_is_allow_shipping) added
* Enhance - Product dashboard auto screen manager added
* Fixed   - Catalog optons saving issue resolved
* Fixed   - Manual Bookings & Appointments with order PHP error issue resolved
* Fixed   - Appointment Staff empty box issue resolved
* Fixed   - Appointments filter list others product visible issue resolved
* Fixed   - Order, Bookings, Appointments listings page customer details capability issue resolved

= 3.4.3 =
* Feature - WC Vendors Stripe Connect compatibility added
* Feature - WC Vendors Mangopay payment option compatibility added
* Enhance - Dokan vendor shipping rates setting improved
* Enhance - Dokan seller new order notification improved
* Fixed   - Dokan vendor product check issue resolved
* Fixed   - WC marketplace shop name update issue resolved
* Fixed   - WC Order deprecated function issue resolved
* Fixed   - BuddyPress non-vendor nav menu wcfm dashboard link issue resolved
* Fixed   - Manual Booking & Appointment create without order issue resolved
* Fixed   - Vendor order dashboard double date filter issue resolved
* Fixed   - Some spelling mistakes resolved

= 3.4.2 =
* Feature - WCFM + BuddyPress integration - [Overview](https://wclovers.com/blog/woocommerce-frontend-manager-buddypress/)
* Feature - WCFM WC Appointments calendar weekly view added
* Feature - Notification Bulk Mark as Read option added
* Enhance - Admin orders date filter added
* Fixed   - Vendors published product edit validation check issue resolved

= 3.4.1 =
* Feature - WCFM Vendor Details screen for Admin added
* Enhance - Vendors listing page product count column added 
* Enhance - Product belongs to vendor restriction check added
* Enhance - WCFM screen top block wrapper added
* Fixed   - WPML allowed category assign issue resolved
* Fixed   - WCFM Edit Published Product capability issue resolved
* Fixed   - WCMp Vendor Verification action missing issue resolved
* Fixed   - Vendor product image update issue resolved
* Fixed   - Notice display CSS issue resolved
* Fixed   - Site default language conflict with English issue resolved

= 3.4.0 =
* Feature - WCFM Catalog Mode integrated - [Documentation](https://wclovers.com/knowledgebase/wcfm-catalog-mode/)
* Feature - PDF Packing Slip download option added
* Feature - Vacation Mode disable purchase option added
* Enhance - Group Manager resource access controller redefined
* Fixed   - WCFM Product Listings WPML issue resolved
* Fixed   - WCMp vendor store template global setting check issue resolved
* Fixed   - Dokan vendors vacation mode issue resolved

= 3.3.8 =
* Enhance - Products Limit label added at Products dashboard
* Enhance - Enquiry dashboard date filter added 
* Fixed   - WooCommerce Germanized Pro auto-calculation decimal issue resolved
* Fixed   - ACF Pro multi-select PHP warning issue resolved

= 3.3.7 =
* Feature - WCMp single product multi-seller compatibility added
* Feature - ACF Pro compatibility added
* Enhance - WooCommerce Germanized Pro auto-calculate base price compatibility added
* Enhance - Sticky footer message display improved

= 3.3.6 =
* Feature - [WCFM - Membership](https://wordpress.org/plugins/wc-multivendor-membership/) compatibility added
* Feature - FedEx WooCommerce Shipping with Print Label compatible
* Enhance - Variable product creation redifined
* Enhance - Store URL added in responsive view

= 3.3.5 =
* Tweaks  - WCFM Message Board now call as Notification Board 
* Enhance - WCFM Dashbaord - Notification & Enquiry widget added
* Enhance - WCMp vendor shop template selection settings added
* Enhance - WCMp PayPal adaptive support added
* Enhance - WCFM gallery multi-image upload support added
* Enhance - Notification capability option added
* Fixed   - Gallery Image multiple delete option issue resolved
* Fixed   - Product Manager minor CSS issue resolved
* Fixed   - Staff availability quantity PHP warning issue resolved
* Fixed   - Groups list registration PHP warning issue resolved

= 3.3.4 =
* Feature - WCFM destop notification added - [Documentation](https://wclovers.com/blog/wcfm-desktop-notification/)
* Enhance - WCFM sticky save bar disable option added
* Enhance - RNB Rental & Booking availability manager added
* Enhance - WC Box Office ticket capability controller added
* Enhance - Booking & Appointment customer details capability added
* Fixed   - Button hover color change style setting issue resolved
* Fixed   - Product type wise Toolset field visible issue resolved
* Fixed   - Staff login capability issue resolved
* Fixed   - Non-wcfm user notification refresher issue resolved

= 3.3.3 =
* Feature - WooCommerce Box Office compatible
* Enhance - WCFM dashbaord new layout 
* Enhance - WCFM dashbaord welcome box & sidebar logo disable controller added
* Enhance - WCFM dashbaord top bar and main container background color changer added
* Enhance - Vendor coupon auto-restriction added
* Enhance - WCFM email module separated from WP default email
* Fixed   - Gallery image delete issue resolved
* Fixed   - Admin message count issue resolved
* Fixed   - Listings product edit JS conflict issue resolved

= 3.3.2 =
* Feature - WCFM dashboard new responsive (tablet & mobile) view
* Feature - WooCommerce Germanized compatible
* Feature - DHL Express/DHL Paket WooCommerce Shipping with Print Label by XAdapter compatible
* Feature - WooCommerce Bookings global availability support for WC Product Vendors
* Enhance - WooCommerce Appointments 3.5.5 compatibility added
* Enhance - WC Marketplace vendors earning pay status filter added
* Fixed   - Booking & Appointments message notification issue resolved
* Fixed   - Products for Listings JS conflict issue resolved

= 3.3.1 =
* Feature - WCFM Exclusive - Seller Verification module - [Documentation](https://wclovers.com/knowledgebase/wcfm-vendor-verification/)
* Enhance - Mobile view layout changes
* Enhance - Minor responsive CSS improved

= 3.3.0 =
* Feature - Dokan Multivendor & Dokan Pro compatible - [Demo](http://dokan.wcfmdemos.com/my-account)
* Feature - Add new term for attribuutes
* Enhance - Global validation rule defined
* Enhance - Minor responsive CSS improved

= 3.2.9 =
* Feature - WooCommerce Role Based Price compatible
* Feature - Order export option
* Feature - Low stock and Out of stock report download option
* Feature - Vendor commission stats download option
* Feature - WCMp commission and transaction download option
* Enhance - WCFM order details page modified
* Enhance - WC Vendors commission detailing improved 
* Enhance - WC Product Vendors commission detailing improved
* Enhance - WCMp commission detailing improved 
* Enhance - WCMp admin fees based commission report for admin and vendor
* Fixed   - is_wcfm_needs_shipping fatal error on backend order update issue resolved
* Fixed   - WCMp withdrawal request disable checkbox issue resolved
* Fixed   - Minor CSS issue resolved

= 3.2.8 =
* Feature - WCFM Enquiry manager integrated - [Documention](https://wclovers.com/knowledgebase/wcfm-enquiry-board/)
* Feature - WCFM module controller added
* Feature - WCFM custom field required option added
* Feature - ACF category specific field controller supported
* Enhance - GEO my WP default location set by vendor store location
* Enhance - Add new hover menu disable settings added
* Enhance - Media upload popup stings added for translation
* Enhance - Location based analytics improved
* FIxed   - WPML icl_object link from backend issue resolved
* Fixed   - Minor responsive CSS issue resolved
* Fixed   - Product manager tab switching issue resolved

= 3.2.7 =
* Feature - WCFM Bulk Stock manager integrated - [Documention](https://wclovers.com/knowledgebase/wcfm-bulk-stock-manager/)
* Feature - Product limit capability controller
* Feature - Custom taxonomy restriction capability controller
* Enhance - Required validation improved
* Fixed   - Minor responsive CSS issue resolved
* Fixed   - Without menu dashbaord shrink issue resolved

= 3.2.6 =
* Feature - ACF product category based field group support added
* Enhance - WooCommerce 3.2.2 compatibility added
* Enhance - WC Vendors Pro Shiping Rates settings added
* Enhance - WC Vendors Pro product specific shipping rate settings added
* Enhance - ACF & Toolset fields default value supported
* Enhance - ACF & Toolset fields required property supported 
* Enhance - Russian translation added (Thanks to Alexey Seregin)
* Fixed   - WCFM WPML optimized script load issue resolved
* Fixed   - WCFM dashbaord product stats JS error resolved

= 3.2.5 =
* Feature - WCFM region specific analytics integrated 
* Fixed   - WCMp setting page error issue resolved
* Fixed   - WC Vendors product export issue resolved
* Fixed   - Minor CSS & JS issue resolved

= 3.2.4 =
* Feature - WCFM - Product Bulk edit option 
* Feature - WCFM Dashboard more optimized - now more faster and smoother
* Feature - GEO my WP fully integrated (WCFM Free)
* Feature - Toolset Maps integrated
* Feature - Per product shipping for Product Vendors in default (no more additional plugin required)
* Enhance - WC Appointments 3.4.8 compatible
* Fixed   - Custom product attribute screen expand issue resolved
* Fixed   - WC Appointments appointment details page broken issue resolved
* Fixed   - WC Simple Auctions auction dashboard broken for WC Vendors issue resolved

= 3.2.3 =
* Feature - [WCFM - Product HUB](https://wclovers.com/product/woocommerce-frontend-manager-product-hub/) compatible
* Feature - RnB Rental & Bookings Systems request a quote integrated
* Enhance - Virtual product option is now in WCFM core
* Enhance - WC Bookings list and details with status update is now in WCFM core
* Enhance - WCMp request withdrawal is now in WCFM core
* Fixed   - Products for Listings edit issue resolved
* Fixed   - Minor CSS issue resolved

= 3.2.2 =
* Feature - Vendnor order status capability (now this is part of WCFM Free)
* Enhance - WCFM dashboard shows only Gross sales (no more net sales no more confussion)
* Feature - WordPress caching and minifier plugins compatibility
* Enhance - Order dashboard gross sales column added
* Enhance - WCFM uploader custom field type added
* Enhance - ACF multi-select field supported

= 3.2.1 =
* Feature - Create product under add listings page - [Documentation](https://wclovers.com/knowledgebase/wcfm-product-listings/)
* Feature - Vendors sales dashbaord for Admin
* Feature - Admin and Vendor separate screen manager
* Feature - WCFM dashbaord theme header disbale option added
* Feature - WCFM menu sortable using priority
* Feature - Bulgarian translation added

= 3.2.0 =
* Feature - WCFM Multilingual (WPML Integration) - [Documentation](https://wclovers.com/knowledgebase/wcfm-wpml/)

= 3.1.7 =
* Enhance - WCMp Admin Fees support
* Enhance - Vendor association from WCFM admin front-end dashboard
* Enhance - Product wise commission manage from WCFM admin front-end dashboard
* Feature - Product Type wise Toolset field group setup
* Fixed   - Listings status filter count issue resolved
* Fixed   - Listings association issue resolved  

= 3.1.6 =
* Feature - WCMp vendor stripe connect integrated
* Enhance - Bookable product virtual option added
* Fixed   - Bookings add resource options dummy issue resolved
* Fixed   - Admin dashboard net sales disaply issue resolved 

= 3.1.5 =
* Enhance - Add Attribute capability
* Enhance - Delete Media capability
* Enhance - Product preview on save
* Feature - Listings status filter added
* Feature - Listings views added
* Enhance - Minor CSS changes
* Fixed   - Bookings JS issue (Listify) resolved

= 3.1.4 =
* Enhance - WCFM text-editor RTL support added
* Enhance - WCFM Dashbaord template child theme header/footer support added
* Enhance - Minor CSS changes
* Fixed   - Vendor dashboard sales report graph data display issue resolved

= 3.1.3 =
* Feature - WCFM Dashboard setup widget added
* Enhance - WCFM Dashbaord template child theme support added
* Fixed   - Minor CSS fixes

= 3.1.2 =
* Feature - WCFM Dashboard full page view 
* Feature - Reports graph library changed

= 3.1.1 =
* Feature - Address Geocoder integrated
* Feature - Notice topic reply
* Enhance - Toolset repeator field support
* Feature - Admin product dashboard Vendor filter
* Fixed   - On update featured product issue resolved
* Fixed   - Booking calendar page reload loop issue resolved
* Fixed   - Minor CSS fixes

= 3.1.0 =
* Feature - WCFM New Dashboard
* Feature - Advanced Notice Board
* Enhance - Order notification on Message Board
* Enhance - Booking notification on Message Board
* Enhance - Appointment notification on Message Board
* Enhance - ACF Image/File type field support
* Enhance - Toolset Image/File type field support
* Fixed   - Category filtering by product type issue resolved

= 3.0.5 =
* Enhance - Exclusively compatible with Listify and Listable Themes
* Enhance - Category widget sub-category capability controller added
* Fixed   - Accommodation Bookings resource & person save issue resolved
* Fixed   - Shipping Tracking status display for Bookable products issue resolved
* Fixed   - Minor CSS/JS issue resolved

= 3.0.4 =
* Feature - Advanced Custom Fields(ACF) compatible - [Documentation](https://wclovers.com/knowledgebase/wcfm-advanced-custom-fields-acf/)
* Enhance - Shipment Tracking - Tracking Code field added
* Feature - YITH Auctions Free compatible
* Enhance - Product Manager category checklist/flat - both view supported
* Fixed   - MapPress 2.46+ conflict issue resolved
* Fixed   - Custom Attributes visible as taxonomy issue resolved
* Enhance - CSS enhance to more compatible with Themes

= 3.0.3 =
* Feature - Shipping Tracking Module integrated - [Documentation](https://wclovers.com/knowledgebase/wcfm-shipping-tracking/)
* Enhance - Product Manager Category widget modified
* Fixed   - PDF invoice CSS issue resolved

= 3.0.2 =
* Feature - WooCommerce Additional Variation Images compatible
* Feature - All Vendor Settings (except Shipping) are now in WCFM FREE
* Feature - Toolset Types Taxonomy field supported - Product Vendors additional settings
* Fixed   - Booking/Appointment confirmation customer issue resolved

= 3.0.1 =
* Feature - Category level capability controller
* Feature - Product type wise category grouping
* Feature - Toolset Types User field support added
* Feature - Product Mark as Featured from Products Dashboard

= 3.0.0 =
* Feature - WCFM Analytics compatibility
* Feature - Map Press compatibility
* Feature - User Locale support
* Feature - Listings Stats
* Feature - Product filter by SKU
* Feature - Bookable Product "Restrict Start Days" added (New feature from WC Bookings 1.10.7)
* Feature - Vendor column in Product dashboard
* Enhance - TinyMCE spell-checker compatibility
* Enhance - Page collapsable improved
* Fixed   - Some label grammer fixed

= 2.6.2 =
* Enhance - Advanced Knowledge-base
* Feature - Booking new/confirm vendor notification
* Enhance - Improved Variation block
* Feature - Appointment new/confirm vendor notification
* Feature - Multi-vendor WooCommerce Sequential Order Number support added

= 2.6.1 =
* Feature - Dashboard settings
* Feature - WooCommerce Sequential Order Number support added
* Fixed   - Some minor CSS fixed - responsive

= 2.6.0 =
* Feature - WCFM Groups & Staffs compatibility

= 2.5.2 =
* Feature - WCMp Request withdrawal mode compatibility
* Feature - Duplicate Product
* Enhance - ToolSet Types Address( Map) field compatible
* Fixed   - Products Dashboard Ajax error issue resolved
* Enhance - Product Editor Google Map embed code supported
* Feature - WCMp vendor store slug edit
* Fixed   - Product Vendors store name update issue resolved

= 2.5.1 =
* Feature - WCMp Vendor Shipping Settings
* Feature - Amazon Web Service (S3) media library compatible
* Enhance - Advance Capability to restrict Vendor from using Rich Editor

= 2.5.0 =
* Feature - WC Per Product Shipping compatibility
* Feature - Toolset Types compatibility
* Fixed   - WooCommerce Appointments 3.2.0+ compatible
* Fixed   - Order Guest customer name display issue resolved
* Enance  - Modified Capability Controller
* Fixed   - Minor Javascript issue resolved

= 2.4.6 =
* Feature - WCfM Listings Dashboard
* Feature - Variation Bulk Actions
* Fixed   - WooCommerce PDF Invoices & Packing Slips 2.0+ compatible
* Fixed   - Booking and Rental System (Woocommerce) 1.0.7 compatible
* Fixed   - Minor Javascript issue resolved

= 2.4.5 =
* Feature - RTL support added
* Fixed   - Knowledgebase video embed issue resolved 
* Enhance - Vendor Dashboard Hooks added
* Fixed   - Vendor Order Price filter
* Fixed   - Admin Dashboard Notice controller

= 2.4.4 =
* Feature - WC Booking Accommodation compatibility
* Fixed   - Deleted order handling
* Fixed   - Product excerpt or description disable JS issue resolved

= 2.4.3 =
* Feature - Product Importer integrated
* Enhance - Short code support "menu=false" option
* Feature - Header Panel disable option
* Feature - Bookings Global Availability settings
* Feature - Appointment Global Availability settings
* Feature - Rental product Inventory Management
* Enhance - Pie chart color midifier filter added - wcfm_sales_by_product_pie_chart_colors
* Enhance - Bar chart color midifier filter added - wcfm_sales_by_product_bar_chart_colors
* Enhance - Bar Chart color midifier filer for Admin - wcfm_admin_sales_by_date_chart_colors
* Enhance - Bar Chart color midifier filer for Vendor - wcfm_vendor_sales_by_date_chart_colors

= 2.4.2 =
* Feature - Product Exporter integrated

= 2.4.1 =
* Feature - WooCommerce Product Addons compatible
* Feature - WCFM endpoints as short code
* Feature - WP Hide & Security Enhancer compatible

= 2.4.0 =
* Feature - WooCommerce Appointments compatibility
* Feature - Auctions Dashboard

= 2.3.9 =
* Feature - Manual Booking for Vendor
* Feature - WooCommerce Simple Auction compatibility
* Feature - WooCommerce Rental & Bookings System compatibility

= 2.3.8 =
* Feature - Manual Booking
* Feature - YITH Auctions compatibility
* Feature - WooCommerce Rental & Booking compatibility
* Enhance - Custom Field enable/disable option added

= 2.3.7 =
* Feature - Product Custom Field
* Feature - Screen Manager

= 2.3.6 =
* Enhance - WooCommerce Bookings Manage Resources
* Enhance - WooCommerce Bookings Dashboard

= 2.3.5 =
* Feature - WooCommerce Bulk Discount compatibility
* Feature - WooCommerce Product Fees compatibility

= 2.3.4 =
* Feature - WP Job Manager - WC Paid Listing compatibility
* Feature - WP Job Manager - Resume Manager compatibility
* Enhance - Messaging action integrated

= 2.3.3 =
* Enhance - WC Marketplace Knowledgebase compatibility

= 2.3.2 =
* Feature - Knowledgebase Module
* Feature - Notification Module
* Feature - Direct messaging system
* Enhance - WC Marketplace 2.7 compatibility
* Feature - Category n-th hirerchy supported

= 2.3.1 =
* Feature - WCFM Capability Controller
* Feature - Vendor Vacation Mode
* Feature - WCFM menu & loader controller
* Feature - GEO my WordPress compatibility
* Enhance - Vendor Settings redefined
* Enhance - Profile page redifined
* Fixed   - Order details total commission resolved
* Fixed   - Product Vendors manual booking listing resolved

= 2.3.0 =
* Feature - New Product Manager
* Fixed   - Mobile responsive issues resolved
* Fixed   - Loco Translate template missing resolved

= 2.2.6 =
* Feature - Booking Calendar integrated
* Feature - WCFM endpoints editable
* Fixed   - Minor CSS fixed

= 2.2.5 =
* Feature - Profile page integrated
* Feature - WCMp orders dashboard integrated
* Feature - WCMp order details integrated
* Feature - WCMp settings page integrated
* Enhance - WC Vendors more settings option added
* Fixed   - Orders dashboard pagination issue resolved

= 2.2.4 =
* Feature - WooCommerce PDF Invoices & Packing Slips compatibility

= 2.2.3 = 
* Feature - WP Job Manager compatibility

= 2.2.2 =
* Feature - WooCommerce Subscription compatibility
* Feature - WooCommerce MSRP Pricing compatibility
* Enhance - WCFM Dashboard changed
* Fixed   - Minor PHP and JS issue resolved

= 2.2.1 = 
* Fixed - Minor CSS Fixed
* Fixed - Some spelling corrected

= 2.2.0 = 
* Enhance - New menu style
* Feature - Bookings pricing rule
* Fixed   - Minor JS conflict issue resolved

= 2.1.1 =
* Enhance - WC Vendors settings page
* Enhance - WC Product Vendors settings page
* Enhance - Menu icon color settings

= 2.1.0 =
* Feature - WooCommerce Product Vendors compatible
* Feature - WC Vendors Pro compatible
* Feature - WooCommerce Bookings integration now free
* Feature - Vendors sales by date graphical report
* Fixed   - Orders dashboard responsive issue resolved
* Fixed   - Minor CSS issue fixed

= 2.0.5 =
* Feature - YITH WooCommerce Brands Add-On support added
* Feature - WooCommerce Custom Product Tabs Lite support added
* Feature - WC Marketplace product specific commission integrated
* Fixed   - On update custom css mixed-up issue resolved 

= 2.0.4 =
* Feature - WC Vendors product specific commission integrated
* Feature - Top selling products graph in dashboard
* Enhance - Coupon manager WC3.0 data source integrated
* Fixed   - WC Vendors sales by product report issue resolved
* Fixed   - Vendor Dashboard top saller count issue resolved
* Fixed   - Vendor Dashboard commission graph issue resolved


= 2.0.3 =
* Enhance - Product manager WC3.0 data source integrated
* Fixed   - Select type attribute issue resolved
* Fixed   - Variation attribute mixed up issue resolved

= 2.0.2 =
* Feature - Grouped Product support added
* Feature - Linked Product support added
* Fixed   - Custom taxonomy & Product attribute mixedup issue resolved

= 2.0.1 =
* Fixed - WC not installed issue resolved
* Fixed - WC Marketplace product vendor association by taxonomy fixed

= 2.0.0 =
* Feature - WooCommerce Bookings compatible
* Feature - Product Custom Taxonomy support added
* Feature - New Menu style added - Traditional
* Enhance - Fully responsive for any device
* Fixed   - Store admin Vendor product publish issue resolved
* Fixed   - WC Vendors vendor order listing issue resolved 
* Fixed   - DataTable responsive column hide issue resolved 
* Fixed   - WC Vendor capabilities update issue resolved

= 1.1.6 =
* Feature - WCFM quick access menu at admin bar
* Feature - Change dashboard color to match your theme
* Fixed   - Minor responsive CSS issue fixed

= 1.1.5 =
* Fixed - WC Vendors order details total commission fixed

= 1.1.4 =
* Feature - WC Vendors Coupon capability control settings, no WC Vendor Pro required
* Enhance - WC Vendors dashboard visibility control using vendor capability settings
* Enhance - Product category selection using choosen
* Enhance - New menu style introduced

= 1.1.3 =
* Feature - WC Vendors coupon support added, no Pro required
* Enhance - WC new coupon model integrated
* Fixed - WC Vendors 1.9.10 compatibility added

= 1.1.2 =
* Feature - Fully compatible with YoastSEO
* Feature - WCFM is fully synced with wp-admin
* Feature - WCFM widget at WP Dashboard
* Fixed - Minor CSS fixes for mobile view

= 1.1.1 =
* Fixed - Responsive CSS improved to look more compact
* Add - Flatsome full support added
* Add - Avada full support added
* Add - Porto ful support added
* Enhance - Top selling product graph using flot to reduce script loading
* Enhance - Quick Edit popup replace with Modal Popup for better usability

= 1.1.0 =
* Feature - Fully compatible with WC Marketplace
* Fixed   - Minor CSS fixes

= 1.0.7 = 
* Fixed - WP custom permalink issue resolved
* Fixed - WP default theme CSS issue resolved

= 1.0.6 = 
* Fixed   - WP default permalink issue resolved
* Upgrade - WooCommerce 3.0 products deprecated method issue resolved
* Upgrade - WC Vendors 1.9.9 support added

= 1.0.5 = 
* Upgrade - WooCommerce 3.0 deprecated method issue resolved

= 1.0.4 = 
* Upgrade - WooCommerce 3.0 support added
* Fixed   - Product Dashboard issue fixed

= 1.0.3 =
* Fixed   - Permalink rules flush issue resolved
* Feature - More attractive Dashboard with Sales Report Charts
* Fixed   - Minor CSS fixes

= 1.0.2 =
* Fixed   - Responsive issue fixes
* Feature - Add Product button added at Products Dashboard
* Feature - Add Coupon button added at Coupons Dashboard

= 1.0.1 =
* Feature - Fully compatible with WC Vendors

= 1.0.0 =
* Initial version release

== Upgrade Notice ==

= 6.7.19 =
* Enhance - WooCommerce 9.7+ compatibility check added
* Enhance - Compatibility with German Market version 3.47
* Fixed   - Store Manager dashboard settings update issue for vendors