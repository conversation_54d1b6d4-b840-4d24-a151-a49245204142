.wcicon-truck-2 { color: #ccc; font-size: 35px; }
.wcicon-plus { color: #ccc; font-size: 20px; }
.wcicon-status-refunded { color: #ccc; font-size: 20px; }

#orders_details_general_expander a, #orders_details_items_expander a { color: #2ea2cc !important; }
#orders_details_general_expander .order-again, #orders_details_items_expander div.wc-booking-summary-actions, #orders_details_items_expander div.wc-appointment-summary-actions { display: none; }

#orders_details_items_expander table td, #orders_details_items_expander table th {
	text-align: left;
}

#orders_details_items_expander table.woocommerce_order_items th.line_cost, #orders_details_items_expander table.woocommerce_order_items td.line_cost { text-align: center; }

#orders_details_items_expander table.woocommerce_order_items td.thumb .wc-order-item-thumbnail {
	width: 38px;
	height: 38px;
	border: 2px solid #e8e8e8;
	background: #f8f8f8;
	color: #ccc;
	position: relative;
	font-size: 21px;
	display: block;
	text-align: center;
}

#orders_details_items_expander table.woocommerce_order_items td.thumb .wc-order-item-thumbnail img {
	max-width: 100%;
	height: auto;
}

#orders_details_items_expander table.woocommerce_order_items td.thumb {
	text-align: left;
	width: 38px;
	padding-bottom: 1.5em;
}

#orders_details_items_expander table.woocommerce_order_items td.line_cost {
	width: 1%;
}

#orders_details_items_expander table.woocommerce_order_items td.shipping_tax_cost {
	width: 150px;
}

#orders_details_items_expander .wc-order-totals {
	float: right;
	width: 50%;
	margin: 0;
	padding: 0;
}

#orders_details_items_expander table.woocommerce_order_items table.display_meta tr th, #orders_details_items_expander table.woocommerce_order_items table.display_meta tr td, #orders_details_items_expander table.woocommerce_order_items table.meta tr th {
	border: 0;
	padding: 0 4px .5em 0 !important;
	line-height: 1.5em;
	font-size: .92em !important;
	color: #888;
	vertical-align: middle;
	background: #fdfdfd;
}
#orders_details_items_expander table.woocommerce_order_items table.display_meta tr th { width: 110px; }

#orders_details_items_expander .wc-order-totals .total { font-weight: 500; }
#orders_details_items_expander .wc-order-totals .total, #orders_details_items_expander .wc-order-totals .label {
	color: #43454b !important;
	text-align: right !important;
	padding: 5px 10px;
}
#orders_details_items_expander .wc-order-totals .refunded-total { color: #a00; }

#orders_details_items_expander table.woocommerce_order_items .wc-order-item-discount {
	display: block;
  margin-top: .5em;
  white-space: nowrap;
  text-align: right;
}

#orders_details_items_expander tr.shipping>td, #orders_details_items_expander tr.shipping>th {
	display: table-cell;
}

#orders_details_items_expander .wc-item-meta {
	font-size: .875em;
	margin-left: 0;
	list-style: none;
}

#orders_details_items_expander .wc-item-meta li p, #orders_details_items_expander .wc-item-meta li strong {
	display: inline-block;
}

#orders_details_items_expander table.woocommerce_order_items .wc-order-item-discount .amount {
	font-size: .92em!important;
  color: #888;
}

#orders_details_items_expander .wc-order-data-row .wc-used-coupons {
	text-align: left;
	float: left;
	width: 50%;
}

#orders_details_items_expander ul.wc_coupon_list {
	padding-bottom: 5px;
	margin: 0;
	overflow: hidden;
	zoom: 1;
	clear: both;
	list-style: none;
}

#orders_details_items_expander ul.wc_coupon_list li {
	margin: 0;
}

#orders_details_items_expander ul.wc_coupon_list li.code {
	display: inline-block;
	position: relative;
	padding: 0 .5em;
	background-color: #fff;
	border: 1px solid #aaa;
	box-shadow: 0 1px 0 #dfdfdf;
	border-radius: 4px;
	margin-right: 5px;
	margin-top: 5px;
}

#orders_details_items_expander ul.wc_coupon_list li.code a { margin-right: 5px; }

#orders_details_items_expander ul.wc_coupon_list li.code a span {
	display: inline-block;
	text-decoration: none;
	color: #888;
	cursor: pointer;
}

#orders_details_items_expander table.display_meta, #orders_details_items_expander table.meta {
	margin: .5em 0 0;
	font-size: .92em!important;
	color: #888;
}

#orders_details_items_expander table.display_meta tr th, #orders_details_items_expander table.meta tr th {
	border: 0;
	padding: 0 4px .5em 0;
	line-height: 1.5em;
	width: 20%;
}

#orders_details_items_expander table.display_meta tr td, #orders_details_items_expander table.meta tr td {
	padding: 0 4px .5em 0;
	border: 0;
	line-height: 1.5em;
	vertical-align: top;
	text-align: left;
}

#orders_details_items_expander table.display_meta tr td p:last-child, #orders_details_items_expander table.meta tr td p:last-child {
	margin: 0;
	line-height: 1.5em;
}

.wc-order-item-sku, .wc-order-item-variation {
	display: block;
	margin-top: .5em;
	font-size: .92em!important;
	color: #888;
}

#order_quick_actions {
	float: right;
	margin-bottom: 8px;
	display: table-cell;
}

.wcfm_wp_admin_view { margin-top: 0px; }

.order-status {
	padding: 4px 4px;
	color: #fff;
	border-radius: 2px;
	font-size: 12px;
	line-height: 10px;
	margin-top: 8px;
	margin-left: 10px;
	display: inline-block;
	float: left;
	background-color: #20a8d8;
}

.order-status-pending { background-color: #f8cb00; }
.order-status-processing { background-color: #20c997; }
.order-status-completed { background-color: #4dbd74; }
.order-status-failed { background-color: #ffc107; }
.order-status-cancelled { background-color: #f86c6b; }
.order-status-on-hold { background-color: #6d6d6d; }
.order-status-refunded { background-color: #e83e8c; }
.order-status-shipped { background-color: #20a8d8; }

.wcfm_order_delivery_boy { display: inline-block; }

#wcfm-main-contentainer #wcfm_add_order_note, #wcfm-main-contentainer #wcfm_modify_order_status {
	float: right;
	margin-top: 0px;
	margin-left: 10px;
	
	background: #1a1a1a none repeat scroll 0 0;
	border: 0 none;
	border-radius: 4px;
	color: #fff;
	font-family: Montserrat,"Helvetica Neue",sans-serif;
	font-weight: 500;
	letter-spacing: 0.046875em;
	line-height: 1;
	padding: 0.84375em 0.875em 0.78125em !important;
	text-transform: uppercase;
}

#wcfm-main-contentainer #wcfm_add_order_note:hover, #wcfm-main-contentainer #wcfm_modify_order_status:hover {
	background: #17a2b8 none repeat scroll 0 0;
}

#wcfm-main-contentainer #wcfm_modify_order_status {
	color: #ffffff !important;
	float: none;
	padding: 4px;
}

#wcfm-main-contentainer table.woocommerce_order_items small.refunded {
	display: block;
	color: #a00;
	white-space: nowrap;
	margin-top: .5em;
}

#wcfm-main-contentainer table.woocommerce_order_items small.refunded::before {
	font-family: Dashicons;
	speak: none;
	font-weight: 400;
	text-transform: none;
	-webkit-font-smoothing: antialiased;
	text-indent: 0px;
	width: 100%;
	height: 100%;
	text-align: center;
	content: "";
	position: relative;
	top: auto;
	left: auto;
	vertical-align: middle;
	line-height: 1em;
	font-variant: normal;
	margin: -1px 4px 0px 0px;
}

#orders_details_items_expander .wc-order-totals .refunded-total, #orders_details_items_expander #order_refunds .refunded-total {
	color: #a00 !important;
}

td.bkap_edit_column a, p.wc-deposits-order-item-description a {display: none !important;}

@media only screen and (max-width: 414px) {
  .no_mob {
  	display: none !important;
  }
  
  a.add_new_wcfm_ele_dashboard .text { display: none; }
}

@media only screen and (max-width: 768px) {
  .no_ipad {
  	display: none !important;
  }
}