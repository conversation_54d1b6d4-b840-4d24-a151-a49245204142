$wcfm_products_table="";var $wcfm_is_valid_form=!0,$wcfm_message_close_timer="",tinyMce_toolbar="undo redo | insert | styleselect | bold italic | alignleft aligncenter alignright alignjustify |  bullist numlist outdent indent | link image | ltr rtl";function initiateTip(){jQuery(".img_tip, .text_tip").each(function(){jQuery(this).qtip({content:jQuery(this).attr("data-tip"),position:{my:"top center",at:"bottom center",viewport:jQuery(window)},show:{solo:!0},hide:{inactive:6e4,fixed:!0},style:{classes:"qtip-dark qtip-shadow qtip-rounded qtip-wcfm-css qtip-wcfm-core-css"}})})}function GetURLParameter(t){for(var e=window.location.search.substring(1).split("&"),i=0;i<e.length;i++){var s=e[i].split("=");if(s[0]==t)return s[1]}}function wcfmMessageHide(){clearTimeout($wcfm_message_close_timer),$wcfm_message_close_timer=setTimeout(function(){jQuery(".wcfm-message").slideUp("slow",function(){jQuery(".wcfm-message").html("").removeClass("wcfm-success").removeClass("wcfm-error"),$wcfm_is_valid_form=!0})},1e4)}function getWCFMEditorContent(t){var e="";return jQuery("#"+t).length>0&&(e=(jQuery("#"+t).hasClass("rich_editor")||jQuery("#"+t).hasClass("wcfm_wpeditor"))&&"undefined"!=typeof tinymce&&null!=tinymce.get(t)?tinymce.get(t).getContent({format:"raw"}):jQuery("#"+t).val()),e}function intiateWCFMuQuickEdit(){jQuery(".wcfmu_product_quick_edit").each(function(){jQuery(this).click(function(t){t.preventDefault(),jQueryquick_edit=jQuery(this),jQueryproduct=jQueryquick_edit.data("product"),jQuery(".products").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var e={action:"wcfmu_quick_edit_html",product:jQueryproduct,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};return jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:e,success:function(t){jQuery.colorbox({html:t,width:$popup_width,onComplete:function(){jQuery("#wcfm_quick_edit_button").click(function(){if($wcfm_is_valid_form=!0,jQuery("#wcfm_quick_edit_form").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),jQuery(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp(),0==jQuery("input[name=wcfm_quick_edit_title]").val().length)jQuery("#wcfm_quick_edit_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfmu_products_manage_messages.no_title).addClass("wcfm-error").slideDown(),wcfm_notification_sound.play(),jQuery("#wcfm_quick_edit_form").unblock();else if(jQuery(document.body).trigger("wcfm_form_validate",jQuery("#wcfm_quick_edit_form")),$wcfm_is_valid_form){var t={action:"wcfm_ajax_controller",controller:"wcfm-products-quick-manage",wcfm_quick_edit_form:jQuery("#wcfm_quick_edit_form").serialize(),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};jQuery.post(wcfm_params.ajax_url,t,function(t){t&&(jQueryresponse_json=jQuery.parseJSON(t),jQuery(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp(),jQueryresponse_json.status?(jQuery("#wcfm_quick_edit_form .wcfm-message").html('<span class="wcicon-status-completed"></span>'+jQueryresponse_json.message).addClass("wcfm-success").slideDown(),jQuery("#wcfm_quick_edit_button").hide()):jQuery("#wcfm_quick_edit_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+jQueryresponse_json.message).addClass("wcfm-error").slideDown(),wcfm_notification_sound.play(),jQuery("#wcfm_quick_edit_form").unblock(),setTimeout(function(){$wcfm_products_table&&$wcfm_products_table.ajax.reload(),jQuery.colorbox.remove()},2e3))})}else wcfm_notification_sound.play(),jQuery("#wcfm_quick_edit_form").unblock()})}}),jQuery(".products").unblock()}}),!1})})}function intiateWCFMuScreenManager(){jQuery(".wcfm_screen_manager").each(function(){jQuery(this).click(function(t){t.preventDefault(),jQueryScreen_Manager=jQuery(this),jQueryScreen=jQueryScreen_Manager.data("screen");var e={action:"wcfmu_screen_manager_html",screen:jQueryScreen,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};return jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:e,success:function(t){jQuery.colorbox({html:t,width:$popup_width,onComplete:function(){jQuery("#wcfm_screen_manager_button").click(function(){jQuery("#wcfm_screen_manager_form").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),jQuery(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp();var t={action:"wcfm_ajax_controller",controller:"wcfm-screen-manage",wcfm_screen_manager_form:jQuery("#wcfm_screen_manager_form").serialize(),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};jQuery.post(wcfm_params.ajax_url,t,function(t){t&&(jQueryresponse_json=jQuery.parseJSON(t),jQuery(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp(),jQueryresponse_json.status?jQuery("#wcfm_screen_manager_form .wcfm-message").html('<span class="wcicon-status-completed"></span>'+jQueryresponse_json.message).addClass("wcfm-success").slideDown():jQuery("#wcfm_screen_manager_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+jQueryresponse_json.message).addClass("wcfm-error").slideDown(),jQuery("#wcfm_screen_manager_form").unblock(),setTimeout(function(){jQuery.colorbox.remove(),window.location=window.location.href},2e3))})})}})}}),!1})})}function wcfmstripHtml(t){var e=document.createElement("div");return e.innerHTML=t,e.textContent||e.innerText||""}function wcfmcapitalizeFirstLetter(t){return t.charAt(0).toUpperCase()+t.slice(1)}if(wcfm_params.wcfm_allow_tinymce_options&&(tinyMce_toolbar=wcfm_params.wcfm_allow_tinymce_options),$popup_width="50%",$large_popup_width="75%",jQuery(window).width()<=768&&($popup_width="95%",$large_popup_width="95%"),function(t,e,s){!function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery","imagesloaded"],t):jQuery&&!jQuery.fn.qtip&&t(jQuery)}(function(o){function n(t,e,i,s){this.id=i,this.target=t,this.tooltip=Q,this.elements=elements={target:t},this._id=B+"-"+i,this.timers={img:{}},this.options=e,this.plugins={},this.cache=cache={event:{},target:o(),disabled:S,attr:s,onTooltip:S,lastClass:""},this.rendered=this.destroyed=this.disabled=this.waiting=this.hiddenDuringWait=this.positioning=this.triggering=S}function a(t){return t===Q||"object"!==o.type(t)}function r(t){return!(o.isFunction(t)||t&&t.attr||t.length||"object"===o.type(t)&&(t.jquery||t.then))}function c(t){var e,i,s,n;return a(t)?S:(a(t.metadata)&&(t.metadata={type:t.metadata}),"content"in t&&(a(e=t.content)||e.jquery||e.done?e=t.content={text:i=r(e)?S:e}:i=e.text,"ajax"in e&&(s=e.ajax,n=s&&s.once!==S,delete e.ajax,e.text=function(t,e){var a=i||o(this).attr(e.options.content.attr)||"Loading...",r=o.ajax(o.extend({},s,{context:e})).then(s.success,Q,s.error).then(function(t){return t&&n&&e.set("content.text",t),t},function(t,i,s){e.destroyed||0===t.status||e.set("content.text",i+": "+s)});return n?a:(e.set("content.text",a),r)}),"title"in e&&(a(e.title)||(e.button=e.title.button,e.title=e.title.text),r(e.title||S)&&(e.title=S))),"position"in t&&a(t.position)&&(t.position={my:t.position,at:t.position}),"show"in t&&a(t.show)&&(t.show=t.show.jquery?{target:t.show}:t.show===$?{ready:$}:{event:t.show}),"hide"in t&&a(t.hide)&&(t.hide=t.hide.jquery?{target:t.hide}:{event:t.hide}),"style"in t&&a(t.style)&&(t.style={classes:t.style}),o.each(P,function(){this.sanitize&&this.sanitize(t)}),t)}function l(t,e){for(var i,s=0,o=t,n=e.split(".");o=o[n[s++]];)n.length>s&&(i=o);return[i||t,n.pop()]}function h(t,e){var i,s,o;for(i in this.checks)for(s in this.checks[i])(o=RegExp(s,"i").exec(t))&&(e.push(o),("builtin"===i||this.plugins[i])&&this.checks[i][s].apply(this.plugins[i]||this,e))}function d(t){return V.concat("").join(t?"-"+t+" ":" ")}function p(t){if(this.tooltip.hasClass(Z))return S;clearTimeout(this.timers.show),clearTimeout(this.timers.hide);var e=o.proxy(function(){this.toggle($,t)},this);this.options.show.delay>0?this.timers.show=setTimeout(e,this.options.show.delay):e()}function u(t){if(this.tooltip.hasClass(Z))return S;var e=o(t.relatedTarget),i=e.closest(U)[0]===this.tooltip[0],s=e[0]===this.options.show.target[0];if(clearTimeout(this.timers.show),clearTimeout(this.timers.hide),this!==e[0]&&"mouse"===this.options.position.target&&i||this.options.hide.fixed&&/mouse(out|leave|move)/.test(t.type)&&(i||s))try{t.preventDefault(),t.stopImmediatePropagation()}catch(t){}else{var n=o.proxy(function(){this.toggle(S,t)},this);this.options.hide.delay>0?this.timers.hide=setTimeout(n,this.options.hide.delay):n()}}function f(t){return this.tooltip.hasClass(Z)||!this.options.hide.inactive?S:(clearTimeout(this.timers.inactive),this.timers.inactive=setTimeout(o.proxy(function(){this.hide(t)},this),this.options.hide.inactive),s)}function m(t){this.rendered&&this.tooltip[0].offsetWidth>0&&this.reposition(t)}function _(t,i,s){o(e.body).delegate(t,(i.split?i:i.join(nt+" "))+nt,function(){var t=j.api[o.attr(this,A)];t&&!t.disabled&&s.apply(t,arguments)})}function g(t,i,s){var a,r,l,h,d,p=o(e.body),u=t[0]===e?p:t,f=t.metadata?t.metadata(s.metadata):Q,m="html5"===s.metadata.type&&f?f[s.metadata.name]:Q,_=t.data(s.metadata.name||"qtipopts");try{_="string"==typeof _?o.parseJSON(_):_}catch(t){}if(r=(h=o.extend($,{},j.defaults,s,"object"==typeof _?c(_):Q,c(m||f))).position,h.id=i,"boolean"==typeof h.content.text){if(l=t.attr(h.content.attr),h.content.attr===S||!l)return S;h.content.text=l}if(r.container.length||(r.container=p),r.target===S&&(r.target=u),h.show.target===S&&(h.show.target=u),h.show.solo===$&&(h.show.solo=r.container.closest("body")),h.hide.target===S&&(h.hide.target=u),h.position.viewport===$&&(h.position.viewport=r.container),r.container=r.container.eq(0),r.at=new k(r.at,$),r.my=new k(r.my),t.data(B))if(h.overwrite)t.qtip("destroy");else if(h.overwrite===S)return S;return t.attr(H,i),h.suppress&&(d=t.attr("title"))&&t.removeAttr("title").attr(et,d).attr("title",""),a=new n(t,h,i,!!l),t.data(B,a),t.one("remove.qtip-"+i+" removeqtip.qtip-"+i,function(){var t;(t=o(this).data(B))&&t.destroy()}),a}function w(t){return t.charAt(0).toUpperCase()+t.slice(1)}function y(t,e){return parseInt(function(t,e){var i,o,n=e.charAt(0).toUpperCase()+e.slice(1),a=(e+" "+_t.join(n+" ")+n).split(" "),r=0;if(mt[e])return t.css(mt[e]);for(;i=a[r++];)if((o=t.css(i))!==s)return mt[e]=i,o}(t,e),10)}function v(t,e){this._ns="tip",this.options=e,this.offset=e.offset,this.size=[e.width,e.height],this.init(this.qtip=t)}function b(t,e){this.options=e,this._ns="-modal",this.init(this.qtip=t)}function x(t){this._ns="ie6",this.init(this.qtip=t)}var j,C,k,q,T,$=!0,S=!1,Q=null,M="x",W="y",z="width",D="height",L="top",F="left",N="bottom",E="right",O="center",R="flipinvert",I="shift",P={},B="qtip",H="data-hasqtip",A="data-qtip-id",V=["ui-widget","ui-tooltip"],U="."+B,X="click dblclick mousedown mouseup mousemove mouseleave mouseenter".split(" "),Y=B+"-fixed",K=B+"-default",G=B+"-focus",J=B+"-hover",Z=B+"-disabled",tt="_replacedByqTip",et="oldtitle";BROWSER={ie:function(){for(var t=3,i=e.createElement("div");(i.innerHTML="\x3c!--[if gt IE "+ ++t+"]><i></i><![endif]--\x3e")&&i.getElementsByTagName("i")[0];);return t>4?t:NaN}(),iOS:parseFloat((""+(/CPU.*OS ([0-9_]{1,5})|(CPU like).*AppleWebKit.*Mobile/i.exec(navigator.userAgent)||[0,""])[1]).replace("undefined","3_2").replace("_",".").replace("_",""))||S},(C=n.prototype).render=function(t){if(this.rendered||this.destroyed)return this;var e=this,i=this.options,s=this.cache,n=this.elements,a=i.content.text,r=i.content.title,c=i.content.button,l=i.position,h="."+this._id+" ",d=[];return o.attr(this.target[0],"aria-describedby",this._id),this.tooltip=n.tooltip=tooltip=o("<div/>",{id:this._id,class:[B,K,i.style.classes,B+"-pos-"+i.position.my.abbrev()].join(" "),width:i.style.width||"",height:i.style.height||"",tracking:"mouse"===l.target&&l.adjust.mouse,role:"alert","aria-live":"polite","aria-atomic":S,"aria-describedby":this._id+"-content","aria-hidden":$}).toggleClass(Z,this.disabled).attr(A,this.id).data(B,this).appendTo(l.container).append(n.content=o("<div />",{class:B+"-content",id:this._id+"-content","aria-atomic":$})),this.rendered=-1,this.positioning=$,r&&(this._createTitle(),o.isFunction(r)||d.push(this._updateTitle(r,S))),c&&this._createButton(),o.isFunction(a)||d.push(this._updateContent(a,S)),this.rendered=$,this._setWidget(),o.each(i.events,function(t,e){o.isFunction(e)&&tooltip.bind(("toggle"===t?["tooltipshow","tooltiphide"]:["tooltip"+t]).join(h)+h,e)}),o.each(P,function(t){var i;"render"===this.initialize&&(i=this(e))&&(e.plugins[t]=i)}),this._assignEvents(),o.when.apply(o,d).then(function(){e._trigger("render"),e.positioning=S,e.hiddenDuringWait||!i.show.ready&&!t||e.toggle($,s.event,S),e.hiddenDuringWait=S}),j.api[this.id]=this,this},C.destroy=function(t){function e(){if(!this.destroyed){this.destroyed=$;var t=this.target,e=t.attr(et);this.rendered&&this.tooltip.stop(1,0).find("*").remove().end().remove(),o.each(this.plugins,function(){this.destroy&&this.destroy()}),clearTimeout(this.timers.show),clearTimeout(this.timers.hide),this._unassignEvents(),t.removeData(B).removeAttr(A).removeAttr("aria-describedby"),this.options.suppress&&e&&t.attr("title",e).removeAttr(et),this._unbind(t),this.options=this.elements=this.cache=this.timers=this.plugins=this.mouse=Q,delete j.api[this.id]}}return this.destroyed?this.target:(t!==$&&this.rendered?(tooltip.one("tooltiphidden",o.proxy(e,this)),!this.triggering&&this.hide()):e.call(this),this.target)},q=C.checks={builtin:{"^id$":function(t,e,i,s){var n=i===$?j.nextid:i,a=B+"-"+n;n!==S&&n.length>0&&!o("#"+a).length?(this._id=a,this.rendered&&(this.tooltip[0].id=this._id,this.elements.content[0].id=this._id+"-content",this.elements.title[0].id=this._id+"-title")):t[e]=s},"^prerender":function(t,e,i){i&&!this.rendered&&this.render(this.options.show.ready)},"^content.text$":function(t,e,i){this._updateContent(i)},"^content.attr$":function(t,e,i,s){this.options.content.text===this.target.attr(s)&&this._updateContent(this.target.attr(i))},"^content.title$":function(t,e,i){return i?(i&&!this.elements.title&&this._createTitle(),this._updateTitle(i),s):this._removeTitle()},"^content.button$":function(t,e,i){this._updateButton(i)},"^content.title.(text|button)$":function(t,e,i){this.set("content."+e,i)},"^position.(my|at)$":function(t,e,i){"string"==typeof i&&(t[e]=new k(i,"at"===e))},"^position.container$":function(t,e,i){this.tooltip.appendTo(i)},"^show.ready$":function(t,e,i){i&&(!this.rendered&&this.render($)||this.toggle($))},"^style.classes$":function(t,e,i,s){this.tooltip.removeClass(s).addClass(i)},"^style.width|height":function(t,e,i){this.tooltip.css(e,i)},"^style.widget|content.title":function(){this._setWidget()},"^style.def":function(t,e,i){this.tooltip.toggleClass(K,!!i)},"^events.(render|show|move|hide|focus|blur)$":function(t,e,i){tooltip[(o.isFunction(i)?"":"un")+"bind"]("tooltip"+e,i)},"^(show|hide|position).(event|target|fixed|inactive|leave|distance|viewport|adjust)":function(){var t=this.options.position;tooltip.attr("tracking","mouse"===t.target&&t.adjust.mouse),this._unassignEvents(),this._assignEvents()}}},C.get=function(t){if(this.destroyed)return this;var e=l(this.options,t.toLowerCase()),i=e[0][e[1]];return i.precedance?i.string():i};var it=/^position\.(my|at|adjust|target|container|viewport)|style|content|show\.ready/i,st=/^prerender|show\.ready/i;C.set=function(t,e){if(this.destroyed)return this;var i,n=this.rendered,a=S,r=this.options;return this.checks,"string"==typeof t?(i=t,(t={})[i]=e):t=o.extend({},t),o.each(t,function(e,i){if(!n&&!st.test(e))return delete t[e],s;var c,h=l(r,e.toLowerCase());c=h[0][h[1]],h[0][h[1]]=i&&i.nodeType?o(i):i,a=it.test(e)||a,t[e]=[h[0],h[1],i,c]}),c(r),this.positioning=$,o.each(t,o.proxy(h,this)),this.positioning=S,this.rendered&&this.tooltip[0].offsetWidth>0&&a&&this.reposition("mouse"===r.position.target?Q:this.cache.event),this},C._update=function(t,e){var i=this,s=this.cache;return this.rendered&&t?(o.isFunction(t)&&(t=t.call(this.elements.target,s.event,this)||""),o.isFunction(t.then)?(s.waiting=$,t.then(function(t){return s.waiting=S,i._update(t,e)},Q,function(t){return i._update(t,e)})):t===S||!t&&""!==t?S:(t.jquery&&t.length>0?e.children().detach().end().append(t.css({display:"block"})):e.html(t),s.waiting=$,(o.fn.imagesLoaded?e.imagesLoaded():o.Deferred().resolve(o([]))).done(function(t){s.waiting=S,t.length&&i.rendered&&i.tooltip[0].offsetWidth>0&&i.reposition(s.event,!t.length)}).promise())):S},C._updateContent=function(t,e){this._update(t,this.elements.content,e)},C._updateTitle=function(t,e){this._update(t,this.elements.title,e)===S&&this._removeTitle(S)},C._createTitle=function(){var t=this.elements,e=this._id+"-title";t.titlebar&&this._removeTitle(),t.titlebar=o("<div />",{class:B+"-titlebar "+(this.options.style.widget?d("header"):"")}).append(t.title=o("<div />",{id:e,class:B+"-title","aria-atomic":$})).insertBefore(t.content).delegate(".qtip-close","mousedown keydown mouseup keyup mouseout",function(t){o(this).toggleClass("ui-state-active ui-state-focus","down"===t.type.substr(-4))}).delegate(".qtip-close","mouseover mouseout",function(t){o(this).toggleClass("ui-state-hover","mouseover"===t.type)}),this.options.content.button&&this._createButton()},C._removeTitle=function(t){var e=this.elements;e.title&&(e.titlebar.remove(),e.titlebar=e.title=e.button=Q,t!==S&&this.reposition())},C.reposition=function(i,s){if(!this.rendered||this.positioning||this.destroyed)return this;this.positioning=$;var n,a,r=this.cache,c=this.tooltip,l=this.options.position,h=l.target,d=l.my,p=l.at,u=l.viewport,f=l.container,m=l.adjust,_=m.method.split(" "),g=c.outerWidth(S),w=c.outerHeight(S),y=0,v=0,b=c.css("position"),x={left:0,top:0},j=c[0].offsetWidth>0,C=i&&"scroll"===i.type,k=o(t),q=f[0].ownerDocument,T=this.mouse;if(o.isArray(h)&&2===h.length)p={x:F,y:L},x={left:h[0],top:h[1]};else if("mouse"===h&&(i&&i.pageX||r.event.pageX))p={x:F,y:L},i=!T||!T.pageX||!m.mouse&&i&&i.pageX?(!i||"resize"!==i.type&&"scroll"!==i.type?i&&i.pageX&&"mousemove"===i.type?i:(!m.mouse||this.options.show.distance)&&r.origin&&r.origin.pageX?r.origin:i:r.event)||i||r.event||T||{}:T,"static"!==b&&(x=f.offset()),q.body.offsetWidth!==(t.innerWidth||q.documentElement.clientWidth)&&(a=o(q.body).offset()),x={left:i.pageX-x.left+(a&&a.left||0),top:i.pageY-x.top+(a&&a.top||0)},m.mouse&&C&&(x.left-=T.scrollX-k.scrollLeft(),x.top-=T.scrollY-k.scrollTop());else{if("event"===h&&i&&i.target&&"scroll"!==i.type&&"resize"!==i.type?r.target=o(i.target):"event"!==h&&(r.target=o(h.jquery?h:elements.target)),h=r.target,0===(h=o(h).eq(0)).length)return this;h[0]===e||h[0]===t?(y=BROWSER.iOS?t.innerWidth:h.width(),v=BROWSER.iOS?t.innerHeight:h.height(),h[0]===t&&(x={top:(u||h).scrollTop(),left:(u||h).scrollLeft()})):P.imagemap&&h.is("area")?n=P.imagemap(this,h,p,P.viewport?_:S):P.svg&&h[0].ownerSVGElement?n=P.svg(this,h,p,P.viewport?_:S):(y=h.outerWidth(S),v=h.outerHeight(S),x=h.offset()),n&&(y=n.width,v=n.height,a=n.offset,x=n.position),x=this.reposition.offset(h,x,f),(BROWSER.iOS>3.1&&4.1>BROWSER.iOS||BROWSER.iOS>=4.3&&4.33>BROWSER.iOS||!BROWSER.iOS&&"fixed"===b)&&(x.left-=k.scrollLeft(),x.top-=k.scrollTop()),(!n||n&&n.adjustable!==S)&&(x.left+=p.x===E?y:p.x===O?y/2:0,x.top+=p.y===N?v:p.y===O?v/2:0)}return x.left+=m.x+(d.x===E?-g:d.x===O?-g/2:0),x.top+=m.y+(d.y===N?-w:d.y===O?-w/2:0),P.viewport?(x.adjusted=P.viewport(this,x,l,y,v,g,w),a&&x.adjusted.left&&(x.left+=a.left),a&&x.adjusted.top&&(x.top+=a.top)):x.adjusted={left:0,top:0},this._trigger("move",[x,u.elem||u],i)?(delete x.adjusted,s===S||!j||isNaN(x.left)||isNaN(x.top)||"mouse"===h||!o.isFunction(l.effect)?c.css(x):o.isFunction(l.effect)&&(l.effect.call(c,this,o.extend({},x)),c.queue(function(t){o(this).css({opacity:"",height:""}),BROWSER.ie&&this.style.removeAttribute("filter"),t()})),this.positioning=S,this):this},C.reposition.offset=function(t,i,s){function n(t,e){i.left+=e*t.scrollLeft(),i.top+=e*t.scrollTop()}if(!s[0])return i;var a,r,c,l,h=o(t[0].ownerDocument),d=!!BROWSER.ie&&"CSS1Compat"!==e.compatMode,p=s[0];do{"static"!==(r=o.css(p,"position"))&&("fixed"===r?(c=p.getBoundingClientRect(),n(h,-1)):((c=o(p).position()).left+=parseFloat(o.css(p,"borderLeftWidth"))||0,c.top+=parseFloat(o.css(p,"borderTopWidth"))||0),i.left-=c.left+(parseFloat(o.css(p,"marginLeft"))||0),i.top-=c.top+(parseFloat(o.css(p,"marginTop"))||0),a||"hidden"===(l=o.css(p,"overflow"))||"visible"===l||(a=o(p)))}while(p=p.offsetParent);return a&&(a[0]!==h[0]||d)&&n(a,1),i};var ot=(k=C.reposition.Corner=function(t,e){t=(""+t).replace(/([A-Z])/," $1").replace(/middle/gi,O).toLowerCase(),this.x=(t.match(/left|right/i)||t.match(/center/)||["inherit"])[0].toLowerCase(),this.y=(t.match(/top|bottom|center/i)||["inherit"])[0].toLowerCase(),this.forceY=!!e;var i=t.charAt(0);this.precedance="t"===i||"b"===i?W:M}).prototype;ot.invert=function(t,e){this[t]=this[t]===F?E:this[t]===E?F:e||this[t]},ot.string=function(){var t=this.x,e=this.y;return t===e?t:this.precedance===W||this.forceY&&"center"!==e?e+" "+t:t+" "+e},ot.abbrev=function(){var t=this.string().split(" ");return t[0].charAt(0)+(t[1]&&t[1].charAt(0)||"")},ot.clone=function(){return new k(this.string(),this.forceY)},C.toggle=function(t,i){var s=this.cache,n=this.options,a=this.tooltip;if(i){if(/over|enter/.test(i.type)&&/out|leave/.test(s.event.type)&&n.show.target.add(i.target).length===n.show.target.length&&a.has(i.relatedTarget).length)return this;s.event=o.extend({},i)}if(this.waiting&&!t&&(this.hiddenDuringWait=$),!this.rendered)return t?this.render(1):this;if(this.destroyed||this.disabled)return this;var r,c,l=t?"show":"hide",h=this.options[l],d=(this.options[t?"hide":"show"],this.options.position),p=this.options.content,u=this.tooltip.css("width"),f=this.tooltip[0].offsetWidth>0,m=t||1===h.target.length,_=!i||2>h.target.length||s.target[0]===i.target;return(typeof t).search("boolean|number")&&(t=!f),(c=(r=!a.is(":animated")&&f===t&&_)?Q:!!this._trigger(l,[90]))!==S&&t&&this.focus(i),!c||r?this:(o.attr(a[0],"aria-hidden",!t),t?(s.origin=o.extend({},this.mouse),o.isFunction(p.text)&&this._updateContent(p.text,S),o.isFunction(p.title)&&this._updateTitle(p.title,S),!T&&"mouse"===d.target&&d.adjust.mouse&&(o(e).bind("mousemove."+B,this._storeMouse),T=$),u||a.css("width",a.outerWidth(S)),this.reposition(i,arguments[2]),u||a.css("width",""),h.solo&&("string"==typeof h.solo?o(h.solo):o(U,h.solo)).not(a).not(h.target).qtip("hide",o.Event("tooltipsolo"))):(clearTimeout(this.timers.show),delete s.origin,T&&!o(U+'[tracking="true"]:visible',h.solo).not(a).length&&(o(e).unbind("mousemove."+B),T=S),this.blur(i)),after=o.proxy(function(){t?(BROWSER.ie&&a[0].style.removeAttribute("filter"),a.css("overflow",""),"string"==typeof h.autofocus&&o(this.options.show.autofocus,a).focus(),this.options.show.target.trigger("qtip-"+this.id+"-inactive")):a.css({display:"",visibility:"",opacity:"",left:"",top:""}),this._trigger(t?"visible":"hidden")},this),h.effect===S||m===S?(a[l](),after()):o.isFunction(h.effect)?(a.stop(1,1),h.effect.call(a,this),a.queue("fx",function(t){after(),t()})):a.fadeTo(90,t?1:0,after),t&&h.target.trigger("qtip-"+this.id+"-inactive"),this)},C.show=function(t){return this.toggle($,t)},C.hide=function(t){return this.toggle(S,t)},C.focus=function(t){if(!this.rendered||this.destroyed)return this;var e=o(U),i=this.tooltip,s=parseInt(i[0].style.zIndex,10),n=j.zindex+e.length;return i.hasClass(G)||this._trigger("focus",[n],t)&&(s!==n&&(e.each(function(){this.style.zIndex>s&&(this.style.zIndex=this.style.zIndex-1)}),e.filter("."+G).qtip("blur",t)),i.addClass(G)[0].style.zIndex=n),this},C.blur=function(t){return!this.rendered||this.destroyed?this:(this.tooltip.removeClass(G),this._trigger("blur",[this.tooltip.css("zIndex")],t),this)},C.disable=function(t){return this.destroyed?this:("boolean"!=typeof t&&(t=!(this.tooltip.hasClass(Z)||this.disabled)),this.rendered&&this.tooltip.toggleClass(Z,t).attr("aria-disabled",t),this.disabled=!!t,this)},C.enable=function(){return this.disable(S)},C._createButton=function(){var t=this,e=this.elements,i=e.tooltip,s=this.options.content.button,n="string"==typeof s?s:"Close tooltip";e.button&&e.button.remove(),e.button=s.jquery?s:o("<a />",{class:"qtip-close "+(this.options.style.widget?"":B+"-icon"),title:n,"aria-label":n}).prepend(o("<span />",{class:"ui-icon ui-icon-close",html:"&times;"})),e.button.appendTo(e.titlebar||i).attr("role","button").click(function(e){return i.hasClass(Z)||t.hide(e),S})},C._updateButton=function(t){if(!this.rendered)return S;var e=this.elements.button;t?this._createButton():e.remove()},C._setWidget=function(){var t=this.options.style.widget,e=this.elements,i=e.tooltip,s=i.hasClass(Z);i.removeClass(Z),Z=t?"ui-state-disabled":"qtip-disabled",i.toggleClass(Z,s),i.toggleClass("ui-helper-reset "+d(),t).toggleClass(K,this.options.style.def&&!t),e.content&&e.content.toggleClass(d("content"),t),e.titlebar&&e.titlebar.toggleClass(d("header"),t),e.button&&e.button.toggleClass(B+"-icon",!t)},C._storeMouse=function(i){this.mouse={pageX:i.pageX,pageY:i.pageY,type:"mousemove",scrollX:t.pageXOffset||e.body.scrollLeft||e.documentElement.scrollLeft,scrollY:t.pageYOffset||e.body.scrollTop||e.documentElement.scrollTop}},C._bind=function(t,e,i,s,n){var a="."+this._id+(s?"-"+s:"");e.length&&o(t).bind((e.split?e:e.join(a+" "))+a,o.proxy(i,n||this))},C._unbind=function(t,e){o(t).unbind("."+this._id+(e?"-"+e:""))};var nt="."+B;o(function(){_(U,["mouseenter","mouseleave"],function(t){var e="mouseenter"===t.type,i=o(t.currentTarget),s=o(t.relatedTarget||t.target),n=this.options;e?(this.focus(t),i.hasClass(Y)&&!i.hasClass(Z)&&clearTimeout(this.timers.hide)):"mouse"===n.position.target&&n.hide.event&&n.show.target&&!s.closest(n.show.target[0]).length&&this.hide(t),i.toggleClass(J,e)}),_("["+A+"]",X,f)}),C._trigger=function(t,e,i){var s=o.Event("tooltip"+t);return s.originalEvent=i&&o.extend({},i)||this.cache.event||Q,this.triggering=$,this.tooltip.trigger(s,[this].concat(e||[])),this.triggering=S,!s.isDefaultPrevented()},C._assignEvents=function(){var i=this.options,n=i.position,a=this.tooltip,r=i.show.target,c=i.hide.target,l=n.container,h=n.viewport,d=o(e),_=(o(e.body),o(t)),g=i.show.event?o.trim(""+i.show.event).split(" "):[],w=i.hide.event?o.trim(""+i.hide.event).split(" "):[],y=[];/mouse(out|leave)/i.test(i.hide.event)&&"window"===i.hide.leave&&this._bind(d,["mouseout","blur"],function(t){/select|option/.test(t.target.nodeName)||t.relatedTarget||this.hide(t)}),i.hide.fixed?c=c.add(a.addClass(Y)):/mouse(over|enter)/i.test(i.show.event)&&this._bind(c,"mouseleave",function(){clearTimeout(this.timers.show)}),(""+i.hide.event).indexOf("unfocus")>-1&&this._bind(l.closest("html"),["mousedown","touchstart"],function(t){var e=o(t.target),i=this.rendered&&!this.tooltip.hasClass(Z)&&this.tooltip[0].offsetWidth>0,s=e.parents(U).filter(this.tooltip[0]).length>0;e[0]===this.target[0]||e[0]===this.tooltip[0]||s||this.target.has(e[0]).length||!i||this.hide(t)}),"number"==typeof i.hide.inactive&&(this._bind(r,"qtip-"+this.id+"-inactive",f),this._bind(c.add(a),j.inactiveEvents,f,"-inactive")),w=o.map(w,function(t){var e=o.inArray(t,g);return e>-1&&c.add(r).length===c.length?(y.push(g.splice(e,1)[0]),s):t}),this._bind(r,g,p),this._bind(c,w,u),this._bind(r,y,function(t){(this.tooltip[0].offsetWidth>0?u:p).call(this,t)}),this._bind(r.add(a),"mousemove",function(t){if("number"==typeof i.hide.distance){var e=this.cache.origin||{},s=this.options.hide.distance,o=Math.abs;(o(t.pageX-e.pageX)>=s||o(t.pageY-e.pageY)>=s)&&this.hide(t)}this._storeMouse(t)}),"mouse"===n.target&&n.adjust.mouse&&(i.hide.event&&this._bind(r,["mouseenter","mouseleave"],function(t){this.cache.onTarget="mouseenter"===t.type}),this._bind(d,"mousemove",function(t){this.rendered&&this.cache.onTarget&&!this.tooltip.hasClass(Z)&&this.tooltip[0].offsetWidth>0&&this.reposition(t)})),(n.adjust.resize||h.length)&&this._bind(o.event.special.resize?h:_,"resize",m),n.adjust.scroll&&this._bind(_.add(n.container),"scroll",m)},C._unassignEvents=function(){var i=[this.options.show.target[0],this.options.hide.target[0],this.rendered&&this.tooltip[0],this.options.position.container[0],this.options.position.viewport[0],this.options.position.container.closest("html")[0],t,e];this.rendered?this._unbind(o([]).pushStack(o.grep(i,function(t){return"object"==typeof t}))):o(i[0]).unbind("."+this._id+"-create")},(j=o.fn.qtip=function(t,e,i){var n=(""+t).toLowerCase(),a=Q,r=o.makeArray(arguments).slice(1),l=r[r.length-1],h=this[0]?o.data(this[0],B):Q;return!arguments.length&&h||"api"===n?h:"string"==typeof t?(this.each(function(){var t=o.data(this,B);if(!t)return $;if(l&&l.timeStamp&&(t.cache.event=l),!e||"option"!==n&&"options"!==n)t[n]&&t[n].apply(t,r);else{if(i===s&&!o.isPlainObject(e))return a=t.get(e),S;t.set(e,i)}}),a!==Q?a:this):"object"!=typeof t&&arguments.length?s:(h=c(o.extend($,{},t)),j.bind.call(this,h,l))}).bind=function(t,e){return this.each(function(i){function n(t){function e(){h.render("object"==typeof t||a.show.ready),r.show.add(r.hide).unbind(l)}return h.disabled?S:(h.cache.event=o.extend({},t),h.cache.target=t?o(t.target):[s],a.show.delay>0?(clearTimeout(h.timers.show),h.timers.show=setTimeout(e,a.show.delay),c.show!==c.hide&&r.hide.bind(c.hide,function(){clearTimeout(h.timers.show)})):e(),s)}var a,r,c,l,h,d;return d=!(d=o.isArray(t.id)?t.id[i]:t.id)||d===S||1>d.length||j.api[d]?j.nextid++:d,l=".qtip-"+d+"-create",(h=g(o(this),d,t))===S?$:(j.api[d]=h,a=h.options,o.each(P,function(){"initialize"===this.initialize&&this(h)}),r={show:a.show.target,hide:a.hide.target},c={show:o.trim(""+a.show.event).replace(/ /g,l+" ")+l,hide:o.trim(""+a.hide.event).replace(/ /g,l+" ")+l},/mouse(over|enter)/i.test(c.show)&&!/mouse(out|leave)/i.test(c.hide)&&(c.hide+=" mouseleave"+l),r.show.bind("mousemove"+l,function(t){h._storeMouse(t),h.cache.onTarget=$}),r.show.bind(c.show,n),(a.show.ready||a.prerender)&&n(e),s)})},j.api={},o.each({attr:function(t,e){if(this.length){var i=this[0],s="title",n=o.data(i,"qtip");if(t===s&&n&&"object"==typeof n&&n.options.suppress)return 2>arguments.length?o.attr(i,et):(n&&n.options.content.attr===s&&n.cache.attr&&n.set("content.text",e),this.attr(et,e))}return o.fn["attr"+tt].apply(this,arguments)},clone:function(t){var e=(o([]),o.fn["clone"+tt].apply(this,arguments));return t||e.filter("["+et+"]").attr("title",function(){return o.attr(this,et)}).removeAttr(et),e}},function(t,e){if(!e||o.fn[t+tt])return $;var i=o.fn[t+tt]=o.fn[t];o.fn[t]=function(){return e.apply(this,arguments)||i.apply(this,arguments)}}),o.ui||(o["cleanData"+tt]=o.cleanData,o.cleanData=function(t){for(var e,i=0;(e=o(t[i])).length;i++)if(e.attr(H))try{e.triggerHandler("removeqtip")}catch(t){}o["cleanData"+tt].apply(this,arguments)}),j.version="2.1.1",j.nextid=0,j.inactiveEvents=X,j.zindex=15e3,j.defaults={prerender:S,id:S,overwrite:$,suppress:$,content:{text:$,attr:"title",title:S,button:S},position:{my:"top left",at:"bottom right",target:S,container:S,viewport:S,adjust:{x:0,y:0,mouse:$,scroll:$,resize:$,method:"flipinvert flipinvert"},effect:function(t,e){o(this).animate(e,{duration:200,queue:S})}},show:{target:S,event:"mouseenter",effect:$,delay:90,solo:S,ready:S,autofocus:S},hide:{target:S,event:"mouseleave",effect:$,delay:0,fixed:S,inactive:S,leave:"window",distance:S},style:{classes:"",widget:S,width:S,height:S,def:$},events:{render:Q,move:Q,show:Q,hide:Q,toggle:Q,visible:Q,hidden:Q,focus:Q,blur:Q}};var at,rt="margin",ct="border",lt="color",ht="background-color",dt="transparent",pt=" !important",ut=!!e.createElement("canvas").getContext,ft=/rgba?\(0, 0, 0(, 0)?\)|transparent|#123456/i,mt={},_t=["Webkit","O","Moz","ms"];ut||(createVML=function(t,e,i){return"<qtipvml:"+t+' xmlns="urn:schemas-microsoft.com:vml" class="qtip-vml" '+(e||"")+' style="behavior: url(#default#VML); '+(i||"")+'" />'}),o.extend(v.prototype,{init:function(t){var e,i;i=this.element=t.elements.tip=o("<div />",{class:B+"-tip"}).prependTo(t.tooltip),ut?((e=o("<canvas />").appendTo(this.element)[0].getContext("2d")).lineJoin="miter",e.miterLimit=100,e.save()):(e=createVML("shape",'coordorigin="0,0"',"position:absolute;"),this.element.html(e+e),t._bind(o("*",i).add(i),["click","mousedown"],function(t){t.stopPropagation()},this._ns)),t._bind(t.tooltip,"tooltipmove",this.reposition,this._ns,this),this.create()},_swapDimensions:function(){this.size[0]=this.options.height,this.size[1]=this.options.width},_resetDimensions:function(){this.size[0]=this.options.width,this.size[1]=this.options.height},_useTitle:function(t){var e=this.qtip.elements.titlebar;return e&&(t.y===L||t.y===O&&this.element.position().top+this.size[1]/2+this.options.offset<e.outerHeight($))},_parseCorner:function(t){var e=this.qtip.options.position.my;return t===S||e===S?t=S:t===$?t=new k(e.string()):t.string||((t=new k(t)).fixed=$),t},_parseWidth:function(t,e,i){var s=this.qtip.elements,o=ct+w(e)+"Width";return(i?y(i,o):y(s.content,o)||y(this._useTitle(t)&&s.titlebar||s.content,o)||y(tooltip,o))||0},_parseRadius:function(t){var e=this.qtip.elements,i=ct+w(t.y)+w(t.x)+"Radius";return 9>BROWSER.ie?0:y(this._useTitle(t)&&e.titlebar||e.content,i)||y(e.tooltip,i)||0},_invalidColour:function(t,e,i){var s=t.css(e);return!s||i&&s===t.css(i)||ft.test(s)?S:s},_parseColours:function(t){var e=this.qtip.elements,i=this.element.css("cssText",""),s=ct+w(t[t.precedance])+w(lt),n=this._useTitle(t)&&e.titlebar||e.content,a=this._invalidColour,r=[];return r[0]=a(i,ht)||a(n,ht)||a(e.content,ht)||a(tooltip,ht)||i.css(ht),r[1]=a(i,s,lt)||a(n,s,lt)||a(e.content,s,lt)||a(tooltip,s,lt)||tooltip.css(s),o("*",i).add(i).css("cssText",ht+":"+dt+pt+";"+ct+":0"+pt+";"),r},_calculateSize:function(t){var e,i,s=t.precedance===W,o=this.options[s?"height":"width"],n=this.options[s?"width":"height"],a="c"===t.abbrev(),r=o*(a?.5:1),c=Math.pow,l=Math.round,h=Math.sqrt(c(r,2)+c(n,2)),d=[this.border/r*h,this.border/n*h];return d[2]=Math.sqrt(c(d[0],2)-c(this.border,2)),d[3]=Math.sqrt(c(d[1],2)-c(this.border,2)),i=[l((e=(h+d[2]+d[3]+(a?0:d[0]))/h)*o),l(e*n)],s?i:i.reverse()},_calculateTip:function(t){var e=this.size[0],i=this.size[1],s=Math.ceil(e/2),o=Math.ceil(i/2),n={br:[0,0,e,i,e,0],bl:[0,0,e,0,0,i],tr:[0,i,e,0,e,i],tl:[0,0,0,i,e,i],tc:[0,i,s,0,e,i],bc:[0,0,e,0,s,i],rc:[0,0,e,o,0,i],lc:[e,0,e,i,0,o]};return n.lt=n.br,n.rt=n.bl,n.lb=n.tr,n.rb=n.tl,n[t.abbrev()]},create:function(){var t=this.corner=(ut||BROWSER.ie)&&this._parseCorner(this.options.corner);return(this.enabled=!!this.corner&&"c"!==this.corner.abbrev())&&(this.qtip.cache.corner=t.clone(),this.update()),this.element.toggle(this.enabled),this.corner},update:function(t,e){if(!this.enabled)return this;var i,s,n,a,r,c,l,h=(this.qtip.elements,this.element),d=h.children(),p=this.options,u=this.size,f=p.mimic,m=Math.round;t||(t=this.qtip.cache.corner||this.corner),f===S?f=t:((f=new k(f)).precedance=t.precedance,"inherit"===f.x?f.x=t.x:"inherit"===f.y?f.y=t.y:f.x===f.y&&(f[t.precedance]=t[t.precedance])),s=f.precedance,t.precedance===M?this._swapDimensions():this._resetDimensions(),(i=this.color=this._parseColours(t))[1]!==dt?(l=this.border=this._parseWidth(t,t[t.precedance]),p.border&&1>l&&(i[0]=i[1]),this.border=l=p.border!==$?p.border:l):this.border=l=0,a=this._calculateTip(f),c=this.size=this._calculateSize(t),h.css({width:c[0],height:c[1],lineHeight:c[1]+"px"}),r=t.precedance===W?[m(f.x===F?l:f.x===E?c[0]-u[0]-l:(c[0]-u[0])/2),m(f.y===L?c[1]-u[1]:0)]:[m(f.x===F?c[0]-u[0]:0),m(f.y===L?l:f.y===N?c[1]-u[1]-l:(c[1]-u[1])/2)],ut?(d.attr(z,c[0]).attr(D,c[1]),(n=d[0].getContext("2d")).restore(),n.save(),n.clearRect(0,0,3e3,3e3),n.fillStyle=i[0],n.strokeStyle=i[1],n.lineWidth=2*l,n.translate(r[0],r[1]),n.beginPath(),n.moveTo(a[0],a[1]),n.lineTo(a[2],a[3]),n.lineTo(a[4],a[5]),n.closePath(),l&&("border-box"===tooltip.css("background-clip")&&(n.strokeStyle=i[0],n.stroke()),n.strokeStyle=i[1],n.stroke()),n.fill()):(a="m"+a[0]+","+a[1]+" l"+a[2]+","+a[3]+" "+a[4]+","+a[5]+" xe",r[2]=l&&/^(r|b)/i.test(t.string())?8===BROWSER.ie?2:1:0,d.css({coordsize:u[0]+l+" "+(u[1]+l),antialias:""+(f.string().indexOf(O)>-1),left:r[0]-r[2]*Number(s===M),top:r[1]-r[2]*Number(s===W),width:u[0]+l,height:u[1]+l}).each(function(t){var e=o(this);e[e.prop?"prop":"attr"]({coordsize:u[0]+l+" "+(u[1]+l),path:a,fillcolor:i[0],filled:!!t,stroked:!t}).toggle(!(!l&&!t)),!t&&e.html(createVML("stroke",'weight="'+2*l+'px" color="'+i[1]+'" miterlimit="1000" joinstyle="miter"'))})),e!==S&&this.calculate(t)},calculate:function(t){if(!this.enabled)return S;var e,i,s,n=this,a=this.qtip.elements,r=this.element,c=this.options.offset,l=(this.qtip.tooltip.hasClass("ui-widget"),{});return t=t||this.corner,e=t.precedance,i=this._calculateSize(t),s=[t.x,t.y],e===M&&s.reverse(),o.each(s,function(s,o){var r,h,d;o===O?(l[r=e===W?F:L]="50%",l[rt+"-"+r]=-Math.round(i[e===W?0:1]/2)+c):(r=n._parseWidth(t,o,a.tooltip),h=n._parseWidth(t,o,a.content),d=n._parseRadius(t),l[o]=Math.max(-n.border,s?h:c+(d>r?d:-r)))}),l[t[e]]-=i[e===M?0:1],r.css({margin:"",top:"",bottom:"",left:"",right:""}).css(l),l},reposition:function(t,e,i){if(this.enabled){var o,n,a=e.cache,r=this.corner.clone(),c=i.adjusted,l=e.options.position.adjust.method.split(" "),h=l[0],d=l[1]||l[0],p={left:S,top:S,x:0,y:0},u={};this.corner.fixed!==$&&(h===I&&r.precedance===M&&c.left&&r.y!==O?r.precedance=r.precedance===M?W:M:h!==I&&c.left&&(r.x=r.x===O?c.left>0?F:E:r.x===F?E:F),d===I&&r.precedance===W&&c.top&&r.x!==O?r.precedance=r.precedance===W?M:W:d!==I&&c.top&&(r.y=r.y===O?c.top>0?L:N:r.y===L?N:L),r.string()===a.corner.string()||a.cornerTop===c.top&&a.cornerLeft===c.left||this.update(r,S)),(o=this.calculate(r,c)).right!==s&&(o.left=-o.right),o.bottom!==s&&(o.top=-o.bottom),o.user=this.offset,(p.left=h===I&&!!c.left)&&(r.x===O?u[rt+"-left"]=p.x=o[rt+"-left"]-c.left:(n=o.right!==s?[c.left,-o.left]:[-c.left,o.left],(p.x=Math.max(n[0],n[1]))>n[0]&&(i.left-=c.left,p.left=S),u[o.right!==s?E:F]=p.x)),(p.top=d===I&&!!c.top)&&(r.y===O?u[rt+"-top"]=p.y=o[rt+"-top"]-c.top:(n=o.bottom!==s?[c.top,-o.top]:[-c.top,o.top],(p.y=Math.max(n[0],n[1]))>n[0]&&(i.top-=c.top,p.top=S),u[o.bottom!==s?N:L]=p.y)),this.element.css(u).toggle(!(p.x&&p.y||r.x===O&&p.y||r.y===O&&p.x)),i.left-=o.left.charAt?o.user:h!==I||p.top||!p.left&&!p.top?o.left:0,i.top-=o.top.charAt?o.user:d!==I||p.left||!p.left&&!p.top?o.top:0,a.cornerLeft=c.left,a.cornerTop=c.top,a.corner=r.clone()}},destroy:function(){this.qtip._unbind(this.qtip.tooltip,this._ns),this.qtip.elements.tip&&this.qtip.elements.tip.find("*").remove().end().remove()}}),(at=P.tip=function(t){return new v(t,t.options.style.tip)}).initialize="render",at.sanitize=function(t){t.style&&"tip"in t.style&&(opts=t.style.tip,"object"!=typeof opts&&(opts=t.style.tip={corner:opts}),/string|boolean/i.test(typeof opts.corner)||(opts.corner=$))},q.tip={"^position.my|style.tip.(corner|mimic|border)$":function(){this.create(),this.qtip.reposition()},"^style.tip.(height|width)$":function(t){this.size=size=[t.width,t.height],this.update(),this.qtip.reposition()},"^content.title|style.(classes|widget)$":function(){this.update()}},o.extend($,j.defaults,{style:{tip:{corner:$,mimic:S,width:6,height:6,border:$,offset:0}}});var gt,wt,yt="qtip-modal",vt="."+yt;wt=new(wt=function(){function i(t){1>l.length&&t.length?t.not("body").blur():l.first().focus()}function s(t){if(r.is(":visible")){var e=o(t.target),s=n.tooltip,a=e.closest(U);(1>a.length?S:parseInt(a[0].style.zIndex,10)>parseInt(s[0].style.zIndex,10))||e.closest(U)[0]===s[0]||i(e),t.target===l[l.length-1]}}var n,a,r,c=this,l={};o.extend(c,{init:function(){function i(){var t=o(this);r.css({height:t.height(),width:t.width()})}return r=c.elem=o("<div />",{id:"qtip-overlay",html:"<div></div>",mousedown:function(){return S}}).hide(),o(t).bind("resize"+vt,i),i(),o(e.body).bind("focusin"+vt,s),o(e).bind("keydown"+vt,function(t){n&&n.options.show.modal.escape&&27===t.keyCode&&n.hide(t)}),r.bind("click"+vt,function(t){n&&n.options.show.modal.blur&&n.hide(t)}),c},update:function(t){n=t,l=t.options.show.modal.stealfocus!==S?t.tooltip.find("*").filter(function(){return function(t){if(o.expr[":"].focusable)return o.expr[":"].focusable;var e,i,s,n=!isNaN(o.attr(t,"tabindex")),a=t.nodeName&&t.nodeName.toLowerCase();return"area"===a?(i=(e=t.parentNode).name,!(!t.href||!i||"map"!==e.nodeName.toLowerCase())&&!!(s=o("img[usemap=#"+i+"]")[0])&&s.is(":visible")):/input|select|textarea|button|object/.test(a)?!t.disabled:"a"===a&&t.href||n}(this)}):[]},toggle:function(t,s,l){var h=(o(e.body),t.tooltip),d=t.options.show.modal,p=d.effect,u=s?"show":"hide",f=r.is(":visible"),m=o(vt).filter(":visible:not(:animated)").not(h);return c.update(t),s&&d.stealfocus!==S&&i(o(":focus")),r.toggleClass("blurs",d.blur),s&&r.css({left:0,top:0}).appendTo(e.body),r.is(":animated")&&f===s&&a!==S||!s&&m.length?c:(r.stop($,S),o.isFunction(p)?p.call(r,s):p===S?r[u]():r.fadeTo(parseInt(l,10)||90,s?1:0,function(){s||r.hide()}),s||r.queue(function(t){r.css({left:"",top:""}),o(vt).length||r.detach(),t()}),a=s,n.destroyed&&(n=Q),c)}}),c.init()}),o.extend(b.prototype,{init:function(t){var e=t.tooltip;return this.options.on?(t.elements.overlay=wt.elem,e.addClass(yt).css("z-index",P.modal.zindex+o(vt).length),t._bind(e,["tooltipshow","tooltiphide"],function(t,i,s){var n=t.originalEvent;if(t.target===e[0])if(n&&"tooltiphide"===t.type&&/mouse(leave|enter)/.test(n.type)&&o(n.relatedTarget).closest(overlay[0]).length)try{t.preventDefault()}catch(t){}else(!n||n&&!n.solo)&&this.toggle(t,"tooltipshow"===t.type,s)},this._ns,this),t._bind(e,"tooltipfocus",function(t,i){if(!t.isDefaultPrevented()&&t.target===e[0]){var s=o(vt),n=P.modal.zindex+s.length,a=parseInt(e[0].style.zIndex,10);wt.elem[0].style.zIndex=n-1,s.each(function(){this.style.zIndex>a&&(this.style.zIndex-=1)}),s.filter("."+G).qtip("blur",t.originalEvent),e.addClass(G)[0].style.zIndex=n,wt.update(i);try{t.preventDefault()}catch(t){}}},this._ns,this),t._bind(e,"tooltiphide",function(t){t.target===e[0]&&o(vt).filter(":visible").not(e).last().qtip("focus",t)},this._ns,this),s):this},toggle:function(t,e,i){return t&&t.isDefaultPrevented()?this:(wt.toggle(this.qtip,!!e,i),s)},destroy:function(){this.qtip.tooltip.removeClass(yt),this.qtip._unbind(this.qtip.tooltip,this._ns),wt.toggle(this.qtip,S),delete this.qtip.elements.overlay}}),(gt=P.modal=function(t){return new b(t,t.options.show.modal)}).sanitize=function(t){t.show&&("object"!=typeof t.show.modal?t.show.modal={on:!!t.show.modal}:t.show.modal.on===s&&(t.show.modal.on=$))},gt.zindex=j.zindex-200,gt.initialize="render",q.modal={"^show.modal.(on|blur)$":function(){this.destroy(),this.init(),this.qtip.elems.overlay.toggle(this.qtip.tooltip[0].offsetWidth>0)}},o.extend($,j.defaults,{show:{modal:{on:S,effect:$,blur:$,stealfocus:$,escape:$}}}),P.viewport=function(i,s,o,n,a,r,c){function l(t,e,i,o,n,a,r,c,l){var h=s[n],p=m[t],u=_[t],f=i===I,g=-x.offset[n]+b.offset[n]+b["scroll"+n],w=p===n?l:p===a?-l:-l/2,y=u===n?c:u===a?-c:-c/2,v=C&&C.size&&C.size[r]||0,j=C&&C.corner&&C.corner.precedance===t&&!f?v:0,k=g-h+j,q=h+l-b[r]-g+j,T=w-(m.precedance===t||p===m[e]?y:0)-(u===O?c/2:0);return f?(T=(p===n?1:-1)*w-(j=C&&C.corner&&C.corner.precedance===e?v:0),s[n]+=k>0?k:q>0?-q:0,s[n]=Math.max(-x.offset[n]+b.offset[n]+(j&&C.corner[t]===O?C.offset:0),h-T,Math.min(Math.max(-x.offset[n]+b.offset[n]+b[r],h+T),s[n]))):(o*=i===R?2:0,k>0&&(p!==n||q>0)?(s[n]-=T+o,d.invert(t,n)):q>0&&(p!==a||k>0)&&(s[n]-=(p===O?-T:T)+o,d.invert(t,a)),g>s[n]&&-s[n]>q&&(s[n]=h,d=m.clone())),s[n]-h}var h,d,p,u=o.target,f=i.elements.tooltip,m=o.my,_=o.at,g=o.adjust,w=g.method.split(" "),y=w[0],v=w[1]||w[0],b=o.viewport,x=o.container,j=i.cache,C=i.plugins.tip,k={left:0,top:0};return b.jquery&&u[0]!==t&&u[0]!==e.body&&"none"!==g.method?(h="fixed"===f.css("position"),b={elem:b,width:b[0]===t?b.width():b.outerWidth(S),height:b[0]===t?b.height():b.outerHeight(S),scrollleft:h?0:b.scrollLeft(),scrolltop:h?0:b.scrollTop(),offset:b.offset()||{left:0,top:0}},x={elem:x,scrollLeft:x.scrollLeft(),scrollTop:x.scrollTop(),offset:x.offset()||{left:0,top:0}},("shift"!==y||"shift"!==v)&&(d=m.clone()),k={left:"none"!==y?l(M,W,y,g.x,F,E,z,n,r):0,top:"none"!==v?l(W,M,v,g.y,L,N,D,a,c):0},d&&j.lastClass!==(p=B+"-pos-"+d.abbrev())&&f.removeClass(i.cache.lastClass).addClass(i.cache.lastClass=p),k):k},P.polys={polygon:function(t,e){var i,s,o,n={width:0,height:0,position:{top:1e10,right:0,bottom:0,left:1e10},adjustable:S},a=0,r=[],c=1,l=1,h=0,d=0;for(a=t.length;a--;)(i=[parseInt(t[--a],10),parseInt(t[a+1],10)])[0]>n.position.right&&(n.position.right=i[0]),i[0]<n.position.left&&(n.position.left=i[0]),i[1]>n.position.bottom&&(n.position.bottom=i[1]),i[1]<n.position.top&&(n.position.top=i[1]),r.push(i);if(s=n.width=Math.abs(n.position.right-n.position.left),o=n.height=Math.abs(n.position.bottom-n.position.top),"c"===e.abbrev())n.position={left:n.position.left+n.width/2,top:n.position.top+n.height/2};else{for(;s>0&&o>0&&c>0&&l>0;)for(s=Math.floor(s/2),o=Math.floor(o/2),e.x===F?c=s:e.x===E?c=n.width-s:c+=Math.floor(s/2),e.y===L?l=o:e.y===N?l=n.height-o:l+=Math.floor(o/2),a=r.length;a--&&!(2>r.length);)h=r[a][0]-n.position.left,d=r[a][1]-n.position.top,(e.x===F&&h>=c||e.x===E&&c>=h||e.x===O&&(c>h||h>n.width-c)||e.y===L&&d>=l||e.y===N&&l>=d||e.y===O&&(l>d||d>n.height-l))&&r.splice(a,1);n.position={left:r[0][0],top:r[0][1]}}return n},rect:function(t,e,i,s){return{width:Math.abs(i-t),height:Math.abs(s-e),position:{left:Math.min(t,i),top:Math.min(e,s)}}},_angles:{tc:1.5,tr:7/4,tl:5/4,bc:.5,br:.25,bl:.75,rc:2,lc:1,c:0},ellipse:function(t,e,i,s,o){var n=P.polys._angles[o.abbrev()],a=i*Math.cos(n*Math.PI),r=s*Math.sin(n*Math.PI);return{width:2*i-Math.abs(a),height:2*s-Math.abs(r),position:{left:t+a,top:e+r},adjustable:S}},circle:function(t,e,i,s){return P.polys.ellipse(t,e,i,i,s)}},P.svg=function(t,s,n){for(var a,r,c,l=o(e),h=s[0],d={};!h.getBBox;)h=h.parentNode;if(!h.getBBox||!h.parentNode)return S;switch(h.nodeName){case"rect":r=P.svg.toPixel(h,h.x.baseVal.value,h.y.baseVal.value),c=P.svg.toPixel(h,h.x.baseVal.value+h.width.baseVal.value,h.y.baseVal.value+h.height.baseVal.value),d=P.polys.rect(r[0],r[1],c[0],c[1],n);break;case"ellipse":case"circle":r=P.svg.toPixel(h,h.cx.baseVal.value,h.cy.baseVal.value),d=P.polys.ellipse(r[0],r[1],(h.rx||h.r).baseVal.value,(h.ry||h.r).baseVal.value,n);break;case"line":case"polygon":case"polyline":for(points=h.points||[{x:h.x1.baseVal.value,y:h.y1.baseVal.value},{x:h.x2.baseVal.value,y:h.y2.baseVal.value}],d=[],i=-1,len=points.numberOfItems||points.length;len>++i;)next=points.getItem?points.getItem(i):points[i],d.push.apply(d,P.svg.toPixel(h,next.x,next.y));d=P.polys.polygon(d,n);break;default:if(a=h.getBBox(),mtx=h.getScreenCTM(),root=h.farthestViewportElement||h,!root.createSVGPoint)return S;point=root.createSVGPoint(),point.x=a.x,point.y=a.y,tPoint=point.matrixTransform(mtx),d.position={left:tPoint.x,top:tPoint.y},point.x+=a.width,point.y+=a.height,tPoint=point.matrixTransform(mtx),d.width=tPoint.x-d.position.left,d.height=tPoint.y-d.position.top}return d.position.left+=l.scrollLeft(),d.position.top+=l.scrollTop(),d},P.svg.toPixel=function(t,e,i){var s,o,n=t.getScreenCTM(),a=t.farthestViewportElement||t;return a.createSVGPoint?((o=a.createSVGPoint()).x=e,o.y=i,[(s=o.matrixTransform(n)).x,s.y]):S},P.imagemap=function(t,e,i){e.jquery||(e=o(e));var s,n,a,r=e.attr("shape").toLowerCase().replace("poly","polygon"),c=o('img[usemap="#'+e.parent("map").attr("name")+'"]'),l=e.attr("coords").split(",");if(!c.length)return S;if("polygon"===r)result=P.polys.polygon(l,i);else{if(!P.polys[r])return S;for(a=-1,len=l.length,n=[];len>++a;)n.push(parseInt(l[a],10));result=P.polys[r].apply(this,n.concat(i))}return(s=c.offset()).left+=Math.ceil((c.outerWidth(S)-c.width())/2),s.top+=Math.ceil((c.outerHeight(S)-c.height())/2),result.position.left+=s.left,result.position.top+=s.top,result};o.extend(x.prototype,{_scroll:function(){var e=this.qtip.elements.overlay;e&&(e[0].style.top=o(t).scrollTop()+"px")},init:function(i){var s=i.tooltip;1>o("select, object").length&&(this.bgiframe=i.elements.bgiframe=o('<iframe class="qtip-bgiframe" frameborder="0" tabindex="-1" src="javascript:\'\';"  style="display:block; position:absolute; z-index:-1; filter:alpha(opacity=0); -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";"></iframe>').appendTo(s),i._bind(s,"tooltipmove",this.adjustBGIFrame,this._ns,this)),this.redrawContainer=o("<div/>",{id:B+"-rcontainer"}).appendTo(e.body),i.elements.overlay&&i.elements.overlay.addClass("qtipmodal-ie6fix")&&(i._bind(t,["scroll","resize"],this._scroll,this._ns,this),i._bind(s,["tooltipshow"],this._scroll,this._ns,this)),this.redraw()},adjustBGIFrame:function(){var t,e,i=this.qtip.tooltip,s={height:i.outerHeight(S),width:i.outerWidth(S)},o=this.qtip.plugins.tip,n=this.qtip.elements.tip;e={left:-(e=parseInt(i.css("borderLeftWidth"),10)||0),top:-e},o&&n&&(e[(t="x"===o.corner.precedance?[z,F]:[D,L])[1]]-=n[t[0]]()),this.bgiframe.css(e).css(s)},redraw:function(){if(1>this.qtip.rendered||this.drawing)return self;var t,e,i,s,o=this.qtip.tooltip,n=this.qtip.options.style,a=this.qtip.options.position.container;return this.qtip.drawing=1,n.height&&o.css(D,n.height),n.width?o.css(z,n.width):(o.css(z,"").appendTo(this.redrawContainer),1>(e=o.width())%2&&(e+=1),t=((i=o.css("maxWidth")||"")+(s=o.css("minWidth")||"")).indexOf("%")>-1?a.width()/100:0,e=(i=(i.indexOf("%")>-1?t:1)*parseInt(i,10)||e)+(s=(s.indexOf("%")>-1?t:1)*parseInt(s,10)||0)?Math.min(Math.max(e,s),i):e,o.css(z,Math.round(e)).appendTo(a)),this.drawing=0,self},destroy:function(){this.bgiframe&&this.bgiframe.remove(),this.qtip._unbind([t,this.qtip.tooltip],this._ns)}}),(P.ie6=function(t){return 6===BROWSER.ie?new x(t):S}).initialize="render",q.ie6={"^content|style$":function(){this.redraw()}}})}(window,document),jQuery(document).ready(function(t){initiateTip(),setTimeout(function(){t(".wcfm_popup_wrapper").find("select").each(function(){t(this).parent().is("span")&&t(this).css("padding","5px").css("min-width","15px").css("min-height","35px").css("padding-top","5px").css("padding-right","5px")}),function e(){t(".wcfm_popup_wrapper").find("select").each(function(){t(this).parent().is("span")&&t(this).unwrap("span"),(t(this).parent().hasClass("select-option")||t(this).parent().hasClass("buddyboss-select-inner")||t(this).parent().hasClass("buddyboss-select"))&&(t(this).parent().find(".ti-angle-down").remove(),t(this).parent().find("span").remove(),t(this).unwrap("div"))}),setTimeout(function(){e()},500)}()},500),t(".wcfm_delete_product").each(function(){t(this).click(function(e){return e.preventDefault(),confirm(wcfm_core_dashboard_messages.product_delete_confirm)&&function(t){jQuery(".products").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var e={action:"delete_wcfm_product",proid:t.data("proid"),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:e,success:function(t){window.location=wcfm_params.shop_url}})}(t(this)),!1})}),t(document.body).on("wcfm_form_validate",function(e,i){$proccessed_required_fileds=[],i&&($form=t(i),$form.find('[data-required="1"]').each(function(){$data_name=t(this).attr("name"),-1===t.inArray($data_name,$proccessed_required_fileds)&&($proccessed_required_fileds.push($data_name),t(this).parents(".wcfm-container").hasClass("wcfm_block_hide")||t(this).parents(".wcfm-container").hasClass("wcfm_custom_hide")||t(this).parents(".wcfm-container").hasClass("wcfm_wpml_hide")||t(this).parent().parent().hasClass("wcfm_block_hide")||t(this).parent().parent().hasClass("wcfm_custom_hide")||t(this).parent().parent().hasClass("wcfm_wpml_hide")||t(this).parent().hasClass("wcfm_block_hide")||t(this).parent().hasClass("wcfm_custom_hide")||t(this).parent().hasClass("wcfm_wpml_hide")||t(this).hasClass("wcfm_ele_hide")||t(this).hasClass("wcfm_custom_hide")||t(this).hasClass("wcfm_wpml_hide")||(t(this).is('input[type="checkbox"]')||t(this).is('input[type="radio"]')?(t('[name="'+$data_name+'"]').removeClass("wcfm_validation_failed").addClass("wcfm_validation_success"),t('[name="'+$data_name+'"]').is(":checked")||($wcfm_is_valid_form?t("#"+$form.attr("id")+" .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+t(this).data("required_message")).addClass("wcfm-error").slideDown():t("#"+$form.attr("id")+" .wcfm-message").append('<br /><span class="wcicon-status-cancelled"></span>'+t(this).data("required_message")),$wcfm_is_valid_form=!1,t('[name="'+$data_name+'"]').removeClass("wcfm_validation_success").addClass("wcfm_validation_failed"))):(t(this).removeClass("wcfm_validation_failed").addClass("wcfm_validation_success"),$data_val=t(this).val(),$data_val||($wcfm_is_valid_form?t("#"+$form.attr("id")+" .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+t(this).data("required_message")).addClass("wcfm-error").slideDown():t("#"+$form.attr("id")+" .wcfm-message").append('<br /><span class="wcicon-status-cancelled"></span>'+t(this).data("required_message")),$wcfm_is_valid_form=!1,t(this).removeClass("wcfm_validation_success").addClass("wcfm_validation_failed")))))}),$form.find(".wcfm_wpeditor_required").each(function(){$data_id=t(this).attr("id"),$data_name=t(this).attr("name"),$form=t(this).parents("form"),-1===t.inArray($data_name,$proccessed_required_fileds)&&($proccessed_required_fileds.push($data_name),t(this).parents(".wcfm-container").hasClass("wcfm_block_hide")||t(this).parents(".wcfm-container").hasClass("wcfm_custom_hide")||t(this).parent().parent().hasClass("wcfm_block_hide")||t(this).parent().parent().hasClass("wcfm_custom_hide")||t(this).parent().hasClass("wcfm_block_hide")||t(this).parent().hasClass("wcfm_custom_hide")||t(this).hasClass("wcfm_ele_hide")||t(this).hasClass("wcfm_custom_hide")||($data_val=wcfmstripHtml(getWCFMEditorContent($data_id)),1==$data_val.length&&($wcfm_is_valid_form?t("#"+$form.attr("id")+" .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfmcapitalizeFirstLetter($data_name)+": "+wcfm_core_dashboard_messages.required_message).addClass("wcfm-error").slideDown():t("#"+$form.attr("id")+" .wcfm-message").append('<br /><span class="wcicon-status-cancelled"></span>'+wcfmcapitalizeFirstLetter($data_name)+": "+wcfm_core_dashboard_messages.required_message),$wcfm_is_valid_form=!1)))}),$wcfm_is_valid_form||wcfmMessageHide())});var e="";wcfm_params.wcfm_is_allow_wcfm&&wcfm_params.wcfm_is_allow_new_message_check&&function s(){clearTimeout(e),e=setTimeout(function(){var e={action:"wcfm_message_count",wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:e,success:function(e){$response_json=t.parseJSON(e),$response_json.status?($response_json.notice&&t(".notice_count").text($response_json.notice),$response_json.message&&(t(".message_count").text($response_json.message),wcfm_params.wcfm_is_desktop_notification&&(!wcfm_params.is_mobile||wcfm_params.is_mobile&&wcfm_params.is_mobile_desktop_notification)&&function(e){e=parseInt(e);var s=parseInt(wcfm_params.unread_message);if(e>s){clearTimeout(i),t(".wcfm_notification_wrapper").slideDown(function(){t(".wcfm_notification_wrapper").remove()});var o={action:"wcfm_message_notification",limit:e-s,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:o,success:function(s){s&&(wcfm_params.unread_message=e,t("body").append(s),wcfm_desktop_notification_sound.play(),i=setTimeout(function(){t(".wcfm_notification_wrapper").slideDown(function(){t(".wcfm_notification_wrapper").remove()})},3e4),t(".wcfm_notification_close").click(function(){clearTimeout(i),t(".wcfm_notification_wrapper").slideDown(function(){t(".wcfm_notification_wrapper").remove()})}))}})}}($response_json.message)),$response_json.enquiry&&t(".enquiry_count").text($response_json.enquiry)):$response_json.redirect&&(window.location=$response_json.redirect)}}),s()},wcfm_params.wcfm_new_message_check_duration)}();var i="";t(".wcfm_tutorials").colorbox({iframe:!0,width:$large_popup_width,innerHeight:390}),t(".wcfm_linked_images").colorbox({iframe:!0,width:$popup_width,innerHeight:390}),t(".wcfm_linked_attached").colorbox({iframe:!0,width:$large_popup_width,innerHeight:390}),wcfm_params.wcfm_is_allow_external_product_analytics&&t(".product_type_external").each(function(){t(this).click(function(e){e.preventDefault(),$product_id=t(this).data("product_id"),$href=t(this).attr("href"),$is_blank=t(this).attr("target");var i={action:"wcfm_store_external_product_view_update",product_id:$product_id,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:i,success:function(t){"undefined"!=typeof $is_blank&&!1!==$is_blank?window.open($href,"_blank"):window.location=$href}}).always(function(t){})})}),setTimeout(function(){t(".wcfm_datepicker").each(function(){t(this).hasClass("hasDatepicker")||($dateFormat=t(this).data("date_format"),$dateFormat||($dateFormat=wcfm_datepicker_params.dateFormat),t(this).datepicker({dateFormat:$dateFormat,closeText:wcfm_datepicker_params.closeText,currentText:wcfm_datepicker_params.currentText,monthNames:wcfm_datepicker_params.monthNames,monthNamesShort:wcfm_datepicker_params.monthNamesShort,dayNames:wcfm_datepicker_params.dayNames,dayNamesShort:wcfm_datepicker_params.dayNamesShort,dayNamesMin:wcfm_datepicker_params.dayNamesMin,firstDay:wcfm_datepicker_params.firstDay,isRTL:wcfm_datepicker_params.isRTL,changeMonth:!0,changeYear:!0,yearRange:"1920:2030"}))}),t(".wcfm_datetimepicker").each(function(){$dateFormat=t(this).data("date_format"),$dateFormat||($dateFormat=wcfm_datepicker_params.dateFormat),t(this).datetimepicker({dateFormat:$dateFormat,closeText:wcfm_datepicker_params.closeText,currentText:wcfm_datepicker_params.currentText,monthNames:wcfm_datepicker_params.monthNames,monthNamesShort:wcfm_datepicker_params.monthNamesShort,dayNames:wcfm_datepicker_params.dayNames,dayNamesShort:wcfm_datepicker_params.dayNamesShort,dayNamesMin:wcfm_datepicker_params.dayNamesMin,firstDay:wcfm_datepicker_params.firstDay,isRTL:wcfm_datepicker_params.isRTL,timeFormat:"h:mm tt",changeMonth:!0,changeYear:!0,yearRange:"1920:2030"})}),t(".wcfm_timepicker").each(function(){t(this).timepicker({timeFormat:"h:mm tt"})})},500)}),$wcfm_enquiry_submited=!1,jQuery(document).ready(function(t){function e(){var e={action:"wcfm_enquiry_form_content",store:0,product:0,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:e,success:function(e){t("body").append(e),t("#enquiry_form").find(".wcfm_datepicker").each(function(){t(this).datepicker({closeText:wcfm_datepicker_params.closeText,currentText:wcfm_datepicker_params.currentText,monthNames:wcfm_datepicker_params.monthNames,monthNamesShort:wcfm_datepicker_params.monthNamesShort,dayNames:wcfm_datepicker_params.dayNames,dayNamesShort:wcfm_datepicker_params.dayNamesShort,dayNamesMin:wcfm_datepicker_params.dayNamesMin,firstDay:wcfm_datepicker_params.firstDay,isRTL:wcfm_datepicker_params.isRTL,dateFormat:wcfm_datepicker_params.dateFormat,changeMonth:!0,changeYear:!0})}),initiateTip(),t("#wcfm_enquiry_submit_button").off("click").on("click",function(e){e.preventDefault(),function(e){if($is_valid=function(e){if($is_valid=!0,jQuery(".wcfm-message").html("").removeClass("wcfm-success").removeClass("wcfm-error").slideUp(),0==jQuery.trim(e.find("#enquiry_comment").val()).length&&($is_valid=!1,e.find(".wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfm_enquiry_manage_messages.no_enquiry).addClass("wcfm-error").slideDown()),e.find("#enquiry_author").length>0){var i=jQuery.trim(e.find("#enquiry_author").val());0==i.length&&($is_valid?e.find(".wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfm_enquiry_manage_messages.no_name).addClass("wcfm-error").slideDown():e.find(".wcfm-message").append('<br /><span class="wcicon-status-cancelled"></span>'+wcfm_enquiry_manage_messages.no_name).addClass("wcfm-error").slideDown(),$is_valid=!1)}if(e.find("#enquiry_email").length>0){var s=jQuery.trim(e.find("#enquiry_email").val());0==s.length&&($is_valid?e.find(".wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfm_enquiry_manage_messages.no_email).addClass("wcfm-error").slideDown():e.find(".wcfm-message").append('<br /><span class="wcicon-status-cancelled"></span>'+wcfm_enquiry_manage_messages.no_email).addClass("wcfm-error").slideDown(),$is_valid=!1)}return $wcfm_is_valid_form=$is_valid,t(document.body).trigger("wcfm_form_validate",e),$is_valid=$wcfm_is_valid_form,$is_valid}(e),$is_valid){t("#enquiry_form_wrapper").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var i={action:"wcfm_ajax_controller",controller:"wcfm-enquiry-tab",wcfm_enquiry_tab_form:e.serialize(),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce,status:"submit"};jQuery.post(wcfm_params.ajax_url,i,function(i){i&&($response_json=jQuery.parseJSON(i),e.find(".wcfm-message").html("").removeClass("wcfm-success").removeClass("wcfm-error").slideUp(),wcfm_notification_sound.play(),$response_json.status?(e.find(".wcfm-message").html('<span class="wcicon-status-completed"></span>'+$response_json.message).addClass("wcfm-success").slideDown("slow"),setTimeout(function(){t.colorbox.remove(),e.find("#enquiry_comment").val(""),jQuery(".wcfm-message").html("").removeClass("wcfm-success").removeClass("wcfm-error").slideUp()},2e3)):e.find(".wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+$response_json.message).addClass("wcfm-error").slideDown(),jQuery(".wcfm_gglcptch_wrapper").length>0&&"undefined"!=typeof grecaptcha&&grecaptcha.reset(),t("#enquiry_form_wrapper").unblock())})}}(t("#wcfm_enquiry_form"))}),$inquiryFormLoaded=!0}})}t(".enquiry-form").length>1&&t(".enquiry-form")[1].remove(),$inquiryFormLoaded=!1,t(".add_enquiry").length>0&&e(),t(".wcfm_catalog_enquiry").length>0&&($inquiryFormLoaded||e()),t(".wcfm_store_enquiry").length>0&&($inquiryFormLoaded||e()),$wcfm_anr_loaded=!1,t(".add_enquiry, .wcfm_catalog_enquiry, .wcfm_store_enquiry").each(function(){t(this).hasClass("wcfm_login_popup")||t(this).click(function(e){if(e.preventDefault(),!$inquiryFormLoaded)return!1;$store=t(this).data("store"),$product=t(this).data("product"),t.colorbox({inline:!0,href:"#enquiry_form_wrapper",width:$popup_width,onComplete:function(){t("#wcfm_enquiry_form").find("#enquiry_vendor_id").val($store),t("#wcfm_enquiry_form").find("#enquiry_product_id").val($product),jQuery(".anr_captcha_field").length>0&&"undefined"!=typeof grecaptcha&&($wcfm_anr_loaded?grecaptcha.reset():wcfm_anr_onloadCallback(),$wcfm_anr_loaded=!0)}})})})}),jQuery(document).ready(function(t){t(".wcfm_membership_subscribe_button").click(function(e){e.preventDefault(),t("#wcfm_membership_container").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var i={action:"wcfm_choose_membership",membership:t(this).data("membership"),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};t.post(wcfm_params.ajax_url,i,function(e){e&&($response_json=t.parseJSON(e),$response_json.status&&$response_json.redirect&&(window.location=$response_json.redirect),t("#wcfm_membership_container").unblock())})})}),jQuery(document).ready(function(t){intiateWCFMuQuickEdit(),intiateWCFMuScreenManager(),t(".wcfm_mark_as_recived").click(function(e){e.preventDefault(),t(this).hide(),t(".woocommerce-order-details").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var i={action:"wcfm_mark_as_recived",orderid:t(this).data("orderid"),productid:t(this).data("productid"),orderitemid:t(this).data("orderitemid"),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};t.ajax({type:"POST",url:wcfm_params.ajax_url,data:i,success:function(e){window.location=window.location.href,t(".woocommerce-order-details").unblock()}})})}),function(t,e,i){var s,o,n,a,r,c,l,h,d,p,u,f,m,_,g,w,y,v,b,x,j,C,k,q,T,$,S,Q,M,W,z,D,L,F={html:!1,photo:!1,iframe:!1,inline:!1,transition:"elastic",speed:300,fadeOut:300,width:!1,initialWidth:"600",innerWidth:!1,maxWidth:!1,height:!1,initialHeight:"450",innerHeight:!1,maxHeight:!1,scalePhotos:!0,scrolling:!0,opacity:.9,preloading:!0,className:!1,overlayClose:!0,escKey:!0,arrowKey:!0,top:!1,bottom:!1,left:!1,right:!1,fixed:!1,data:void 0,closeButton:!0,fastIframe:!0,open:!1,reposition:!0,loop:!0,slideshow:!1,slideshowAuto:!0,slideshowSpeed:2500,slideshowStart:"start slideshow",slideshowStop:"stop slideshow",photoRegex:/\.(gif|png|jp(e|g|eg)|bmp|ico|webp|jxr|svg)((#|\?).*)?$/i,retinaImage:!1,retinaUrl:!1,retinaSuffix:"@2x.$1",current:"image {current} of {total}",previous:"previous",next:"next",close:"close",xhrError:"This content failed to load.",imgError:"This image failed to load.",returnFocus:!0,trapFocus:!0,onOpen:!1,onLoad:!1,onComplete:!1,onCleanup:!1,onClosed:!1,rel:function(){return this.rel},href:function(){return t(this).attr("href")},title:function(){return this.title},createImg:function(){var e=new Image,i=t(this).data("cbox-img-attrs");return"object"==typeof i&&t.each(i,function(t,i){e[t]=i}),e},createIframe:function(){var i=e.createElement("iframe"),s=t(this).data("cbox-iframe-attrs");return"object"==typeof s&&t.each(s,function(t,e){i[t]=e}),"frameBorder"in i&&(i.frameBorder=0),"allowTransparency"in i&&(i.allowTransparency="true"),i.name=(new Date).getTime(),i.allowFullscreen=!0,i}},N="colorbox",E="cbox",O=E+"Element",R=E+"_open",I=E+"_load",P=E+"_complete",B=E+"_cleanup",H=E+"_closed",A=E+"_purge",V=t("<a/>"),U="div",X=0,Y={};function K(i,s,o){var n=e.createElement(i);return s&&(n.id=E+s),o&&(n.style.cssText=o),t(n)}function G(){return i.innerHeight?i.innerHeight:t(i).height()}function J(e,i){i!==Object(i)&&(i={}),this.cache={},this.el=e,this.value=function(e){var s;return void 0===this.cache[e]&&(void 0!==(s=t(this.el).attr("data-cbox-"+e))?this.cache[e]=s:void 0!==i[e]?this.cache[e]=i[e]:void 0!==F[e]&&(this.cache[e]=F[e])),this.cache[e]},this.get=function(e){var i=this.value(e);return t.isFunction(i)?i.call(this.el,this):i}}function Z(t){var e=d.length,i=($+t)%e;return i<0?e+i:i}function tt(t,e){return Math.round((/%/.test(t)?("x"===e?p.width():G())/100:1)*parseInt(t,10))}function et(t,e){return t.get("photo")||t.get("photoRegex").test(e)}function it(t,e){return t.get("retinaUrl")&&i.devicePixelRatio>1?e.replace(t.get("photoRegex"),t.get("retinaSuffix")):e}function st(t){"contains"in o[0]&&!o[0].contains(t.target)&&t.target!==s[0]&&(t.stopPropagation(),o.focus())}function ot(t){ot.str!==t&&(o.add(s).removeClass(ot.str).addClass(t),ot.str=t)}function nt(i){t(e).trigger(i),V.triggerHandler(i)}var at=function(){var t,e,i=E+"Slideshow_",s="click."+E;function n(){clearTimeout(e)}function a(){(j.get("loop")||d[$+1])&&(n(),e=setTimeout(D.next,j.get("slideshowSpeed")))}function r(){w.html(j.get("slideshowStop")).unbind(s).one(s,c),V.bind(P,a).bind(I,n),o.removeClass(i+"off").addClass(i+"on")}function c(){n(),V.unbind(P,a).unbind(I,n),w.html(j.get("slideshowStart")).unbind(s).one(s,function(){D.next(),r()}),o.removeClass(i+"on").addClass(i+"off")}function l(){t=!1,w.hide(),n(),V.unbind(P,a).unbind(I,n),o.removeClass(i+"off "+i+"on")}return function(){t?j.get("slideshow")||(V.unbind(B,l),l()):j.get("slideshow")&&d[1]&&(t=!0,V.one(B,l),j.get("slideshowAuto")?r():c(),w.show())}}();function rt(n){var p,g;if(!W){if(p=t(n).data(N),j=new J(n,p),g=j.get("rel"),$=0,g&&!1!==g&&"nofollow"!==g?(d=t("."+O).filter(function(){return new J(this,t.data(this,N)).get("rel")===g}),-1===($=d.index(j.el))&&(d=d.add(j.el),$=d.length-1)):d=t(j.el),!Q){Q=M=!0,ot(j.get("className")),o.css({visibility:"hidden",display:"block",opacity:""}),u=K(U,"LoadedContent","width:0; height:0; overflow:hidden; visibility:hidden"),a.css({width:"",height:""}).append(u),C=r.height()+h.height()+a.outerHeight(!0)-a.height(),k=c.width()+l.width()+a.outerWidth(!0)-a.width(),q=u.outerHeight(!0),T=u.outerWidth(!0);var w=tt(j.get("initialWidth"),"x"),y=tt(j.get("initialHeight"),"y"),v=j.get("maxWidth"),L=j.get("maxHeight");j.w=Math.max((!1!==v?Math.min(w,tt(v,"x")):w)-T-k,0),j.h=Math.max((!1!==L?Math.min(y,tt(L,"y")):y)-q-C,0),u.css({width:"",height:j.h}),D.position(),nt(R),j.get("onOpen"),x.add(_).hide(),o.focus(),j.get("trapFocus")&&e.addEventListener&&(e.addEventListener("focus",st,!0),V.one(H,function(){e.removeEventListener("focus",st,!0)})),j.get("returnFocus")&&V.one(H,function(){t(j.el).focus()})}var F=parseFloat(j.get("opacity"));s.css({opacity:F==F?F:"",cursor:j.get("overlayClose")?"pointer":"",visibility:"visible"}).show(),j.get("closeButton")?b.html(j.get("close")).appendTo(a):b.appendTo("<div/>"),function(){var e,s,o,n=D.prep,a=++X;M=!0,S=!1,nt(A),nt(I),j.get("onLoad"),j.h=j.get("height")?tt(j.get("height"),"y")-q-C:j.get("innerHeight")&&tt(j.get("innerHeight"),"y"),j.w=j.get("width")?tt(j.get("width"),"x")-T-k:j.get("innerWidth")&&tt(j.get("innerWidth"),"x"),j.mw=j.w,j.mh=j.h,j.get("maxWidth")&&(j.mw=tt(j.get("maxWidth"),"x")-T-k,j.mw=j.w&&j.w<j.mw?j.w:j.mw);j.get("maxHeight")&&(j.mh=tt(j.get("maxHeight"),"y")-q-C,j.mh=j.h&&j.h<j.mh?j.h:j.mh);if(e=j.get("href"),z=setTimeout(function(){m.show()},100),j.get("inline")){var r=t(e).eq(0);o=t("<div>").hide().insertBefore(r),V.one(A,function(){o.replaceWith(r)}),n(r)}else j.get("iframe")?n(" "):j.get("html")?n(j.get("html")):et(j,e)?(e=it(j,e),S=j.get("createImg"),t(S).addClass(E+"Photo").bind("error."+E,function(){n(K(U,"Error").html(j.get("imgError")))}).one("load",function(){a===X&&setTimeout(function(){var e;j.get("retinaImage")&&i.devicePixelRatio>1&&(S.height=S.height/i.devicePixelRatio,S.width=S.width/i.devicePixelRatio),j.get("scalePhotos")&&(s=function(){S.height-=S.height*e,S.width-=S.width*e},j.mw&&S.width>j.mw&&(e=(S.width-j.mw)/S.width,s()),j.mh&&S.height>j.mh&&(e=(S.height-j.mh)/S.height,s())),j.h&&(S.style.marginTop=Math.max(j.mh-S.height,0)/2+"px"),d[1]&&(j.get("loop")||d[$+1])&&(S.style.cursor="pointer",t(S).bind("click."+E,function(){D.next()})),S.style.width=S.width+"px",S.style.height=S.height+"px",n(S)},1)}),S.src=e):e&&f.load(e,j.get("data"),function(e,i){a===X&&n("error"===i?K(U,"Error").html(j.get("xhrError")):t(this).contents())})}()}}function ct(){o||(L=!1,p=t(i),o=K(U).attr({id:N,class:!1===t.support.opacity?E+"IE":"",role:"dialog",tabindex:"-1"}).hide(),s=K(U,"Overlay").hide(),m=t([K(U,"LoadingOverlay")[0],K(U,"LoadingGraphic")[0]]),n=K(U,"Wrapper"),a=K(U,"Content").append(_=K(U,"Title"),g=K(U,"Current"),v=t('<button type="button"/>').attr({id:E+"Previous"}),y=t('<button type="button"/>').attr({id:E+"Next"}),w=t('<button type="button"/>').attr({id:E+"Slideshow"}),m),b=t('<button type="button"/>').attr({id:E+"Close"}),n.append(K(U).append(K(U,"TopLeft"),r=K(U,"TopCenter"),K(U,"TopRight")),K(U,!1,"clear:left").append(c=K(U,"MiddleLeft"),a,l=K(U,"MiddleRight")),K(U,!1,"clear:left").append(K(U,"BottomLeft"),h=K(U,"BottomCenter"),K(U,"BottomRight"))).find("div div").css({float:"left"}),f=K(U,!1,"position:absolute; width:9999px; visibility:hidden; display:none; max-width:none;"),x=y.add(v).add(g).add(w)),e.body&&!o.parent().length&&t(e.body).append(s,o.append(n,f))}function lt(){function i(t){t.which>1||t.shiftKey||t.altKey||t.metaKey||t.ctrlKey||(t.preventDefault(),rt(this))}return!!o&&(L||(L=!0,y.click(function(){D.next()}),v.click(function(){D.prev()}),b.click(function(){D.close()}),s.click(function(){j.get("overlayClose")&&D.close()}),t(e).bind("keydown."+E,function(t){var e=t.keyCode;Q&&j.get("escKey")&&27===e&&(t.preventDefault(),D.close()),Q&&j.get("arrowKey")&&d[1]&&!t.altKey&&(37===e?(t.preventDefault(),v.click()):39===e&&(t.preventDefault(),y.click()))}),t.isFunction(t.fn.on)?t(e).on("click."+E,"."+O,i):t("."+O).live("click."+E,i)),!0)}t[N]||(t(ct),(D=t.fn[N]=t[N]=function(e,i){var s=this;return e=e||{},t.isFunction(s)&&(s=t("<a/>"),e.open=!0),s[0]?(ct(),lt()&&(i&&(e.onComplete=i),s.each(function(){var i=t.data(this,N)||{};t.data(this,N,t.extend(i,e))}).addClass(O),new J(s[0],e).get("open")&&rt(s[0])),s):s}).position=function(e,i){var s,d,u,f=0,m=0,_=o.offset();function g(){r[0].style.width=h[0].style.width=a[0].style.width=parseInt(o[0].style.width,10)-k+"px",a[0].style.height=c[0].style.height=l[0].style.height=parseInt(o[0].style.height,10)-C+"px"}if(p.unbind("resize."+E),o.css({top:-9e4,left:-9e4}),d=p.scrollTop(),u=p.scrollLeft(),j.get("fixed")?(_.top-=d,_.left-=u,o.css({position:"fixed"})):(f=d,m=u,o.css({position:"absolute"})),!1!==j.get("right")?m+=Math.max(p.width()-j.w-T-k-tt(j.get("right"),"x"),0):!1!==j.get("left")?m+=tt(j.get("left"),"x"):m+=Math.round(Math.max(p.width()-j.w-T-k,0)/2),!1!==j.get("bottom")?f+=Math.max(G()-j.h-q-C-tt(j.get("bottom"),"y"),0):!1!==j.get("top")?f+=tt(j.get("top"),"y"):f+=Math.round(Math.max(G()-j.h-q-C,0)/2),o.css({top:_.top,left:_.left,visibility:"visible"}),n[0].style.width=n[0].style.height="9999px",s={width:j.w+T+k,height:j.h+q+C,top:f,left:m},e){var w=0;t.each(s,function(t){s[t]===Y[t]||(w=e)}),e=w}Y=s,e||o.css(s),o.dequeue().animate(s,{duration:e||0,complete:function(){g(),M=!1,n[0].style.width=j.w+T+k+"px",n[0].style.height=j.h+q+C+"px",j.get("reposition")&&setTimeout(function(){p.bind("resize."+E,D.position)},1),t.isFunction(i)&&i()},step:g})},D.resize=function(t){var e;Q&&((t=t||{}).width&&(j.w=tt(t.width,"x")-T-k),t.innerWidth&&(j.w=tt(t.innerWidth,"x")),u.css({width:j.w}),t.height&&(j.h=tt(t.height,"y")-q-C),t.innerHeight&&(j.h=tt(t.innerHeight,"y")),t.innerHeight||t.height||(e=u.scrollTop(),u.css({height:"auto"}),j.h=u.height()),u.css({height:j.h}),e&&u.scrollTop(e),D.position("none"===j.get("transition")?0:j.get("speed")))},D.prep=function(i){if(Q){var s,n="none"===j.get("transition")?0:j.get("speed");u.remove(),(u=K(U,"LoadedContent").append(i)).hide().appendTo(f.show()).css({width:(j.w=j.w||u.width(),j.w=j.mw&&j.mw<j.w?j.mw:j.w,j.w),overflow:j.get("scrolling")?"auto":"hidden"}).css({height:(j.h=j.h||u.height(),j.h=j.mh&&j.mh<j.h?j.mh:j.h,j.h)}).prependTo(a),f.hide(),t(S).css({float:"none"}),ot(j.get("className")),s=function(){var i,s,a=d.length;function r(){!1===t.support.opacity&&o[0].style.removeAttribute("filter")}Q&&(s=function(){clearTimeout(z),m.hide(),nt(P),j.get("onComplete")},_.html(j.get("title")).show(),u.show(),a>1?("string"==typeof j.get("current")&&g.html(j.get("current").replace("{current}",$+1).replace("{total}",a)).show(),y[j.get("loop")||$<a-1?"show":"hide"]().html(j.get("next")),v[j.get("loop")||$?"show":"hide"]().html(j.get("previous")),at(),j.get("preloading")&&t.each([Z(-1),Z(1)],function(){var i=d[this],s=new J(i,t.data(i,N)),o=s.get("href");o&&et(s,o)&&(o=it(s,o),e.createElement("img").src=o)})):x.hide(),j.get("iframe")?(i=j.get("createIframe"),j.get("scrolling")||(i.scrolling="no"),t(i).attr({src:j.get("href"),class:E+"Iframe"}).one("load",s).appendTo(u),V.one(A,function(){i.src="//about:blank"}),j.get("fastIframe")&&t(i).trigger("load")):s(),"fade"===j.get("transition")?o.fadeTo(n,1,r):r())},"fade"===j.get("transition")?o.fadeTo(n,0,function(){D.position(0,s)}):D.position(n,s)}},D.next=function(){!M&&d[1]&&(j.get("loop")||d[$+1])&&($=Z(1),rt(d[$]))},D.prev=function(){!M&&d[1]&&(j.get("loop")||$)&&($=Z(-1),rt(d[$]))},D.close=function(){Q&&!W&&(W=!0,Q=!1,nt(B),j.get("onCleanup"),p.unbind("."+E),s.fadeTo(j.get("fadeOut")||0,0),o.stop().fadeTo(j.get("fadeOut")||0,0,function(){o.hide(),s.hide(),nt(A),u.remove(),setTimeout(function(){W=!1,nt(H),j.get("onClosed")},1)}))},D.remove=function(){o&&(o.stop(),t[N].close(),o.stop(!1,!0).remove(),s.remove(),W=!1,o=null,t("."+O).removeData(N).removeClass(O),t(e).unbind("click."+E).unbind("keydown."+E))},D.element=function(){return t(j.el)},D.settings=F)}(jQuery,document,window),void 0!==wcfm_notification_sound)var audio=new Audio(wcfm_notification_sound.file),wcfm_notification_sound=new Audio(wcfm_notification_sound.file);else audio=new Audio(wcfm_notification_sound),wcfm_notification_sound=new Audio(wcfm_notification_sound);if(void 0!==wcfm_desktop_notification_sound)var wcfm_desktop_notification_sound=new Audio(wcfm_desktop_notification_sound.file);else wcfm_desktop_notification_sound=new Audio(wcfm_desktop_notification_sound);