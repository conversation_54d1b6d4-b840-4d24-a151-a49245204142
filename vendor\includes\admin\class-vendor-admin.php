<?php
/**
 * Vendor Admin
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin class
 */
class Vendor_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'admin_scripts'));
        add_action('admin_init', array($this, 'admin_init'));
        
        // AJAX handlers
        add_action('wp_ajax_vendor_approve_vendor', array($this, 'approve_vendor'));
        add_action('wp_ajax_vendor_reject_vendor', array($this, 'reject_vendor'));
        add_action('wp_ajax_vendor_approve_withdrawal', array($this, 'approve_withdrawal'));
        add_action('wp_ajax_vendor_reject_withdrawal', array($this, 'reject_withdrawal'));
    }
    
    /**
     * Admin menu
     */
    public function admin_menu() {
        // Main menu
        add_menu_page(
            __('Vendor', 'vendor'),
            __('Vendor', 'vendor'),
            'manage_options',
            'vendor',
            array($this, 'dashboard_page'),
            'dashicons-store',
            56
        );
        
        // Dashboard submenu
        add_submenu_page(
            'vendor',
            __('Dashboard', 'vendor'),
            __('Dashboard', 'vendor'),
            'manage_options',
            'vendor',
            array($this, 'dashboard_page')
        );
        
        // Vendors submenu
        add_submenu_page(
            'vendor',
            __('Vendors', 'vendor'),
            __('Vendors', 'vendor'),
            'manage_options',
            'vendor-vendors',
            array($this, 'vendors_page')
        );
        
        // Withdrawals submenu
        add_submenu_page(
            'vendor',
            __('Withdrawals', 'vendor'),
            __('Withdrawals', 'vendor'),
            'manage_options',
            'vendor-withdrawals',
            array($this, 'withdrawals_page')
        );
        
        // Commissions submenu
        add_submenu_page(
            'vendor',
            __('Commissions', 'vendor'),
            __('Commissions', 'vendor'),
            'manage_options',
            'vendor-commissions',
            array($this, 'commissions_page')
        );
        
        // Settings submenu
        add_submenu_page(
            'vendor',
            __('Settings', 'vendor'),
            __('Settings', 'vendor'),
            'manage_options',
            'vendor-settings',
            array($this, 'settings_page')
        );
    }
    
    /**
     * Admin scripts
     */
    public function admin_scripts($hook) {
        if (strpos($hook, 'vendor') === false) {
            return;
        }
        
        wp_enqueue_script('vendor-admin', VENDOR_PLUGIN_URL . 'assets/js/admin.js', array('jquery'), VENDOR_VERSION, true);
        wp_enqueue_style('vendor-admin', VENDOR_PLUGIN_URL . 'assets/css/admin.css', array(), VENDOR_VERSION);
        
        wp_localize_script('vendor-admin', 'vendorAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('vendor_admin_nonce'),
            'strings' => array(
                'confirm_approve' => __('Are you sure you want to approve this?', 'vendor'),
                'confirm_reject' => __('Are you sure you want to reject this?', 'vendor'),
                'processing' => __('Processing...', 'vendor'),
                'success' => __('Success!', 'vendor'),
                'error' => __('Error occurred!', 'vendor')
            )
        ));
    }
    
    /**
     * Admin init
     */
    public function admin_init() {
        // Register settings
        register_setting('vendor_settings', 'vendor_general_settings');
        register_setting('vendor_settings', 'vendor_commission_settings');
        register_setting('vendor_settings', 'vendor_withdrawal_settings');
    }
    
    /**
     * Dashboard page
     */
    public function dashboard_page() {
        $stats = $this->get_dashboard_stats();
        include VENDOR_PLUGIN_DIR . 'includes/admin/views/dashboard.php';
    }
    
    /**
     * Vendors page
     */
    public function vendors_page() {
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
        $vendor_id = isset($_GET['vendor_id']) ? intval($_GET['vendor_id']) : 0;
        
        if ($action === 'view' && $vendor_id) {
            $vendor = vendor()->vendor_manager->get_vendor($vendor_id);
            include VENDOR_PLUGIN_DIR . 'includes/admin/views/vendor-details.php';
        } else {
            $vendors = $this->get_vendors_list();
            include VENDOR_PLUGIN_DIR . 'includes/admin/views/vendors.php';
        }
    }
    
    /**
     * Withdrawals page
     */
    public function withdrawals_page() {
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
        $withdrawal_id = isset($_GET['withdrawal_id']) ? intval($_GET['withdrawal_id']) : 0;
        
        if ($action === 'view' && $withdrawal_id) {
            $withdrawal = vendor()->withdrawal->get_withdrawal($withdrawal_id);
            include VENDOR_PLUGIN_DIR . 'includes/admin/views/withdrawal-details.php';
        } else {
            $withdrawals = $this->get_withdrawals_list();
            include VENDOR_PLUGIN_DIR . 'includes/admin/views/withdrawals.php';
        }
    }
    
    /**
     * Commissions page
     */
    public function commissions_page() {
        $commissions = $this->get_commissions_list();
        include VENDOR_PLUGIN_DIR . 'includes/admin/views/commissions.php';
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'general';
        include VENDOR_PLUGIN_DIR . 'includes/admin/views/settings.php';
    }
    
    /**
     * Get dashboard stats
     */
    private function get_dashboard_stats() {
        $total_vendors = vendor()->vendor_manager->get_vendor_count();
        $pending_vendors = vendor()->vendor_manager->get_vendor_count('pending');
        $active_vendors = vendor()->vendor_manager->get_vendor_count('active');
        
        $withdrawal_summary = vendor()->withdrawal->get_withdrawal_summary();
        
        global $wpdb;
        $commissions_table = Vendor_Database::get_commissions_table();
        
        $commission_stats = $wpdb->get_row(
            "SELECT 
                SUM(commission_amount) as total_commission,
                SUM(CASE WHEN status = 'pending' THEN commission_amount ELSE 0 END) as pending_commission,
                SUM(CASE WHEN status = 'approved' THEN commission_amount ELSE 0 END) as approved_commission
            FROM $commissions_table"
        );
        
        return array(
            'total_vendors' => $total_vendors,
            'pending_vendors' => $pending_vendors,
            'active_vendors' => $active_vendors,
            'withdrawal_summary' => $withdrawal_summary,
            'commission_stats' => $commission_stats
        );
    }
    
    /**
     * Get vendors list
     */
    private function get_vendors_list() {
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 20;
        $offset = ($page - 1) * $per_page;
        
        $status = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
        
        $args = array(
            'limit' => $per_page,
            'offset' => $offset,
            'status' => $status
        );
        
        return vendor()->vendor_manager->get_vendors($args);
    }
    
    /**
     * Get withdrawals list
     */
    private function get_withdrawals_list() {
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 20;
        $offset = ($page - 1) * $per_page;
        
        $status = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
        $vendor_id = isset($_GET['vendor_id']) ? intval($_GET['vendor_id']) : '';
        
        $args = array(
            'limit' => $per_page,
            'offset' => $offset,
            'status' => $status,
            'vendor_id' => $vendor_id
        );
        
        return vendor()->withdrawal->get_all_withdrawals($args);
    }
    
    /**
     * Get commissions list
     */
    private function get_commissions_list() {
        global $wpdb;
        
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 20;
        $offset = ($page - 1) * $per_page;
        
        $commissions_table = Vendor_Database::get_commissions_table();
        $vendors_table = Vendor_Database::get_vendors_table();
        
        $where = array('1=1');
        $values = array();
        
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            $where[] = 'c.status = %s';
            $values[] = sanitize_text_field($_GET['status']);
        }
        
        if (isset($_GET['vendor_id']) && !empty($_GET['vendor_id'])) {
            $where[] = 'c.vendor_id = %d';
            $values[] = intval($_GET['vendor_id']);
        }
        
        $where_clause = implode(' AND ', $where);
        
        $query = $wpdb->prepare(
            "SELECT c.*, v.store_name, v.store_email 
            FROM $commissions_table c 
            LEFT JOIN $vendors_table v ON c.vendor_id = v.id 
            WHERE $where_clause 
            ORDER BY c.created_at DESC 
            LIMIT %d OFFSET %d",
            array_merge($values, array($per_page, $offset))
        );
        
        return $wpdb->get_results($query);
    }
    
    /**
     * Approve vendor
     */
    public function approve_vendor() {
        check_ajax_referer('vendor_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Unauthorized', 'vendor'));
        }
        
        $vendor_id = intval($_POST['vendor_id']);
        
        $result = vendor()->vendor_manager->update_vendor($vendor_id, array('status' => 'active'));
        
        if ($result) {
            wp_send_json_success(__('Vendor approved successfully', 'vendor'));
        } else {
            wp_send_json_error(__('Failed to approve vendor', 'vendor'));
        }
    }
    
    /**
     * Reject vendor
     */
    public function reject_vendor() {
        check_ajax_referer('vendor_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Unauthorized', 'vendor'));
        }
        
        $vendor_id = intval($_POST['vendor_id']);
        
        $result = vendor()->vendor_manager->update_vendor($vendor_id, array('status' => 'rejected'));
        
        if ($result) {
            wp_send_json_success(__('Vendor rejected successfully', 'vendor'));
        } else {
            wp_send_json_error(__('Failed to reject vendor', 'vendor'));
        }
    }
    
    /**
     * Approve withdrawal
     */
    public function approve_withdrawal() {
        check_ajax_referer('vendor_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Unauthorized', 'vendor'));
        }
        
        $withdrawal_id = intval($_POST['withdrawal_id']);
        $note = sanitize_textarea_field($_POST['note']);
        
        $result = vendor()->withdrawal->update_withdrawal_status($withdrawal_id, 'completed', $note);
        
        if ($result) {
            wp_send_json_success(__('Withdrawal approved successfully', 'vendor'));
        } else {
            wp_send_json_error(__('Failed to approve withdrawal', 'vendor'));
        }
    }
    
    /**
     * Reject withdrawal
     */
    public function reject_withdrawal() {
        check_ajax_referer('vendor_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Unauthorized', 'vendor'));
        }
        
        $withdrawal_id = intval($_POST['withdrawal_id']);
        $note = sanitize_textarea_field($_POST['note']);
        
        $result = vendor()->withdrawal->update_withdrawal_status($withdrawal_id, 'cancelled', $note);
        
        if ($result) {
            wp_send_json_success(__('Withdrawal rejected successfully', 'vendor'));
        } else {
            wp_send_json_error(__('Failed to reject withdrawal', 'vendor'));
        }
    }
}
