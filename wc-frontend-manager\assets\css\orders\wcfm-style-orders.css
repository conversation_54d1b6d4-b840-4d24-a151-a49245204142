.order_items { display: none; }
.order_items_visible { display: table; width: 100%; }
.show_order_items { font-size: 18px; color: #e83e8c; }

.dataTables_wrapper select {
	font-size: 15px;
}

.order-status {
  font-size: 20px;	
}

.wcpv-paid-status, .wcpv-unpaid-status, .wcpv-pending-status, .wcpv-void-status {
	padding: 4px 4px;
	color: #fff;
	border-radius: 2px;
	font-size: 12px;
	line-height: 10px;
	width: 75px;
	display: block;
	margin: 0 auto;
}

.wcpv-paid-status { background-color: #20c997; }
.wcpv-unpaid-status { background-color: #f86c6b; }
.wcpv-pending-status { background-color: #63c2de; }
.wcpv-void-status { background-color: #ffc107; }

.wcfm_order_by_customer{color:#f86c6b}

table.dataTable.display tr td:nth-child(2),
table.dataTable.display tr td:nth-child(3),
table.dataTable.display tr td:nth-child(4),
table.dataTable.display tr td:nth-child(5),
table.dataTable.display tr td:nth-child(6),
table.dataTable.display tr td:nth-child(7),
table.dataTable.display tr td:nth-child(8),
table.dataTable.display tr td:nth-child(9),
table.dataTable.display tr th:nth-child(2),
table.dataTable.display tr th:nth-child(3),
table.dataTable.display tr th:nth-child(6),
table.dataTable.display tr th:nth-child(7),
table.dataTable.display tr th:nth-child(8),
table.dataTable.display tr th:nth-child(9) {
	text-align: center;
}

@media only screen and (max-width: 640px) {
	#wcfm-main-contentainer .dataTables_wrapper .dataTables_filter {float:right;}
  .wcfm_orders_filter_wrap { margin-top: 10px !important; }
}

@media only screen and (max-width: 414px) {
	a.add_new_wcfm_ele_dashboard .text { display: none; }
}