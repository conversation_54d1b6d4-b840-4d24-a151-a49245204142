/*! DO NOT EDIT THIS FILE. This file is a auto generated on 2023-03-25 */
$product_type="",$product_cat="",$product_taxonomy={},$product_vendor="",jQuery(document).ready(function(t){$wcfm_sell_items_catalog_table=t("#wcfm-sell_items_catalog").DataTable({processing:!0,serverSide:!0,responsive:!0,pageLength:parseInt(dataTables_config.pageLength),language:t.parseJSON(dataTables_language),columns:[{responsivePriority:1},{responsivePriority:1},{responsivePriority:1},{responsivePriority:2},{responsivePriority:4},{responsivePriority:5},{responsivePriority:6},{responsivePriority:1}],columnDefs:[{targets:0,orderable:!1},{targets:1,orderable:!1},{targets:2,orderable:!1},{targets:3,orderable:!0},{targets:4,orderable:!1},{targets:5,orderable:!1},{targets:6,orderable:!1},{targets:7,orderable:!1}],ajax:{type:"POST",url:wcfm_params.ajax_url,data:function(e){e.action="wcfm_ajax_controller",e.controller="wcfm-sell-items-catalog",e.product_type=$product_type,e.product_cat=$product_cat,e.product_taxonomy=$product_taxonomy,e.wcfm_ajax_nonce=wcfm_params.wcfm_ajax_nonce},complete:function(){initiateTip(),"undefined"!=typeof intiateWCFMuQuickEdit&&t.isFunction(intiateWCFMuQuickEdit)&&intiateWCFMuQuickEdit(),t(document.body).trigger("updated_wcfm-sell_items_catalog")}}}),0<t("#dropdown_product_type").length&&t("#dropdown_product_type").on("change",function(){$product_type=t("#dropdown_product_type").val(),$wcfm_sell_items_catalog_table.ajax.reload()}),0<t("#dropdown_product_cat").length&&t("#dropdown_product_cat").on("change",function(){$product_cat=t("#dropdown_product_cat").val(),$wcfm_sell_items_catalog_table.ajax.reload()}).select2($wcfm_taxonomy_select_args),0<t(".dropdown_product_custom_taxonomy").length&&t(".dropdown_product_custom_taxonomy").each(function(){t(this).on("change",function(){$product_taxonomy[t(this).data("taxonomy")]=t(this).val(),$wcfm_sell_items_catalog_table.ajax.reload()}).select2()}),t(document.body).on("updated_wcfm-sell_items_catalog",function(){t(".wcfm_sell_this_item").each(function(){t(this).click(function(e){e.preventDefault();e=confirm(wcfm_dashboard_messages.sell_this_item_confirm);if(e){e=t(this);jQuery("#wcfm-sell_items_catalog_wrapper").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),e={action:"wcfmmp_product_multivendor_clone",product_id:e.data("proid"),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce},jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:e,success:function(e){$wcfm_sell_items_catalog_table&&$wcfm_sell_items_catalog_table.ajax.reload(),jQuery("#wcfm-sell_items_catalog_wrapper").unblock()}})}return!1})})}),t(".bulk_action_checkbox_all").click(function(){t(this).is(":checked")?(t(".bulk_action_checkbox_all").attr("checked",!0),t(".bulk_action_checkbox_single").attr("checked",!0)):(t(".bulk_action_checkbox_all").attr("checked",!1),t(".bulk_action_checkbox_single").attr("checked",!1))}),t("#wcfm_bulk_add_to_my_store, #wcfm_bulk_add_to_my_store_bottom").click(function(e){return e.preventDefault(),$selected_products=[],t(".bulk_action_checkbox_single").each(function(){t(this).is(":checked")&&$selected_products.push(t(this).val())}),0===$selected_products.length?alert(wcfm_dashboard_messages.bulk_no_itm_selected):(jQuery("#wcfm-sell_items_catalog_wrapper").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),e={action:"wcfmmp_product_multivendor_bulk_clone",product_ids:$selected_products,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce},jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:e,success:function(e){$wcfm_sell_items_catalog_table&&$wcfm_sell_items_catalog_table.ajax.reload(),jQuery("#wcfm-sell_items_catalog_wrapper").unblock()}})),!1}),0<t(".wcfm_filters_wrap").length&&(t(".dataTable").before(t(".wcfm_filters_wrap")),t(".wcfm_filters_wrap").css("display","inline-block")),t(document.body).on("updated_wcfm-sell_items_catalog",function(){t.each(wcfm_sell_items_catalog_screen_manage,function(e,t){$wcfm_sell_items_catalog_table.column(e).visible(!1)})})});