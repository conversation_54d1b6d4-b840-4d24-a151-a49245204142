<?php
/**
 * Plugin Name: Vendor - Multi-vendor Marketplace
 * Plugin URI: https://example.com/vendor
 * Description: Modern React-based multi-vendor marketplace plugin with advanced vendor management, commission system, withdrawal management, and Tutor LMS integration.
 * Version: 1.0.0
 * Author: Vendor Team
 * Author URI: https://example.com
 * Text Domain: vendor
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 4.0
 * WC tested up to: 8.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('VENDOR_VERSION', '1.0.0');
define('VENDOR_PLUGIN_FILE', __FILE__);
define('VENDOR_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('VENDOR_PLUGIN_URL', plugin_dir_url(__FILE__));
define('VENDOR_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('VENDOR_TEXT_DOMAIN', 'vendor');

// Check if WooCommerce is active
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
    add_action('admin_notices', 'vendor_woocommerce_missing_notice');
    return;
}

/**
 * WooCommerce missing notice
 */
function vendor_woocommerce_missing_notice() {
    ?>
    <div class="notice notice-error">
        <p><?php _e('Vendor plugin requires WooCommerce to be installed and active.', 'vendor'); ?></p>
    </div>
    <?php
}

// Autoloader
require_once VENDOR_PLUGIN_DIR . 'includes/class-vendor-autoloader.php';
Vendor_Autoloader::init();

// Main plugin class
final class Vendor {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Plugin version
     */
    public $version = VENDOR_VERSION;
    
    /**
     * Database manager
     */
    public $db;
    
    /**
     * Admin manager
     */
    public $admin;
    
    /**
     * Frontend manager
     */
    public $frontend;
    
    /**
     * API manager
     */
    public $api;
    
    /**
     * Vendor manager
     */
    public $vendor_manager;
    
    /**
     * Commission manager
     */
    public $commission;
    
    /**
     * Withdrawal manager
     */
    public $withdrawal;

    /**
     * Gateway manager
     */
    public $gateway_manager;

    /**
     * Tutor integration
     */
    public $tutor_integration;
    
    /**
     * Get plugin instance
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->init_components();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'init'), 0);
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // Activation/Deactivation hooks
        register_activation_hook(VENDOR_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(VENDOR_PLUGIN_FILE, array($this, 'deactivate'));
    }
    
    /**
     * Initialize components
     */
    private function init_components() {
        // Database manager
        $this->db = new Vendor_Database();
        
        // Admin manager
        if (is_admin()) {
            $this->admin = new Vendor_Admin();
        }
        
        // Frontend manager
        if (!is_admin() || wp_doing_ajax()) {
            $this->frontend = new Vendor_Frontend();
        }
        
        // API manager
        $this->api = new Vendor_API();
        
        // Core managers
        $this->vendor_manager = new Vendor_Manager();
        $this->commission = new Vendor_Commission();
        $this->withdrawal = new Vendor_Withdrawal();
        $this->gateway_manager = new Vendor_Gateway_Manager();

        // Integrations
        $this->tutor_integration = new Vendor_Tutor_Integration();
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Initialize components that need WordPress to be loaded
        do_action('vendor_init');
    }
    
    /**
     * Load text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain('vendor', false, dirname(VENDOR_PLUGIN_BASENAME) . '/languages');
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->db->create_tables();
        
        // Create vendor role
        $this->create_vendor_role();
        
        // Create vendor dashboard page
        $this->create_vendor_page();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Set activation flag
        update_option('vendor_activated', true);
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Remove activation flag
        delete_option('vendor_activated');
    }
    
    /**
     * Create vendor role
     */
    private function create_vendor_role() {
        add_role('vendor', __('Vendor', 'vendor'), array(
            'read' => true,
            'edit_posts' => false,
            'delete_posts' => false,
            'publish_posts' => false,
            'upload_files' => true,
            'edit_products' => true,
            'read_products' => true,
            'delete_products' => true,
            'edit_published_products' => true,
            'delete_published_products' => true,
            'edit_shop_orders' => true,
            'read_shop_orders' => true,
            'edit_shop_coupons' => true,
            'read_shop_coupons' => true,
            'delete_shop_coupons' => true,
            'edit_published_shop_coupons' => true,
            'delete_published_shop_coupons' => true,
        ));
    }
    
    /**
     * Create vendor dashboard page
     */
    private function create_vendor_page() {
        $page_id = wp_insert_post(array(
            'post_title' => __('Vendor Dashboard', 'vendor'),
            'post_content' => '[vendor_dashboard]',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_slug' => 'vendor-dashboard'
        ));
        
        if ($page_id && !is_wp_error($page_id)) {
            update_option('vendor_dashboard_page_id', $page_id);
        }
    }
    
    /**
     * Get plugin URL
     */
    public function plugin_url() {
        return untrailingslashit(plugins_url('/', VENDOR_PLUGIN_FILE));
    }
    
    /**
     * Get plugin path
     */
    public function plugin_path() {
        return untrailingslashit(plugin_dir_path(VENDOR_PLUGIN_FILE));
    }
    
    /**
     * Get template path
     */
    public function template_path() {
        return apply_filters('vendor_template_path', 'vendor/');
    }
}

/**
 * Get main plugin instance
 */
function vendor() {
    return Vendor::instance();
}

// Initialize plugin
vendor();
