<?php
/**
 * Vendor Withdrawal Manager
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Withdrawal manager class
 */
class Vendor_Withdrawal {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Initialize
     */
    public function init() {
        // Add any initialization hooks here
    }
    
    /**
     * Create withdrawal request
     */
    public function create_withdrawal_request($vendor_id, $amount, $payment_method, $payment_details = array()) {
        global $wpdb;
        
        // Validate vendor
        $vendor = vendor()->vendor_manager->get_vendor($vendor_id);
        if (!$vendor) {
            return new WP_Error('invalid_vendor', __('Invalid vendor', 'vendor'));
        }
        
        // Check available balance
        $available_balance = vendor()->commission->get_vendor_available_balance($vendor_id);
        if ($amount > $available_balance) {
            return new WP_Error('insufficient_balance', __('Insufficient balance', 'vendor'));
        }
        
        // Calculate charges
        $charges = $this->calculate_withdrawal_charges($amount, $payment_method);
        $net_amount = $amount - $charges;
        
        $table = Vendor_Database::get_withdrawals_table();
        
        $data = array(
            'vendor_id' => $vendor_id,
            'amount' => $amount,
            'charges' => $charges,
            'net_amount' => $net_amount,
            'payment_method' => $payment_method,
            'payment_details' => maybe_serialize($payment_details),
            'status' => 'pending'
        );
        
        $result = $wpdb->insert($table, $data);
        
        if ($result) {
            $withdrawal_id = $wpdb->insert_id;
            
            // Mark commissions as requested for withdrawal
            $this->mark_commissions_for_withdrawal($vendor_id, $amount, $withdrawal_id);
            
            do_action('vendor_withdrawal_requested', $withdrawal_id, $vendor_id, $amount);
            
            return $withdrawal_id;
        }
        
        return new WP_Error('withdrawal_failed', __('Failed to create withdrawal request', 'vendor'));
    }
    
    /**
     * Get withdrawal
     */
    public function get_withdrawal($withdrawal_id) {
        global $wpdb;
        
        $table = Vendor_Database::get_withdrawals_table();
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table WHERE id = %d",
            $withdrawal_id
        ));
    }
    
    /**
     * Update withdrawal status
     */
    public function update_withdrawal_status($withdrawal_id, $status, $note = '', $transaction_id = '') {
        global $wpdb;
        
        $table = Vendor_Database::get_withdrawals_table();
        
        $data = array(
            'status' => $status,
            'note' => $note
        );
        
        if ($status === 'completed') {
            $data['processed_at'] = current_time('mysql');
            if ($transaction_id) {
                $data['transaction_id'] = $transaction_id;
            }
        }
        
        $result = $wpdb->update($table, $data, array('id' => $withdrawal_id));
        
        if ($result !== false) {
            // Update commission status
            if ($status === 'completed') {
                $this->complete_withdrawal_commissions($withdrawal_id);
            } elseif ($status === 'cancelled') {
                $this->cancel_withdrawal_commissions($withdrawal_id);
            }
            
            do_action('vendor_withdrawal_status_updated', $withdrawal_id, $status);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Get vendor withdrawals
     */
    public function get_vendor_withdrawals($vendor_id, $args = array()) {
        global $wpdb;
        
        $table = Vendor_Database::get_withdrawals_table();
        
        $defaults = array(
            'status' => '',
            'limit' => 20,
            'offset' => 0,
            'orderby' => 'requested_at',
            'order' => 'DESC',
            'date_from' => '',
            'date_to' => ''
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $where = array('vendor_id = %d');
        $values = array($vendor_id);
        
        if (!empty($args['status'])) {
            $where[] = 'status = %s';
            $values[] = $args['status'];
        }
        
        if (!empty($args['date_from'])) {
            $where[] = 'requested_at >= %s';
            $values[] = $args['date_from'];
        }
        
        if (!empty($args['date_to'])) {
            $where[] = 'requested_at <= %s';
            $values[] = $args['date_to'];
        }
        
        $where_clause = implode(' AND ', $where);
        
        $query = $wpdb->prepare(
            "SELECT * FROM $table WHERE $where_clause ORDER BY {$args['orderby']} {$args['order']} LIMIT %d OFFSET %d",
            array_merge($values, array($args['limit'], $args['offset']))
        );
        
        return $wpdb->get_results($query);
    }
    
    /**
     * Get all withdrawals (admin)
     */
    public function get_all_withdrawals($args = array()) {
        global $wpdb;
        
        $table = Vendor_Database::get_withdrawals_table();
        $vendors_table = Vendor_Database::get_vendors_table();
        
        $defaults = array(
            'status' => '',
            'vendor_id' => '',
            'limit' => 20,
            'offset' => 0,
            'orderby' => 'requested_at',
            'order' => 'DESC'
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $where = array('1=1');
        $values = array();
        
        if (!empty($args['status'])) {
            $where[] = 'w.status = %s';
            $values[] = $args['status'];
        }
        
        if (!empty($args['vendor_id'])) {
            $where[] = 'w.vendor_id = %d';
            $values[] = $args['vendor_id'];
        }
        
        $where_clause = implode(' AND ', $where);
        
        $query = $wpdb->prepare(
            "SELECT w.*, v.store_name, v.store_email 
            FROM $table w 
            LEFT JOIN $vendors_table v ON w.vendor_id = v.id 
            WHERE $where_clause 
            ORDER BY w.{$args['orderby']} {$args['order']} 
            LIMIT %d OFFSET %d",
            array_merge($values, array($args['limit'], $args['offset']))
        );
        
        return $wpdb->get_results($query);
    }
    
    /**
     * Calculate withdrawal charges
     */
    public function calculate_withdrawal_charges($amount, $payment_method) {
        $charges = 0;
        
        // Get withdrawal settings
        $withdrawal_settings = get_option('vendor_withdrawal_settings', array());
        
        if (isset($withdrawal_settings[$payment_method]['charges'])) {
            $charge_settings = $withdrawal_settings[$payment_method]['charges'];
            
            if ($charge_settings['type'] === 'percentage') {
                $charges = ($amount * $charge_settings['value']) / 100;
            } elseif ($charge_settings['type'] === 'fixed') {
                $charges = $charge_settings['value'];
            }
        }
        
        return apply_filters('vendor_withdrawal_charges', $charges, $amount, $payment_method);
    }
    
    /**
     * Mark commissions for withdrawal
     */
    private function mark_commissions_for_withdrawal($vendor_id, $amount, $withdrawal_id) {
        global $wpdb;
        
        $commissions_table = Vendor_Database::get_commissions_table();
        
        // Get approved commissions for this vendor
        $commissions = $wpdb->get_results($wpdb->prepare(
            "SELECT id, commission_amount FROM $commissions_table 
            WHERE vendor_id = %d AND status = 'approved' AND withdrawal_id IS NULL 
            ORDER BY created_at ASC",
            $vendor_id
        ));
        
        $remaining_amount = $amount;
        $commission_ids = array();
        
        foreach ($commissions as $commission) {
            if ($remaining_amount <= 0) {
                break;
            }
            
            if ($commission->commission_amount <= $remaining_amount) {
                $commission_ids[] = $commission->id;
                $remaining_amount -= $commission->commission_amount;
            }
        }
        
        if (!empty($commission_ids)) {
            $commission_ids_str = implode(',', $commission_ids);
            $wpdb->query($wpdb->prepare(
                "UPDATE $commissions_table SET withdrawal_id = %d WHERE id IN ($commission_ids_str)",
                $withdrawal_id
            ));
        }
    }
    
    /**
     * Complete withdrawal commissions
     */
    private function complete_withdrawal_commissions($withdrawal_id) {
        global $wpdb;
        
        $commissions_table = Vendor_Database::get_commissions_table();
        
        $wpdb->update(
            $commissions_table,
            array('status' => 'withdrawn'),
            array('withdrawal_id' => $withdrawal_id)
        );
    }
    
    /**
     * Cancel withdrawal commissions
     */
    private function cancel_withdrawal_commissions($withdrawal_id) {
        global $wpdb;
        
        $commissions_table = Vendor_Database::get_commissions_table();
        
        $wpdb->update(
            $commissions_table,
            array('withdrawal_id' => null),
            array('withdrawal_id' => $withdrawal_id)
        );
    }
    
    /**
     * Get withdrawal summary
     */
    public function get_withdrawal_summary($vendor_id = null) {
        global $wpdb;
        
        $table = Vendor_Database::get_withdrawals_table();
        
        $where = '1=1';
        $values = array();
        
        if ($vendor_id) {
            $where = 'vendor_id = %d';
            $values[] = $vendor_id;
        }
        
        $query = $wpdb->prepare(
            "SELECT 
                COUNT(*) as total_requests,
                SUM(amount) as total_amount,
                SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as completed_amount,
                SUM(CASE WHEN status = 'cancelled' THEN amount ELSE 0 END) as cancelled_amount
            FROM $table 
            WHERE $where",
            $values
        );
        
        return $wpdb->get_row($query);
    }
    
    /**
     * Get available payment methods
     */
    public function get_payment_methods() {
        $methods = array(
            'paypal' => __('PayPal', 'vendor'),
            'bank_transfer' => __('Bank Transfer', 'vendor'),
            'stripe' => __('Stripe', 'vendor')
        );
        
        return apply_filters('vendor_withdrawal_payment_methods', $methods);
    }
    
    /**
     * Process automatic withdrawal
     */
    public function process_automatic_withdrawal($vendor_id) {
        $vendor = vendor()->vendor_manager->get_vendor($vendor_id);
        if (!$vendor) {
            return false;
        }
        
        // Check if auto withdrawal is enabled for this vendor
        $auto_withdrawal_settings = get_user_meta($vendor->user_id, 'vendor_auto_withdrawal', true);
        if (!$auto_withdrawal_settings || !$auto_withdrawal_settings['enabled']) {
            return false;
        }
        
        $available_balance = vendor()->commission->get_vendor_available_balance($vendor_id);
        $minimum_amount = $auto_withdrawal_settings['minimum_amount'];
        
        if ($available_balance >= $minimum_amount) {
            return $this->create_withdrawal_request(
                $vendor_id,
                $available_balance,
                $auto_withdrawal_settings['payment_method'],
                $auto_withdrawal_settings['payment_details']
            );
        }
        
        return false;
    }
}
