.page_collapsible, .wcfm-collapse a.page_collapsible_dummy {
	margin: 0;
	padding:10px;
	margin-top: 5px;
	min-height:20px;

	border-bottom: #f0f0f0 1px solid;
	background: #1C2B36;

	text-decoration:none;
	color: #fff;
	font-size: 13px;
	box-sizing: content-box;
	white-space: normal;
	display: block;
	
	-moz-border-radius: 3px 3px 0px 0px;
	-webkit-border-radius: 3px 3px 0px 0px;
	border-radius: 3px 3px 0px 0px;
	
	position: relative;
	cursor: pointer;
	transition: all .5s;
}

.page_collapsible_content_holder {
	float: left;
}

#wcfm-main-contentainer .page_collapsible label.wcfmfa { width: 20px; }

.collapse-open {
	background: #1C2B36;
	color: #17a2b8;
}

.page_collapsible:hover label, .page_collapsible.collapse-open label {
	color: #17a2b8;
}

.collapse-open span {
	display:block;
	float:right;
	padding:10px;
}

.collapse-close span {
	display:none; 
}

.page_collapsible span.block-indicator {
	float: right;
	padding: 5px;
	display: inline-block;
}

div.wcfm-container {
    padding: 1px;
    margin:0;
    background: #f1f1f1;
}

div.wcfm-content {
    background:#ffffff;
    margin: 0 10px 10px 10px;
    padding: 10px;
    font-size:.9em;
    line-height:1.5em;
}

div.wcfm-content ul, div.wcfm-content p {
    margin:0;
    padding:3px;
}

div.wcfm-content ul li {
    list-style-position:inside;
    line-height:25px;
}

div.wcfm-content ul li a {
    color:#555555;
}