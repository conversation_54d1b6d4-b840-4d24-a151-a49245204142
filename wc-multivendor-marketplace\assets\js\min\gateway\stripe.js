/*! DO NOT EDIT THIS FILE. This file is a auto generated on 2023-03-25 */
jQuery(function(s){"use strict";var n,e,t,r,i,o,a,p;wcfmmp_stripe_split_pay_params.key&&(n=Stripe(wcfmmp_stripe_split_pay_params.key),e=wcfmmp_stripe_split_pay_params.elements_options.length?wcfmmp_stripe_split_pay_params.elements_options:{},t=n.elements(e),(p={getAjaxURL:function(e){return wcfmmp_stripe_split_pay_params.ajaxurl.toString().replace("%%endpoint%%","wc_stripe_"+e)},unmountElements:function(){i.unmount("#wcfmmp-stripe-split-pay-card-element"),o.unmount("#wcfmmp-stripe-split-pay-exp-element"),a.unmount("#wcfmmp-stripe-split-pay-cvc-element")},mountElements:function(){s("#wcfmmp-stripe-split-pay-card-element").length&&(i.mount("#wcfmmp-stripe-split-pay-card-element"),o.mount("#wcfmmp-stripe-split-pay-exp-element"),a.mount("#wcfmmp-stripe-split-pay-cvc-element"))},createElements:function(){var e={base:{iconColor:"#666EE8",color:"#31325F",fontSize:"15px","::placeholder":{color:"#CFD7E0"}}},r={focus:"focused",empty:"empty",invalid:"invalid"},e=wcfmmp_stripe_split_pay_params.elements_styling||e,r=wcfmmp_stripe_split_pay_params.elements_classes||r;i=t.create("cardNumber",{style:e,classes:r}),o=t.create("cardExpiry",{style:e,classes:r}),a=t.create("cardCvc",{style:e,classes:r}),i.addEventListener("change",function(e){p.onCCFormChange(),p.updateCardBrand(e.brand),e.error&&s(document.body).trigger("stripeError",e)}),o.addEventListener("change",function(e){p.onCCFormChange(),e.error&&s(document.body).trigger("stripeError",e)}),a.addEventListener("change",function(e){p.onCCFormChange(),e.error&&s(document.body).trigger("stripeError",e)}),0<jQuery("input[name=wcfmmp_stripe_customer_id]").length&&p.form.on("change","input[name=wcfmmp_stripe_customer_id]",function(){"new"==jQuery("input[name=wcfmmp_stripe_customer_id]:checked").val()?jQuery("div.wcfmmp_stripe_new_card").slideDown(200):jQuery("div.wcfmmp_stripe_new_card").slideUp(200),p.onCCFormChange()}),"yes"===wcfmmp_stripe_split_pay_params.is_checkout?s(document.body).on("updated_checkout",function(){i&&p.unmountElements(),p.mountElements()}):"yes"===wcfmmp_stripe_split_pay_params.is_pay_for_order_page&&p.mountElements()},updateCardBrand:function(e){var r={visa:"stripe-visa-brand",mastercard:"stripe-mastercard-brand",amex:"stripe-amex-brand",discover:"stripe-discover-brand",diners:"stripe-diners-brand",jcb:"stripe-jcb-brand",unknown:"stripe-credit-card-brand"},t=s(".stripe-card-brand"),e=e in r?r[e]:"stripe-credit-card-brand";s.each(r,function(e,r){t.removeClass(r)}),t.addClass(e)},init:function(){this.stripe_checkout_submit=!1,s("form.woocommerce-checkout").length&&(this.form=s("form.woocommerce-checkout")),s("form.woocommerce-checkout").on("checkout_place_order_stripe_split",this.onSubmit),s("form#order_review").length&&(this.form=s("form#order_review")),s("form#order_review").on("submit",this.onSubmit),s("form.woocommerce-checkout").on("change",this.reset),s(document).on("stripeError",this.onError).on("checkout_error",this.reset),p.createElements(),wcfmmp_stripe_split_pay_params.is_3d_secure&&(window.addEventListener("hashchange",p.onHashChange),p.maybeConfirmIntent())},isStripeChosen:function(){return s("#payment_method_stripe_split")},hasSource:function(){return 0<s("input.stripe-source").length},hasToken:function(){return 0<s("input.stripe-token").length},isMobile:function(){return!!/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)},block:function(){p.isMobile()||p.form.block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},unblock:function(){p.form.unblock()},getSelectedPaymentElement:function(){return s('.payment_methods input[name="payment_method"]:checked')},getOwnerDetails:function(){var e=s("#billing_first_name").length?s("#billing_first_name").val():wcfmmp_stripe_split_pay_params.billing_first_name,r=s("#billing_last_name").length?s("#billing_last_name").val():wcfmmp_stripe_split_pay_params.billing_last_name,t={owner:{name:"",address:{},email:"",phone:""}};return t.owner.name=e,t.owner.name=e&&r?e+" "+r:s("#wcfmmp-stripe-split-pay-payment-data").data("full-name"),t.owner.email=s("#billing_email").val(),t.owner.phone=s("#billing_phone").val(),void 0!==t.owner.phone&&t.owner.phone.length<=0&&delete t.owner.phone,void 0!==t.owner.email&&t.owner.email.length<=0&&delete t.owner.email,void 0!==t.owner.name&&t.owner.name.length<=0&&delete t.owner.name,0<s("#billing_address_1").length?(t.owner.address.line1=s("#billing_address_1").val(),t.owner.address.line2=s("#billing_address_2").val(),t.owner.address.state=s("#billing_state").val(),t.owner.address.city=s("#billing_city").val(),t.owner.address.postal_code=s("#billing_postcode").val(),t.owner.address.country=s("#billing_country").val()):wcfmmp_stripe_split_pay_params.billing_address_1&&(t.owner.address.line1=wcfmmp_stripe_split_pay_params.billing_address_1,t.owner.address.line2=wcfmmp_stripe_split_pay_params.billing_address_2,t.owner.address.state=wcfmmp_stripe_split_pay_params.billing_state,t.owner.address.city=wcfmmp_stripe_split_pay_params.billing_city,t.owner.address.postal_code=wcfmmp_stripe_split_pay_params.billing_postcode,t.owner.address.country=wcfmmp_stripe_split_pay_params.billing_country),t},createSource:function(){var e=p.getOwnerDetails();n.createSource(i,e).then(p.sourceResponse)},createToken:function(){for(var e=0;e<wcfmmp_stripe_split_pay_params.no_of_vendor;e++)n.createToken(i).then(p.tokenResponse)},savedCardToken:function(){r={number:jQuery("input[name=wcfmmp_stripe_customer_id]:checked").data("last4"),cvc:jQuery("input[name=wcfmmp_stripe_customer_id]:checked").data("cvv"),exp_month:jQuery("input[name=wcfmmp_stripe_customer_id]:checked").data("exp_month"),exp_year:jQuery("input[name=wcfmmp_stripe_customer_id]:checked").data("exp_year"),name:wcfmmp_stripe_split_pay_params.billing_first_name+" "+wcfmmp_stripe_split_pay_params.billing_last_name,address_line1:wcfmmp_stripe_split_pay_params.billing_address_1,address_line2:wcfmmp_stripe_split_pay_params.billing_address_2,address_state:wcfmmp_stripe_split_pay_params.billing_state,address_city:wcfmmp_stripe_split_pay_params.billing_city,address_zip:wcfmmp_stripe_split_pay_params.billing_postcode,address_country:wcfmmp_stripe_split_pay_params.billing_country};for(var e=0;e<wcfmmp_stripe_split_pay_params.no_of_vendor;e++)n.createToken(r).then(p.tokenResponse)},sourceResponse:function(e){e.error?s(document.body).trigger("stripeError",e):p.processStripeResponse(e.source)},tokenResponse:function(e){e.error?s(document.body).trigger("stripeError",e):p.processStripeTokenResponse(e.token)},processStripeResponse:function(e){p.reset(),p.form.append("<input type='hidden' class='stripe-source' name='stripe_source' value='"+e.id+"'/>"),p.form.submit()},processStripeTokenResponse:function(e){p.form.append("<input type='hidden' class='stripe-token' name='stripe_token[]' value='"+e.id+"'/>")},onSubmit:function(e){if(p.isStripeChosen())if(0<jQuery("input[name=wcfmmp_stripe_customer_id]").length)if("new"==jQuery("input[name=wcfmmp_stripe_customer_id]:checked").val()){if(!p.hasSource()||!p.hasToken())return e.preventDefault(),p.block(),p.createSource(),p.createToken(),!1}else e.preventDefault(),p.block(),p.form.append("<input type='hidden' class='stripe-source' name='stripe_source' value='"+jQuery("input[name=wcfmmp_stripe_customer_id]:checked").val()+"'/>"),p.createToken();else if(!p.hasSource()||!p.hasToken())return e.preventDefault(),p.block(),p.createSource(),p.createToken(),!1},onCCFormChange:function(){p.reset()},reset:function(){s(".wc-stripe-error, .stripe-source, .stripe_token").remove(),"yes"===wcfmmp_stripe_split_pay_params.is_stripe_checkout&&(p.stripe_submit=!1)},onError:function(e,r){var t=r.error.message,n=p.getSelectedPaymentElement().parents("li").eq(0).find(".wcfmmp-stripe-split-pay-source-errors");"invalid_request_error"!==r.error.type&&"api_connection_error"!==r.error.type&&"api_error"!==r.error.type&&"authentication_error"!==r.error.type&&"rate_limit_error"!==r.error.type||(t=wcfmmp_stripe_split_pay_params.invalid_request_error),"card_error"===r.error.type&&wcfmmp_stripe_split_pay_params.hasOwnProperty(r.error.code)&&(t=wcfmmp_stripe_split_pay_params[r.error.code]),"validation_error"===r.error.type&&wcfmmp_stripe_split_pay_params.hasOwnProperty(r.error.code)&&(t=wcfmmp_stripe_split_pay_params[r.error.code]),p.reset(),s(".woocommerce-NoticeGroup-checkout").remove(),console.log(r.error.message),s(n).html('<ul class="woocommerce_error woocommerce-error wc-stripe-error"><li>'+t+"</li></ul>"),s(".wc-stripe-error").length&&s("html, body").animate({scrollTop:s(".wc-stripe-error").offset().top-200},200),p.unblock()},onHashChange:function(){var e,r=window.location.hash.match(/^#?confirm-pi-([^:]+):(.+)$/);!r||r.length<3||(e=r[1],r=decodeURIComponent(r[2]),window.location.hash="",p.openIntentModal(e,r))},maybeConfirmIntent:function(){var e,r;s("#stripe-intent-id").length&&s("#stripe-intent-return").length&&(e=s("#stripe-intent-id").val(),r=s("#stripe-intent-return").val(),p.openIntentModal(e,r,!0))},openIntentModal:function(e,r,t){n.handleCardPayment(e).then(function(e){if(e.error)throw e.error;"requires_capture"!==e.paymentIntent.status&&"succeeded"!==e.paymentIntent.status||(window.location=r)}).catch(function(e){if(t)return window.location=r;s(document.body).trigger("stripeError",{error:e}),p.form&&p.form.removeClass("processing"),s.get(r+"&is_ajax")})}}).init())});