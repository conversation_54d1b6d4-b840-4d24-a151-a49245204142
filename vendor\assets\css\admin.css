/**
 * Vendor Admin Styles
 */

/* Dashboard Stats */
.vendor-dashboard-stats {
    margin: 20px 0;
}

.vendor-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.vendor-stat-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.vendor-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.vendor-stat-icon {
    margin-right: 15px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.vendor-stat-icon .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: #fff;
}

.vendor-stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.vendor-stat-content p {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 14px;
}

.vendor-stat-details {
    font-size: 12px;
}

.vendor-stat-details span {
    margin-right: 10px;
}

.vendor-stat-details .pending {
    color: #f56500;
}

.vendor-stat-details .active,
.vendor-stat-details .completed,
.vendor-stat-details .approved {
    color: #46b450;
}

/* Dashboard Layout */
.vendor-dashboard-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.vendor-widget {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.vendor-widget-header {
    background: #f9f9f9;
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.vendor-widget-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.vendor-widget-content {
    padding: 20px;
}

/* Tables */
.vendor-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
}

.vendor-table th,
.vendor-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.vendor-table th {
    background: #f9f9f9;
    font-weight: 600;
    color: #333;
}

.vendor-table tbody tr:hover {
    background: #f8f9fa;
}

/* Status Badges */
.vendor-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    display: inline-block;
}

.vendor-status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.vendor-status-active,
.vendor-status-completed,
.vendor-status-approved {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.vendor-status-rejected,
.vendor-status-cancelled {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.vendor-status-processing {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Buttons */
.vendor-btn {
    display: inline-block;
    padding: 8px 16px;
    background: #0073aa;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 13px;
    line-height: 1.4;
    transition: background-color 0.2s;
}

.vendor-btn:hover {
    background: #005a87;
    color: #fff;
}

.vendor-btn-secondary {
    background: #6c757d;
}

.vendor-btn-secondary:hover {
    background: #545b62;
}

.vendor-btn-success {
    background: #46b450;
}

.vendor-btn-success:hover {
    background: #3e9b47;
}

.vendor-btn-danger {
    background: #dc3232;
}

.vendor-btn-danger:hover {
    background: #c62d2d;
}

.vendor-btn-small {
    padding: 4px 8px;
    font-size: 11px;
}

/* Settings Tabs */
.vendor-settings-tabs {
    margin-bottom: 20px;
}

.vendor-settings-content .tab-content {
    display: none;
    background: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 0 4px 4px 4px;
}

.vendor-settings-content .tab-content.active {
    display: block;
}

/* Form Elements */
.vendor-form-table {
    width: 100%;
}

.vendor-form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
    vertical-align: top;
    text-align: left;
    font-weight: 600;
}

.vendor-form-table td {
    padding: 15px 10px;
    vertical-align: top;
}

.vendor-form-table input[type="text"],
.vendor-form-table input[type="email"],
.vendor-form-table input[type="number"],
.vendor-form-table input[type="password"],
.vendor-form-table select,
.vendor-form-table textarea {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.vendor-form-table textarea {
    height: 100px;
    resize: vertical;
}

.vendor-form-table .description {
    margin-top: 5px;
    color: #666;
    font-size: 13px;
    font-style: italic;
}

.vendor-form-table input[type="checkbox"] {
    margin-right: 8px;
}

/* Error States */
.vendor-form-table input.error,
.vendor-form-table select.error,
.vendor-form-table textarea.error {
    border-color: #dc3232;
    box-shadow: 0 0 2px rgba(220, 50, 50, 0.8);
}

.vendor-form-table .description.error {
    color: #dc3232;
    font-weight: bold;
    font-style: normal;
}

/* Loading States */
.vendor-processing {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.vendor-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.vendor-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: vendor-spin 1s linear infinite;
}

@keyframes vendor-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notices */
.vendor-notice {
    padding: 12px;
    margin: 5px 0 15px;
    border-left: 4px solid #0073aa;
    background: #fff;
    box-shadow: 0 1px 1px 0 rgba(0,0,0,0.1);
}

.vendor-notice-success {
    border-left-color: #46b450;
}

.vendor-notice-warning {
    border-left-color: #f56500;
}

.vendor-notice-error {
    border-left-color: #dc3232;
}

/* Responsive Design */
@media (max-width: 768px) {
    .vendor-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .vendor-dashboard-row {
        grid-template-columns: 1fr;
    }
    
    .vendor-stat-card {
        padding: 15px;
    }
    
    .vendor-widget-content {
        padding: 15px;
    }
    
    .vendor-form-table th,
    .vendor-form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .vendor-form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .vendor-table {
        font-size: 12px;
    }
    
    .vendor-table th,
    .vendor-table td {
        padding: 8px 4px;
    }
}

/* Print Styles */
@media print {
    .vendor-btn,
    .vendor-settings-tabs,
    .page-title-action {
        display: none !important;
    }
    
    .vendor-stat-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .vendor-table {
        border-collapse: collapse;
    }
    
    .vendor-table th,
    .vendor-table td {
        border: 1px solid #ddd;
    }
}
