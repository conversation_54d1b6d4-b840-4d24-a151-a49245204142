msgid ""
msgstr ""
"Project-Id-Version: WooCommerce Frontend Manager\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-02-07 03:45+0000\n"
"PO-Revision-Date: 2018-02-07 04:09+0000\n"
"Last-Translator: Soporte Jaralo Market <<EMAIL>>\n"
"Language-Team: Spanish (Mexico)\n"
"Language: es-MX\n"
"Plural-Forms: nplurals=2; plural=n != 1\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco - https://localise.biz/\n"
"X-Loco-Template: lang/wc-frontend-manager-es_ES.po"

#: controllers/wcfm-controller-capability.php:39
msgid "Capability saved successfully"
msgstr ""

#: controllers/wcfm-controller-messages.php:153
msgid "System"
msgstr ""

#: controllers/wcfm-controller-messages.php:205 
#: controllers/wcfm-controller-messages.php:207
msgid "Approve / Reject"
msgstr ""

#: controllers/wcfm-controller-products.php:271
msgid "Listings Package"
msgstr ""

#: controllers/wcfm-controller-products.php:273
msgid "Resume Package"
msgstr ""

#: controllers/wcfm-controller-products.php:275 
#: core/class-wcfm-thirdparty-support.php:217 
#: core/class-wcfm-thirdparty-support.php:245 
#: core/class-wcfm-thirdparty-support.php:356
msgid "Auction"
msgstr ""

#: controllers/wcfm-controller-products.php:277 
#: core/class-wcfm-thirdparty-support.php:239 
#: core/class-wcfm-thirdparty-support.php:306
msgid "Rental"
msgstr ""

#: controllers/wcfm-controller-products.php:279
msgid "Accommodation"
msgstr ""

#: controllers/wcfm-controller-products.php:281
msgid "Appointment"
msgstr ""

#: controllers/wcfm-controller-products.php:283
msgid "Bundle"
msgstr ""

#: controllers/wcfm-controller-products.php:285
msgid "Composite"
msgstr ""

#: controllers/wcfm-controller-products.php:331
msgid "No Featured"
msgstr ""

#: controllers/wcfm-controller-products.php:337
msgid ""
"Featured Product: Upgrade your WCFM to WCFM Ultimate to avail this feature."
msgstr ""

#: controllers/wcfm-controller-products.php:349
msgid ""
"Duplicate Product: Upgrade your WCFM to WCFM Ultimate to avail this feature."
msgstr ""

#: core/class-wcfm-admin.php:97
msgid ""
"<h2>\n"
"\t\t\t\t\t\t\t\t\t\t           Is there anything missing in your front-end "
"dashboard !!!\n"
"\t\t\t\t\t\t\t\t               </h2>"
msgstr ""

#: core/class-wcfm-admin.php:103
msgid ""
"<p>WooCommerce Frontend Manage - Ultimate is there to fill up all those for "
"you. Product image gallery, shipment tracing, direct messaging, GEO map, "
"product importer, custom attriutes and many many more, almost a never ending "
"features list for you.</p>"
msgstr ""

#: core/class-wcfm-admin.php:109
msgid "WCFM U >>"
msgstr ""

#: core/class-wcfm-admin.php:145
msgid ""
"<h2>\n"
"\t\t\t\t\t\t\t\t\t\t           Now setup your vendor membership subscription "
"in minutes & it's FREE !!!\n"
"\t\t\t\t\t\t\t\t               </h2>"
msgstr ""

#: core/class-wcfm-admin.php:148
msgid ""
"<p>A simple membership plugin for offering FREE AND PREMIUM SUBSCRIPTION for "
"your multi-vendor marketplace. You may set up unlimited membership levels "
"(example: free, silver, gold etc) with different pricing plan, capabilities "
"and commission.</p>"
msgstr ""

#: core/class-wcfm-admin.php:190
msgid ""
"<h2>\n"
"\t\t\t\t\t\t\t\t\t\t           Do you want to have different capabilities "
"for each membership levels !!!\n"
"\t\t\t\t\t\t\t\t               </h2>"
msgstr ""

#: core/class-wcfm-admin.php:196
msgid ""
"<p>WCFM - Groups & Staffs will empower you to set up totaly different "
"capability rules for your each membership levels very easily.</p>"
msgstr ""

#: core/class-wcfm-admin.php:202
msgid "WCFM GS >>"
msgstr ""

#: core/class-wcfm-admin.php:341
#, php-format
msgid ""
"WCFM totally works from front-end ... check dashboard settings %shere >>%s"
msgstr ""

#: core/class-wcfm-ajax.php:228
#, php-format
msgid "A new %s <b>%s</b> added to the store by <b>%s</b>"
msgstr ""

#: core/class-wcfm-article.php:74
msgid "Articles Dashboard"
msgstr ""

#: core/class-wcfm-article.php:77
msgid "Articles Manager"
msgstr ""

#: core/class-wcfm-article.php:121 views/wcfm-view-capability.php:200 
#: views/articles/wcfm-view-articles.php:32
msgid "Articles"
msgstr ""

#: core/class-wcfm-capability.php:154
msgid "Products Limit: "
msgstr ""

#: core/class-wcfm-capability.php:161
msgid "remaining"
msgstr ""

#: core/class-wcfm-capability.php:163 
#: core/class-wcfm-thirdparty-support.php:274
msgid "Unlimited"
msgstr ""

#: core/class-wcfm-catalog.php:42 core/class-wcfm.php:453 
#: views/wcfm-view-capability.php:154
msgid "Catalog"
msgstr ""

#: core/class-wcfm-catalog.php:64
msgid "Catalog Mode"
msgstr ""

#: core/class-wcfm-catalog.php:69
msgid "Disable Add to Cart?"
msgstr ""

#: core/class-wcfm-catalog.php:70
msgid "Hide Price?"
msgstr ""

#: core/class-wcfm-customer.php:72
msgid "Customers Dashboard"
msgstr ""

#: core/class-wcfm-customer.php:75
msgid "Customers Manager"
msgstr ""

#: core/class-wcfm-customer.php:78
msgid "Customers Details"
msgstr ""

#: core/class-wcfm-customer.php:123
msgid "Customers"
msgstr ""

#: core/class-wcfm-customfield-support.php:37
msgid "Product Custom Field"
msgstr ""

#: core/class-wcfm-customfield-support.php:43
msgid "Custom Fields"
msgstr ""

#: core/class-wcfm-customfield-support.php:43
msgid ""
"You can integrate any Third Party plugin using Custom Fields, but you should "
"use the same fields name as used by Third Party plugins."
msgstr ""

#: core/class-wcfm-customfield-support.php:45
msgid "Block Name"
msgstr ""

#: core/class-wcfm-customfield-support.php:46
msgid "Fields as Group?"
msgstr ""

#: core/class-wcfm-customfield-support.php:47
msgid "Group name"
msgstr ""

#: core/class-wcfm-customfield-support.php:48
msgid "Fields"
msgstr ""

#: core/class-wcfm-customfield-support.php:49
msgid "Field Type"
msgstr ""

#: core/class-wcfm-customfield-support.php:50
msgid "Label"
msgstr ""

#: core/class-wcfm-customfield-support.php:51
msgid ""
"This is will going to use as `meta_key` for storing this field value in "
"database."
msgstr ""

#: core/class-wcfm-customfield-support.php:52
msgid "Options"
msgstr ""

#: core/class-wcfm-customfield-support.php:52
msgid "Insert option values | separated"
msgstr ""

#: core/class-wcfm-customfield-support.php:53
msgid "Help Content"
msgstr ""

#: core/class-wcfm-customfield-support.php:54
msgid "Required?"
msgstr ""

#: core/class-wcfm-dokan.php:188 core/class-wcfm-wcmarketplace.php:410 
#: core/class-wcfm-wcpvendors.php:202 core/class-wcfm-wcvendors.php:330
#, php-format
msgid "Product awaiting <b>%s</b> for review"
msgstr ""

#: core/class-wcfm-dokan.php:323 core/class-wcfm-wcmarketplace.php:752 
#: core/class-wcfm-wcpvendors.php:470 core/class-wcfm-wcvendors.php:612
msgid "Total Earning"
msgstr ""

#: core/class-wcfm-enquiry.php:289 core/class-wcfm-notification.php:131
msgid "Show All"
msgstr ""

#: core/class-wcfm-library.php:353
msgid "Memebership"
msgstr ""

#: core/class-wcfm-library.php:696
msgid "Print"
msgstr ""

#: core/class-wcfm-library.php:696
msgid "PDF"
msgstr ""

#: core/class-wcfm-library.php:696
msgid "Excel"
msgstr ""

#: core/class-wcfm-library.php:696
msgid "CSV"
msgstr ""

#: core/class-wcfm-library.php:774
msgid "Choose Media"
msgstr ""

#: core/class-wcfm-library.php:774
msgid "Add to Gallery"
msgstr ""

#: core/class-wcfm-non-ajax.php:53
msgid "Online"
msgstr ""

#: core/class-wcfm-non-ajax.php:203
msgid "View WCFM FAQ"
msgstr ""

#: core/class-wcfm-non-ajax.php:203
msgid "FAQ"
msgstr ""

#: core/class-wcfm-query.php:124
msgid "Products Stock Manager"
msgstr ""

#: core/class-wcfm-query.php:127 views/wcfm-view-products-export.php:58 
#: views/wcfm-view-products.php:90
msgid "Products Import"
msgstr ""

#: core/class-wcfm-query.php:130 views/wcfm-view-products-export.php:33 
#: views/wcfm-view-products.php:77
msgid "Products Export"
msgstr ""

#: core/class-wcfm-query.php:175
msgid "Knowledgebase Manager"
msgstr ""

#: core/class-wcfm-query.php:178
msgid "Notice Dashboard"
msgstr ""

#: core/class-wcfm-query.php:181
msgid "Notice Manager"
msgstr ""

#: core/class-wcfm-shortcode.php:110 views/wcfm-view-header-panels.php:57 
#: views/notice/wcfm-view-notices.php:26
msgid "Notice Board"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:126
msgid "Listings Dashboard"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:210
msgid "Rental Product"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:233
msgid "Listing Package"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:311
msgid "Set Price Type"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:311
msgid "General Pricing"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:311
msgid "Choose a price type - this controls the schema."
msgstr ""

#: core/class-wcfm-thirdparty-support.php:312
msgid "Hourly Price"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:312
msgid "Hourly price will be applicabe if booking or rental days min 1day"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:312 
#: core/class-wcfm-thirdparty-support.php:313
msgid "Enter price here"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:313
msgid "General Price"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:319
msgid "Availability"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:324
msgid "Product Availabilities"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:324
msgid "Please select the date range to be disabled for the product."
msgstr ""

#: core/class-wcfm-thirdparty-support.php:325
msgid "Custom Date"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:328
msgid "NO"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:361
msgid "Auction Date From"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:362
msgid "Auction Date To"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:394
msgid "Has Voucher"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:422
msgid "-- Choose Template --"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:428
msgid "Voucher Template"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:428
msgid "Select a voucher template to make this into a voucher product."
msgstr ""

#: core/class-wcfm-thirdparty-support.php:540 
#: core/class-wcfm-thirdparty-support.php:563
msgid "Select Delivery Time"
msgstr ""

#: core/class-wcfm-vendor-support.php:103
msgid "Vendors Dashboard"
msgstr ""

#: core/class-wcfm-vendor-support.php:106
msgid "Vendors Manager"
msgstr ""

#: core/class-wcfm-vendor-support.php:109
msgid "Vendors Commission"
msgstr ""

#: core/class-wcfm-vendor-support.php:249 
#: core/class-wcfm-vendor-support.php:254 
#: views/vendors/wcfm-view-vendors.php:74 
#: views/vendors/wcfm-view-vendors.php:90
msgid "Vendor"
msgstr ""

#: core/class-wcfm-vendor-support.php:395 
#: core/class-wcfm-vendor-support.php:424
msgid "Fixed (per transaction)"
msgstr ""

#: core/class-wcfm-vendor-support.php:400 
#: core/class-wcfm-vendor-support.php:429
msgid "Fixed (per unit)"
msgstr ""

#: core/class-wcfm-vendor-support.php:1372
msgid "Review"
msgstr ""

#: core/class-wcfm-vendor-support.php:1373
msgid "New Category"
msgstr ""

#: core/class-wcfm-wcbookings.php:97 
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:99 
#: views/wc_bookings/wcfm-view-wcbookings-details.php:66 
#: views/wc_bookings/wcfm-view-wcbookings.php:36
msgid "Bookings List"
msgstr ""

#: core/class-wcfm-wcbookings.php:100
msgid "Bookings Resources"
msgstr ""

#: core/class-wcfm-wcbookings.php:103
msgid "Bookings Resources Manage"
msgstr ""

#: core/class-wcfm-wcbookings.php:106
msgid "Create Bookings"
msgstr ""

#: core/class-wcfm-wcbookings.php:109 
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:114
msgid "Bookings Calendar"
msgstr ""

#: core/class-wcfm-wcbookings.php:115
msgid "Bookings settings"
msgstr ""

#: core/class-wcfm-wcmarketplace.php:558 views/wcfm-view-orders.php:86 
#: views/wcfm-view-orders.php:103
msgid "Fees"
msgstr ""

#: core/class-wcfm-wcmarketplace.php:572 core/class-wcfm-wcmarketplace.php:721 
#: core/class-wcfm-wcpvendors.php:354 core/class-wcfm-wcpvendors.php:461
msgid "Shipping Tax"
msgstr ""

#: core/class-wcfm-wcmarketplace.php:609 core/class-wcfm-wcmarketplace.php:695 
#: core/class-wcfm-wcmarketplace.php:743 core/class-wcfm-wcmarketplace.php:757 
#: controllers/orders/wcfm-controller-orders.php:180 
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:231
msgid "N/A"
msgstr ""

#: core/class-wcfm-wcmarketplace.php:734
msgid "Total Fees"
msgstr ""

#: core/class-wcfm-wcmarketplace.php:764 core/class-wcfm-wcpvendors.php:479 
#: core/class-wcfm-wcvendors.php:621
msgid "Gross Total"
msgstr ""

#: core/class-wcfm-withdrawal.php:63 
#: views/withdrawal/dokan/wcfm-view-payments.php:26 
#: views/withdrawal/wcmp/wcfm-view-payments.php:26
msgid "Payments History"
msgstr ""

#: core/class-wcfm-withdrawal.php:67 
#: views/withdrawal/dokan/wcfm-view-payments.php:42 
#: views/withdrawal/dokan/wcfm-view-withdrawal.php:53 
#: views/withdrawal/wcmp/wcfm-view-payments.php:43 
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:28
msgid "Withdrawal Request"
msgstr ""

#: core/class-wcfm-withdrawal.php:98
msgid "Payments"
msgstr ""

#: core/class-wcfm.php:452
msgid "Enquiry Tab"
msgstr ""

#: core/class-wcfm.php:452
msgid ""
"If you just want to hide Single Product page `Enquiry Tab`, but keep enable "
"`Enquiry Module` for `Catalog Mode`."
msgstr ""

#: core/class-wcfm.php:453
msgid ""
"If you disable `Enquiry Module` then `Catalog Module` will stop working "
"automatically."
msgstr ""

#: core/class-wcfm.php:454 
#: includes/reports/class-dokan-report-sales-by-date.php:805 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:528 
#: includes/reports/class-wcpvendors-report-sales-by-date.php:431 
#: includes/reports/class-wcvendors-report-sales-by-date.php:452
msgid "Withdrawal"
msgstr ""

#: core/class-wcfm.php:455
msgid "Article"
msgstr ""

#: core/class-wcfm.php:461
msgid "Custom Field"
msgstr ""

#: core/class-wcfm.php:462
msgid "Sub-menu"
msgstr ""

#: core/class-wcfm.php:462
msgid "This will disable `Add New` sub-menus on hover."
msgstr ""

#: core/class-wcfm.php:466
msgid "BuddyPress Integration"
msgstr ""

#: core/class-wcfm.php:489
msgid "Base Highlighter Color"
msgstr ""

#: core/class-wcfm.php:490
msgid "Top Bar Background Color"
msgstr ""

#: core/class-wcfm.php:491
msgid "Top Bar Text Color"
msgstr ""

#: core/class-wcfm.php:492
msgid "Dashboard Background Color"
msgstr ""

#: core/class-wcfm.php:493
msgid "Container Background Color"
msgstr ""

#: core/class-wcfm.php:494
msgid "Container Head Color"
msgstr ""

#: core/class-wcfm.php:495
msgid "Container Head Text Color"
msgstr ""

#: core/class-wcfm.php:496
msgid "Container Head Active Color"
msgstr ""

#: core/class-wcfm.php:497
msgid "Container Head Active Text Color"
msgstr ""

#: core/class-wcfm.php:500
msgid "Menu Item Text Color"
msgstr ""

#: core/class-wcfm.php:502
msgid "Menu Active Item Text Color"
msgstr ""

#: core/class-wcfm.php:503
msgid "Button Background Color"
msgstr ""

#: core/class-wcfm.php:504
msgid "Button Text Color"
msgstr ""

#: helpers/class-wcfm-setup.php:51
msgid "Introduction"
msgstr ""

#: helpers/class-wcfm-setup.php:56
msgid "Dashboard Setup"
msgstr ""

#: helpers/class-wcfm-setup.php:66 helpers/class-wcfm-setup.php:536 
#: views/settings/wcfm-view-settings.php:80
msgid "Capability"
msgstr ""

#: helpers/class-wcfm-setup.php:71
msgid "Ready!"
msgstr ""

#: helpers/class-wcfm-setup.php:86
msgctxt "enhanced select"
msgid "No matches found"
msgstr ""

#: helpers/class-wcfm-setup.php:87
msgctxt "enhanced select"
msgid "Loading failed"
msgstr ""

#: helpers/class-wcfm-setup.php:88
msgctxt "enhanced select"
msgid "Please enter 1 or more characters"
msgstr ""

#: helpers/class-wcfm-setup.php:89
msgctxt "enhanced select"
msgid "Please enter %qty% or more characters"
msgstr ""

#: helpers/class-wcfm-setup.php:90
msgctxt "enhanced select"
msgid "Please delete 1 character"
msgstr ""

#: helpers/class-wcfm-setup.php:91
msgctxt "enhanced select"
msgid "Please delete %qty% characters"
msgstr ""

#: helpers/class-wcfm-setup.php:92
msgctxt "enhanced select"
msgid "You can only select 1 item"
msgstr ""

#: helpers/class-wcfm-setup.php:93
msgctxt "enhanced select"
msgid "You can only select %qty% items"
msgstr ""

#: helpers/class-wcfm-setup.php:94
msgctxt "enhanced select"
msgid "Loading more results&hellip;"
msgstr ""

#: helpers/class-wcfm-setup.php:95
msgctxt "enhanced select"
msgid "Searching&hellip;"
msgstr ""

#: helpers/class-wcfm-setup.php:144 helpers/class-wcfm-setup.php:387
msgid "WCFM &rsaquo; Setup Wizard"
msgstr ""

#: helpers/class-wcfm-setup.php:215
msgid "WCFM requires WooCommerce plugin to be active!"
msgstr ""

#: helpers/class-wcfm-setup.php:311
#, php-format
msgid ""
"%1$s could not be installed (%2$s). <a href=\"%3$s\">Please install it "
"manually by clicking here.</a>"
msgstr ""

#: helpers/class-wcfm-setup.php:331
#, php-format
msgid ""
"%1$s was installed but could not be activated. <a href=\"%2$s\">Please "
"activate it manually by clicking here.</a>"
msgstr ""

#: helpers/class-wcfm-setup.php:438
msgid "Let's experience the best ever WC Frontend Dashboard!!"
msgstr ""

#: helpers/class-wcfm-setup.php:439
msgid ""
"Thank you for choosing WCFM! This quick setup wizard will help you to "
"configure the basic settings and you will have your dashboard ready in no "
"time. <strong>It’s completely optional as WCFM already auto-setup.</strong>"
msgstr ""

#: helpers/class-wcfm-setup.php:440
msgid ""
"If you don't want to go through the wizard right now, you can skip and "
"return to the WordPress dashboard. Come back anytime if you change your mind!"
msgstr ""

#: helpers/class-wcfm-setup.php:442
msgid "Let's go!"
msgstr ""

#: helpers/class-wcfm-setup.php:443
msgid "Not right now"
msgstr ""

#: helpers/class-wcfm-setup.php:461
msgid "Dashboard setup"
msgstr ""

#: helpers/class-wcfm-setup.php:466
msgid "WCFM Full View"
msgstr ""

#: helpers/class-wcfm-setup.php:467
msgid "Theme Header"
msgstr ""

#: helpers/class-wcfm-setup.php:468
msgid "WCFM Slick Menu"
msgstr ""

#: helpers/class-wcfm-setup.php:469
msgid "WCFM Header Panel"
msgstr ""

#: helpers/class-wcfm-setup.php:470
msgid "Welcome Box"
msgstr ""

#: helpers/class-wcfm-setup.php:471
msgid "Category Checklist View"
msgstr ""

#: helpers/class-wcfm-setup.php:471 views/settings/wcfm-view-settings.php:112
msgid ""
"Disable this to have Product Manager Category/Custom Taxonomy Selector - "
"Flat View."
msgstr ""

#: helpers/class-wcfm-setup.php:476 helpers/class-wcfm-setup.php:507 
#: helpers/class-wcfm-setup.php:570
msgid "Continue"
msgstr ""

#: helpers/class-wcfm-setup.php:477 helpers/class-wcfm-setup.php:508 
#: helpers/class-wcfm-setup.php:571
msgid "Skip this step"
msgstr ""

#: helpers/class-wcfm-setup.php:495
msgid "Dashboard Style"
msgstr ""

#: helpers/class-wcfm-setup.php:562 views/wcfm-view-capability.php:230
msgid "Status Update"
msgstr ""

#: helpers/class-wcfm-setup.php:586
msgid "We are done!"
msgstr ""

#: helpers/class-wcfm-setup.php:588
msgid ""
"Your front-end dashboard is ready. It's time to experience the things more "
"Easily and Peacefully. Also you will be a bit more relax than ever before, "
"have fun!!"
msgstr ""

#: helpers/class-wcfm-setup.php:592
msgid "Next steps"
msgstr ""

#: helpers/class-wcfm-setup.php:594
msgid "Let's go to Dashboard"
msgstr ""

#: helpers/class-wcfm-setup.php:598
msgid "Learn more"
msgstr ""

#: helpers/class-wcfm-setup.php:600
msgid "Watch the tutorial videos"
msgstr ""

#: helpers/class-wcfm-setup.php:601
msgid "WCFM - What & Why?"
msgstr ""

#: helpers/class-wcfm-setup.php:602
msgid "Choose your multi-vendor plugin"
msgstr ""

#: helpers/class-wcfm-setup.php:742
msgid "Return to the WordPress Dashboard"
msgstr ""

#: helpers/wcfm-core-functions.php:44
#, php-format
msgid ""
"%s: You don't have permission to access this page. Please contact your "
"%sStore Admin%s for assistance."
msgstr ""

#: helpers/wcfm-core-functions.php:59 helpers/wcfm-core-functions.php:89
msgid ""
": Please ask your Store Admin to upgrade your dashboard to access this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:64 helpers/wcfm-core-functions.php:94
#, php-format
msgid ""
"%s: Please ask your %sStore Admin%s to upgrade your dashboard to access this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:71
msgid ": Upgrade your WCFM to WCFM - Ultimate to avail this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:75
#, php-format
msgid "%s: Upgrade your WCFM to %sWCFM - Ultimate%s to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:101
msgid ""
": Associate your WCFM with WCFM - Groups & Staffs to avail this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:105
#, php-format
msgid ""
"%s: Associate your WCFM with %sWCFM - Groups & Staffs%s to access this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:119
msgid ": Please contact your Store Admin to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:124
#, php-format
msgid "%s: Please contact your %sStore Admin%s to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:131
msgid ": Associate your WCFM with WCFM - Analytics to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:135
#, php-format
msgid ""
"%s: Associate your WCFM with %sWCFM - Analytics%s to access this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:633
msgid "Please insert Article Title before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:634
msgid "Article Successfully Saved."
msgstr ""

#: helpers/wcfm-core-functions.php:635
msgid "Article Successfully submitted for moderation."
msgstr ""

#: helpers/wcfm-core-functions.php:636
msgid "Article Successfully Published."
msgstr ""

#: helpers/wcfm-core-functions.php:652
msgid "Product Successfully submitted for moderation."
msgstr ""

#: helpers/wcfm-core-functions.php:679
msgid "Please insert atleast Knowledgebase Title before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:680
msgid "Knowledgebase Successfully Saved."
msgstr ""

#: helpers/wcfm-core-functions.php:681
msgid "Knowledgebase Successfully Published."
msgstr ""

#: helpers/wcfm-core-functions.php:693
msgid "Please insert atleast Topic Title before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:694
msgid "Topic Successfully Saved."
msgstr ""

#: helpers/wcfm-core-functions.php:695
msgid "Topic Successfully Published."
msgstr ""

#: helpers/wcfm-core-functions.php:707
msgid "Please write something before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:708
msgid "Reply send failed, try again."
msgstr ""

#: helpers/wcfm-core-functions.php:709
msgid "Reply Successfully Send."
msgstr ""

#: helpers/wcfm-core-functions.php:721
msgid "Name is required."
msgstr ""

#: helpers/wcfm-core-functions.php:722
msgid "Email is required."
msgstr ""

#: helpers/wcfm-core-functions.php:737
msgid ""
"Are you sure and want to delete this 'Article'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:738
msgid ""
"Are you sure and want to delete this 'Product'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:739
msgid ""
"Are you sure and want to delete this 'Message'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:740
msgid ""
"Are you sure and want to delete this 'Order'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:741
msgid "Are you sure and want to 'Mark as Complete' this Order?"
msgstr ""

#: helpers/wcfm-core-functions.php:744
msgid "Select all"
msgstr ""

#: helpers/wcfm-core-functions.php:745
msgid "Select none"
msgstr ""

#: helpers/wcfm-core-functions.php:746
msgid "Any"
msgstr ""

#: helpers/wcfm-core-functions.php:747
msgid "Enter a name for the new attribute term:"
msgstr ""

#: helpers/wcfm-core-functions.php:748
msgid "Search for a attribute ..."
msgstr ""

#: helpers/wcfm-core-functions.php:749
msgid "Search for a product ..."
msgstr ""

#: helpers/wcfm-core-functions.php:750
msgid "Choose Categoies ..."
msgstr ""

#: helpers/wcfm-core-functions.php:751
msgid "No categories"
msgstr ""

#: helpers/wcfm-core-functions.php:752
msgid "Choose "
msgstr ""

#: helpers/wcfm-core-functions.php:753
msgid "Choose Listings ..."
msgstr ""

#: helpers/wcfm-core-functions.php:754
msgid ""
"Please upgrade your WC Frontend Manager to Ultimate version and avail this "
"feature."
msgstr ""

#: helpers/wcfm-core-functions.php:755
msgid ""
"Install WC Frontend Manager Ultimate and WooCommerce PDF Invoices & Packing "
"Slips to avail this feature."
msgstr ""

#: helpers/wcfm-core-functions.php:756
msgid "Please select some element first!!"
msgstr ""

#: helpers/wcfm-core-functions.php:757
msgid ""
"Are you sure and want to do this?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:769
msgid "Direct"
msgstr ""

#: views/wcfm-view-capability.php:85 views/settings/wcfm-view-settings.php:80
msgid "Capability Controller"
msgstr ""

#: views/wcfm-view-capability.php:92
msgid "Capability Settings"
msgstr ""

#: views/wcfm-view-capability.php:95
msgid "Dashboard Settings"
msgstr ""

#: views/wcfm-view-capability.php:116
msgid "Configure what to hide from all Vendors"
msgstr ""

#: views/wcfm-view-capability.php:124
msgid "Manage Products"
msgstr ""

#: views/wcfm-view-capability.php:125
msgid "Add Products"
msgstr ""

#: views/wcfm-view-capability.php:128
msgid "Auto Publish Live Products"
msgstr ""

#: views/wcfm-view-capability.php:175 views/wcfm-view-capability.php:178
msgid "Manage Appointments"
msgstr ""

#: views/wcfm-view-capability.php:178
msgid "Install WC Appointments to enable this feature."
msgstr ""

#: views/wcfm-view-capability.php:203
msgid "Manage Articles"
msgstr ""

#: views/wcfm-view-capability.php:204
msgid "Add Articles"
msgstr ""

#: views/wcfm-view-capability.php:205
msgid "Publish Articles"
msgstr ""

#: views/wcfm-view-capability.php:206
msgid "Edit Live Articles"
msgstr ""

#: views/wcfm-view-capability.php:207
msgid "Auto Publish Live Articles"
msgstr ""

#: views/wcfm-view-capability.php:208
msgid "Delete Articles"
msgstr ""

#: views/wcfm-view-capability.php:232
msgid "Billing Address"
msgstr ""

#: views/wcfm-view-capability.php:233
msgid "Shipping Address"
msgstr ""

#: views/wcfm-view-capability.php:242 views/wcfm-view-orders-details.php:131 
#: controllers/orders/wcfm-controller-dokan-orders.php:232 
#: controllers/orders/wcfm-controller-orders.php:203 
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:289 
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:286 
#: controllers/orders/wcfm-controller-wcvendors-orders.php:285
msgid "PDF Packing Slip"
msgstr ""

#: views/wcfm-view-capability.php:265 views/wcfm-view-capability.php:269
msgid "Backend Access"
msgstr ""

#: views/wcfm-view-capability.php:298 views/wcfm-view-capability.php:308
msgid "Shop Managers Capability"
msgstr ""

#: views/wcfm-view-capability.php:320 views/wcfm-view-capability.php:330
msgid "Shop Staffs Capability"
msgstr ""

#: views/wcfm-view-capability.php:339
msgid "*** Vendor Managers are treated as Shop Staff for a Vendor Store."
msgstr ""

#: views/wcfm-view-coupons-manage.php:49
msgid "Edit Coupon"
msgstr ""

#: views/wcfm-view-coupons.php:35 views/wcfm-view-listings.php:82 
#: views/wcfm-view-orders.php:64 views/wcfm-view-products.php:67 
#: views/wc_bookings/wcfm-view-wcbookings.php:63
msgid "Screen Manager"
msgstr ""

#: views/wcfm-view-knowledgebase-manage.php:42
msgid "Manage Knowledgebase"
msgstr ""

#: views/wcfm-view-knowledgebase-manage.php:49
msgid "Edit Knowledgebase"
msgstr ""

#: views/wcfm-view-knowledgebase-manage.php:49
msgid "Add Knowledgebase"
msgstr ""

#: views/wcfm-view-knowledgebase.php:33
msgid "Guidelines for Store Users"
msgstr ""

#: views/wcfm-view-knowledgebase.php:38
msgid "Add New Knowledgebase"
msgstr ""

#: views/wcfm-view-listings.php:39
msgid "Expired"
msgstr ""

#: views/wcfm-view-listings.php:40 
#: views/articles/wcfm-view-articles-manage.php:120 
#: views/articles/wcfm-view-articles-manage.php:364 
#: views/products-manager/wcfm-view-products-manage.php:373 
#: views/products-manager/wcfm-view-products-manage.php:878
msgid "Preview"
msgstr ""

#: views/wcfm-view-listings.php:91
msgid "Add New Listing"
msgstr ""

#: views/wcfm-view-listings.php:103 views/wcfm-view-listings.php:114
msgid "Listing"
msgstr ""

#: views/wcfm-view-listings.php:106 views/wcfm-view-listings.php:117 
#: views/wcfm-view-products.php:199 views/wcfm-view-products.php:221 
#: views/articles/wcfm-view-articles-manage.php:114 
#: views/articles/wcfm-view-articles.php:115 
#: views/articles/wcfm-view-articles.php:126 
#: views/products-manager/wcfm-view-products-manage.php:367
msgid "Views"
msgstr ""

#: views/wcfm-view-messages.php:69 views/notice/wcfm-view-notice-view.php:163 
#: views/vendors/wcfm-view-vendors-manage.php:334
msgid "Send"
msgstr ""

#: views/wcfm-view-messages.php:93
msgid "Only Unread"
msgstr ""

#: views/wcfm-view-messages.php:94
msgid "Only Read"
msgstr ""

#: views/wcfm-view-messages.php:111 views/wcfm-view-messages.php:124
msgid "Select all for mark as read"
msgstr ""

#: views/wcfm-view-orders-details-fedex-dhl-express.php:49
msgid "Fedex"
msgstr ""

#: views/wcfm-view-orders-details-fedex-dhl-express.php:163
msgid "DHL Express"
msgstr ""

#: views/wcfm-view-orders-details.php:109
msgid "Order #"
msgstr ""

#: views/wcfm-view-orders-details.php:124
msgid "CSV Export"
msgstr ""

#: views/wcfm-view-orders-details.php:181 
#: views/vendors/wcfm-view-vendors-manage.php:365 
#: views/wc_bookings/wcfm-view-wcbookings-details.php:122
msgid "Update"
msgstr ""

#: views/wcfm-view-products.php:104
msgid "Stock Manager"
msgstr ""

#: views/wcfm-view-products.php:187 views/wcfm-view-products.php:209
msgid "Select all for bulk edit"
msgstr ""

#: views/wcfm-view-profile.php:133
msgid "Profile Manager"
msgstr ""

#: views/wcfm-view-profile.php:156
msgid "Avatar"
msgstr ""

#: views/wcfm-view-profile.php:174
msgid "Site Default"
msgstr ""

#: views/wcfm-view-profile.php:182
msgid "Language"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23 
#: views/wc_bookings/wcfm-view-wcbookings.php:21
msgid "Paid & Confirmed"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23 
#: views/wc_bookings/wcfm-view-wcbookings.php:23
msgid "Pending Confirmation"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
msgid "Un-paid"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23 
#: views/wc_bookings/wcfm-view-wcbookings.php:24 
#: views/withdrawal/dokan/wcfm-view-payments.php:54
msgid "Cancelled"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23 
#: views/wc_bookings/wcfm-view-wcbookings.php:20
msgid "Complete"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23 
#: views/wc_bookings/wcfm-view-wcbookings.php:22
msgid "Confirmed"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings.php:90
msgid "#"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings.php:105
#, php-format
msgctxt "Guest string with name from booking order in brackets"
msgid "Guest (%s)"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:569 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:205 
#: includes/reports/class-wcpvendors-report-sales-by-date.php:201 
#: includes/reports/class-wcvendors-report-sales-by-date.php:184
#, php-format
msgid "%s total withdrawal"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:570
msgid ""
"This is the sum of the commission withdraw including shipping and taxes if "
"applicable."
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:797 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:520 
#: includes/reports/class-wcpvendors-report-sales-by-date.php:423 
#: includes/reports/class-wcvendors-report-sales-by-date.php:444
msgid "Earning"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:823 
#: includes/reports/class-wcfm-report-sales-by-date.php:716 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:546 
#: includes/reports/class-wcpvendors-report-sales-by-date.php:449 
#: includes/reports/class-wcvendors-report-sales-by-date.php:470
msgid "Order Counts"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:831 
#: includes/reports/class-wcfm-report-sales-by-date.php:724 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:554 
#: includes/reports/class-wcpvendors-report-sales-by-date.php:457 
#: includes/reports/class-wcvendors-report-sales-by-date.php:478
msgid "Order Item Counts"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:839 
#: includes/reports/class-wcfm-report-sales-by-date.php:740
msgid "Coupon Amounts"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:847 
#: includes/reports/class-wcfm-report-sales-by-date.php:748
msgid "Refund Amounts"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:882 
#: includes/reports/class-wcfm-report-sales-by-date.php:783 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:589 
#: includes/reports/class-wcpvendors-report-sales-by-date.php:492 
#: includes/reports/class-wcvendors-report-sales-by-date.php:513 
#: views/withdrawal/dokan/wcfm-view-payments.php:68 
#: views/withdrawal/dokan/wcfm-view-payments.php:76
msgid "Amount"
msgstr ""

#: includes/reports/class-wcfm-report-analytics.php:256
msgid "Daily Views"
msgstr ""

#: includes/reports/class-wcfm-report-sales-by-date.php:732
msgid "Net Sales"
msgstr ""

#: includes/reports/class-wcmarketplace-report-sales-by-date.php:174 
#: includes/reports/class-wcvendors-report-sales-by-date.php:171
msgid ""
"This is the sum of the order totals after any refunds and including shipping "
"and taxes."
msgstr ""

#: includes/reports/class-wcmarketplace-report-sales-by-date.php:184
#, php-format
msgid "%s total admin fees"
msgstr ""

#: includes/reports/class-wcmarketplace-report-sales-by-date.php:185
msgid ""
"This is the sum of the admin fees including shipping and taxes if applicable."
msgstr ""

#: includes/reports/class-wcmarketplace-report-sales-by-date.php:191
#, php-format
msgid "%s total paid fees"
msgstr ""

#: includes/reports/class-wcmarketplace-report-sales-by-date.php:192
msgid ""
"This is the sum of the admin fees paid including shipping and taxes if "
"applicable."
msgstr ""

#: includes/reports/class-wcmarketplace-report-sales-by-date.php:520
msgid "Admin Fees"
msgstr ""

#: includes/reports/class-wcmarketplace-report-sales-by-date.php:528
msgid "Paid Fees"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:97
msgid "Manage Article"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:105
msgid "Edit Article"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:105
msgid "Add Article"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:133 
#: views/articles/wcfm-view-articles.php:65
msgid "Add New Article"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:151
msgid "Article Title"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:204 
#: views/articles/wcfm-view-articles-manage.php:303
msgid "Separate Article Tags with commas"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:204 
#: views/articles/wcfm-view-articles-manage.php:303 
#: views/products-manager/wcfm-view-products-manage.php:479 
#: views/products-manager/wcfm-view-products-manage.php:616
msgid "Choose from the most used tags"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:216 
#: views/articles/wcfm-view-articles-manage.php:315 
#: views/products-manager/wcfm-view-products-manage.php:491 
#: views/products-manager/wcfm-view-products-manage.php:628
msgid " with commas"
msgstr ""

#: views/articles/wcfm-view-articles.php:117 
#: views/articles/wcfm-view-articles.php:128
msgid "Author"
msgstr ""

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:58
msgid "Last Login"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:141
msgid "admin fees in last 7 days"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:141
msgid "commission in last 7 days"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:150
msgid "sold in last 7 days"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:163
msgid "received in last 7 days"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:223 
#: views/dashboard/wcfm-view-dokan-dashboard.php:234 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:251 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:254 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:238
msgid "Product Stats"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:275
#, php-format
msgid "<strong>%s order</strong> - on-hold"
msgid_plural "<strong>%s orders</strong> - on-hold"
msgstr[0] ""
msgstr[1] ""

#: views/dashboard/wcfm-view-dashboard.php:327 
#: views/dashboard/wcfm-view-dokan-dashboard.php:340 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:358 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:365 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:344
msgid "Top Regions"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:369 
#: views/dashboard/wcfm-view-dokan-dashboard.php:382 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:400 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:407 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:386
msgid "There is no topic yet!!"
msgstr ""

#: views/dashboard/wcfm-view-dokan-dashboard.php:286 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:303 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:311 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:290
#, php-format
msgid "<strong>%s product</strong> - awaiting fulfillment"
msgid_plural "<strong>%s products</strong> - awaiting fulfillment"
msgstr[0] ""
msgstr[1] ""

#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:171 
#: views/vendors/wcfm-view-vendors-manage.php:216
msgid "admin fees in this month"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:64
msgid "View Product"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-tab.php:87
msgid "by"
msgstr ""

#: views/enquiry/wcfm-view-enquiry.php:41
msgid "Filter by Product"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:47
msgid "Manage Topic"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:55 
#: views/notice/wcfm-view-notice-view.php:59
msgid "Edit Topic"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:55
msgid "Add Topic"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:58 
#: views/notice/wcfm-view-notice-manage.php:58 
#: views/notice/wcfm-view-notice-view.php:57 
#: views/notice/wcfm-view-notice-view.php:57 
#: views/notice/wcfm-view-notices.php:33
msgid "Topics"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:59
msgid "View Topic"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:72
msgid "Allow Reply"
msgstr ""

#: views/notice/wcfm-view-notice-manage.php:73
msgid "Close for New Reply"
msgstr ""

#: views/notice/wcfm-view-notice-view.php:47
msgid "Topic"
msgstr ""

#: views/notice/wcfm-view-notice-view.php:104
msgid "Replies"
msgstr ""

#: views/notice/wcfm-view-notice-view.php:152
msgid "New Reply"
msgstr ""

#: views/notice/wcfm-view-notices.php:37
msgid "Add New Topic"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:358
msgid "Edit Product"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:553
msgid "Add new category"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:559
msgid "-- Parent category --"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:569 
#: views/products-manager/wcfm-view-products-manage.php:769
msgid "Add"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:753
msgid "Active?"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:772
msgid "Pre-defined Attributes"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:787
#, php-format
msgid ""
"Before you can add a variation you need to add some variation attributes on "
"the Attributes tab. %sLearn more%s"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:797 
#: views/products-manager/wcfm-view-products-manage.php:798
msgid "Variations Bulk Options"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:801
msgid "Pricing"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:802
msgid "Regular prices"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:803
msgid "Regular price increase"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:804
msgid "Regular price decrease"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:805
msgid "Sale prices"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:806
msgid "Sale price increase"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:807
msgid "Sale price decrease"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:240
msgid "Product Fees"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:245
msgid "Fee Name"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:245
msgid "This will be shown at the checkout description the added fee."
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:246
msgid "Fee Amount"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:246
msgid ""
"Enter a monetary decimal without any currency symbols or thousand separator. "
"This field also accepts percentages."
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:247
msgid "Multiple Fee by Quantity"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:247
msgid ""
"Multiply the fee by the quantity of this product that is added to the cart."
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:260
msgid "Bulk Discount"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:265
msgid "Bulk Discount enabled"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:266
msgid "Bulk discount special offer text in product description"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:267
msgid "Discount Rules"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:268
msgid "Quantity (min.)"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:269
msgid "Discount (%)"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:283
msgid "Role Based Price"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:295
msgid "Selling Price"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:153
msgid "Profile Image"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:156
msgid "Store Product Per Page"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:158
msgid "Show email in store"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:159
msgid "Show tab on product single page view"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:168
msgid "Street"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:168
msgid "Street adress"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:169
msgid "Street 2"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:169
msgid "Apartment, suit, unit etc. (optional)"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:170
msgid "Town / City"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:171
msgid "Postcode / Zip"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:179
msgid "Store Location"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:218 
#: views/settings/wcfm-view-wcmarketplace-settings.php:191 
#: views/settings/wcfm-view-wcpvendors-settings.php:141 
#: views/settings/wcfm-view-wcvendors-settings.php:183
msgid "Payment"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:224 
#: views/settings/wcfm-view-wcmarketplace-settings.php:198 
#: views/settings/wcfm-view-wcvendors-settings.php:189 
#: views/vendors/wcfm-view-vendors-manage.php:273
msgid "PayPal Email"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:225
msgid "Skrill Email"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:230
msgid "Bank Details"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:234
msgid "Account Name"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:235 
#: views/settings/wcfm-view-wcmarketplace-settings.php:200
msgid "Account Number"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:236 
#: views/settings/wcfm-view-wcmarketplace-settings.php:201
msgid "Bank Name"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:237 
#: views/settings/wcfm-view-wcmarketplace-settings.php:203
msgid "Bank Address"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:238
msgid "Routing Number"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:239
msgid "IBAN"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:240
msgid "Swift Code"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:292
msgid "Enable Shipping"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:318 
#: views/settings/wcfm-view-wcvendors-settings.php:288
msgid "Shipping Rates"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:318
msgid ""
"Add the countries you deliver your products to. You can specify states as "
"well. If the shipping price is same except some countries/states, there is "
"an option Everywhere Else, you can use that."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:321
msgid "State Shipping Rates"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:322 
#: views/settings/wcfm-view-wcvendors-settings.php:290
msgid "State"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:329
msgid "Dokan Pro Shipping Settings"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:340
msgid "SEO"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:361
msgid "Dokan Pro SEO Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:62
msgid "WCfM Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:66
msgid "Bookings Global Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:74
msgid "Appointments Global Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:102
msgid "Quick access icon"
msgstr ""

#: views/settings/wcfm-view-settings.php:103
msgid "Disable Quick Access"
msgstr ""

#: views/settings/wcfm-view-settings.php:104
msgid "Disable Sidebar Logo"
msgstr ""

#: views/settings/wcfm-view-settings.php:105
msgid "Disable Welcome Box"
msgstr ""

#: views/settings/wcfm-view-settings.php:106
msgid "Disable WCFM Menu"
msgstr ""

#: views/settings/wcfm-view-settings.php:107
msgid "Disable Theme Header"
msgstr ""

#: views/settings/wcfm-view-settings.php:108
msgid "Disable WCFM Full View"
msgstr ""

#: views/settings/wcfm-view-settings.php:109
msgid "Disable WCFM Slick Menu"
msgstr ""

#: views/settings/wcfm-view-settings.php:110
msgid "Disable WCFM Header Panel"
msgstr ""

#: views/settings/wcfm-view-settings.php:111
msgid "Disable Float Button"
msgstr ""

#: views/settings/wcfm-view-settings.php:112
msgid "Disable Category Checklist View"
msgstr ""

#: views/settings/wcfm-view-settings.php:113
msgid "Disable Ultimate Notice"
msgstr ""

#: views/settings/wcfm-view-settings.php:125
msgid "Modules"
msgstr ""

#: views/settings/wcfm-view-settings.php:129
msgid "Configure what to hide from your dashboard"
msgstr ""

#: views/settings/wcfm-view-settings.php:184
msgid "Reset to Default"
msgstr ""

#: views/settings/wcfm-view-settings.php:229
msgid "Product Type Categories"
msgstr ""

#: views/settings/wcfm-view-settings.php:252
msgid ""
"Create group of your Store Categories as per Product Types. Product Manager "
"will work according to that."
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:123
msgid "Preferred logo should be 200x200 px."
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:125
msgid "Shop Slug"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:125
msgid "Your shop slug is public and must be unique."
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:144
msgid "Preferred banner should be 1200x245 px."
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:166
msgid "Shop Template"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:199
msgid "Account Type"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:199
msgid "Current"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:199
msgid "Savings"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:202
msgid "ABA Routing Number"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:204
msgid "Destination Currency"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:205
msgid "Account IBAN"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:206
msgid "Account Holder Name"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:470 
#: views/settings/wcfm-view-wcmarketplace-settings.php:478
msgid "Shipping Zone"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:499
msgid "Shipping Rules"
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:94
msgid ""
"Enter the email for this vendor. This is the email where all notifications "
"will be send such as new orders and customer inquiries. You may enter more "
"than one email separating each with a comma."
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:233
msgid "Bank Payment (Mangopay)"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:246
msgid "CHECKING"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:246
msgid "SAVINGS"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:291
msgid "Postcode"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:292
msgid "Shipping Fee"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:293
msgid "Override Qty"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:303
msgid "Leave empty to disable"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:144
msgid "Manage Vendor"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:228
#, php-format
msgid "<strong>%s product</strong><br />"
msgid_plural "<strong>%s products</strong><br />"
msgstr[0] ""
msgstr[1] ""

#: views/vendors/wcfm-view-vendors-manage.php:230
msgid "total products posted"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:265 
#: views/vendors/wcfm-view-vendors-manage.php:267
msgid "Store Admin"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:304 
#: views/vendors/wcfm-view-vendors.php:76 
#: views/vendors/wcfm-view-vendors.php:92
msgid "Membership"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:312
msgid "Vendor not yet subscribed for a membership!"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:323
msgid "Send Message"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:342
msgid "Direct Message"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:353
msgid "Profile Update"
msgstr ""

#: views/vendors/wcfm-view-vendors.php:32
msgid "Vendors Listing"
msgstr ""

#: views/vendors/wcfm-view-vendors.php:77 
#: views/vendors/wcfm-view-vendors.php:93
msgid "No. of Products"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:24
msgid "Bookings"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:52 
#: views/wc_bookings/wcfm-view-wcbookings.php:70
msgid "Create Booking"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:64 
#: views/wc_bookings/wcfm-view-wcbookings-details.php:75 
#: views/wc_bookings/wcfm-view-wcbookings.php:81
msgid "Create Bookable"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:81 
#: views/wc_bookings/wcfm-view-wcbookings-details.php:70 
#: views/wc_bookings/wcfm-view-wcbookings.php:76
msgid "Manage Resources"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:44
msgid "Booking Details"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:51
msgid "Booking #"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:62 
#: views/wc_bookings/wcfm-view-wcbookings.php:86
msgid "Calendar View"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:86
msgid "Overview"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:92
msgid "Booking Created:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:97
msgid "Order Number:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:134 
#: views/wc_bookings/wcfm-view-wcbookings.php:115 
#: views/wc_bookings/wcfm-view-wcbookings.php:126
msgid "Booking"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:253
msgid "Email:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:259
msgid "Phone:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings.php:118 
#: views/wc_bookings/wcfm-view-wcbookings.php:129
msgid "Start Date"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings.php:119 
#: views/wc_bookings/wcfm-view-wcbookings.php:130
msgid "End Date"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:88 
#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:76
msgid "PayPal"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:90 
#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:78
msgid "Stripe"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:92
msgid "Bank Transfer"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:51 
#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal-request.php:37
msgid "Request successfully sent"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:53
msgid "Something went wrong please try again later"
msgstr ""

#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:80
msgid "Direct Bank Transfer"
msgstr ""

#: includes/libs/php/class-wcfm-fields.php:81 
#: includes/libs/php/class-wcfm-fields.php:137 
#: includes/libs/php/class-wcfm-fields.php:206 
#: includes/libs/php/class-wcfm-fields.php:260 
#: includes/libs/php/class-wcfm-fields.php:320 
#: includes/libs/php/class-wcfm-fields.php:379 
#: includes/libs/php/class-wcfm-fields.php:483 
#: includes/libs/php/class-wcfm-fields.php:550 
#: includes/libs/php/class-wcfm-fields.php:613 
#: includes/libs/php/class-wcfm-fields.php:676 
#: includes/libs/php/class-wcfm-fields.php:767 
#: includes/libs/php/class-wcfm-fields.php:818
msgid "This field is required."
msgstr ""

#: includes/libs/php/class-wcfm-fields.php:566
msgid "-Select a location-"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-payments.php:34 
#: views/withdrawal/wcmp/wcfm-view-payments.php:34
msgid "Transactions for: "
msgstr ""

#: views/withdrawal/dokan/wcfm-view-payments.php:51
msgid "Show all .."
msgstr ""

#: views/withdrawal/dokan/wcfm-view-payments.php:52
msgid "Approved"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-payments.php:53
msgid "Processing"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-payments.php:69 
#: views/withdrawal/dokan/wcfm-view-payments.php:77 
#: views/withdrawal/wcmp/wcfm-view-payments.php:68 
#: views/withdrawal/wcmp/wcfm-view-payments.php:79
msgid "Pay Mode"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:64 
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:42
msgid "Transaction History"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:104 
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:88
msgid "Request"
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-payments.php:64 
#: views/withdrawal/wcmp/wcfm-view-payments.php:75
msgid "Transc.ID"
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-payments.php:65 
#: views/withdrawal/wcmp/wcfm-view-payments.php:76
msgid "Commission IDs"
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-payments.php:67 
#: views/withdrawal/wcmp/wcfm-view-payments.php:78
msgid "Net Earnings"
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:36
msgid "Threshold for withdrawals: "
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:57 
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:66
msgid "Send Request"
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:58 
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:67
msgid "Order ID"
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:59 
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:68
msgid "Commission ID"
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:60 
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:69
msgid "My Earnings"
msgstr ""

#. Author URI of the plugin
msgid "https://wclovers.com"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-tab.php:31
msgid "There are no enquiries yet."
msgstr ""
"Este producto no tiene preguntas aún. ¡Escribe tus consultas al vendedor!"

#: views/settings/wcfm-view-dokan-settings.php:264 
#: views/settings/wcfm-view-wcmarketplace-settings.php:584 
#: views/settings/wcfm-view-wcpvendors-settings.php:122 
#: views/settings/wcfm-view-wcvendors-settings.php:348
msgid "Disable Purchase During Vacation"
msgstr "Desactivar ventas durante las vacaciones"

#: controllers/wcfm-controller-messages.php:209 views/wcfm-view-messages.php:91
msgid "Mark Read"
msgstr "Maecar como leído"

#: controllers/wcfm-controller-products.php:227 
#: views/wcfm-view-listings.php:37 views/wcfm-view-products.php:11 
#: controllers/articles/wcfm-controller-articles.php:138 
#: views/articles/wcfm-view-articles-manage.php:109 
#: views/articles/wcfm-view-articles.php:11 
#: views/products-manager/wcfm-view-products-manage.php:362
msgid "Published"
msgstr "Publicado"

#: controllers/wcfm-controller-products.php:333
msgid "Mark Featured"
msgstr "Marcar destacado"

#: controllers/wcfm-controller-products.php:346
msgid "Duplicate"
msgstr "Duplicar"

#: core/class-wcfm-catalog.php:190 views/enquiry/wcfm-view-enquiry-tab.php:34
msgid "Ask a Question"
msgstr "Escribe tu pregunta"

#: core/class-wcfm-enquiry.php:83
msgid "Enquiry Dashboard"
msgstr "Panel de preguntas y respuestas"

#: core/class-wcfm-enquiry.php:86
msgid "Enquiry Manager"
msgstr "Administrador de preguntas y respuestas"

#: core/class-wcfm-enquiry.php:243 core/class-wcfm-enquiry.php:277 
#: helpers/wcfm-core-functions.php:871 
#: views/enquiry/wcfm-view-enquiry-manage.php:63 
#: views/enquiry/wcfm-view-enquiry-manage.php:63 
#: views/enquiry/wcfm-view-enquiry.php:68
msgid "Enquiries"
msgstr "Preguntas y Respuestas"

#: core/class-wcfm-enquiry.php:292
msgid "There is no enquiry yet!!"
msgstr "No tienes preguntas aún."

#: core/class-wcfm-enquiry.php:311 core/class-wcfm.php:451 
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:86 
#: views/enquiry/wcfm-view-enquiry-manage.php:80
msgid "Enquiry"
msgstr "Pregunta"

#: core/class-wcfm-library.php:774
msgid "Choose Image"
msgstr "Escoge la imagen"

#: core/class-wcfm-non-ajax.php:53 views/wcfm-view-listings.php:38 
#: views/wcfm-view-products.php:13 views/articles/wcfm-view-articles.php:13
msgid "Pending"
msgstr "pendiente"

#: core/class-wcfm-non-ajax.php:135
msgid "No sales yet ..!!!"
msgstr "¡Aun no tienes ventas!"

#: core/class-wcfm-notification.php:52
#, php-format
msgid "You have received an Order <b>#%s</b>"
msgstr "Has recibido un pedido <b>#%s</b>"

#: core/class-wcfm-notification.php:69
#, php-format
msgid "You have received an Order <b>#%s</b> for <b>%s</b>"
msgstr "Tienes un nuevo pedido de <b>#%s</b> for <b>%s</b>"

#: core/class-wcfm-notification.php:111 core/class-wcfm.php:458 
#: views/wcfm-view-messages.php:85
msgid "Notifications"
msgstr "Notificaciones"

#: core/class-wcfm-notification.php:134
msgid "There is no notification yet!!"
msgstr "No tienes notificaciones"

#: core/class-wcfm-shortcode.php:102 views/wcfm-view-header-panels.php:49
msgid "Notification Board"
msgstr "Notificaciones"

#: core/class-wcfm-shortcode.php:106 views/wcfm-view-header-panels.php:53 
#: views/enquiry/wcfm-view-enquiry.php:61
msgid "Enquiry Board"
msgstr "Preguntas y respuestas"

#: core/class-wcfm-vendor-support.php:672 helpers/wcfm-core-functions.php:742
msgid "Choose Vendor ..."
msgstr "Escoge un Vendedor"

#: helpers/wcfm-core-functions.php:723
msgid "Please insert your enquiry before submit."
msgstr "Por favor coloque su pregunta antes de enviar"

#: helpers/wcfm-core-functions.php:724
msgid "Your enquiry successfully sent."
msgstr ""
"Su pregunta fue correctamente enviada, el vendedor responderá en breve."

#: helpers/wcfm-core-functions.php:725
msgid "Enquiry reply successfully published."
msgstr "Respuesta publicada correctamente."

#: views/wcfm-view-coupons-manage.php:49
msgid "Add Coupon"
msgstr "Agregar cupón "

#: views/wcfm-view-coupons.php:24
msgid "Coupons Listing"
msgstr "Lista de cupones"

#: views/wcfm-view-messages.php:35
msgid "Notification Dashboard"
msgstr "Panel de Notificaciones"

#: views/wcfm-view-orders.php:40
msgid "Orders Listing"
msgstr "Control de Pedidos"

#: views/wcfm-view-orders.php:84 views/wcfm-view-orders.php:101 
#: includes/reports/class-dokan-report-sales-by-date.php:789 
#: includes/reports/class-wcfm-report-sales-by-date.php:700 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:512 
#: includes/reports/class-wcpvendors-report-sales-by-date.php:415 
#: includes/reports/class-wcvendors-report-sales-by-date.php:436
msgid "Gross Sales"
msgstr "Ventas Totales"

#: views/wcfm-view-products.php:131 views/articles/wcfm-view-articles.php:86
msgid "Select a category"
msgstr "Seleccionar categoría"

#: views/wcfm-view-products.php:142
msgid "Show all product types"
msgstr "Tipos de productos"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:71
msgid "Reply for your enquiry"
msgstr "Respuesta a su preguta"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:72 
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:77
msgid "Hi"
msgstr "Hola"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:74
msgid ""
"We recently have a enquiry from you regarding \"{product_title}\". Please "
"check below for our input for the same: "
msgstr ""
"Hace poco recibimos una pregunta en el producto \"{product_title}\".  Esta "
"es la respuesta del vendedor a su pregunta:"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:78 
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:83
msgid "Check more details here"
msgstr "Revisa mas detalles aquí:"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:79 
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:84
msgid "Thank You"
msgstr "Gracias."

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:76
msgid "New enquiry for"
msgstr "Nueva pregunta en tu producto"

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:79
msgid ""
"You recently have a enquiry for \"{product_title}\". Please check below for "
"the details: "
msgstr ""
"Recibiste una pregunta en el producto \"{product_title}\". Revisa los "
"detalles:"

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:97 
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:109
#, php-format
msgid "You have received an Enquiry for <b>%s</b>"
msgstr "Tienes una nueva pregunta en tu producto <b>%s</b>"

#: controllers/orders/wcfm-controller-dokan-orders.php:219 
#: controllers/orders/wcfm-controller-orders.php:194 
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:270 
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:268 
#: controllers/orders/wcfm-controller-wcvendors-orders.php:267
msgid "Mark as Complete"
msgstr "Marcar como completo"

#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:279 
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:276 
#: controllers/orders/wcfm-controller-wcvendors-orders.php:275
msgid "Mark Shipped"
msgstr "Marcar como enviado"

#: controllers/wc_bookings/wcfm-controller-wcbookings.php:174
msgid "Mark as Confirmed"
msgstr "Marcar como confirmado"

#: includes/reports/class-dokan-report-sales-by-date.php:562 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:198 
#: includes/reports/class-wcpvendors-report-sales-by-date.php:194 
#: includes/reports/class-wcvendors-report-sales-by-date.php:177
#, php-format
msgid "%s total earnings"
msgstr "%s Ganancias Totales"

#: includes/reports/class-dokan-report-sales-by-date.php:858 
#: includes/reports/class-wcfm-report-sales-by-date.php:759 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:565 
#: includes/reports/class-wcpvendors-report-sales-by-date.php:468 
#: includes/reports/class-wcvendors-report-sales-by-date.php:489
msgid "Sales Report by Date"
msgstr "Reporte de ventas por fecha"

#: includes/reports/class-wcfm-report-analytics.php:266 
#: views/dashboard/wcfm-view-dashboard.php:199 
#: views/dashboard/wcfm-view-dokan-dashboard.php:210 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:227 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:230 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:214
msgid "Store Analytics"
msgstr "Comportamiento de tu Tienda"

#: includes/reports/class-wcmarketplace-report-sales-by-date.php:173 
#: includes/reports/class-wcvendors-report-sales-by-date.php:170
#, php-format
msgid "%s gross sales in this period"
msgstr "%s Ventas totales en este periodo"

#: includes/reports/class-wcmarketplace-report-sales-by-date.php:537 
#: includes/reports/class-wcpvendors-report-sales-by-date.php:440 
#: includes/reports/class-wcvendors-report-sales-by-date.php:461
msgid "Shipping Amounts"
msgstr "Montos de Envío"

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:44
#, php-format
msgid "Welcome to %s Dashboard"
msgstr "Tu Panel de Control"

#: views/dashboard/wcfm-view-dashboard.php:110
msgid "gross sales in last 7 days"
msgstr "Ventas totales últimos 7 días"

#: views/dashboard/wcfm-view-dashboard.php:149 
#: views/dashboard/wcfm-view-dokan-dashboard.php:163 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:179 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:183 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:167 
#: views/vendors/wcfm-view-vendors-manage.php:242
#, php-format
msgid "<strong>%s item</strong><br />"
msgid_plural "<strong>%s items</strong><br />"
msgstr[0] "<strong>%s producto</strong><br />"
msgstr[1] "<strong>%s productos</strong><br />"

#: views/dashboard/wcfm-view-dashboard.php:162 
#: views/dashboard/wcfm-view-dokan-dashboard.php:174 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:190 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:194 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:178
#, php-format
msgid "<strong>%s order</strong><br />"
msgid_plural "<strong>%s orders</strong><br />"
msgstr[0] "<strong>%s órden</strong><br />"
msgstr[1] "<strong>%s órdenes</strong><br />"

#: views/dashboard/wcfm-view-dashboard.php:245 
#: views/dashboard/wcfm-view-dokan-dashboard.php:254 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:271 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:274 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:258
msgid "Store Stats"
msgstr "Estadísticas de tu tienda"

#: views/dashboard/wcfm-view-dashboard.php:269 
#: views/dashboard/wcfm-view-dokan-dashboard.php:280 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:297 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:305 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:284
#, php-format
msgid "<strong>%s order</strong> - processing"
msgid_plural "<strong>%s orders</strong> - processing"
msgstr[0] "<strong>%s órden</strong> - procesando"
msgstr[1] "<strong>%s órdenes</strong> - procesando"

#: views/dashboard/wcfm-view-dashboard.php:285 
#: views/dashboard/wcfm-view-dokan-dashboard.php:297 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:314 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:322 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:301
#, php-format
msgid "<strong>%s product</strong> - low in stock"
msgid_plural "<strong>%s products</strong> - low in stock"
msgstr[0] "<strong>%s producto</strong> - con bajas existencias"
msgstr[1] "<strong>%s productos</strong> - con bajas existencias"

#: views/dashboard/wcfm-view-dashboard.php:291 
#: views/dashboard/wcfm-view-dokan-dashboard.php:303 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:320 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:328 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:307
#, php-format
msgid "<strong>%s product</strong> - out of stock"
msgid_plural "<strong>%s products</strong> - out of stock"
msgstr[0] "<strong>%s producto</strong> - Agotado"
msgstr[1] "<strong>%s productos</strong> - Agotados"

#: views/dashboard/wcfm-view-dashboard.php:347 
#: views/dashboard/wcfm-view-dokan-dashboard.php:360 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:378 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:385 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:364
msgid "Latest Topics"
msgstr "Avisos recientes"

#: views/dashboard/wcfm-view-dokan-dashboard.php:146 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:162 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:166 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:150 
#: views/vendors/wcfm-view-vendors-manage.php:198
msgid "gross sales in this month"
msgstr "Ventas totales en este mes"

#: views/dashboard/wcfm-view-dokan-dashboard.php:155 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:171 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:175 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:159 
#: views/vendors/wcfm-view-vendors-manage.php:216
msgid "earnings in this month"
msgstr "Ganancias en este mes"

#: views/dashboard/wcfm-view-dokan-dashboard.php:164 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:180 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:184 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:168 
#: views/vendors/wcfm-view-vendors-manage.php:244
msgid "sold in this month"
msgstr "Ventas en este mes"

#: views/dashboard/wcfm-view-dokan-dashboard.php:175 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:191 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:195 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:179
msgid "received in this month"
msgstr "Pedidos recibidos este mes"

#: views/enquiry/wcfm-view-enquiry-manage.php:53
msgid "Manage Enquiry"
msgstr "Administrador de preguntas"

#: views/enquiry/wcfm-view-enquiry-manage.php:60
msgid "Edit Enquiry"
msgstr "Editar pregunta"

#: views/enquiry/wcfm-view-enquiry-manage.php:60
msgid "Add Enquiry"
msgstr "Agregar pregunta"

#: views/enquiry/wcfm-view-enquiry-manage.php:81 
#: views/enquiry/wcfm-view-enquiry.php:117 
#: views/enquiry/wcfm-view-enquiry.php:128
msgid "Reply"
msgstr "Respuesta"

#: views/enquiry/wcfm-view-enquiry-manage.php:82
msgid "Is Private?"
msgstr ""
"Marcar como privado. (solo el cliente, usted y los administradores podrán "
"verlo)"

#: views/enquiry/wcfm-view-enquiry-manage.php:83
msgid "Notify Customer"
msgstr "Notificar al cliente"

#: views/enquiry/wcfm-view-enquiry-tab.php:26
msgid "General Enquiries"
msgstr "Preguntas y Respuestas"

#: views/enquiry/wcfm-view-enquiry-tab.php:42
msgid "Your email address will not be published."
msgstr "Su direccion email no será publicada."

#: views/enquiry/wcfm-view-enquiry-tab.php:46
msgid "Your enquiry"
msgstr ""
"Su pregunta (no coloque datos de contacto o su pregunta será eliminada)."

#: views/enquiry/wcfm-view-enquiry.php:113 
#: views/enquiry/wcfm-view-enquiry.php:124
msgid "Query"
msgstr "Pregunta"

#: views/enquiry/wcfm-view-enquiry.php:114 
#: views/enquiry/wcfm-view-enquiry.php:125 
#: views/wc_bookings/wcfm-view-wcbookings.php:116 
#: views/wc_bookings/wcfm-view-wcbookings.php:127
msgid "Product"
msgstr "Producto"

#: views/enquiry/wcfm-view-enquiry.php:115 
#: views/enquiry/wcfm-view-enquiry.php:126
msgid "Customer"
msgstr "Cliente"

#: views/products-manager/wcfm-view-products-manage.php:767
msgid "Add attribute"
msgstr "Agregar atributo"

#: views/products-manager/wcfm-view-products-manage.php:800
msgid "Choose option"
msgstr "Escoge una opción"

#: controllers/wcfm-controller-coupons.php:98 
#: controllers/wcfm-controller-coupons.php:100 
#: controllers/wcfm-controller-knowledgebase.php:77 
#: controllers/wcfm-controller-listings.php:101 
#: controllers/wcfm-controller-products.php:355 
#: controllers/wcfm-controller-products.php:358 
#: controllers/wcfm-controller-reports-out-of-stock.php:107 
#: controllers/wcfm-controller-reports-out-of-stock.php:110 
#: core/class-wcfm-frontend.php:168 
#: controllers/articles/wcfm-controller-articles.php:156 
#: controllers/articles/wcfm-controller-articles.php:159 
#: controllers/enquiry/wcfm-controller-enquiry.php:154 
#: controllers/notice/wcfm-controller-notices.php:80 
#: views/notice/wcfm-view-notice-view.php:59
msgid "Edit"
msgstr "Editar"

#: controllers/wcfm-controller-knowledgebase.php:78 
#: controllers/wcfm-controller-listings.php:127 
#: controllers/wcfm-controller-messages.php:215 
#: controllers/wcfm-controller-products.php:356 
#: controllers/wcfm-controller-products.php:359 
#: controllers/wcfm-controller-reports-out-of-stock.php:108 
#: controllers/wcfm-controller-reports-out-of-stock.php:111 
#: core/class-wcfm-frontend.php:172 
#: controllers/articles/wcfm-controller-articles.php:157 
#: controllers/articles/wcfm-controller-articles.php:160 
#: controllers/enquiry/wcfm-controller-enquiry.php:155 
#: controllers/notice/wcfm-controller-notices.php:81
msgid "Delete"
msgstr "Eliminar"

#: controllers/wcfm-controller-listings.php:98 
#: controllers/wcfm-controller-products.php:324 
#: controllers/wcfm-controller-reports-out-of-stock.php:105 
#: controllers/articles/wcfm-controller-articles.php:153 
#: controllers/notice/wcfm-controller-notices.php:77 
#: views/articles/wcfm-view-articles-manage.php:370 
#: views/notice/wcfm-view-notice-manage.php:59 
#: views/products-manager/wcfm-view-products-manage.php:884
msgid "View"
msgstr "Ver"

#: controllers/wcfm-controller-message-sent.php:47
msgid "Message sent successfully"
msgstr "Mensaje enviado "

#: controllers/wcfm-controller-messages.php:155 
#: controllers/wcfm-controller-messages.php:179 
#: views/wcfm-view-products.php:201 views/wcfm-view-products.php:223 
#: views/enquiry/wcfm-view-enquiry.php:116 
#: views/enquiry/wcfm-view-enquiry.php:127 
#: views/settings/wcfm-view-dokan-settings.php:147 
#: views/settings/wcfm-view-wcmarketplace-settings.php:116 
#: views/settings/wcfm-view-wcvendors-settings.php:115 
#: views/vendors/wcfm-view-vendors-manage.php:258 
#: views/vendors/wcfm-view-vendors.php:75 
#: views/vendors/wcfm-view-vendors.php:91
msgid "Store"
msgstr "Tienda "

#: controllers/wcfm-controller-messages.php:164 
#: controllers/wcfm-controller-messages.php:170 
#: controllers/wcfm-controller-messages.php:187 
#: controllers/wcfm-controller-messages.php:193
msgid "You"
msgstr "Tu"

#: controllers/wcfm-controller-messages.php:177 
#: core/class-wcfm-vendor-support.php:670 views/wcfm-view-listings.php:36 
#: views/wcfm-view-messages.php:97 views/wcfm-view-products.php:10 
#: views/articles/wcfm-view-articles.php:10 
#: views/wc_bookings/wcfm-view-wcbookings.php:19
msgid "All"
msgstr "Todo "

#: controllers/wcfm-controller-products.php:251 
#: views/wcfm-view-capability.php:139
msgid "Grouped"
msgstr "Agrupado"

#: controllers/wcfm-controller-products.php:253
msgid "External/Affiliate"
msgstr "Externo/Afiliado"

#: controllers/wcfm-controller-products.php:257 
#: views/wcfm-view-products.php:156 
#: views/products-manager/wcfm-view-products-manage.php:405
msgid "Virtual"
msgstr "Virtual"

#: controllers/wcfm-controller-products.php:259 
#: views/wcfm-view-products.php:152
msgid "Downloadable"
msgstr "Descargable"

#: controllers/wcfm-controller-products.php:261 
#: views/wcfm-view-capability.php:137
msgid "Simple"
msgstr "Simple"

#: controllers/wcfm-controller-products.php:265 
#: views/wcfm-view-capability.php:138
msgid "Variable"
msgstr "Variable"

#: controllers/wcfm-controller-products.php:267
msgid "Subscription"
msgstr "Suscripción "

#: controllers/wcfm-controller-products.php:269
msgid "Variable Subscription"
msgstr "Suscripción variables"

#: controllers/wcfm-controller-profile.php:107 
#: controllers/vendors/wcfm-controller-vendors-manage.php:40
msgid "Profile saved successfully"
msgstr "Perfil guardado con éxito."

#: core/class-wcfm-admin.php:154 views/wcfm-view-capability.php:231 
#: controllers/orders/wcfm-controller-dokan-orders.php:223 
#: controllers/orders/wcfm-controller-orders.php:198 
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:274 
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:272 
#: controllers/orders/wcfm-controller-wcvendors-orders.php:271 
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:177
msgid "View Details"
msgstr "Ver Detalles "

#: core/class-wcfm-admin.php:222 core/class-wcfm-admin.php:262 
#: core/class-wcfm-admin.php:263 core/class-wcfm-admin.php:264
msgid "WCFM View"
msgstr "Vista WCFM"

#: core/class-wcfm-admin.php:281 core/class-wcfm-admin.php:291 
#: core/class-wcfm-admin.php:293 core/class-wcfm-admin.php:295 
#: core/class-wcfm-wcvendors.php:178 core/class-wcfm-wcvendors.php:178
msgid "WCFM Home"
msgstr "Inicio WCFM"

#: core/class-wcfm-admin.php:338
msgid "This page should contain \"[wc_frontend_manager]\" short code"
msgstr "Esta página debe contener el short code \"[wc_frontend_manager]\" "

#: core/class-wcfm-customfield-support.php:44 
#: views/products-manager/wcfm-view-products-manage.php:822
msgid "Enable"
msgstr "Habilitar "

#: core/class-wcfm-customfield-support.php:51 views/wcfm-view-products.php:193 
#: views/wcfm-view-products.php:215 views/articles/wcfm-view-articles.php:113 
#: views/articles/wcfm-view-articles.php:124 
#: views/enquiry/wcfm-view-enquiry-tab.php:52 
#: views/products-manager/wcfm-view-products-manage.php:754
msgid "Name"
msgstr "Nombre "

#: core/class-wcfm-dokan.php:109 core/class-wcfm-vendor-support.php:740 
#: core/class-wcfm-vendor-support.php:748 
#: core/class-wcfm-vendor-support.php:755 
#: core/class-wcfm-vendor-support.php:762 
#: core/class-wcfm-wcmarketplace.php:153 core/class-wcfm-wcpvendors.php:106 
#: core/class-wcfm-wcvendors.php:143
msgid "Shop"
msgstr "Tienda"

#: core/class-wcfm-frontend.php:143 core/class-wcfm-frontend.php:143 
#: views/dashboard/wcfm-view-dashboard.php:92 
#: views/dashboard/wcfm-view-dokan-dashboard.php:127 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:144 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:148 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:131 
#: views/settings/wcfm-view-settings.php:95 
#: views/settings/wcfm-view-settings.php:210
msgid "Dashboard"
msgstr "Resumen"

#: core/class-wcfm-library.php:696
msgid "Processing..."
msgstr "Trabajando en ello..."

#: core/class-wcfm-library.php:696
msgid "Search:"
msgstr "Buscar: "

#: core/class-wcfm-library.php:696
msgid "Show _MENU_ entries"
msgstr "Mostar _MENU_ entradas"

#: core/class-wcfm-library.php:696
msgid "Showing _START_ to _END_ of _TOTAL_ entries"
msgstr "Mostando_START_ de _END_ una _TOTAL_ entradas "

#: core/class-wcfm-library.php:696
msgid "Showing 0 to 0 of 0 entries"
msgstr "Mostrando 0 to 0 of 0 entradas"

#: core/class-wcfm-library.php:696
msgid "(filtered _MAX_ entries of total)"
msgstr "(filtradas _MAX_ entradas en totall)"

#: core/class-wcfm-library.php:696
msgid "Loading..."
msgstr "Cargando..."

#: core/class-wcfm-library.php:696
msgid "No matching records found"
msgstr "Tu búsqueda no dio resultados"

#: core/class-wcfm-library.php:696
msgid "No data in the table"
msgstr "Sin Resultados"

#: core/class-wcfm-library.php:696
msgid "First"
msgstr "Primero"

#: core/class-wcfm-library.php:696
msgid "Previous"
msgstr "Anterior "

#: core/class-wcfm-library.php:696
msgid "Next"
msgstr "Siguiente "

#: core/class-wcfm-library.php:696
msgid "Last"
msgstr "Ultimo"

#: core/class-wcfm-non-ajax.php:53 views/wcfm-view-coupons-manage.php:100 
#: views/wcfm-view-products.php:12 
#: views/articles/wcfm-view-articles-manage.php:357 
#: views/articles/wcfm-view-articles.php:12 
#: views/products-manager/wcfm-view-products-manage.php:871
msgid "Draft"
msgstr "Borrador "

#: core/class-wcfm-non-ajax.php:185
msgid "View WCFM settings"
msgstr "Ver Ajustes WCFM"

#: core/class-wcfm-non-ajax.php:185 core/class-wcfm-query.php:169 
#: core/class-wcfm.php:431 views/wcfm-view-capability.php:95 
#: views/settings/wcfm-view-dokan-settings.php:125 
#: views/settings/wcfm-view-settings.php:53 
#: views/settings/wcfm-view-wcmarketplace-settings.php:94 
#: views/settings/wcfm-view-wcpvendors-settings.php:63 
#: views/settings/wcfm-view-wcvendors-settings.php:93
msgid "Settings"
msgstr "Ajustes "

#: core/class-wcfm-non-ajax.php:202
msgid "View WCFM documentation"
msgstr "Ver Documentación WCFM"

#: core/class-wcfm-non-ajax.php:202
msgid "Documentation"
msgstr "Documentación"

#: core/class-wcfm-non-ajax.php:210
msgid "Add more power to your WCFM"
msgstr "Añade mas poder a WCFM"

#: core/class-wcfm-non-ajax.php:210
msgid "WCFM Ultimate"
msgstr "WCFM Ultimate"

#: core/class-wcfm-non-ajax.php:257 helpers/wcfm-core-functions.php:743 
#: views/wcfm-view-coupons-manage.php:64 views/wcfm-view-coupons.php:43 
#: views/wcfm-view-knowledgebase.php:38 views/wcfm-view-listings.php:91 
#: views/wcfm-view-menu.php:94 views/wcfm-view-products-export.php:64 
#: views/wcfm-view-products.php:110 
#: views/articles/wcfm-view-articles-manage.php:133 
#: views/articles/wcfm-view-articles.php:65 
#: views/notice/wcfm-view-notices.php:37 
#: views/products-manager/wcfm-view-products-manage.php:386
msgid "Add New"
msgstr "Publicar "

#: core/class-wcfm-query.php:116
msgid "Products Dashboard"
msgstr "Publicaciones "

#: core/class-wcfm-query.php:121
#, php-format
msgid "Product Manager -%s"
msgstr "Producto Nuevo -%s"

#: core/class-wcfm-query.php:121
msgid "Product Manager"
msgstr "Producto Nuevo "

#: core/class-wcfm-query.php:133
msgid "Coupons Dashboard"
msgstr "Cupones "

#: core/class-wcfm-query.php:138
#, php-format
msgid "Coupon Manager -%s"
msgstr "Administrador de cupón -%s"

#: core/class-wcfm-query.php:138
msgid "Coupon Manager"
msgstr "Administrador de cupón"

#: core/class-wcfm-query.php:141
msgid "Orders Dashboard"
msgstr "Ventas "

#: core/class-wcfm-query.php:145
#, php-format
msgid "Order Details #%s"
msgstr "Detalle de la Venta #%s"

#: core/class-wcfm-query.php:145 views/wcfm-view-orders-details.php:102
msgid "Order Details"
msgstr "Detalle de la Venta"

#: core/class-wcfm-query.php:148
msgid "Reports - Sales by Date"
msgstr "Reportes - Ventas por Fecha "

#: core/class-wcfm-query.php:151
msgid "Reports - Sales by Product"
msgstr "Reportes - Ventas por Producto"

#: core/class-wcfm-query.php:154
msgid "Reports - Coupons by Date"
msgstr "Reportes - Cupones por fecha"

#: core/class-wcfm-query.php:157
msgid "Reports - Out of Stock"
msgstr "Reportes - Sin Stock "

#: core/class-wcfm-query.php:160
msgid "Reports - Low in Stock"
msgstr "Reportes - Con Stock Bajo"

#: core/class-wcfm-query.php:163 views/settings/wcfm-view-settings.php:152 
#: views/settings/wcfm-view-settings.php:158
msgid "Analytics"
msgstr "Estadísticas "

#: core/class-wcfm-query.php:166 core/class-wcfm.php:460 
#: views/wcfm-view-header-panels.php:45 views/wcfm-view-profile.php:126 
#: views/settings/wcfm-view-wcpvendors-settings.php:95
msgid "Profile"
msgstr "Perfil "

#: core/class-wcfm-query.php:172 core/class-wcfm.php:459 
#: views/wcfm-view-header-panels.php:61 
#: views/wcfm-view-knowledgebase-manage.php:52 
#: views/wcfm-view-knowledgebase-manage.php:52 
#: views/wcfm-view-knowledgebase.php:26
msgid "Knowledgebase"
msgstr "Tutoriales"

#: core/class-wcfm-query.php:184 core/class-wcfm.php:457
msgid "Notice"
msgstr "Notificaciones"

#: core/class-wcfm-query.php:187
msgid "Message Dashboard"
msgstr "Mensajes"

#: core/class-wcfm-thirdparty-support.php:181 helpers/class-wcfm-setup.php:557 
#: views/wcfm-view-listings.php:27
msgid "Listings"
msgstr "Listas"

#: core/class-wcfm-thirdparty-support.php:325 views/wcfm-view-coupons.php:56 
#: views/wcfm-view-coupons.php:66 views/wcfm-view-messages.php:113 
#: views/wcfm-view-messages.php:126 views/wcfm-view-products.php:198 
#: views/wcfm-view-products.php:220
msgid "Type"
msgstr "Tipo "

#: core/class-wcfm-thirdparty-support.php:326 views/wcfm-view-messages.php:115 
#: views/wcfm-view-messages.php:128
msgid "From"
msgstr "Desde "

#: core/class-wcfm-thirdparty-support.php:327 views/wcfm-view-messages.php:116 
#: views/wcfm-view-messages.php:129
msgid "To"
msgstr "Para"

#: core/class-wcfm-thirdparty-support.php:328 
#: core/class-wcfm-wcbookings.php:188
msgid "Bookable"
msgstr "Se puede reservar"

#: core/class-wcfm-vendor-support.php:155 
#: views/vendors/wcfm-view-vendors-manage.php:178 
#: views/vendors/wcfm-view-vendors.php:24
msgid "Vendors"
msgstr "Vendedores "

#: core/class-wcfm-vendor-support.php:233 
#: core/class-wcfm-vendor-support.php:349 
#: core/class-wcfm-vendor-support.php:388 
#: core/class-wcfm-vendor-support.php:404 
#: core/class-wcfm-vendor-support.php:433 
#: core/class-wcfm-wcmarketplace.php:560 core/class-wcfm-wcpvendors.php:351 
#: core/class-wcfm-wcvendors.php:477 
#: views/settings/wcfm-view-wcpvendors-settings.php:148
msgid "Commission"
msgstr "Comisión "

#: core/class-wcfm-vendor-support.php:354 
#: core/class-wcfm-vendor-support.php:394 
#: core/class-wcfm-vendor-support.php:399 
#: core/class-wcfm-vendor-support.php:423 
#: core/class-wcfm-vendor-support.php:428
msgid "Commission(%)"
msgstr "Comisión(%)"

#: core/class-wcfm-wcbookings.php:94
msgid "Bookings Dashboard"
msgstr "Reserva "

#: core/class-wcfm-wcbookings.php:112
#, php-format
msgid "Booking Details #%s"
msgstr "Detalles de la Reserva #%s"

#: core/class-wcfm-wcmarketplace.php:177
msgid "WCMp"
msgstr "WCMp"

#: core/class-wcfm-wcmarketplace.php:184
msgid "WCFM"
msgstr "WCFM"

#: core/class-wcfm-wcmarketplace.php:197 
#: views/products-manager/wcfm-view-products-manage.php:358
msgid "Add Product"
msgstr "Añadir Producto"

#: core/class-wcfm-wcmarketplace.php:204 core/class-wcfm.php:402 
#: views/wcfm-view-capability.php:121 views/wcfm-view-products.php:32
msgid "Products"
msgstr "Productos "

#: core/class-wcfm-wcmarketplace.php:226
msgid "by Date"
msgstr "Por Fecha "

#: core/class-wcfm-wcmarketplace.php:233 views/wcfm-view-reports-menu.php:5 
#: views/products-manager/wcfm-view-products-manage.php:674 
#: views/products-manager/wcfm-view-products-manage.php:830
msgid "Out of stock"
msgstr "Sin Stock "

#: core/class-wcfm-wcmarketplace.php:565 core/class-wcfm-wcmarketplace.php:701 
#: core/class-wcfm-wcpvendors.php:352 core/class-wcfm-wcpvendors.php:443 
#: core/class-wcfm-wcvendors.php:481 core/class-wcfm-wcvendors.php:591 
#: views/wcfm-view-capability.php:149 views/wcfm-view-orders-details.php:570 
#: views/wcfm-view-orders-details.php:825 views/wcfm-view-profile.php:219 
#: includes/reports/class-dokan-report-sales-by-date.php:814 
#: includes/reports/class-wcfm-report-sales-by-date.php:708 
#: views/products-manager/wcfm-view-products-manage.php:702 
#: views/products-manager/wcfm-view-products-manage.php:809 
#: views/settings/wcfm-view-dokan-settings.php:284 
#: views/settings/wcfm-view-wcmarketplace-settings.php:441 
#: views/settings/wcfm-view-wcvendors-settings.php:279
msgid "Shipping"
msgstr "Envío "

#: core/class-wcfm-wcmarketplace.php:571 core/class-wcfm-wcmarketplace.php:712 
#: core/class-wcfm-wcpvendors.php:353 core/class-wcfm-wcpvendors.php:452 
#: core/class-wcfm-wcvendors.php:487 core/class-wcfm-wcvendors.php:602 
#: views/wcfm-view-orders-details.php:360 
#: views/wcfm-view-orders-details.php:361 
#: views/products-manager/wcfm-view-products-manage.php:726
msgid "Tax"
msgstr "Impuesto"

#: core/class-wcfm-wcmarketplace.php:576 core/class-wcfm-wcpvendors.php:355 
#: core/class-wcfm-wcvendors.php:492 views/wcfm-view-orders-details.php:352
msgid "Total"
msgstr "Total"

#: core/class-wcfm-wcmarketplace.php:690 core/class-wcfm-wcpvendors.php:434 
#: core/class-wcfm-wcvendors.php:581
msgid "Line Commission"
msgstr "Comisión de línea"

#: core/class-wcfm-wcsubscriptions.php:55
msgid "Subscriptions"
msgstr "Suscripciones "

#: core/class-wcfm-wcsubscriptions.php:56
msgid "Variable Subscriptions"
msgstr "Suscripciones variables"

#: core/class-wcfm.php:412 views/wcfm-view-capability.php:215 
#: views/wcfm-view-coupons.php:16
msgid "Coupons"
msgstr "Cupones "

#: core/class-wcfm.php:421 views/wcfm-view-capability.php:226 
#: views/wcfm-view-orders.php:30
msgid "Orders"
msgstr "Ventas "

#: core/class-wcfm.php:426 views/wcfm-view-capability.php:252
msgid "Reports"
msgstr "Reportes"

#: core/class-wcfm.php:498
msgid "Menu Background Color"
msgstr "Color de fondo para el menú"

#: core/class-wcfm.php:499
msgid "Menu Item Background"
msgstr "Fondo del menú"

#: core/class-wcfm.php:501
msgid "Menu Active Item Background"
msgstr "Menú Fondo del elemento activo"

#: helpers/class-wcfm-install.php:97
msgctxt "page_slug"
msgid "wcfm"
msgstr "wcfm"

#: helpers/class-wcfm-install.php:97
msgid "WC Frontend Manager"
msgstr "Administrador "

#: helpers/class-wcfm-setup.php:61 views/settings/wcfm-view-settings.php:170
msgid "Style"
msgstr "Estilo"

#: helpers/class-wcfm-setup.php:540
msgid "Submit Products"
msgstr "Publicar Productos"

#: helpers/class-wcfm-setup.php:541 views/wcfm-view-capability.php:126
msgid "Publish Products"
msgstr "Publicar Productos"

#: helpers/class-wcfm-setup.php:542 views/wcfm-view-capability.php:127
msgid "Edit Live Products"
msgstr "Editar productos en vivo"

#: helpers/class-wcfm-setup.php:543 views/wcfm-view-capability.php:129
msgid "Delete Products"
msgstr "Eliminar Productos"

#: helpers/class-wcfm-setup.php:547 views/wcfm-view-capability.php:166 
#: views/wcfm-view-capability.php:169
msgid "Manage Bookings"
msgstr "Administrar Reservas"

#: helpers/class-wcfm-setup.php:552 views/wcfm-view-capability.php:184 
#: views/wcfm-view-capability.php:187
msgid "Manage Subscriptions"
msgstr "Administrar Suscripciones"

#: helpers/class-wcfm-setup.php:557 views/wcfm-view-capability.php:192
msgid "by WP Job Manager."
msgstr "Por WP Trabajo "

#: helpers/class-wcfm-setup.php:561 views/wcfm-view-capability.php:229
msgid "View Orders"
msgstr "Ver Ventas"

#: helpers/class-wcfm-setup.php:565 views/wcfm-view-capability.php:255
msgid "View Reports"
msgstr "Ver Reportes"

#: helpers/wcfm-core-functions.php:6
#, php-format
msgid ""
"%sWooCommerce Frontend Manager is inactive.%s The %sWooCommerce plugin%s "
"must be active for the WooCommerce Frontend Manager to work. Please "
"%sinstall & activate WooCommerce%s"
msgstr ""
"%sWooCommerce Frontend Manager esta inactivo.%s The %sWooCommerce plugin%s "
"debe estar activo para que WCFM funcione. Por favor %instala & activa "
"WooCommerce%s"

#: helpers/wcfm-core-functions.php:16
#, php-format
msgid ""
"%sOpps ..!!!%s You are using %sWC %s. WCFM works only with %sWC 3.0+%s. "
"PLease upgrade your WooCommerce version now to make your life easier and "
"peaceful by using WCFM."
msgstr ""
"%sOpps ..!!!%s estas usando %sWC %s. WCFM funciona solo con %sWC 3.0+%s. Por "
"favor actualiza a la versión mas reciente de Woocommerce para hacer tu vida "
"mas fácil usando WCFM"

#: helpers/wcfm-core-functions.php:648
msgid "Please insert Product Title before submit."
msgstr "Inserta el titulo del producto antes de publicarlo."

#: helpers/wcfm-core-functions.php:649
msgid "Product SKU must be unique."
msgstr "El SKU del producto debe ser único."

#: helpers/wcfm-core-functions.php:650
msgid "Variation SKU must be unique."
msgstr "El SKU de la variación debe ser único."

#: helpers/wcfm-core-functions.php:651
msgid "Product Successfully Saved."
msgstr "Producto Guardado."

#: helpers/wcfm-core-functions.php:653
msgid "Product Successfully Published."
msgstr "Producto Publicado."

#: helpers/wcfm-core-functions.php:665
msgid "Please insert atleast Coupon Title before submit."
msgstr "Por favor ingresa el titulo del cupón antes de publicarlo"

#: helpers/wcfm-core-functions.php:666
msgid "Coupon Successfully Saved."
msgstr "Cupón guardado con éxito "

#: helpers/wcfm-core-functions.php:667
msgid "Coupon Successfully Published."
msgstr "Cupón publicado con éxito "

#: helpers/wcfm-core-functions.php:770 views/wcfm-view-orders.php:82 
#: views/wcfm-view-orders.php:99 
#: views/wc_bookings/wcfm-view-wcbookings.php:117 
#: views/wc_bookings/wcfm-view-wcbookings.php:128
msgid "Order"
msgstr "Venta "

#: views/wcfm-view-capability.php:111
msgid "Vendors Capability"
msgstr "Capacidades de los Vendedores"

#: views/wcfm-view-capability.php:134
msgid "Types"
msgstr "Tipos"

#: views/wcfm-view-capability.php:140
msgid "External / Affiliate"
msgstr "Externo/Afiliado "

#: views/wcfm-view-capability.php:145
msgid "Panels"
msgstr "Paneles"

#: views/wcfm-view-capability.php:148 
#: views/products-manager/wcfm-view-products-manage.php:665
msgid "Inventory"
msgstr "Inventario "

#: views/wcfm-view-capability.php:150
msgid "Taxes"
msgstr "Impuestos"

#: views/wcfm-view-capability.php:151 
#: views/products-manager/wcfm-view-products-manage.php:843
msgid "Linked"
msgstr "Vinculado"

#: views/wcfm-view-capability.php:152 
#: views/products-manager/wcfm-view-products-manage.php:744 
#: views/products-manager/wcfm-view-products-manage.php:751
msgid "Attributes"
msgstr "Atributos "

#: views/wcfm-view-capability.php:153
msgid "Advanced"
msgstr "Avanzado "

#: views/wcfm-view-capability.php:162
msgid "Miscellaneous"
msgstr "Diverso"

#: views/wcfm-view-capability.php:169
msgid "Install WC Bookings to enable this feature."
msgstr "Instala WC Bookings para habilitar esta característica. "

#: views/wcfm-view-capability.php:187
msgid "Install WC Subscriptions to enable this feature."
msgstr "Instala WC Subscriptions para habilitar esta característica."

#: views/wcfm-view-capability.php:192 views/wcfm-view-capability.php:195
msgid "Associate Listings"
msgstr "Listados de Asociados"

#: views/wcfm-view-capability.php:195
msgid "Install WP Job Manager to enable this feature."
msgstr "Instala WP Job Manager para habilitar esta característica."

#: views/wcfm-view-capability.php:218
msgid "Submit Coupons"
msgstr "Publicar Cupones"

#: views/wcfm-view-capability.php:219
msgid "Publish Coupons"
msgstr "Publicar Cupones"

#: views/wcfm-view-capability.php:220
msgid "Edit Live Coupons"
msgstr "Editar cupones en vivo"

#: views/wcfm-view-capability.php:221
msgid "Delete Coupons"
msgstr "Eliminar Cupones"

#: views/wcfm-view-capability.php:234
msgid "Customer Email"
msgstr "Email del Cliente"

#: views/wcfm-view-capability.php:235
msgid "View Comments"
msgstr "Ver Comentarios"

#: views/wcfm-view-capability.php:236
msgid "Submit Comments"
msgstr "Publicar Comentarios"

#: views/wcfm-view-capability.php:237 
#: includes/reports/class-wcfm-report-analytics.php:176 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:290 
#: includes/reports/class-wcvendors-report-sales-by-date.php:268
msgid "Export CSV"
msgstr "Exportar CSV"

#: views/wcfm-view-capability.php:241 views/wcfm-view-capability.php:246 
#: views/wcfm-view-orders-details.php:134 
#: views/wcfm-view-orders-details.php:139 
#: views/wcfm-view-orders-details.php:141 
#: controllers/orders/wcfm-controller-dokan-orders.php:229 
#: controllers/orders/wcfm-controller-dokan-orders.php:236 
#: controllers/orders/wcfm-controller-orders.php:202 
#: controllers/orders/wcfm-controller-orders.php:206 
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:286 
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:293 
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:283 
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:290 
#: controllers/orders/wcfm-controller-wcvendors-orders.php:282 
#: controllers/orders/wcfm-controller-wcvendors-orders.php:289
msgid "PDF Invoice"
msgstr "Factura PDF "

#: views/wcfm-view-capability.php:246 views/wcfm-view-capability.php:269
msgid "Install WCFM Ultimate to enable this feature."
msgstr "Instala WCFM Ultimate para habilitar esta característica."

#: views/wcfm-view-capability.php:260
msgid "Access"
msgstr "Acceso"

#: views/wcfm-view-capability.php:281
msgid "Advanced Capability"
msgstr "Capacidad Avanzada"

#: views/wcfm-view-capability.php:351 views/wcfm-view-profile.php:277 
#: views/settings/wcfm-view-dokan-settings.php:378 
#: views/settings/wcfm-view-settings.php:264 
#: views/settings/wcfm-view-wcmarketplace-settings.php:640 
#: views/settings/wcfm-view-wcpvendors-settings.php:163 
#: views/settings/wcfm-view-wcvendors-settings.php:369
msgid "Save"
msgstr "Guardar"

#: views/wcfm-view-coupons-manage.php:42
msgid "Manage Coupon"
msgstr "Administrar Cupones"

#: views/wcfm-view-coupons-manage.php:59 views/wcfm-view-coupons.php:39 
#: views/wcfm-view-listings.php:86 views/wcfm-view-orders-details.php:115 
#: views/wcfm-view-orders.php:68 views/wcfm-view-products-export.php:45 
#: views/wcfm-view-products.php:71 views/wcfm-view-reports-out-of-stock.php:37 
#: views/articles/wcfm-view-articles-manage.php:128 
#: views/articles/wcfm-view-articles.php:60 
#: views/products-manager/wcfm-view-products-manage.php:381 
#: views/reports/wcfm-view-reports-sales-by-date.php:78 
#: views/wc_bookings/wcfm-view-wcbookings-details.php:56 
#: views/wc_bookings/wcfm-view-wcbookings.php:64
msgid "WP Admin View"
msgstr "WP Admin Vista"

#: views/wcfm-view-coupons-manage.php:64 views/wcfm-view-coupons.php:43
msgid "Add New Coupon"
msgstr "Publicar Cupón "

#: views/wcfm-view-coupons-manage.php:79 views/wcfm-view-coupons.php:55 
#: views/wcfm-view-coupons.php:65
msgid "Code"
msgstr "Código "

#: views/wcfm-view-coupons-manage.php:80 
#: views/articles/wcfm-view-articles-manage.php:233 
#: views/articles/wcfm-view-articles-manage.php:337 
#: views/products-manager/wcfm-view-products-manage.php:508 
#: views/products-manager/wcfm-view-products-manage.php:650
msgid "Description"
msgstr "Descripción "

#: views/wcfm-view-coupons-manage.php:81
msgid "Discount Type"
msgstr "Tipo de Descuento"

#: views/wcfm-view-coupons-manage.php:81
msgid "Percentage discount"
msgstr "Porcentaje de descuento"

#: views/wcfm-view-coupons-manage.php:81
msgid "Fixed Cart Discount"
msgstr "Descuento Fijo en el Carrito"

#: views/wcfm-view-coupons-manage.php:81
msgid "Fixed Product Discount"
msgstr "Descuento Fijo en el Producto"

#: views/wcfm-view-coupons-manage.php:82
msgid "Coupon Amount"
msgstr "Monto del Cupón"

#: views/wcfm-view-coupons-manage.php:83
msgid "Allow free shipping"
msgstr "Permitir Envío Gratis "

#: views/wcfm-view-coupons-manage.php:83
msgid ""
"Check this box if the coupon grants free shipping. The free shipping method "
"must be enabled and be set to require \"a valid free shipping coupon\" (see "
"the \"Free Shipping Requires\" setting)."
msgstr ""
"Marca esta casilla si el cupón es para envío gratis. El método de envío "
"gratis debe estar habilitado y requiere \"un cupón valido de envió gratis\" "
"(ve los ajustes de \"Requerimientos de envío gratis\")"

#: views/wcfm-view-coupons-manage.php:84
msgid "Coupon expiry date"
msgstr "Fecha de vencimiento del cupón"

#: views/wcfm-view-coupons-manage.php:98 
#: views/wcfm-view-knowledgebase-manage.php:81 
#: views/articles/wcfm-view-articles-manage.php:352 
#: views/articles/wcfm-view-articles-manage.php:354 
#: views/enquiry/wcfm-view-enquiry-manage.php:97 
#: views/enquiry/wcfm-view-enquiry-tab.php:67 
#: views/notice/wcfm-view-notice-manage.php:88 
#: views/products-manager/wcfm-view-products-manage.php:866 
#: views/products-manager/wcfm-view-products-manage.php:868 
#: views/vendors/wcfm-view-vendors-manage.php:381
msgid "Submit"
msgstr "Publicar "

#: views/wcfm-view-coupons-manage.php:98 
#: views/articles/wcfm-view-articles-manage.php:352 
#: views/articles/wcfm-view-articles-manage.php:354 
#: views/products-manager/wcfm-view-products-manage.php:866 
#: views/products-manager/wcfm-view-products-manage.php:868
msgid "Submit for Review"
msgstr "Subir para reseña"

#: views/wcfm-view-coupons.php:57 views/wcfm-view-coupons.php:67
msgid "Amt"
msgstr "Amt "

#: views/wcfm-view-coupons.php:58 views/wcfm-view-coupons.php:68
msgid "Usage Limit"
msgstr "Limite de Uso "

#: views/wcfm-view-coupons.php:59 views/wcfm-view-coupons.php:69
msgid "Expiry date"
msgstr "Fecha de Vencimiento "

#: views/wcfm-view-coupons.php:60 views/wcfm-view-coupons.php:70 
#: views/wcfm-view-listings.php:109 views/wcfm-view-listings.php:120
msgid "Action"
msgstr "Acción "

#: views/wcfm-view-header-panels.php:66 views/wcfm-view-menu.php:108
msgid "Logout"
msgstr "Cerrar Sesión "

#: views/wcfm-view-knowledgebase-manage.php:66 
#: views/wcfm-view-knowledgebase.php:93 views/wcfm-view-knowledgebase.php:99 
#: views/notice/wcfm-view-notice-manage.php:71 
#: views/notice/wcfm-view-notices.php:51 views/notice/wcfm-view-notices.php:57 
#: views/products-manager/wcfm-view-thirdparty-products-manage.php:164
msgid "Title"
msgstr "Titulo"

#: views/wcfm-view-knowledgebase-manage.php:67 
#: views/notice/wcfm-view-notice-manage.php:74 
#: views/products-manager/wcfm-view-thirdparty-products-manage.php:165
msgid "Content"
msgstr "Contenido "

#: views/wcfm-view-knowledgebase.php:94 views/wcfm-view-knowledgebase.php:100 
#: views/wcfm-view-messages.php:118 views/wcfm-view-messages.php:131 
#: views/wcfm-view-orders.php:93 views/wcfm-view-orders.php:110 
#: views/wcfm-view-products.php:202 views/wcfm-view-products.php:224 
#: views/wcfm-view-reports-out-of-stock.php:54 
#: views/wcfm-view-reports-out-of-stock.php:63 
#: views/articles/wcfm-view-articles.php:118 
#: views/articles/wcfm-view-articles.php:129 
#: views/enquiry/wcfm-view-enquiry.php:119 
#: views/enquiry/wcfm-view-enquiry.php:130 
#: views/notice/wcfm-view-notices.php:52 views/notice/wcfm-view-notices.php:58 
#: views/wc_bookings/wcfm-view-wcbookings.php:120 
#: views/wc_bookings/wcfm-view-wcbookings.php:131
msgid "Actions"
msgstr "Acciones "

#: views/wcfm-view-listings.php:104 views/wcfm-view-listings.php:115 
#: views/wcfm-view-orders.php:81 views/wcfm-view-orders.php:98 
#: views/wcfm-view-products.php:195 views/wcfm-view-products.php:217 
#: views/articles/wcfm-view-articles.php:114 
#: views/articles/wcfm-view-articles.php:125 
#: views/wc_bookings/wcfm-view-wcbookings.php:114 
#: views/wc_bookings/wcfm-view-wcbookings.php:125 
#: views/withdrawal/dokan/wcfm-view-payments.php:67 
#: views/withdrawal/dokan/wcfm-view-payments.php:75 
#: views/withdrawal/wcmp/wcfm-view-payments.php:63 
#: views/withdrawal/wcmp/wcfm-view-payments.php:74
msgid "Status"
msgstr "Estado "

#: views/wcfm-view-menu.php:73
msgid "Home"
msgstr "Inicio "

#: views/wcfm-view-messages.php:47
msgid "To Store Admin"
msgstr "Para el Administrador"

#: views/wcfm-view-messages.php:47
msgid "To Store Vendors"
msgstr "Para los Vendedores"

#: views/wcfm-view-messages.php:63
msgid "Direct TO:"
msgstr "Directo A:"

#: views/wcfm-view-messages.php:114 views/wcfm-view-messages.php:127
msgid "Message"
msgstr "Mensaje"

#: views/wcfm-view-messages.php:117 views/wcfm-view-messages.php:130 
#: views/wcfm-view-orders.php:92 views/wcfm-view-orders.php:109 
#: views/wcfm-view-products.php:200 views/wcfm-view-products.php:222 
#: includes/reports/class-dokan-report-sales-by-date.php:876 
#: includes/reports/class-wcfm-report-analytics.php:170 
#: includes/reports/class-wcfm-report-sales-by-date.php:777 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:284 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:583 
#: includes/reports/class-wcpvendors-report-sales-by-date.php:486 
#: includes/reports/class-wcvendors-report-sales-by-date.php:262 
#: includes/reports/class-wcvendors-report-sales-by-date.php:507 
#: views/articles/wcfm-view-articles.php:116 
#: views/articles/wcfm-view-articles.php:127 
#: views/enquiry/wcfm-view-enquiry.php:118 
#: views/enquiry/wcfm-view-enquiry.php:129 
#: views/withdrawal/dokan/wcfm-view-payments.php:70 
#: views/withdrawal/dokan/wcfm-view-payments.php:78 
#: views/withdrawal/wcmp/wcfm-view-payments.php:69 
#: views/withdrawal/wcmp/wcfm-view-payments.php:80 
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:61 
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:70
msgid "Date"
msgstr "Fecha "

#: views/wcfm-view-orders-details.php:66 
#: views/products-manager/wcfm-view-products-manage.php:296 
#: views/products-manager/wcfm-view-products-manage.php:297
msgid "Standard"
msgstr "Estandar"

#: views/wcfm-view-orders-details.php:157
msgid "Order date:"
msgstr "Fecha de la Venta:"

#: views/wcfm-view-orders-details.php:162
msgid "Order status:"
msgstr "Estado de la Venta"

#: views/wcfm-view-orders-details.php:168
msgid "Customer payment page"
msgstr "Página de pago del cliente"

#: views/wcfm-view-orders-details.php:189
msgid "Customer:"
msgstr "Cliente: "

#: views/wcfm-view-orders-details.php:197
msgid "View other orders"
msgstr "Ver otras ventas"

#: views/wcfm-view-orders-details.php:218
#, php-format
msgid "<label for=\"order_payment_via\">Payment via: </label> %s"
msgstr "<label for=\"order_payment_via\">Pagar vía: </label> %s"

#: views/wcfm-view-orders-details.php:237
msgid "Customer IP"
msgstr "IP del Cliente"

#: views/wcfm-view-orders-details.php:247
msgid "Billing Details"
msgstr "Detalles de Facturación "

#: views/wcfm-view-orders-details.php:250
msgid "Shipping Details"
msgstr "Detalles de Envío"

#: views/wcfm-view-orders-details.php:263 
#: views/wcfm-view-orders-details.php:265 
#: views/wcfm-view-orders-details.php:299 
#: views/wcfm-view-orders-details.php:301 views/wcfm-view-profile.php:200
msgid "Address"
msgstr "Dirección "

#: views/wcfm-view-orders-details.php:265
msgid "No billing address set."
msgstr "No hay dirección de facturación."

#: views/wcfm-view-orders-details.php:301
msgid "No shipping address set."
msgstr "Sin dirección de envió. "

#: views/wcfm-view-orders-details.php:326
msgid "Customer Provided Note"
msgstr "Nota proporcionada para el cliente"

#: views/wcfm-view-orders-details.php:340
msgid "Order Items"
msgstr "Productos Vendidos "

#: views/wcfm-view-orders-details.php:347
msgid "Item"
msgstr "Item"

#: views/wcfm-view-orders-details.php:349 
#: views/settings/wcfm-view-dokan-settings.php:320 
#: views/settings/wcfm-view-dokan-settings.php:323
msgid "Cost"
msgstr "Costo"

#: views/wcfm-view-orders-details.php:350
msgid "Qty"
msgstr "Cantidad"

#: views/wcfm-view-orders-details.php:403
msgid "SKU:"
msgstr "SKU:"

#: views/wcfm-view-orders-details.php:407
msgid "Variation ID:"
msgstr "ID de la Variación:"

#: views/wcfm-view-orders-details.php:411
msgid "No longer exists"
msgstr "No existe"

#: views/wcfm-view-orders-details.php:678 
#: views/withdrawal/wcmp/wcfm-view-payments.php:66 
#: views/withdrawal/wcmp/wcfm-view-payments.php:77
msgid "Fee"
msgstr "Cuota"

#: views/wcfm-view-orders-details.php:796
msgid "Coupon(s) Used"
msgstr "Cupones usados "

#: views/wcfm-view-orders-details.php:813
msgid "This is the total discount. Discounts are defined per line item."
msgstr ""
"Este es el descuento total. Los descuentos se definen por línea de pedido."

#: views/wcfm-view-orders-details.php:813
msgid "Discount"
msgstr "Descuento"

#: views/wcfm-view-orders-details.php:825
msgid "This is the shipping and handling total costs for the order."
msgstr "Se trata de los gastos totales de envío y manipulación de la orden."

#: views/wcfm-view-orders-details.php:861
msgid "Order Total"
msgstr "Total de la Venta"

#: views/wcfm-view-orders-details.php:876
msgid "Refunded"
msgstr "Devuelto"

#: views/wcfm-view-orders.php:46
msgid "Show all dates"
msgstr "Mostrar todas las fechas"

#: views/wcfm-view-orders.php:83 views/wcfm-view-orders.php:100
msgid "Purchased"
msgstr "Comprado "

#: views/wcfm-view-products-export.php:64 views/wcfm-view-products.php:110 
#: views/products-manager/wcfm-view-products-manage.php:386
msgid "Add New Product"
msgstr "Publicar Nuevo Producto"

#: views/wcfm-view-products.php:140 
#: views/products-manager/wcfm-view-products-manage.php:335 
#: views/products-manager/wcfm-view-products-manage.php:340 
#: views/settings/wcfm-view-settings.php:234
msgid "Simple Product"
msgstr "Producto Simple"

#: views/wcfm-view-products.php:140 
#: views/products-manager/wcfm-view-products-manage.php:335 
#: views/settings/wcfm-view-settings.php:234
msgid "Variable Product"
msgstr "Producto Variable"

#: views/wcfm-view-products.php:140 
#: views/products-manager/wcfm-view-products-manage.php:335 
#: views/settings/wcfm-view-settings.php:234
msgid "Grouped Product"
msgstr "Producto Agrupado"

#: views/wcfm-view-products.php:140 
#: views/products-manager/wcfm-view-products-manage.php:335 
#: views/settings/wcfm-view-settings.php:234
msgid "External/Affiliate Product"
msgstr "Producto Externo/Afiliado"

#: views/wcfm-view-products.php:192 views/wcfm-view-products.php:214 
#: views/articles/wcfm-view-articles.php:112 
#: views/articles/wcfm-view-articles.php:123 
#: views/products-manager/wcfm-view-products-manage.php:824
msgid "Image"
msgstr "Imagen"

#: views/wcfm-view-products.php:194 views/wcfm-view-products.php:216 
#: views/products-manager/wcfm-view-products-manage.php:670 
#: views/products-manager/wcfm-view-products-manage.php:829
msgid "SKU"
msgstr "SKU"

#: views/wcfm-view-products.php:196 views/wcfm-view-products.php:218
msgid "Stock"
msgstr "Stock"

#: views/wcfm-view-products.php:197 views/wcfm-view-products.php:219 
#: views/products-manager/wcfm-view-products-manage.php:413
msgid "Price"
msgstr "Precio"

#: views/wcfm-view-profile.php:149
msgid "Personal"
msgstr "Personal "

#: views/wcfm-view-profile.php:157 views/wcfm-view-profile.php:207 
#: views/wcfm-view-profile.php:222 
#: views/vendors/wcfm-view-vendors-manage.php:271 
#: views/vendors/wcfm-view-vendors-manage.php:359
msgid "First Name"
msgstr "Primer Nombre "

#: views/wcfm-view-profile.php:158 views/wcfm-view-profile.php:208 
#: views/wcfm-view-profile.php:223 
#: views/vendors/wcfm-view-vendors-manage.php:272 
#: views/vendors/wcfm-view-vendors-manage.php:360
msgid "Last Name"
msgstr "Apellido "

#: views/wcfm-view-profile.php:160 
#: views/settings/wcfm-view-wcmarketplace-settings.php:618
msgid "Phone"
msgstr "Teléfono "

#: views/wcfm-view-profile.php:188
msgid "About"
msgstr "Descripción de la Tienda "

#: views/wcfm-view-profile.php:204
msgid "Billing"
msgstr "Facturación "

#: views/wcfm-view-profile.php:209 views/wcfm-view-profile.php:224 
#: views/settings/wcfm-view-wcmarketplace-settings.php:155 
#: views/settings/wcfm-view-wcmarketplace-settings.php:620 
#: views/settings/wcfm-view-wcvendors-settings.php:157 
#: views/settings/wcfm-view-wcvendors-settings.php:314
msgid "Address 1"
msgstr "Dirección 1"

#: views/wcfm-view-profile.php:210 views/wcfm-view-profile.php:225 
#: views/settings/wcfm-view-wcmarketplace-settings.php:156 
#: views/settings/wcfm-view-wcmarketplace-settings.php:621 
#: views/settings/wcfm-view-wcvendors-settings.php:158 
#: views/settings/wcfm-view-wcvendors-settings.php:315
msgid "Address 2"
msgstr "Dirección 2 "

#: views/wcfm-view-profile.php:211 views/wcfm-view-profile.php:226 
#: views/settings/wcfm-view-dokan-settings.php:172 
#: views/settings/wcfm-view-dokan-settings.php:319 
#: views/settings/wcfm-view-wcmarketplace-settings.php:157 
#: views/settings/wcfm-view-wcmarketplace-settings.php:622 
#: views/settings/wcfm-view-wcvendors-settings.php:159 
#: views/settings/wcfm-view-wcvendors-settings.php:289 
#: views/settings/wcfm-view-wcvendors-settings.php:316
msgid "Country"
msgstr "País "

#: views/wcfm-view-profile.php:212 views/wcfm-view-profile.php:227 
#: views/settings/wcfm-view-dokan-settings.php:170 
#: views/settings/wcfm-view-wcmarketplace-settings.php:158 
#: views/settings/wcfm-view-wcmarketplace-settings.php:623 
#: views/settings/wcfm-view-wcvendors-settings.php:160 
#: views/settings/wcfm-view-wcvendors-settings.php:317
msgid "City/Town"
msgstr "Ciudad/Pueblo"

#: views/wcfm-view-profile.php:213 views/wcfm-view-profile.php:228 
#: views/settings/wcfm-view-dokan-settings.php:173 
#: views/settings/wcfm-view-wcmarketplace-settings.php:159 
#: views/settings/wcfm-view-wcmarketplace-settings.php:624 
#: views/settings/wcfm-view-wcvendors-settings.php:161 
#: views/settings/wcfm-view-wcvendors-settings.php:318
msgid "State/County"
msgstr "Estado/Departamento "

#: views/wcfm-view-profile.php:214 views/wcfm-view-profile.php:229 
#: views/settings/wcfm-view-dokan-settings.php:171 
#: views/settings/wcfm-view-wcmarketplace-settings.php:160 
#: views/settings/wcfm-view-wcmarketplace-settings.php:625 
#: views/settings/wcfm-view-wcvendors-settings.php:162 
#: views/settings/wcfm-view-wcvendors-settings.php:319
msgid "Postcode/Zip"
msgstr "Código Postal/Zip "

#: views/wcfm-view-profile.php:242
msgid "Social"
msgstr "Social"

#: views/wcfm-view-profile.php:249
msgid "Twitter"
msgstr "Twitter"

#: views/wcfm-view-profile.php:250
msgid "Facebook"
msgstr "Facebook"

#: views/wcfm-view-profile.php:251
msgid "Instagram"
msgstr "Instagram"

#: views/wcfm-view-profile.php:252
msgid "Youtube"
msgstr "YouTube"

#: views/wcfm-view-profile.php:253
msgid "linkdin"
msgstr "linkdin"

#: views/wcfm-view-profile.php:254
msgid "Google Plus"
msgstr "Google Plus"

#: views/wcfm-view-profile.php:255
msgid "Snapchat"
msgstr "Snapchat"

#: views/wcfm-view-profile.php:256
msgid "Pinterest"
msgstr "Pinterest"

#: views/wcfm-view-profile.php:260
msgid "Social Profile"
msgstr "Perfil Social"

#: views/wcfm-view-reports-menu.php:4
msgid "Sales by date"
msgstr "Ventas por Fecha "

#: views/wcfm-view-reports-out-of-stock.php:26
msgid "Out of Stock"
msgstr "Sin Stock "

#: views/wcfm-view-reports-out-of-stock.php:50 
#: views/wcfm-view-reports-out-of-stock.php:59
msgid "product"
msgstr "Producto"

#: views/wcfm-view-reports-out-of-stock.php:51 
#: views/wcfm-view-reports-out-of-stock.php:60
msgid "Parent"
msgstr "Padre"

#: views/wcfm-view-reports-out-of-stock.php:52 
#: views/wcfm-view-reports-out-of-stock.php:61
msgid "Unit in stock"
msgstr "Unidad en Stock"

#: views/wcfm-view-reports-out-of-stock.php:53 
#: views/wcfm-view-reports-out-of-stock.php:62
msgid "Stock Status"
msgstr "Estado del Inventario "

#: controllers/orders/wcfm-controller-dokan-orders.php:152 
#: controllers/orders/wcfm-controller-dokan-orders.php:159 
#: controllers/orders/wcfm-controller-orders.php:115 
#: controllers/orders/wcfm-controller-orders.php:122 
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:172 
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:179 
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:158 
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:165 
#: controllers/orders/wcfm-controller-wcvendors-orders.php:175 
#: controllers/orders/wcfm-controller-wcvendors-orders.php:182
#, php-format
msgctxt "full name"
msgid "%1$s %2$s"
msgstr "%1$s %2$s"

#: controllers/orders/wcfm-controller-dokan-orders.php:163 
#: controllers/orders/wcfm-controller-dokan-orders.php:169 
#: controllers/orders/wcfm-controller-orders.php:126 
#: controllers/orders/wcfm-controller-orders.php:132 
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:183 
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:189 
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:169 
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:175 
#: controllers/orders/wcfm-controller-wcvendors-orders.php:186 
#: controllers/orders/wcfm-controller-wcvendors-orders.php:192
msgid "Guest"
msgstr "Invitado"

#: controllers/orders/wcfm-controller-dokan-orders.php:196 
#: controllers/orders/wcfm-controller-orders.php:159 
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:225 
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:237 
#: controllers/orders/wcfm-controller-wcvendors-orders.php:213
#, php-format
msgid "%d item"
msgid_plural "%d items"
msgstr[0] "%d producto"
msgstr[1] "%d productos"

#: controllers/orders/wcfm-controller-dokan-orders.php:203 
#: controllers/orders/wcfm-controller-orders.php:166
msgid "Via"
msgstr "Vía"

#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:235
msgid "UNPAID"
msgstr "SIN PAGAR"

#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:239 
#: controllers/orders/wcfm-controller-wcvendors-orders.php:243
msgid "PAID"
msgstr "Depositado"

#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:243 
#: controllers/orders/wcfm-controller-wcvendors-orders.php:247
msgid "REVERSED"
msgstr "DEVUELTO"

#: controllers/orders/wcfm-controller-wcvendors-orders.php:239
msgid "DUE"
msgstr "¨Por depositar"

#: controllers/settings/wcfm-controller-dokan-settings.php:60 
#: controllers/settings/wcfm-controller-settings.php:137 
#: controllers/settings/wcfm-controller-wcmarketplace-settings.php:219 
#: controllers/settings/wcfm-controller-wcpvendors-settings.php:65 
#: controllers/settings/wcfm-controller-wcvendors-settings.php:87
msgid "Settings saved successfully"
msgstr "Ajustes Guardados"

#: controllers/settings/wcfm-controller-wcpvendors-settings.php:67
msgid "Settings failed to save"
msgstr "No se pudo guardar la configuración"

#: includes/reports/class-dokan-report-sales-by-date.php:563 
#: includes/reports/class-wcfm-report-analytics.php:127 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:199 
#: includes/reports/class-wcvendors-report-sales-by-date.php:178
msgid ""
"This is the sum of the earned commission including shipping and taxes if "
"applicable."
msgstr ""
"Esta es la suma de la comisión ganada incluyendo el envío e impuestos, si "
"corresponde."

#: includes/reports/class-wcfm-report-analytics.php:117 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:164 
#: includes/reports/class-wcvendors-report-sales-by-date.php:161
#, php-format
msgid "%s average daily sales"
msgstr "%s Promedio de ventas diarias "

#: includes/reports/class-wcfm-report-analytics.php:121 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:168 
#: includes/reports/class-wcvendors-report-sales-by-date.php:165
#, php-format
msgid "%s average monthly sales"
msgstr "%s Promedio de ventas mensuales "

#: includes/reports/class-wcfm-report-analytics.php:126
#, php-format
msgid "%s total earned commission"
msgstr "%s Dinero total ganado"

#: includes/reports/class-wcfm-report-analytics.php:141 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:249 
#: includes/reports/class-wcvendors-report-sales-by-date.php:227 
#: views/enquiry/wcfm-view-enquiry.php:52 
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:29 
#: views/reports/wcfm-view-reports-sales-by-date.php:29 
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:29 
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:29 
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:29 
#: views/vendors/wcfm-view-vendors.php:14
msgid "Year"
msgstr "Año "

#: includes/reports/class-wcfm-report-analytics.php:142 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:250 
#: includes/reports/class-wcvendors-report-sales-by-date.php:228 
#: views/enquiry/wcfm-view-enquiry.php:51 
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:30 
#: views/reports/wcfm-view-reports-sales-by-date.php:30 
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:30 
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:30 
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:30 
#: views/vendors/wcfm-view-vendors.php:13
msgid "Last Month"
msgstr "Mes Anterior "

#: includes/reports/class-wcfm-report-analytics.php:143 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:251 
#: includes/reports/class-wcvendors-report-sales-by-date.php:229 
#: views/enquiry/wcfm-view-enquiry.php:50 
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:31 
#: views/reports/wcfm-view-reports-sales-by-date.php:31 
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:31 
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:31 
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:31 
#: views/vendors/wcfm-view-vendors.php:12
msgid "This Month"
msgstr "Este Mes "

#: includes/reports/class-wcfm-report-analytics.php:144 
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:252 
#: includes/reports/class-wcvendors-report-sales-by-date.php:230 
#: views/enquiry/wcfm-view-enquiry.php:49 
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:32 
#: views/reports/wcfm-view-reports-sales-by-date.php:32 
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:32 
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:32 
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:32 
#: views/vendors/wcfm-view-vendors.php:11
msgid "Last 7 Days"
msgstr "Últimos 7 Días "

#: includes/reports/class-wcmarketplace-report-sales-by-date.php:206 
#: includes/reports/class-wcvendors-report-sales-by-date.php:185
msgid ""
"This is the sum of the commission paid including shipping and taxes if "
"applicable."
msgstr ""
"Esta es la suma de la comisión pagada, incluyendo el envío e impuestos, si "
"corresponde."

#: includes/reports/class-wcmarketplace-report-sales-by-date.php:221 
#: includes/reports/class-wcvendors-report-sales-by-date.php:199
#, php-format
msgid "%s orders placed"
msgstr "%s Ventas hechas"

#: includes/reports/class-wcmarketplace-report-sales-by-date.php:227 
#: includes/reports/class-wcvendors-report-sales-by-date.php:205
#, php-format
msgid "%s items purchased"
msgstr "%s productos comprados"

#: includes/reports/class-wcmarketplace-report-sales-by-date.php:234 
#: includes/reports/class-wcvendors-report-sales-by-date.php:212
#, php-format
msgid "%s charged for shipping"
msgstr "%s Cobrado por el envío"

#: views/articles/wcfm-view-articles-manage.php:159 
#: views/articles/wcfm-view-articles-manage.php:159 
#: views/articles/wcfm-view-articles-manage.php:255 
#: views/articles/wcfm-view-articles-manage.php:255 
#: views/products-manager/wcfm-view-products-manage.php:433 
#: views/products-manager/wcfm-view-products-manage.php:433 
#: views/products-manager/wcfm-view-products-manage.php:538 
#: views/products-manager/wcfm-view-products-manage.php:538 
#: views/settings/wcfm-view-settings.php:240 
#: views/settings/wcfm-view-settings.php:240
msgid "Categories"
msgstr "Categorías "

#: views/articles/wcfm-view-articles-manage.php:204 
#: views/articles/wcfm-view-articles-manage.php:303 
#: views/products-manager/wcfm-view-products-manage.php:479 
#: views/products-manager/wcfm-view-products-manage.php:616
msgid "Tags"
msgstr "Etiquetas"

#: views/articles/wcfm-view-articles-manage.php:232 
#: views/articles/wcfm-view-articles-manage.php:336 
#: views/products-manager/wcfm-view-products-manage.php:507 
#: views/products-manager/wcfm-view-products-manage.php:649
msgid "Short Description"
msgstr "Descripción Corta "

#: views/dashboard/wcfm-view-dashboard.php:256 
#: views/dashboard/wcfm-view-dokan-dashboard.php:266 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:283 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:270
#, php-format
msgid "%s top seller in last 7 days (sold %d)"
msgstr "%s top seller los últimos 7 días (vendidos: %d)"

#: views/dashboard/wcfm-view-dashboard.php:312 
#: views/dashboard/wcfm-view-dokan-dashboard.php:324 
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:342 
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:349 
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:328
msgid "Sales by Product"
msgstr "Ventas por Producto "

#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:294
#, php-format
msgid "%s top seller this month (sold %d)"
msgstr "%s top seller este mes (vendidos: %d)"

#: views/enquiry/wcfm-view-enquiry-tab.php:57 
#: views/settings/wcfm-view-wcmarketplace-settings.php:619 
#: views/vendors/wcfm-view-vendors-manage.php:270
msgid "Email"
msgstr "Email "

#: views/products-manager/wcfm-view-products-manage.php:283 
#: views/products-manager/wcfm-view-products-manage.php:295
msgid "Same as parent"
msgstr "Igual que padre"

#: views/products-manager/wcfm-view-products-manage.php:284
msgid "No shipping class"
msgstr "Sin clase de envío "

#: views/products-manager/wcfm-view-products-manage.php:350
msgid "Manage Product"
msgstr "Administrar Producto"

#: views/products-manager/wcfm-view-products-manage.php:406
msgid "Product Title"
msgstr "Nombre del Producto"

#: views/products-manager/wcfm-view-products-manage.php:411
msgid "URL"
msgstr "URL"

#: views/products-manager/wcfm-view-products-manage.php:411
msgid "Enter the external URL to the product."
msgstr "Ingresa el URL externo del producto"

#: views/products-manager/wcfm-view-products-manage.php:412
msgid "Button Text"
msgstr "Texto del Botón"

#: views/products-manager/wcfm-view-products-manage.php:412
msgid "This text will be shown on the button linking to the external product."
msgstr "Este texto se mostrará en el botón que enlaza con el producto externo."

#: views/products-manager/wcfm-view-products-manage.php:414 
#: views/products-manager/wcfm-view-products-manage.php:826
msgid "Sale Price"
msgstr "Precio en Oferta "

#: views/products-manager/wcfm-view-products-manage.php:414
msgid "schedule"
msgstr "Horario "

#: views/products-manager/wcfm-view-products-manage.php:421
msgid "Sales scheduling"
msgstr "Programación de ventas"

#: views/products-manager/wcfm-view-products-manage.php:479 
#: views/products-manager/wcfm-view-products-manage.php:616
msgid "Separate Product Tags with commas"
msgstr "Separa las etiquetas con comas"

#: views/products-manager/wcfm-view-products-manage.php:524
msgid "Image Gallery"
msgstr "Galería de Imágenes "

#: views/products-manager/wcfm-view-products-manage.php:670
msgid ""
"SKU refers to a Stock-keeping unit, a unique identifier for each distinct "
"product and service that can be purchased."
msgstr ""
"SKU se refiere a un código único distinto para cada producto y que funciona "
"como identificador.  "

#: views/products-manager/wcfm-view-products-manage.php:671
msgid "Manage Stock?"
msgstr "¿Administrar Inventario?"

#: views/products-manager/wcfm-view-products-manage.php:671
msgid "Enable stock management at product level"
msgstr "Marca si quieres elegir la cantidad de inventario"

#: views/products-manager/wcfm-view-products-manage.php:672 
#: views/products-manager/wcfm-view-products-manage.php:827
msgid "Stock Qty"
msgstr "Cantidad "

#: views/products-manager/wcfm-view-products-manage.php:672
msgid ""
"Stock quantity. If this is a variable product this value will be used to "
"control stock for all variations, unless you define stock at variation level."
msgstr ""
"Cantidad de stock. Si se trata de un producto variable, este valor se usará "
"para controlar el stock de todas las variaciones, a menos que defina el "
"stock al nivel de variación."

#: views/products-manager/wcfm-view-products-manage.php:673
msgid "Allow Backorders?"
msgstr "¿Permitir Retrasos?"

#: views/products-manager/wcfm-view-products-manage.php:673 
#: views/products-manager/wcfm-view-products-manage.php:828
msgid "Do not Allow"
msgstr "No Permitir "

#: views/products-manager/wcfm-view-products-manage.php:673 
#: views/products-manager/wcfm-view-products-manage.php:828
msgid "Allow, but notify customer"
msgstr "Permitir, Pero Notificar al Cliente"

#: views/products-manager/wcfm-view-products-manage.php:673 
#: views/products-manager/wcfm-view-products-manage.php:828
msgid "Allow"
msgstr "Permitir "

#: views/products-manager/wcfm-view-products-manage.php:673
msgid ""
"If managing stock, this controls whether or not backorders are allowed. If "
"enabled, stock quantity can go below 0."
msgstr ""
"Si se controla el inventario, esto permite que los compradores reserven tu "
"producto incluso si no tienes inventario disponible. "

#: views/products-manager/wcfm-view-products-manage.php:674 
#: views/products-manager/wcfm-view-products-manage.php:830
msgid "Stock status"
msgstr "Estado del Inventario "

#: views/products-manager/wcfm-view-products-manage.php:674 
#: views/products-manager/wcfm-view-products-manage.php:830
msgid "In stock"
msgstr "En Stock "

#: views/products-manager/wcfm-view-products-manage.php:674
msgid ""
"Controls whether or not the product is listed as \"in stock\" or \"out of "
"stock\" on the frontend."
msgstr ""
"Controla cuando el producto esta marcado como \"en stock\" o \"sin stock\" "
"en el catalogo."

#: views/products-manager/wcfm-view-products-manage.php:675
msgid "Sold Individually"
msgstr "Vendido Individualmente"

#: views/products-manager/wcfm-view-products-manage.php:675
msgid ""
"Enable this to only allow one of this item to be bought in a single order"
msgstr ""
"Habilita esta opción para permitir al cliente solo comprar una vez tu "
"producto en cada pedido"

#: views/products-manager/wcfm-view-products-manage.php:687
msgid "Grouped Products"
msgstr "Productos Agrupados"

#: views/products-manager/wcfm-view-products-manage.php:692
msgid "Grouped products"
msgstr "Productos Agrupados"

#: views/products-manager/wcfm-view-products-manage.php:692
msgid "This lets you choose which products are part of this group."
msgstr "Esto le permite elegir qué productos son parte de este grupo."

#: views/products-manager/wcfm-view-products-manage.php:708 
#: views/products-manager/wcfm-view-products-manage.php:813
msgid "Weight"
msgstr "Peso "

#: views/products-manager/wcfm-view-products-manage.php:709
msgid "Dimensions"
msgstr "Dimensiones "

#: views/products-manager/wcfm-view-products-manage.php:709 
#: views/products-manager/wcfm-view-products-manage.php:810
msgid "Length"
msgstr "Largo "

#: views/products-manager/wcfm-view-products-manage.php:710 
#: views/products-manager/wcfm-view-products-manage.php:811
msgid "Width"
msgstr "Ancho"

#: views/products-manager/wcfm-view-products-manage.php:711 
#: views/products-manager/wcfm-view-products-manage.php:812
msgid "Height"
msgstr "Alto "

#: views/products-manager/wcfm-view-products-manage.php:712
msgid "Shipping class"
msgstr "Clase de Envío "

#: views/products-manager/wcfm-view-products-manage.php:731
msgid "Tax Status"
msgstr "Estado del Impuesto"

#: views/products-manager/wcfm-view-products-manage.php:731
msgid "Taxable"
msgstr "Gravado"

#: views/products-manager/wcfm-view-products-manage.php:731
msgid "Shipping only"
msgstr "Enviar Solo"

#: views/products-manager/wcfm-view-products-manage.php:731
msgctxt "Tax status"
msgid "None"
msgstr "Ninguno"

#: views/products-manager/wcfm-view-products-manage.php:731
msgid ""
"Define whether or not the entire product is taxable, or just the cost of "
"shipping it."
msgstr ""
"Define si el producto completo es o no gravable, o simplemente el costo de "
"su envío."

#: views/products-manager/wcfm-view-products-manage.php:732
msgid "Tax Class"
msgstr "Clase de Impuesto"

#: views/products-manager/wcfm-view-products-manage.php:732
msgid ""
"Choose a tax class for this product. Tax classes are used to apply different "
"tax rates specific to certain types of product."
msgstr ""
"Elige una clase de impuesto para este producto. Las clases de impuestos se "
"utilizan para aplicar tipos impositivos diferentes a ciertos tipos de "
"productos."

#: views/products-manager/wcfm-view-products-manage.php:755
msgid "Value(s):"
msgstr "Valor(es):"

#: views/products-manager/wcfm-view-products-manage.php:755
msgid "Enter some text, some attributes by \"|\" separating values."
msgstr "Empieza a escribir, algunos atributos debes estar separados por \"|\""

#: views/products-manager/wcfm-view-products-manage.php:756
msgid "Visible on the product page"
msgstr "Visible en la pagina del producto"

#: views/products-manager/wcfm-view-products-manage.php:757
msgid "Use as Variation"
msgstr "Usar como Variación"

#: views/products-manager/wcfm-view-products-manage.php:784
msgid "Variations"
msgstr "Variaciones"

#: views/products-manager/wcfm-view-products-manage.php:792
msgid "Default Form Values:"
msgstr "Valores de formulario predeterminados:"

#: views/products-manager/wcfm-view-products-manage.php:823
msgid "Manage Stock"
msgstr "Administrar Stock"

#: views/products-manager/wcfm-view-products-manage.php:825 
#: views/products-manager/wcfm-view-thirdparty-products-manage.php:294
msgid "Regular Price"
msgstr "Precio Normal"

#: views/products-manager/wcfm-view-products-manage.php:828
msgid "Backorders?"
msgstr "¿Backorders?"

#: views/products-manager/wcfm-view-products-manage.php:848
msgid "Up-sells"
msgstr "Ventas Dirigidas"

#: views/products-manager/wcfm-view-products-manage.php:848
msgid ""
"Up-sells are products which you recommend instead of the currently viewed "
"product, for example, products that are more profitable or better quality or "
"more expensive."
msgstr ""
"Ventas Dirigidas son productos que recomiendas en el producto que el "
"comprador esta viendo actualmente, por ejemplo, productos que te dan mas "
"ganancias o tienen mejor calidad."

#: views/products-manager/wcfm-view-products-manage.php:849
msgid "Cross-sells"
msgstr "Ventas Cruzadas "

#: views/products-manager/wcfm-view-products-manage.php:849
msgid ""
"Cross-sells are products which you promote in the cart, based on the current "
"product."
msgstr ""
"Las ventas cruzadas son productos que quieres promocionar en el carrito, "
"basado en el producto que estas publicando"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:140
msgid "Yoast SEO"
msgstr "Yoast SEO"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:145
msgid "Enter a focus keyword"
msgstr "Ingresa una palabra clave"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:145
msgid "It should appear in title and first paragraph of the copy."
msgstr ""
"Debería aparecer en el titulo y en el primer párrafo de la descripción."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:146
msgid "Meta description"
msgstr "Meta descripción"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:146
msgid "It should not be more than 156 characters."
msgstr "No debería superar los 156 caracteres."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:158
msgid "Custom Tabs"
msgstr "Pestañas Personalizadas"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:163
msgid "Tabs"
msgstr "Pestañas"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:164
msgid "Required for tab to be visible"
msgstr "Requerido para que la pestaña sea visible"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:165
msgid "HTML or Text to display ..."
msgstr "HTML o Texto para mostrar..."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:178
msgid "Barcode & ISBN"
msgstr "Código de barras & ISBN"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:183
msgid "Barcode"
msgstr "Código de barras"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:184
msgid "ISBN"
msgstr "ISBN"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:197
msgid "MSRP Pricing"
msgstr "Precios de MSRP"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:202
msgid "MSRP Price"
msgstr "Precio MSRP"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:215
msgid "Quantities and Units"
msgstr "Cantidad y Unidades."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:220
msgid "Deactivate Quantity Rules"
msgstr "Desactivar reglas de cantidad"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:221
msgid "Override Quantity Rules"
msgstr "Reemplazar reglas de cantidad"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:222
msgid "Step Value"
msgstr "Valor del paso"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:223
msgid "Minimum Quantity"
msgstr "Cantidad Mínima "

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:224
msgid "Maximum Quantity"
msgstr "Cantidad Máxima"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:225
msgid "Out of Stock Minimum"
msgstr "Sin cantidad minima de stock"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:226
msgid "Out of Stock Maximum"
msgstr "Sin cantidad máxima de stock"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:227
msgid "Unit"
msgstr "Unidad"

#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:63 
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:65 
#: views/reports/wcfm-view-reports-sales-by-date.php:63 
#: views/reports/wcfm-view-reports-sales-by-date.php:65 
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:61 
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:63 
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:62 
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:64 
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:61 
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:63
msgid "Sales BY Date"
msgstr "Ventas por Fecha "

#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:63 
#: views/reports/wcfm-view-reports-sales-by-date.php:63 
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:61 
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:62 
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:61
#, php-format
msgctxt "start date and end date"
msgid "From %s to %s"
msgstr "Desde %s a %s"

#: views/settings/wcfm-view-dokan-settings.php:132 
#: views/settings/wcfm-view-wcmarketplace-settings.php:101 
#: views/settings/wcfm-view-wcpvendors-settings.php:70 
#: views/settings/wcfm-view-wcpvendors-settings.php:85 
#: views/settings/wcfm-view-wcvendors-settings.php:100
msgid "Store Settings"
msgstr "Ajustes de la tienda"

#: views/settings/wcfm-view-dokan-settings.php:154 
#: views/settings/wcfm-view-wcmarketplace-settings.php:144 
#: views/settings/wcfm-view-wcvendors-settings.php:146
msgid "Banner"
msgstr "Banner"

#: views/settings/wcfm-view-dokan-settings.php:155 
#: views/settings/wcfm-view-wcmarketplace-settings.php:124 
#: views/settings/wcfm-view-wcpvendors-settings.php:93 
#: views/settings/wcfm-view-wcvendors-settings.php:123
msgid "Shop Name"
msgstr "Nombre de la Tienda "

#: views/settings/wcfm-view-dokan-settings.php:157 
#: views/settings/wcfm-view-wcvendors-settings.php:148
msgid "Store Phone"
msgstr "Teléfono de la tienda"

#: views/settings/wcfm-view-dokan-settings.php:164 
#: views/settings/wcfm-view-wcmarketplace-settings.php:151 
#: views/settings/wcfm-view-wcvendors-settings.php:153
msgid "Store Address"
msgstr "Dirección de la Tienda"

#: views/settings/wcfm-view-dokan-settings.php:256 
#: views/settings/wcfm-view-dokan-settings.php:269 
#: views/settings/wcfm-view-wcmarketplace-settings.php:576 
#: views/settings/wcfm-view-wcmarketplace-settings.php:589 
#: views/settings/wcfm-view-wcpvendors-settings.php:114 
#: views/settings/wcfm-view-wcpvendors-settings.php:127 
#: views/settings/wcfm-view-wcvendors-settings.php:340 
#: views/settings/wcfm-view-wcvendors-settings.php:353
msgid "Vacation Mode"
msgstr "Modo Vacaciones "

#: views/settings/wcfm-view-dokan-settings.php:263 
#: views/settings/wcfm-view-wcmarketplace-settings.php:583 
#: views/settings/wcfm-view-wcpvendors-settings.php:121 
#: views/settings/wcfm-view-wcvendors-settings.php:347
msgid "Enable Vacation Mode"
msgstr "Habilitar modo Vacaciones "

#: views/settings/wcfm-view-dokan-settings.php:265 
#: views/settings/wcfm-view-wcmarketplace-settings.php:585 
#: views/settings/wcfm-view-wcpvendors-settings.php:123 
#: views/settings/wcfm-view-wcvendors-settings.php:349
msgid "Vacation Message"
msgstr "Mensaje de Vacaciones"

#: views/settings/wcfm-view-dokan-settings.php:297 
#: views/settings/wcfm-view-wcmarketplace-settings.php:545 
#: views/settings/wcfm-view-wcvendors-settings.php:304
msgid "Shipping Policy"
msgstr "Política de Envíos"

#: views/settings/wcfm-view-dokan-settings.php:298 
#: views/settings/wcfm-view-wcmarketplace-settings.php:553 
#: views/settings/wcfm-view-wcvendors-settings.php:305
msgid "Refund Policy"
msgstr "Política de Devoluciones."

#: views/settings/wcfm-view-settings.php:101 
#: views/settings/wcfm-view-wcmarketplace-settings.php:123 
#: views/settings/wcfm-view-wcpvendors-settings.php:92 
#: views/settings/wcfm-view-wcvendors-settings.php:122
msgid "Logo"
msgstr "Logo"

#: views/settings/wcfm-view-settings.php:194
msgid "WCFM Pages"
msgstr "Paginas WCFM"

#: views/settings/wcfm-view-settings.php:210
msgid "This page should have shortcode - wc_frontend_manager"
msgstr "Esta página debe tener shortcode - wc_frontend_manager"

#: views/settings/wcfm-view-settings.php:215
msgid "WCFM Endpoints"
msgstr "WCFM Endpoints"

#: views/settings/wcfm-view-wcmarketplace-settings.php:124 
#: views/settings/wcfm-view-wcpvendors-settings.php:93 
#: views/settings/wcfm-view-wcvendors-settings.php:123
msgid "Your shop name is public and must be unique."
msgstr "El nombre de tu Tienda es publico y debe ser único "

#: views/settings/wcfm-view-wcmarketplace-settings.php:126 
#: views/settings/wcfm-view-wcvendors-settings.php:125
msgid "Shop Description"
msgstr "Descripción de la Tienda "

#: views/settings/wcfm-view-wcmarketplace-settings.php:126 
#: views/settings/wcfm-view-wcvendors-settings.php:125
msgid "This is displayed on your shop page."
msgstr "Esto se muestra en la página de tu tienda."

#: views/settings/wcfm-view-wcmarketplace-settings.php:138 
#: views/settings/wcfm-view-wcvendors-settings.php:138
msgid "Brand"
msgstr "Marca "

#: views/settings/wcfm-view-wcmarketplace-settings.php:146
msgid "Shop Phone"
msgstr "Teléfono de la tienda"

#: views/settings/wcfm-view-wcmarketplace-settings.php:146
msgid "Your store phone no."
msgstr "El teléfono de tu tienda."

#: views/settings/wcfm-view-wcmarketplace-settings.php:529
msgid "Policies"
msgstr "Políticas."

#: views/settings/wcfm-view-wcmarketplace-settings.php:537
msgid "Policy Tab Label"
msgstr "Pestaña de Políticas"

#: views/settings/wcfm-view-wcmarketplace-settings.php:561
msgid "Cancellation Policy"
msgstr "Política de Cancelación "

#: views/settings/wcfm-view-wcmarketplace-settings.php:604
msgid "Customer Support"
msgstr "Datos para el Cliente "

#: views/settings/wcfm-view-wcpvendors-settings.php:94
msgid "Vendor Email"
msgstr "Email del Vendedor"

#: views/settings/wcfm-view-wcpvendors-settings.php:95
msgid "Enter the profile information you would like for customer to see."
msgstr "Ingresa la información que te gustaría que el cliente viera. "

#: views/settings/wcfm-view-wcpvendors-settings.php:100 
#: views/settings/wcfm-view-wcpvendors-settings.php:101
msgid "Timezone"
msgstr ""
"Zona horaria\n"

#: views/settings/wcfm-view-wcpvendors-settings.php:100
msgid "Set the local timezone."
msgstr "Establecer el huso horario local."

#: views/settings/wcfm-view-wcpvendors-settings.php:147
msgid "Paypal Email"
msgstr "Paypal Email"

#: views/settings/wcfm-view-wcpvendors-settings.php:147
msgid "PayPal email account where you will receive your commission."
msgstr "Cuenta de PayPal donde recibirás tu comisión"

#: views/settings/wcfm-view-wcpvendors-settings.php:148
msgid ""
"Default commission you will receive per product sale. Please note product "
"level commission can override this. Check your product to confirm."
msgstr ""
"Comisión por defecto que tu recibirás por venta del producto. Ten en cuenta "
"que la comisión de nivel de producto puede anular esto. Comprueba tu "
"producto para confirmar."

#: views/settings/wcfm-view-wcvendors-settings.php:124 
#: views/vendors/wcfm-view-vendors-manage.php:281
msgid "Seller Info"
msgstr "Información sobre el vendedor"

#: views/settings/wcfm-view-wcvendors-settings.php:124
msgid "This is displayed on each of your products."
msgstr "Esto se muestra en cada uno de tus productos."

#: views/settings/wcfm-view-wcvendors-settings.php:147
msgid "Store Website / Blog URL"
msgstr "Webiste de la tienda / URL del Blog"

#: views/settings/wcfm-view-wcvendors-settings.php:147
msgid "Your company/blog URL here."
msgstr "EL URL de tu compañía aquí:"

#: views/settings/wcfm-view-wcvendors-settings.php:148
msgid "This is your store contact number."
msgstr "El numero de contacto de tu tienda."

#: views/settings/wcfm-view-wcvendors-settings.php:169 
#: views/settings/wcfm-view-wcvendors-settings.php:326
msgid "WCV Pro Settings"
msgstr "Ajustes WCV Pro "

#: views/settings/wcfm-view-wcvendors-settings.php:189
msgid "Your PayPal address is used to send you your commission."
msgstr "Tu dirección de PayPal se utiliza para enviarle su comisión."

#: views/settings/wcfm-view-wcvendors-settings.php:298
msgid "Min Charge Order"
msgstr "Cargo mínimo de venta"

#: views/settings/wcfm-view-wcvendors-settings.php:298
msgid "The minimum shipping fee charged for an order."
msgstr "La cuota máxima del envío para una venta."

#: views/settings/wcfm-view-wcvendors-settings.php:299
msgid "Max Charge Order"
msgstr "Orden de carga máxima"

#: views/settings/wcfm-view-wcvendors-settings.php:299
msgid "The maximum shipping fee charged for an order."
msgstr "La cuota máxima del envío para una venta."

#: views/settings/wcfm-view-wcvendors-settings.php:300
msgid "Free Shipping Order"
msgstr "Venta con Envío Gratis"

#: views/settings/wcfm-view-wcvendors-settings.php:300
msgid ""
"Free shipping for order spends over this amount. This will override the max "
"shipping charge above."
msgstr ""
"Envío gratis para ventas con un valor superior a este valor. Esto sobre "
"escribirá el cargo máximo de envió. "

#: views/settings/wcfm-view-wcvendors-settings.php:301
msgid "Max Charge Product"
msgstr "Producto de carga máxima"

#: views/settings/wcfm-view-wcvendors-settings.php:301
msgid "The maximum shipping charged per product no matter the quantity."
msgstr "La carga de envío máxima por el producto no importa la cantidad."

#: views/settings/wcfm-view-wcvendors-settings.php:302
msgid "Free Shipping Product"
msgstr "Producto con Envío Gratis "

#: views/settings/wcfm-view-wcvendors-settings.php:302
msgid ""
"Free shipping if the spend per product is over this amount. This will "
"override the max shipping charge above."
msgstr ""
"Envío gratis si el costo del producto es superior a esta cantidad. Esto "
"sobre escribirá el cargo máximo de envió "

#: views/settings/wcfm-view-wcvendors-settings.php:303
msgid "Product handling fee"
msgstr "Cuota de manejo de producto"

#: views/settings/wcfm-view-wcvendors-settings.php:303
msgid ""
"The product handling fee, this can be overridden on a per product basis. "
"Amount (5.00) or Percentage (5%)."
msgstr ""
"La tarifa de manipulación del producto, esto se puede sobreescribir en una "
"base por producto. Cantidad (5,00) o Porcentaje (5%)."

#: views/settings/wcfm-view-wcvendors-settings.php:310
msgid "From Address"
msgstr "De la Dirección"

#. Name of the plugin
msgid "WooCommerce Frontend Manager"
msgstr "WooCommerce Frontend Manager"

#. Description of the plugin
msgid ""
"WooCommerce is really Easy and Beautiful. We are here to make your life much "
"more Easier and Peaceful."
msgstr ""
"WooCommerce es realmente fácil y hermoso. Estamos aquí para hacer su vida "
"mucho más fácil y pacífica."

#. Author of the plugin
msgid "WC Lovers"
msgstr "WC Lovers"
