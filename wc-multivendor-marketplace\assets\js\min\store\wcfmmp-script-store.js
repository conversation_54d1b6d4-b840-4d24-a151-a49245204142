/*! DO NOT EDIT THIS FILE. This file is a auto generated on 2023-03-25 */
jQuery(document).ready(function(a){a("#wcfmmp-store").parent().hasClass("col-md-8")&&(a("#wcfmmp-store").parent().removeClass("col-md-8"),a("#wcfmmp-store").parent().addClass("col-md-12")),a("#wcfmmp-store").parent().hasClass("col-md-9")&&(a("#wcfmmp-store").parent().removeClass("col-md-9"),a("#wcfmmp-store").parent().addClass("col-md-12")),a("#wcfmmp-store").parent().hasClass("col-sm-8")&&(a("#wcfmmp-store").parent().removeClass("col-sm-8"),a("#wcfmmp-store").parent().addClass("col-md-12")),a("#wcfmmp-store").parent().hasClass("col-sm-9")&&(a("#wcfmmp-store").parent().removeClass("col-sm-9"),a("#wcfmmp-store").parent().addClass("col-md-12")),a("#wcfmmp-store").parent().removeClass("col-sm-push-3"),a("#wcfmmp-store").parent().removeClass("col-sm-push-4"),a("#wcfmmp-store").parent().removeClass("col-md-push-3"),a("#wcfmmp-store").parent().removeClass("col-md-push-4"),0<a(".left_sidebar").length&&768<a(window).width()&&($left_sidebar_height=a(".left_sidebar").outerHeight(),$right_side_height=a(".right_side").outerHeight(),$left_sidebar_height<$right_side_height)&&a(".left_sidebar").css("height",$right_side_height+50),768<a(window).width()&&($wcfm_store_header_width=a("#wcfm_store_header").outerWidth(),a("#wcfmmp-store .address").css("width",$wcfm_store_header_width/3*2-100)),$store_address=jQuery(".wcfm_store_address").val(),$store_lat=jQuery(".wcfm_store_lat").val(),$store_lng=jQuery(".wcfm_store_lng").val(),0<a(".wcfmmp-store-map").length&&a(".wcfmmp-store-map").each(function(){var e,s,t;$store_map=a(this).attr("id"),a("#"+$store_map).css("height",a("#"+$store_map).outerWidth()),"google"==wcfm_maps.lib?(e=new google.maps.LatLng($store_lat,$store_lng),t=new google.maps.Map(document.getElementById($store_map),{center:e,blur:!0,mapTypeId:wcfm_maps.map_type,zoom:parseInt(wcfmmp_store_map_options.default_zoom)}),s={url:wcfmmp_store_map_options.store_icon,scaledSize:new google.maps.Size(wcfmmp_store_map_options.icon_width,wcfmmp_store_map_options.icon_height)},new google.maps.Marker({map:t,position:e,animation:google.maps.Animation.DROP,icon:s,draggable:!1}).addListener("click",function(){wcfm_params.is_mobile||wcfm_params.is_tablet?window.open("https://maps.google.com/?q="+$store_address+",16z?hl=en-US","_blank"):window.open("https://google.com/maps/place/"+$store_address+"/@"+$store_lat+","+$store_lng+",16z?hl=en-US","_blank")})):(t=L.map($store_map,{center:[$store_lat,$store_lng],minZoom:2,zoom:parseInt(wcfmmp_store_map_options.default_zoom),zoomAnimation:!1}),L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{subdomains:["a","b","c"]}).addTo(t),L.marker([$store_lat,$store_lng]).addTo(t).on("click",function(){window.open("https://www.openstreetmap.org/?mlat="+$store_lat+"&mlon="+$store_lng+"#map=14/"+$store_lat+"/"+$store_lng,"_blank")}),a('a[href="#tab-wcfm_location_tab"]').click(function(){setTimeout(function(){t.invalidateSize()},500)}),a('a[href="#tab-wcfm_product_store_tab"]').click(function(){setTimeout(function(){t.invalidateSize()},500)}))}),a("#stars li").on("mouseover",function(){var s=parseInt(a(this).data("value"),10);a(this).parent().children("li.star").each(function(e){e<s?a(this).addClass("hover"):a(this).removeClass("hover")})}).on("mouseout",function(){a(this).parent().children("li.star").each(function(e){a(this).removeClass("hover")})}),a(".stars").each(function(){a(this).find("li").on("mouseover",function(){var s=parseInt(a(this).data("value"),10);a(this).parent().children("li.star").each(function(e){e<s?a(this).addClass("hover"):a(this).removeClass("hover")})}).on("mouseout",function(){a(this).parent().children("li.star").each(function(e){a(this).removeClass("hover")})}),a(this).find("li").on("click",function(){var e=parseInt(a(this).data("value"),10),s=a(this).parent().children("li.star");for(i=0;i<s.length;i++)a(s[i]).removeClass("selected");for(i=0;i<e;i++)a(s[i]).addClass("selected");var t=parseInt(a(this).parent().find("li.selected").last().data("value"),10);a(this).parent().parent().find(".rating_value").val(t),a(this).parent().parent().find(".rating_text").text(t)})}),a(".store_rating_value").each(function(){var e=parseInt(a(this).val()),s=a(this).parent().children("i.fa-star");for(i=0;i<e;i++)a(s[i]).addClass("selected")}),a(".reviews_area_live").addClass("wcfm_custom_hide"),a(".reviews_area_dummy").find('button, input[type="text"]').click(function(){wcfm_params.is_user_logged_in?(a(".reviews_area_dummy").addClass("wcfm_custom_hide"),a(".reviews_area_live").removeClass("wcfm_custom_hide")):alert(wcfm_core_dashboard_messages.user_non_logged_in)}),a(".reviews_area_live").find("a.cancel_review_add").click(function(e){return e.preventDefault(),wcfm_params.is_user_logged_in?(a(".reviews_area_live").addClass("wcfm_custom_hide"),a(".reviews_area_dummy").removeClass("wcfm_custom_hide")):alert(wcfm_core_dashboard_messages.user_non_logged_in),!1}),a("#wcfmmp_store_review_submit").click(function(e){e.preventDefault();var e=a("#wcfmmp_store_review_comment").val();$has_rating=!1,a(".rating_value").each(function(){"0"!=($rating_value=a(this).val())&&($has_rating=!0)}),$is_valid=!0,a(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp(),$has_rating?0==e.length&&($is_valid=!1,a("#wcfmmp_store_review_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfm_reviews_messages.no_comment).addClass("wcfm-error").slideDown(),wcfm_notification_sound.play()):($is_valid=!1,a("#wcfmmp_store_review_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfm_reviews_messages.no_rating).addClass("wcfm-error").slideDown(),wcfm_notification_sound.play()),$is_valid&&(a("#wcfmmp_store_review_form").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),e={action:"wcfm_ajax_controller",controller:"wcfm-reviews-submit",wcfm_store_review_form:jQuery("#wcfmmp_store_review_form").serialize(),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce},a.post(wcfm_params.ajax_url,e,function(e){e&&($response_json=a.parseJSON(e),a(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp(),wcfm_notification_sound.play(),$response_json.status?(a("#wcfmmp_store_review_comment").val(""),a("#wcfmmp_store_review_form .wcfm-message").html('<span class="wcicon-status-completed"></span>'+$response_json.message).addClass("wcfm-success").slideDown("slow",function(){setTimeout(function(){$response_json.redirect&&window.location.reload(),a(".reviews_area_live").addClass("wcfm_custom_hide"),a(".reviews_area_dummy").addClass("wcfm_custom_hide")},2e3)})):a("#wcfmmp_store_review_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+$response_json.message).addClass("wcfm-error").slideDown(),a("#wcfmmp_store_review_form").unblock())}))})}),jQuery(document).ready(function(o){var s;0<o(".wcfm_slider_area").length&&(s={slideIndex:1,timeoutId:0,init:function(){var e=o(".wcfm_slider_area").width();o(".wcfm_slideshow_container").css({width:e,overflow:"hidden"}),this.bindEvents(),this.showSlides()},bindEvents:function(){that=this,o("body").on("click",".wcfm_slideshow_container .next",that.CallnextSlide),o("body").on("click",".wcfm_slideshow_container .prev",that.CallprevSlide)},CallnextSlide:function(){s.plusSlides(1)},CallprevSlide:function(){s.plusSlides(-1)},plusSlides:function(e){clearTimeout(that.timeoutId),s.showSlides(s.slideIndex+=e)},showSlides:function(e){var s,t=this,a=o(".wcfmSlides");for(e>a.length&&(t.slideIndex=1),e<1&&(t.slideIndex=a.length),s=0;s<a.length;s++)a[s].style.display="none";a[t.slideIndex-1].style.display="block",t.timeoutId=setTimeout(function(){t.showSlides(t.slideIndex+=1)},wcfm_slider_banner_delay.delay)}}).init()});