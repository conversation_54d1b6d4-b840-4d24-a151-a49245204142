/*! DO NOT EDIT THIS FILE. This file is a auto generated on 2023-03-25 */
window.MoipSdkJs=function(i){var r={};function n(t){var e;return(r[t]||(e=r[t]={i:t,l:!1,exports:{}},i[t].call(e.exports,e,e.exports,n),e.l=!0,e)).exports}return n.m=i,n.c=r,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:i})},n.r=function(t){Object.defineProperty(t,"__esModule",{value:!0})},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=2)}([function(t,e,i){"use strict";i.r(e);class r{static get _eloBins(){return["401178","401179","431274","438935","451416","457393","457631","457632","504175","627780","636297","636368"]}static get _eloBinRanges(){return[[506699,506778],[509e3,509999],[650031,650033],[650035,650051],[650405,650439],[650485,650538],[650541,650598],[650700,650718],[650720,650727],[650901,650920],[651652,651679],[655e3,655019],[655021,655058]]}static get _hiperBins(){return["637095","637612","637599","637609","637568"]}static get _hipercardBins(){return["606282","384100","384140","384160"]}static get _masterCardRanges(){return[222100,272099]}static _isInEloBinRanges(t){var e=parseInt(t);for(let t=0;t<this._eloBinRanges.length;t++){var i=this._eloBinRanges[t][0],r=this._eloBinRanges[t][1];if(i<=e&&e<=r)return!0}return!1}static _isInMasterCardRanges(t){var e=parseInt(t);for(let t=0;t<this._masterCardRanges.length;t+=2){var i=this._masterCardRanges[t],r=this._masterCardRanges[t+1];if(i<=e&&e<=r)return!0}return!1}static normalizeCardNumber(t){return t&&(t+="").replace(/[\s+|\.|\-]/g,"")}static isValidNumber(t){var i=this.normalizeCardNumber(t),t=this.cardType(i);if(t){if("HIPERCARD"===t.brand)return!0;{let e=0;for(let t=2-i.length%2;t<=i.length;t+=2)e+=parseInt(i.charAt(t-1),10);for(let t=i.length%2+1;t<i.length;t+=2){var r=2*parseInt(i.charAt(t-1),10);e+=r<10?r:r-9}return e%10==0}}return!1}static isSecurityCodeValid(t,e){var i,t=this.cardType(t);return!!t&&(t="AMEX"===t.brand?4:3,i=new RegExp(`[0-9]{${t}}`),!!e)&&e.length===t&&i.test(e)}static isExpiryDateValid(t,e){let i=parseInt(t,10),r=parseInt(e,10);return!(i<1||12<i||2!==(r+"").length&&4!==(r+"").length||(r=2===(r+"").length?80<r?"19"+r:"20"+r:r)<1e3||3e3<=r||this.isExpiredDate(i,r))}static isExpiredDate(t,e){var i=new Date,r=("0"+(i.getMonth()+1)).slice(-2),i=i.getFullYear(),t=("0"+t).slice(-2);if(2===e.toString().length){if(80<e)return!0;e="20"+e}i+=r;return parseInt(e+t,10)<parseInt(i,10)}static isValid(t){var{number:t,cvc:e,expirationMonth:i,expirationYear:r}=t;return this.isValidNumber(t)&&this.isSecurityCodeValid(t,e)&&this.isExpiryDateValid(i,r)}static cardType(t,e){const i=this.normalizeCardNumber(t),r=t=>t.substring(0,6);let n=e?{VISA:{matches:t=>/^4\d{3}\d*$/.test(t)},MASTERCARD:{matches:t=>/^5[1-5]\d{4}\d*$/.test(t)||null!==t&&16==t.length&&this._isInMasterCardRanges(r(t))},AMEX:{matches:t=>/^3[4,7]\d{2}\d*$/.test(t)},DINERS:{matches:t=>/^3(?:0[0-5]|[68][0-9])+\d*$/.test(t)},HIPERCARD:{matches:t=>null!==t&&6<=t.length&&-1<this._hipercardBins.indexOf(r(t))},ELO:{matches:t=>null!==t&&6<=t.length&&(-1<this._eloBins.indexOf(r(t))||this._isInEloBinRanges(r(t)))},HIPER:{matches:t=>null!==t&&6<=t.length&&-1<this._hiperBins.indexOf(r(t))}}:{VISA:{matches:t=>/^4\d{15}$/.test(t)},MASTERCARD:{matches:t=>/^5[1-5]\d{14}$/.test(t)||null!==t&&16==t.length&&this._isInMasterCardRanges(r(t))},AMEX:{matches:t=>/^3[4,7]\d{13}$/.test(t)},DINERS:{matches:t=>/^3[0,6,8]\d{12}$/.test(t)},HIPERCARD:{matches:t=>null!==t&&(16==t.length||19==t.length)&&-1<this._hipercardBins.indexOf(r(t))},ELO:{matches:t=>null!==t&&16==t.length&&(-1<this._eloBins.indexOf(r(t))||this._isInEloBinRanges(r(t)))},HIPER:{matches:t=>null!==t&&6<=t.length&&-1<this._hiperBins.indexOf(r(t))}};return n.ELO.matches(i)?{brand:"ELO"}:n.HIPER.matches(i)?{brand:"HIPER"}:n.VISA.matches(i)?{brand:"VISA"}:n.MASTERCARD.matches(i)?{brand:"MASTERCARD"}:n.AMEX.matches(i)?{brand:"AMEX"}:n.HIPERCARD.matches(i)?{brand:"HIPERCARD"}:n.DINERS.matches(i)?{brand:"DINERS"}:null}}class n{static setEncrypter(t,e){this.encrypter=t,this.encrypterName=e}static encrypt(t,e){if(this.encrypter||this.encrypterName||"undefined"==typeof JSEncrypt||(this.encrypter=JSEncrypt,this.encrypterName="js"),this.encrypter&&this.encrypterName)switch(this.encrypterName.toLowerCase()){case"js":case"ionic":return this.jsEncrypt(t,e);case"node":return this.nodeRSA(t,e);case"react-native":return this.reactNativeRsa(t,e)}return Promise.resolve(null)}static jsEncrypt(i,r){return new Promise(t=>{var e=new this.encrypter({default_key_size:2048});return e.setPublicKey(r),t(e.encrypt(i))})}static nodeRSA(e,i){return new Promise(t=>t(new this.encrypter(i).encrypt(e,"base64")))}static reactNativeRsa(t,e){return this.encrypter.encrypt(t,e)}}class s{static setEncrypter(t,e){return n.setEncrypter(t,e),this}static setCreditCard(t){return t&&(this.creditCard=Object.assign(t,{number:r.normalizeCardNumber(t.number)})),this}static getCreditCard(){return this.creditCard}static setPubKey(t){return this.pubKey=t,this}static hash(){var{number:t,cvc:e,expirationMonth:i,expirationYear:r}=this.creditCard;return this.pubKey&&t&&e&&i&&r?(t=["number="+t,"cvc="+e,"expirationMonth="+i,"expirationYear="+r].join("&"),n.encrypt(t,this.pubKey)):Promise.resolve(null)}static isValid(){return r.isValid(this.creditCard)}static cardType(){var t=r.cardType(this.creditCard.number);return t?t.brand:null}}i.d(e,"MoipValidator",function(){return r}),i.d(e,"MoipCreditCard",function(){return s})},function(t,e,i){!function(j){"use strict";function a(t){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(t)}function H(t,e){return t&e}function h(t,e){return t|e}function q(t,e){return t^e}function L(t,e){return t&~e}var o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function _(t){for(var e,i="",r=0;r+3<=t.length;r+=3)e=parseInt(t.substring(r,r+3),16),i+=o.charAt(e>>6)+o.charAt(63&e);for(r+1==t.length?(e=parseInt(t.substring(r,r+1),16),i+=o.charAt(e<<2)):r+2==t.length&&(e=parseInt(t.substring(r,r+2),16),i+=o.charAt(e>>2)+o.charAt((3&e)<<4));0<(3&i.length);)i+="=";return i}var f,u,t=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])}),F={decode:function(t){if(void 0===u){var e="= \f\n\r\t \u2028\u2029";for(u=Object.create(null),s=0;s<64;++s)u["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(s)]=s;for(s=0;s<e.length;++s)u[e.charAt(s)]=-1}for(var i=[],r=0,n=0,s=0;s<t.length;++s){var o=t.charAt(s);if("="==o)break;if(-1!=(o=u[o])){if(void 0===o)throw new Error("Illegal character at offset "+s);r|=o,4<=++n?(i[i.length]=r>>16,i[i.length]=r>>8&255,i[i.length]=255&r,n=r=0):r<<=6}}switch(n){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:i[i.length]=r>>10;break;case 3:i[i.length]=r>>16,i[i.length]=r>>8&255}return i},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(t){var e=F.re.exec(t);if(e)if(e[1])t=e[1];else{if(!e[2])throw new Error("RegExp out of sync");t=e[2]}return F.decode(t)}},c=1e13,l=(e.prototype.mulAdd=function(t,e){for(var i,r=this.buf,n=r.length,s=0;s<n;++s)(i=r[s]*t+e)<c?e=0:i-=(e=0|i/c)*c,r[s]=i;0<e&&(r[s]=e)},e.prototype.sub=function(t){for(var e,i=this.buf,r=i.length,n=0;n<r;++n)t=(e=i[n]-t)<0?(e+=c,1):0,i[n]=e;for(;0===i[i.length-1];)i.pop()},e.prototype.toString=function(t){if(10!=(t||10))throw new Error("only base 10 is supported");for(var e=this.buf,i=e[e.length-1].toString(),r=e.length-2;0<=r;--r)i+=(c+e[r]).toString().substring(1);return i},e.prototype.valueOf=function(){for(var t=this.buf,e=0,i=t.length-1;0<=i;--i)e=e*c+t[i];return e},e.prototype.simplify=function(){var t=this.buf;return 1==t.length?t[0]:this},e),K=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,U=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function e(t){this.buf=[+t||0]}function p(t,e){return t=t.length>e?t.substring(0,e)+"…":t}v.prototype.get=function(t){if((t=void 0===t?this.pos++:t)>=this.enc.length)throw new Error("Requesting byte offset "+t+" on a stream of length "+this.enc.length);return"string"==typeof this.enc?this.enc.charCodeAt(t):this.enc[t]},v.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},v.prototype.hexDump=function(t,e,i){for(var r="",n=t;n<e;++n)if(r+=this.hexByte(this.get(n)),!0!==i)switch(15&n){case 7:r+="  ";break;case 15:r+="\n";break;default:r+=" "}return r},v.prototype.isASCII=function(t,e){for(var i=t;i<e;++i){var r=this.get(i);if(r<32||176<r)return!1}return!0},v.prototype.parseStringISO=function(t,e){for(var i="",r=t;r<e;++r)i+=String.fromCharCode(this.get(r));return i},v.prototype.parseStringUTF=function(t,e){for(var i="",r=t;r<e;){var n=this.get(r++);i+=n<128?String.fromCharCode(n):191<n&&n<224?String.fromCharCode((31&n)<<6|63&this.get(r++)):String.fromCharCode((15&n)<<12|(63&this.get(r++))<<6|63&this.get(r++))}return i},v.prototype.parseStringBMP=function(t,e){for(var i,r,n="",s=t;s<e;)i=this.get(s++),r=this.get(s++),n+=String.fromCharCode(i<<8|r);return n},v.prototype.parseTime=function(t,e,i){t=this.parseStringISO(t,e),e=(i?K:U).exec(t);return e?(i&&(e[1]=+e[1],e[1]+=+e[1]<70?2e3:1900),t=e[1]+"-"+e[2]+"-"+e[3]+" "+e[4],e[5]&&(t+=":"+e[5],e[6])&&(t+=":"+e[6],e[7])&&(t+="."+e[7]),e[8]&&(t+=" UTC","Z"!=e[8])&&(t+=e[8],e[9])&&(t+=":"+e[9]),t):"Unrecognized time: "+t},v.prototype.parseInteger=function(t,e){for(var i,r=this.get(t),n=127<r,s=n?255:0,o="";r==s&&++t<e;)r=this.get(t);if(0==(i=e-t))return n?-1:0;if(4<i){for(o=r,i<<=3;0==(128&(+o^s));)o=+o<<1,--i;o="("+i+" bit)\n"}n&&(r-=256);for(var h=new l(r),a=t+1;a<e;++a)h.mulAdd(256,this.get(a));return o+h.toString()},v.prototype.parseBitString=function(t,e,i){for(var r=this.get(t),n="("+((e-t-1<<3)-r)+" bit)\n",s="",o=t+1;o<e;++o){for(var h=this.get(o),a=o==e-1?r:0,u=7;a<=u;--u)s+=h>>u&1?"1":"0";if(s.length>i)return n+p(s,i)}return n+s},v.prototype.parseOctetString=function(t,e,i){if(this.isASCII(t,e))return p(this.parseStringISO(t,e),i);var r=e-t,n="("+r+" byte)\n";r>(i/=2)&&(e=t+i);for(var s=t;s<e;++s)n+=this.hexByte(this.get(s));return i<r&&(n+="…"),n},v.prototype.parseOID=function(t,e,i){for(var r="",n=new l,s=0,o=t;o<e;++o){var h=this.get(o);if(n.mulAdd(128,127&h),s+=7,!(128&h)){if(""===r?r=(n=n.simplify())instanceof l?(n.sub(80),"2."+n.toString()):(h=n<80?n<40?0:1:2)+"."+(n-40*h):r+="."+n.toString(),r.length>i)return p(r,i);n=new l,s=0}}return 0<s&&(r+=".incomplete"),r};var k=v,z=(m.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},m.prototype.content=function(t){if(void 0!==this.tag){void 0===t&&(t=1/0);var e=this.posContent(),i=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+i,t);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(e)?"false":"true";case 2:return this.stream.parseInteger(e,e+i);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+i,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+i,t);case 6:return this.stream.parseOID(e,e+i,t);case 16:case 17:return null!==this.sub?"("+this.sub.length+" elem)":"(no elem)";case 12:return p(this.stream.parseStringUTF(e,e+i),t);case 18:case 19:case 20:case 21:case 22:case 26:return p(this.stream.parseStringISO(e,e+i),t);case 30:return p(this.stream.parseStringBMP(e,e+i),t);case 23:case 24:return this.stream.parseTime(e,e+i,23==this.tag.tagNumber)}}return null},m.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"},m.prototype.toPrettyString=function(t){var e=(t=void 0===t?"":t)+this.typeName()+" @"+this.stream.pos;if(0<=this.length&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":!this.tag.isUniversal()||3!=this.tag.tagNumber&&4!=this.tag.tagNumber||null===this.sub||(e+=" (encapsulates)"),e+="\n",null!==this.sub){t+="  ";for(var i=0,r=this.sub.length;i<r;++i)e+=this.sub[i].toPrettyString(t)}return e},m.prototype.posStart=function(){return this.stream.pos},m.prototype.posContent=function(){return this.stream.pos+this.header},m.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},m.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},m.decodeLength=function(t){var e=127&(i=t.get());if(e==i)return e;if(6<e)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(0==e)return null;for(var i=0,r=0;r<e;++r)i=256*i+t.get();return i},m.prototype.getHexStringValue=function(){var t=this.toHexString(),e=2*this.header,i=2*this.length;return t.substr(e,i)},m.decode=function(t){function e(){var t=[];if(null!==n){for(var e=s+n;r.pos<e;)t[t.length]=m.decode(r);if(r.pos!=e)throw new Error("Content size is not correct for container starting at offset "+s)}else try{for(;;){var i=m.decode(r);if(i.tag.isEOC())break;t[t.length]=i}n=s-r.pos}catch(t){throw new Error("Exception while decoding undefined length content: "+t)}return t}var r=t instanceof k?t:new k(t,0),t=new k(r),i=new Z(r),n=m.decodeLength(r),s=r.pos,o=s-t.pos,h=null;if(i.tagConstructed)h=e();else if(i.isUniversal()&&(3==i.tagNumber||4==i.tagNumber))try{if(3==i.tagNumber&&0!=r.get())throw new Error("BIT STRINGs with unused bits cannot encapsulate.");for(var h=e(),a=0;a<h.length;++a)if(h[a].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch(t){h=null}if(null===h){if(null===n)throw new Error("We can't skip over an invalid tag with undefined length at offset "+s);r.pos=s+Math.abs(n)}return new m(t,o,n,i,h)},m),Z=(Q.prototype.isUniversal=function(){return 0===this.tagClass},Q.prototype.isEOC=function(){return 0===this.tagClass&&0===this.tagNumber},Q),g=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],$=(1<<26)/g[g.length-1],d=(y.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var i,r=(1<<e)-1,n=!1,s="",o=this.t,h=this.DB-o*this.DB%e;if(0<o--)for(h<this.DB&&0<(i=this[o]>>h)&&(n=!0,s=a(i));0<=o;)h<e?(i=(this[o]&(1<<h)-1)<<e-h,i|=this[--o]>>(h+=this.DB-e)):(i=this[o]>>(h-=e)&r,h<=0&&(h+=this.DB,--o)),(n=0<i?!0:n)&&(s+=a(i));return n?s:"0"},y.prototype.negate=function(){var t=b();return y.ZERO.subTo(this,t),t},y.prototype.abs=function(){return this.s<0?this.negate():this},y.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var i=this.t;if(0!=(e=i-t.t))return this.s<0?-e:e;for(;0<=--i;)if(0!=(e=this[i]-t[i]))return e;return 0},y.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+R(this[this.t-1]^this.s&this.DM)},y.prototype.mod=function(t){var e=b();return this.abs().divRemTo(t,null,e),this.s<0&&0<e.compareTo(y.ZERO)&&t.subTo(e,e),e},y.prototype.modPowInt=function(t,e){e=new(t<256||e.isEven()?Y:X)(e);return this.exp(t,e)},y.prototype.clone=function(){var t=b();return this.copyTo(t),t},y.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},y.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},y.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},y.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},y.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var i,r=this.DB-t*this.DB%8,n=0;if(0<t--)for(r<this.DB&&(i=this[t]>>r)!=(this.s&this.DM)>>r&&(e[n++]=i|this.s<<this.DB-r);0<=t;)r<8?(i=(this[t]&(1<<r)-1)<<8-r,i|=this[--t]>>(r+=this.DB-8)):(i=this[t]>>(r-=8)&255,r<=0&&(r+=this.DB,--t)),0!=(128&i)&&(i|=-256),0==n&&(128&this.s)!=(128&i)&&++n,(0<n||i!=this.s)&&(e[n++]=i);return e},y.prototype.equals=function(t){return 0==this.compareTo(t)},y.prototype.min=function(t){return this.compareTo(t)<0?this:t},y.prototype.max=function(t){return 0<this.compareTo(t)?this:t},y.prototype.and=function(t){var e=b();return this.bitwiseTo(t,H,e),e},y.prototype.or=function(t){var e=b();return this.bitwiseTo(t,h,e),e},y.prototype.xor=function(t){var e=b();return this.bitwiseTo(t,q,e),e},y.prototype.andNot=function(t){var e=b();return this.bitwiseTo(t,L,e),e},y.prototype.not=function(){for(var t=b(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},y.prototype.shiftLeft=function(t){var e=b();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},y.prototype.shiftRight=function(t){var e=b();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},y.prototype.getLowestSetBit=function(){for(var t,e,i=0;i<this.t;++i)if(0!=this[i])return i*this.DB+(t=this[i],e=void 0,0==t?-1:((e=0)==(65535&t)&&(t>>=16,e+=16),0==(255&t)&&(t>>=8,e+=8),0==(15&t)&&(t>>=4,e+=4),0==(3&t)&&(t>>=2,e+=2),0==(1&t)&&++e,e));return this.s<0?this.t*this.DB:-1},y.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,i=0;i<this.t;++i)t+=function(t){for(var e=0;0!=t;)t&=t-1,++e;return e}(this[i]^e);return t},y.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)},y.prototype.setBit=function(t){return this.changeBit(t,h)},y.prototype.clearBit=function(t){return this.changeBit(t,L)},y.prototype.flipBit=function(t){return this.changeBit(t,q)},y.prototype.add=function(t){var e=b();return this.addTo(t,e),e},y.prototype.subtract=function(t){var e=b();return this.subTo(t,e),e},y.prototype.multiply=function(t){var e=b();return this.multiplyTo(t,e),e},y.prototype.divide=function(t){var e=b();return this.divRemTo(t,e,null),e},y.prototype.remainder=function(t){var e=b();return this.divRemTo(t,null,e),e},y.prototype.divideAndRemainder=function(t){var e=b(),i=b();return this.divRemTo(t,e,i),[e,i]},y.prototype.modPow=function(t,e){var i=t.bitLength(),r=w(1);if(i<=0)return r;var n=i<18?1:i<48?3:i<144?4:i<768?5:6,s=new(i<8?Y:e.isEven()?J:X)(e),o=[],h=3,a=n-1,u=(1<<n)-1;if(o[1]=s.convert(this),1<n){var c=b();for(s.sqrTo(o[1],c);h<=u;)o[h]=b(),s.mulTo(c,o[h-2],o[h]),h+=2}for(var l,f,p=t.t-1,g=!0,d=b(),i=R(t[p])-1;0<=p;){for(a<=i?l=t[p]>>i-a&u:(l=(t[p]&(1<<i+1)-1)<<a-i,0<p&&(l|=t[p-1]>>this.DB+i-a)),h=n;0==(1&l);)l>>=1,--h;if((i-=h)<0&&(i+=this.DB,--p),g)o[l].copyTo(r),g=!1;else{for(;1<h;)s.sqrTo(r,d),s.sqrTo(d,r),h-=2;0<h?s.sqrTo(r,d):(f=r,r=d,d=f),s.mulTo(d,o[l],r)}for(;0<=p&&0==(t[p]&1<<i);)s.sqrTo(r,d),f=r,r=d,d=f,--i<0&&(i=this.DB-1,--p)}return s.revert(r)},y.prototype.modInverse=function(t){var e=t.isEven();if(this.isEven()&&e||0==t.signum())return y.ZERO;for(var i=t.clone(),r=this.clone(),n=w(1),s=w(0),o=w(0),h=w(1);0!=i.signum();){for(;i.isEven();)i.rShiftTo(1,i),e?(n.isEven()&&s.isEven()||(n.addTo(this,n),s.subTo(t,s)),n.rShiftTo(1,n)):s.isEven()||s.subTo(t,s),s.rShiftTo(1,s);for(;r.isEven();)r.rShiftTo(1,r),e?(o.isEven()&&h.isEven()||(o.addTo(this,o),h.subTo(t,h)),o.rShiftTo(1,o)):h.isEven()||h.subTo(t,h),h.rShiftTo(1,h);0<=i.compareTo(r)?(i.subTo(r,i),e&&n.subTo(o,n),s.subTo(h,s)):(r.subTo(i,r),e&&o.subTo(n,o),h.subTo(s,h))}return 0!=r.compareTo(y.ONE)?y.ZERO:0<=h.compareTo(t)?h.subtract(t):h.signum()<0&&(h.addTo(t,h),h.signum()<0)?h.add(t):h},y.prototype.pow=function(t){return this.exp(t,new G)},y.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),i=t.s<0?t.negate():t.clone(),r=(e.compareTo(i)<0&&(t=e,e=i,i=t),e.getLowestSetBit()),t=i.getLowestSetBit();if(t<0)return e;for(0<(t=r<t?r:t)&&(e.rShiftTo(t,e),i.rShiftTo(t,i));0<e.signum();)0<(r=e.getLowestSetBit())&&e.rShiftTo(r,e),0<(r=i.getLowestSetBit())&&i.rShiftTo(r,i),0<=e.compareTo(i)?(e.subTo(i,e),e.rShiftTo(1,e)):(i.subTo(e,i),i.rShiftTo(1,i));return 0<t&&i.lShiftTo(t,i),i},y.prototype.isProbablePrime=function(t){var e,i=this.abs();if(1==i.t&&i[0]<=g[g.length-1]){for(e=0;e<g.length;++e)if(i[0]==g[e])return!0;return!1}if(i.isEven())return!1;for(e=1;e<g.length;){for(var r=g[e],n=e+1;n<g.length&&r<$;)r*=g[n++];for(r=i.modInt(r);e<n;)if(r%g[e++]==0)return!1}return i.millerRabin(t)},y.prototype.copyTo=function(t){for(var e=this.t-1;0<=e;--e)t[e]=this[e];t.t=this.t,t.s=this.s},y.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,0<t?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},y.prototype.fromString=function(t,e){var i;if(16==e)i=4;else if(8==e)i=3;else if(256==e)i=8;else if(2==e)i=1;else if(32==e)i=5;else{if(4!=e)return void this.fromRadix(t,e);i=2}this.t=0,this.s=0;for(var r=t.length,n=!1,s=0;0<=--r;){var o=8==i?255&+t[r]:W(t,r);o<0?"-"==t.charAt(r)&&(n=!0):(n=!1,0==s?this[this.t++]=o:s+i>this.DB?(this[this.t-1]|=(o&(1<<this.DB-s)-1)<<s,this[this.t++]=o>>this.DB-s):this[this.t-1]|=o<<s,(s+=i)>=this.DB&&(s-=this.DB))}8==i&&0!=(128&+t[0])&&(this.s=-1,0<s)&&(this[this.t-1]|=(1<<this.DB-s)-1<<s),this.clamp(),n&&y.ZERO.subTo(this,this)},y.prototype.clamp=function(){for(var t=this.s&this.DM;0<this.t&&this[this.t-1]==t;)--this.t},y.prototype.dlShiftTo=function(t,e){for(var i=this.t-1;0<=i;--i)e[i+t]=this[i];for(i=t-1;0<=i;--i)e[i]=0;e.t=this.t+t,e.s=this.s},y.prototype.drShiftTo=function(t,e){for(var i=t;i<this.t;++i)e[i-t]=this[i];e.t=Math.max(this.t-t,0),e.s=this.s},y.prototype.lShiftTo=function(t,e){for(var i=t%this.DB,r=this.DB-i,n=(1<<r)-1,s=Math.floor(t/this.DB),o=this.s<<i&this.DM,h=this.t-1;0<=h;--h)e[h+s+1]=this[h]>>r|o,o=(this[h]&n)<<i;for(h=s-1;0<=h;--h)e[h]=0;e[s]=o,e.t=this.t+s+1,e.s=this.s,e.clamp()},y.prototype.rShiftTo=function(t,e){e.s=this.s;var i=Math.floor(t/this.DB);if(i>=this.t)e.t=0;else{var r=t%this.DB,n=this.DB-r,s=(1<<r)-1;e[0]=this[i]>>r;for(var o=i+1;o<this.t;++o)e[o-i-1]|=(this[o]&s)<<n,e[o-i]=this[o]>>r;0<r&&(e[this.t-i-1]|=(this.s&s)<<n),e.t=this.t-i,e.clamp()}},y.prototype.subTo=function(t,e){for(var i=0,r=0,n=Math.min(t.t,this.t);i<n;)r+=this[i]-t[i],e[i++]=r&this.DM,r>>=this.DB;if(t.t<this.t){for(r-=t.s;i<this.t;)r+=this[i],e[i++]=r&this.DM,r>>=this.DB;r+=this.s}else{for(r+=this.s;i<t.t;)r-=t[i],e[i++]=r&this.DM,r>>=this.DB;r-=t.s}e.s=r<0?-1:0,r<-1?e[i++]=this.DV+r:0<r&&(e[i++]=r),e.t=i,e.clamp()},y.prototype.multiplyTo=function(t,e){var i=this.abs(),r=t.abs(),n=i.t;for(e.t=n+r.t;0<=--n;)e[n]=0;for(n=0;n<r.t;++n)e[n+i.t]=i.am(0,r[n],e,n,0,i.t);e.s=0,e.clamp(),this.s!=t.s&&y.ZERO.subTo(e,e)},y.prototype.squareTo=function(t){for(var e=this.abs(),i=t.t=2*e.t;0<=--i;)t[i]=0;for(i=0;i<e.t-1;++i){var r=e.am(i,e[i],t,2*i,0,1);(t[i+e.t]+=e.am(i+1,2*e[i],t,2*i+1,r,e.t-i-1))>=e.DV&&(t[i+e.t]-=e.DV,t[i+e.t+1]=1)}0<t.t&&(t[t.t-1]+=e.am(i,e[i],t,2*i,0,1)),t.s=0,t.clamp()},y.prototype.divRemTo=function(t,e,i){var r=t.abs();if(!(r.t<=0)){var n=this.abs();if(n.t<r.t)null!=e&&e.fromInt(0),null!=i&&this.copyTo(i);else{null==i&&(i=b());var s=b(),o=this.s,t=t.s,h=this.DB-R(r[r.t-1]),a=(0<h?(r.lShiftTo(h,s),n.lShiftTo(h,i)):(r.copyTo(s),n.copyTo(i)),s.t),u=s[a-1];if(0!=u){var r=u*(1<<this.F1)+(1<a?s[a-2]>>this.F2:0),c=this.FV/r,l=(1<<this.F1)/r,f=1<<this.F2,p=i.t,g=p-a,d=null==e?b():e;for(s.dlShiftTo(g,d),0<=i.compareTo(d)&&(i[i.t++]=1,i.subTo(d,i)),y.ONE.dlShiftTo(a,d),d.subTo(s,s);s.t<a;)s[s.t++]=0;for(;0<=--g;){var m=i[--p]==u?this.DM:Math.floor(i[p]*c+(i[p-1]+f)*l);if((i[p]+=s.am(0,m,i,g,0,a))<m)for(s.dlShiftTo(g,d),i.subTo(d,i);i[p]<--m;)i.subTo(d,i)}null!=e&&(i.drShiftTo(a,e),o!=t)&&y.ZERO.subTo(e,e),i.t=a,i.clamp(),0<h&&i.rShiftTo(h,i),o<0&&y.ZERO.subTo(i,i)}}}},y.prototype.invDigit=function(){var t,e;return this.t<1||0==(1&(t=this[0]))?0:0<(e=(e=(e=(e=(e=3&t)*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)?this.DV-e:-e},y.prototype.isEven=function(){return 0==(0<this.t?1&this[0]:this.s)},y.prototype.exp=function(t,e){if(4294967295<t||t<1)return y.ONE;var i,r=b(),n=b(),s=e.convert(this),o=R(t)-1;for(s.copyTo(r);0<=--o;)e.sqrTo(r,n),0<(t&1<<o)?e.mulTo(n,s,r):(i=r,r=n,n=i);return e.revert(r)},y.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},y.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||36<t)return"0";var e=this.chunkSize(t),i=Math.pow(t,e),r=w(i),n=b(),s=b(),o="";for(this.divRemTo(r,n,s);0<n.signum();)o=(i+s.intValue()).toString(t).substr(1)+o,n.divRemTo(r,n,s);return s.intValue().toString(t)+o},y.prototype.fromRadix=function(t,e){this.fromInt(0);for(var i=this.chunkSize(e=null==e?10:e),r=Math.pow(e,i),n=!1,s=0,o=0,h=0;h<t.length;++h){var a=W(t,h);a<0?"-"==t.charAt(h)&&0==this.signum()&&(n=!0):(o=e*o+a,++s>=i&&(this.dMultiply(r),this.dAddOffset(o,0),o=s=0))}0<s&&(this.dMultiply(Math.pow(e,s)),this.dAddOffset(o,0)),n&&y.ZERO.subTo(this,this)},y.prototype.fromNumber=function(t,e,i){if("number"==typeof e)if(t<2)this.fromInt(1);else for(this.fromNumber(t,i),this.testBit(t-1)||this.bitwiseTo(y.ONE.shiftLeft(t-1),h,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(e);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(y.ONE.shiftLeft(t-1),this);else{var i=[],r=7&t;i.length=1+(t>>3),e.nextBytes(i),0<r?i[0]&=(1<<r)-1:i[0]=0,this.fromString(i,256)}},y.prototype.bitwiseTo=function(t,e,i){for(var r,n=Math.min(t.t,this.t),s=0;s<n;++s)i[s]=e(this[s],t[s]);if(t.t<this.t){for(r=t.s&this.DM,s=n;s<this.t;++s)i[s]=e(this[s],r);i.t=this.t}else{for(r=this.s&this.DM,s=n;s<t.t;++s)i[s]=e(r,t[s]);i.t=t.t}i.s=e(this.s,t.s),i.clamp()},y.prototype.changeBit=function(t,e){t=y.ONE.shiftLeft(t);return this.bitwiseTo(t,e,t),t},y.prototype.addTo=function(t,e){for(var i=0,r=0,n=Math.min(t.t,this.t);i<n;)r+=this[i]+t[i],e[i++]=r&this.DM,r>>=this.DB;if(t.t<this.t){for(r+=t.s;i<this.t;)r+=this[i],e[i++]=r&this.DM,r>>=this.DB;r+=this.s}else{for(r+=this.s;i<t.t;)r+=t[i],e[i++]=r&this.DM,r>>=this.DB;r+=t.s}e.s=r<0?-1:0,0<r?e[i++]=r:r<-1&&(e[i++]=this.DV+r),e.t=i,e.clamp()},y.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},y.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},y.prototype.multiplyLowerTo=function(t,e,i){var r=Math.min(this.t+t.t,e);for(i.s=0,i.t=r;0<r;)i[--r]=0;for(var n=i.t-this.t;r<n;++r)i[r+this.t]=this.am(0,t[r],i,r,0,this.t);for(n=Math.min(t.t,e);r<n;++r)this.am(0,t[r],i,r,0,e-r);i.clamp()},y.prototype.multiplyUpperTo=function(t,e,i){var r=i.t=this.t+t.t- --e;for(i.s=0;0<=--r;)i[r]=0;for(r=Math.max(e-this.t,0);r<t.t;++r)i[this.t+r-e]=this.am(e-r,t[r],i,0,0,this.t+r-e);i.clamp(),i.drShiftTo(1,i)},y.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,i=this.s<0?t-1:0;if(0<this.t)if(0==e)i=this[0]%t;else for(var r=this.t-1;0<=r;--r)i=(e*i+this[r])%t;return i},y.prototype.millerRabin=function(t){var e=this.subtract(y.ONE),i=e.getLowestSetBit();if(i<=0)return!1;var r=e.shiftRight(i);(t=t+1>>1)>g.length&&(t=g.length);for(var n=b(),s=0;s<t;++s){n.fromInt(g[Math.floor(Math.random()*g.length)]);var o=n.modPow(r,this);if(0!=o.compareTo(y.ONE)&&0!=o.compareTo(e)){for(var h=1;h++<i&&0!=o.compareTo(e);)if(0==(o=o.modPowInt(2,this)).compareTo(y.ONE))return!1;if(0!=o.compareTo(e))return!1}}return!0},y.prototype.square=function(){var t=b();return this.squareTo(t),t},y.prototype.gcda=function(t,e){var i,r=this.s<0?this.negate():this.clone(),n=t.s<0?t.negate():t.clone(),s=(r.compareTo(n)<0&&(t=r,r=n,n=t),r.getLowestSetBit()),o=n.getLowestSetBit();o<0?e(r):(0<(o=s<o?s:o)&&(r.rShiftTo(o,r),n.rShiftTo(o,n)),i=function(){0<(s=r.getLowestSetBit())&&r.rShiftTo(s,r),0<(s=n.getLowestSetBit())&&n.rShiftTo(s,n),0<=r.compareTo(n)?(r.subTo(n,r),r.rShiftTo(1,r)):(n.subTo(r,n),n.rShiftTo(1,n)),0<r.signum()?setTimeout(i,0):(0<o&&n.lShiftTo(o,n),setTimeout(function(){e(n)},0))},setTimeout(i,10))},y.prototype.fromNumberAsync=function(t,e,i,r){var n,s,o;"number"==typeof e?t<2?this.fromInt(1):(this.fromNumber(t,i),this.testBit(t-1)||this.bitwiseTo(y.ONE.shiftLeft(t-1),h,this),this.isEven()&&this.dAddOffset(1,0),n=this,s=function(){n.dAddOffset(2,0),n.bitLength()>t&&n.subTo(y.ONE.shiftLeft(t-1),n),n.isProbablePrime(e)?setTimeout(function(){r()},0):setTimeout(s,0)},setTimeout(s,0)):(i=7&t,(o=[]).length=1+(t>>3),e.nextBytes(o),0<i?o[0]&=(1<<i)-1:o[0]=0,this.fromString(o,256))},y),G=(s.prototype.convert=function(t){return t},s.prototype.revert=function(t){return t},s.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i)},s.prototype.sqrTo=function(t,e){t.squareTo(e)},s),Y=(n.prototype.convert=function(t){return t.s<0||0<=t.compareTo(this.m)?t.mod(this.m):t},n.prototype.revert=function(t){return t},n.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},n.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i),this.reduce(i)},n.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},n),X=(r.prototype.convert=function(t){var e=b();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&0<e.compareTo(d.ZERO)&&this.m.subTo(e,e),e},r.prototype.revert=function(t){var e=b();return t.copyTo(e),this.reduce(e),e},r.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var i=32767&t[e],r=i*this.mpl+((i*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[i=e+this.m.t]+=this.m.am(0,r,t,e,0,this.m.t);t[i]>=t.DV;)t[i]-=t.DV,t[++i]++}t.clamp(),t.drShiftTo(this.m.t,t),0<=t.compareTo(this.m)&&t.subTo(this.m,t)},r.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i),this.reduce(i)},r.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},r),J=(i.prototype.convert=function(t){var e;return t.s<0||t.t>2*this.m.t?t.mod(this.m):t.compareTo(this.m)<0?t:(e=b(),t.copyTo(e),this.reduce(e),e)},i.prototype.revert=function(t){return t},i.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);0<=t.compareTo(this.m);)t.subTo(this.m,t)},i.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i),this.reduce(i)},i.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},i);function i(t){this.m=t,this.r2=b(),this.q3=b(),d.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}function r(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}function n(t){this.m=t}function s(){}function y(t,e,i){null!=t&&("number"==typeof t?this.fromNumber(t,e,i):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}function Q(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=0!=(32&e),this.tagNumber=31&e,31==this.tagNumber){for(var i=new l;e=t.get(),i.mulAdd(128,127&e),128&e;);this.tagNumber=i.simplify()}}function m(t,e,i,r,n){if(!(r instanceof Z))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=i,this.tag=r,this.sub=n}function v(t,e){this.hexDigits="0123456789ABCDEF",t instanceof v?(this.enc=t.enc,this.pos=t.pos):(this.enc=t,this.pos=e)}function b(){return new d(null)}function T(t,e){return new d(t,e)}V="Microsoft Internet Explorer"==navigator.appName?(d.prototype.am=function(t,e,i,r,n,s){for(var o=32767&e,h=e>>15;0<=--s;){var a=32767&this[t],u=this[t++]>>15,c=h*a+u*o;n=((a=o*a+((32767&c)<<15)+i[r]+(1073741823&n))>>>30)+(c>>>15)+h*u+(n>>>30),i[r++]=1073741823&a}return n},30):"Netscape"!=navigator.appName?(d.prototype.am=function(t,e,i,r,n,s){for(;0<=--s;){var o=e*this[t++]+i[r]+n;n=Math.floor(o/67108864),i[r++]=67108863&o}return n},26):(d.prototype.am=function(t,e,i,r,n,s){for(var o=16383&e,h=e>>14;0<=--s;){var a=16383&this[t],u=this[t++]>>14,c=h*a+u*o;n=((a=o*a+((16383&c)<<14)+i[r]+n)>>28)+(c>>14)+h*u,i[r++]=268435455&a}return n},28),d.prototype.DB=V,d.prototype.DM=(1<<V)-1,d.prototype.DV=1<<V,d.prototype.FV=Math.pow(2,52),d.prototype.F1=52-V,d.prototype.F2=2*V-52;for(var S=[],E="0".charCodeAt(0),D=0;D<=9;++D)S[E++]=D;for(E="a".charCodeAt(0),D=10;D<36;++D)S[E++]=D;for(E="A".charCodeAt(0),D=10;D<36;++D)S[E++]=D;function W(t,e){t=S[t.charCodeAt(e)];return null==t?-1:t}function w(t){var e=b();return e.fromInt(t),e}function R(t){var e,i=1;return 0!=(e=t>>>16)&&(t=e,i+=16),0!=(e=t>>8)&&(t=e,i+=8),0!=(e=t>>4)&&(t=e,i+=4),0!=(e=t>>2)&&(t=e,i+=2),0!=(e=t>>1)&&(t=e,i+=1),i}d.ZERO=w(0),d.ONE=w(1);et.prototype.init=function(t){for(var e,i,r=0;r<256;++r)this.S[r]=r;for(r=e=0;r<256;++r)e=e+this.S[r]+t[r%t.length]&255,i=this.S[r],this.S[r]=this.S[e],this.S[e]=i;this.i=0,this.j=0},et.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]};var x,tt=et;function et(){this.i=0,this.j=0,this.S=[]}if(null==(B=null)){var B=[],A=0,O=void 0;if(window.crypto&&window.crypto.getRandomValues){var it=new Uint32Array(256);for(window.crypto.getRandomValues(it),O=0;O<it.length;++O)B[A++]=255&it[O]}var rt=function(t){if(this.count=this.count||0,256<=this.count||256<=A)window.removeEventListener?window.removeEventListener("mousemove",rt,!1):window.detachEvent&&window.detachEvent("onmousemove",rt);else try{var e=t.x+t.y;B[A++]=255&e,this.count+=1}catch(t){}};window.addEventListener?window.addEventListener("mousemove",rt,!1):window.attachEvent&&window.attachEvent("onmousemove",rt)}st.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=function(){if(null==x){for(x=new tt;A<256;){var t=Math.floor(65536*Math.random());B[A++]=255&t}for(x.init(B),A=0;A<B.length;++A)B[A]=0;A=0}return x.next()}()};var nt=st,V=(N.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},N.prototype.doPrivate=function(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),i=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(i)<0;)e=e.add(this.p);return e.subtract(i).multiply(this.coeff).mod(this.p).multiply(this.q).add(i)},N.prototype.setPublic=function(t,e){null!=t&&null!=e&&0<t.length&&0<e.length?(this.n=T(t,16),this.e=parseInt(e,16)):console.error("Invalid RSA public key")},N.prototype.encrypt=function(t){var t=function(t,e){if(e<t.length+11)return console.error("Message too long for RSA"),null;for(var i=[],r=t.length-1;0<=r&&0<e;){var n=t.charCodeAt(r--);n<128?i[--e]=n:127<n&&n<2048?(i[--e]=63&n|128,i[--e]=n>>6|192):(i[--e]=63&n|128,i[--e]=n>>6&63|128,i[--e]=n>>12|224)}i[--e]=0;for(var s=new nt,o=[];2<e;){for(o[0]=0;0==o[0];)s.nextBytes(o);i[--e]=o[0]}return i[--e]=2,i[--e]=0,new d(i)}(t,this.n.bitLength()+7>>3);return null==t||null==(t=this.doPublic(t))?null:0==(1&(t=t.toString(16)).length)?t:"0"+t},N.prototype.setPrivate=function(t,e,i){null!=t&&null!=e&&0<t.length&&0<e.length?(this.n=T(t,16),this.e=parseInt(e,16),this.d=T(i,16)):console.error("Invalid RSA private key")},N.prototype.setPrivateEx=function(t,e,i,r,n,s,o,h){null!=t&&null!=e&&0<t.length&&0<e.length?(this.n=T(t,16),this.e=parseInt(e,16),this.d=T(i,16),this.p=T(r,16),this.q=T(n,16),this.dmp1=T(s,16),this.dmq1=T(o,16),this.coeff=T(h,16)):console.error("Invalid RSA private key")},N.prototype.generate=function(t,e){var i=new nt,r=t>>1;this.e=parseInt(e,16);for(var n=new d(e,16);;){for(;this.p=new d(t-r,1,i),0!=this.p.subtract(d.ONE).gcd(n).compareTo(d.ONE)||!this.p.isProbablePrime(10););for(;this.q=new d(r,1,i),0!=this.q.subtract(d.ONE).gcd(n).compareTo(d.ONE)||!this.q.isProbablePrime(10););this.p.compareTo(this.q)<=0&&(s=this.p,this.p=this.q,this.q=s);var s=this.p.subtract(d.ONE),o=this.q.subtract(d.ONE),h=s.multiply(o);if(0==h.gcd(n).compareTo(d.ONE)){this.n=this.p.multiply(this.q),this.d=n.modInverse(h),this.dmp1=this.d.mod(s),this.dmq1=this.d.mod(o),this.coeff=this.q.modInverse(this.p);break}}},N.prototype.decrypt=function(t){t=T(t,16),t=this.doPrivate(t);if(null==t)return null;for(var e=this.n.bitLength()+7>>3,i=t.toByteArray(),r=0;r<i.length&&0==i[r];)++r;if(i.length-r!=e-1||2!=i[r])return null;for(++r;0!=i[r];)if(++r>=i.length)return null;for(var n="";++r<i.length;){var s=255&i[r];s<128?n+=String.fromCharCode(s):191<s&&s<224?(n+=String.fromCharCode((31&s)<<6|63&i[r+1]),++r):(n+=String.fromCharCode((15&s)<<12|(63&i[r+1])<<6|63&i[r+2]),r+=2)}return n},N.prototype.generateAsync=function(t,e,n){var s=new nt,o=t>>1,h=(this.e=parseInt(e,16),new d(e,16)),a=this,u=function(){function e(){a.p=b(),a.p.fromNumberAsync(t-o,1,s,function(){a.p.subtract(d.ONE).gcda(h,function(t){0==t.compareTo(d.ONE)&&a.p.isProbablePrime(10)?setTimeout(r,0):setTimeout(e,0)})})}var i=function(){a.p.compareTo(a.q)<=0&&(t=a.p,a.p=a.q,a.q=t);var t=a.p.subtract(d.ONE),e=a.q.subtract(d.ONE),i=t.multiply(e);0==i.gcd(h).compareTo(d.ONE)?(a.n=a.p.multiply(a.q),a.d=h.modInverse(i),a.dmp1=a.d.mod(t),a.dmq1=a.d.mod(e),a.coeff=a.q.modInverse(a.p),setTimeout(function(){n()},0)):setTimeout(u,0)},r=function(){a.q=b(),a.q.fromNumberAsync(o,1,s,function(){a.q.subtract(d.ONE).gcda(h,function(t){0==t.compareTo(d.ONE)&&a.q.isProbablePrime(10)?setTimeout(i,0):setTimeout(r,0)})})};setTimeout(e,0)};setTimeout(u,0)},N),I={};function N(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}function st(){}I.lang={extend:function(t,e,i){if(!e||!t)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");function r(){}if(r.prototype=e.prototype,t.prototype=new r,(t.prototype.constructor=t).superclass=e.prototype,e.prototype.constructor==Object.prototype.constructor&&(e.prototype.constructor=e),i){for(var n in i)t.prototype[n]=i[n];var e=function(){},s=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(e=function(t,e){for(n=0;n<s.length;n+=1){var i=s[n],r=e[i];"function"==typeof r&&r!=Object.prototype[i]&&(t[i]=r)}})}catch(t){}e(t.prototype,i)}}};var ot,ht,P={},at=(void 0!==P.asn1&&P.asn1||(P.asn1={}),P.asn1.ASN1Util=new function(){this.integerToByteHex=function(t){t=t.toString(16);return t=t.length%2==1?"0"+t:t},this.bigIntToMinTwosComplementsHex=function(t){var e=t.toString(16);if("-"!=e.substr(0,1))e.length%2==1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{var i=e.substr(1).length;i%2==1?i+=1:e.match(/^[0-7]/)||(i+=2);for(var r="",n=0;n<i;n++)r+="f";e=new d(r,16).xor(t).add(d.ONE).toString(16).replace(/^-/,"")}return e},this.getPEMStringFromHex=function(t,e){return hextopem(t,e)},this.newObject=function(t){var e=P.asn1,i=e.DERBoolean,r=e.DERInteger,n=e.DERBitString,s=e.DEROctetString,o=e.DERNull,h=e.DERObjectIdentifier,a=e.DEREnumerated,u=e.DERUTF8String,c=e.DERNumericString,l=e.DERPrintableString,f=e.DERTeletexString,p=e.DERIA5String,g=e.DERUTCTime,d=e.DERGeneralizedTime,m=e.DERSequence,y=e.DERSet,v=e.DERTaggedObject,b=e.ASN1Util.newObject,e=Object.keys(t);if(1!=e.length)throw"key of param shall be only one.";e=e[0];if(-1==":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+e+":"))throw"undefined key: "+e;if("bool"==e)return new i(t[e]);if("int"==e)return new r(t[e]);if("bitstr"==e)return new n(t[e]);if("octstr"==e)return new s(t[e]);if("null"==e)return new o(t[e]);if("oid"==e)return new h(t[e]);if("enum"==e)return new a(t[e]);if("utf8str"==e)return new u(t[e]);if("numstr"==e)return new c(t[e]);if("prnstr"==e)return new l(t[e]);if("telstr"==e)return new f(t[e]);if("ia5str"==e)return new p(t[e]);if("utctime"==e)return new g(t[e]);if("gentime"==e)return new d(t[e]);if("seq"==e){for(var T=t[e],S=[],E=0;E<T.length;E++){var D=b(T[E]);S.push(D)}return new m({array:S})}if("set"==e){for(T=t[e],S=[],E=0;E<T.length;E++)D=b(T[E]),S.push(D);return new y({array:S})}if("tag"==e){i=t[e];if("[object Array]"===Object.prototype.toString.call(i)&&3==i.length)return r=b(i[2]),new v({tag:i[0],explicit:i[1],obj:r});n={};if(void 0!==i.explicit&&(n.explicit=i.explicit),void 0!==i.tag&&(n.tag=i.tag),void 0===i.obj)throw"obj shall be specified for 'tag'.";return n.obj=b(i.obj),new v(n)}},this.jsonToASN1HEX=function(t){return this.newObject(t).getEncodedHex()}},P.asn1.ASN1Util.oidHexToInt=function(t){for(var e="",i=parseInt(t.substr(0,2),16),r=(e=Math.floor(i/40)+"."+i%40,""),n=2;n<t.length;n+=2){var s=("00000000"+parseInt(t.substr(n,2),16).toString(2)).slice(-8);r+=s.substr(1,7),"0"==s.substr(0,1)&&(e=e+"."+new d(r,2).toString(10),r="")}return e},P.asn1.ASN1Util.oidIntToHex=function(t){function h(t){return t=1==(t=t.toString(16)).length?"0"+t:t}if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var e="",i=t.split("."),t=40*parseInt(i[0])+parseInt(i[1]);e+=h(t),i.splice(0,2);for(var r=0;r<i.length;r++)e+=function(t){var e="",i=new d(t,10).toString(2),r=7-i.length%7;7==r&&(r=0);for(var n="",s=0;s<r;s++)n+="0";for(i=n+i,s=0;s<i.length-1;s+=7){var o=i.substr(s,7);s!=i.length-7&&(o="1"+o),e+=h(parseInt(o,2))}return e}(i[r]);return e},P.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(void 0===this.hV||null==this.hV)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n="+"".length+",v="+this.hV;var t=this.hV.length/2,e=t.toString(16);if(e.length%2==1&&(e="0"+e),t<128)return e;var i=e.length/2;if(15<i)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);return(128+i).toString(16)+e},this.getEncodedHex=function(){return null!=this.hTLV&&!this.isModified||(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},P.asn1.DERAbstractString=function(t){P.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?this.setString(t):void 0!==t.str?this.setString(t.str):void 0!==t.hex&&this.setStringHex(t.hex))},I.lang.extend(P.asn1.DERAbstractString,P.asn1.ASN1Object),P.asn1.DERAbstractTime=function(t){P.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,i){var r=this.zeroPadding,t=this.localDateToUTC(t),n=String(t.getFullYear()),e=(n="utc"==e?n.substr(2,2):n)+r(String(t.getMonth()+1),2)+r(String(t.getDate()),2)+r(String(t.getHours()),2)+r(String(t.getMinutes()),2)+r(String(t.getSeconds()),2);return(e=!0===i&&0!=(n=t.getMilliseconds())?e+"."+r(String(n),3).replace(/[0]+$/,""):e)+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,i,r,n,s){t=new Date(Date.UTC(t,e-1,i,r,n,s,0));this.setByDate(t)},this.getFreshValueHex=function(){return this.hV}},I.lang.extend(P.asn1.DERAbstractTime,P.asn1.ASN1Object),P.asn1.DERAbstractStructured=function(t){P.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,void 0!==t&&void 0!==t.array&&(this.asn1Array=t.array)},I.lang.extend(P.asn1.DERAbstractStructured,P.asn1.ASN1Object),P.asn1.DERBoolean=function(){P.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},I.lang.extend(P.asn1.DERBoolean,P.asn1.ASN1Object),P.asn1.DERInteger=function(t){P.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=P.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){t=new d(String(t),10);this.setByBigInteger(t)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.bigint?this.setByBigInteger(t.bigint):void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},I.lang.extend(P.asn1.DERInteger,P.asn1.ASN1Object),P.asn1.DERBitString=function(t){var e;void 0!==t&&void 0!==t.obj&&(e=P.asn1.ASN1Util.newObject(t.obj),t.hex="00"+e.getEncodedHex()),P.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(t){this.hTLV=null,this.isModified=!0,this.hV=t},this.setUnusedBitsAndHexValue=function(t,e){if(t<0||7<t)throw"unused bits shall be from 0 to 7: u = "+t;t="0"+t;this.hTLV=null,this.isModified=!0,this.hV=t+e},this.setByBinaryString=function(t){var e=8-(t=t.replace(/0+$/,"")).length%8;8==e&&(e=0);for(var i=0;i<=e;i++)t+="0";for(var r="",i=0;i<t.length-1;i+=8){var n=t.substr(i,8),n=parseInt(n,2).toString(16);r+=n=1==n.length?"0"+n:n}this.hTLV=null,this.isModified=!0,this.hV="0"+e+r},this.setByBooleanArray=function(t){for(var e="",i=0;i<t.length;i++)1==t[i]?e+="1":e+="0";this.setByBinaryString(e)},this.newFalseArray=function(t){for(var e=new Array(t),i=0;i<t;i++)e[i]=!1;return e},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t&&t.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(t):void 0!==t.hex?this.setHexValueIncludingUnusedBits(t.hex):void 0!==t.bin?this.setByBinaryString(t.bin):void 0!==t.array&&this.setByBooleanArray(t.array))},I.lang.extend(P.asn1.DERBitString,P.asn1.ASN1Object),P.asn1.DEROctetString=function(t){var e;void 0!==t&&void 0!==t.obj&&(e=P.asn1.ASN1Util.newObject(t.obj),t.hex=e.getEncodedHex()),P.asn1.DEROctetString.superclass.constructor.call(this,t),this.hT="04"},I.lang.extend(P.asn1.DEROctetString,P.asn1.DERAbstractString),P.asn1.DERNull=function(){P.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},I.lang.extend(P.asn1.DERNull,P.asn1.ASN1Object),P.asn1.DERObjectIdentifier=function(t){var h=function(t){t=t.toString(16);return t=1==t.length?"0"+t:t};P.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.setValueOidString=function(t){if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var e="",i=t.split("."),t=40*parseInt(i[0])+parseInt(i[1]);e+=h(t),i.splice(0,2);for(var r=0;r<i.length;r++)e+=function(t){var e="",i=new d(t,10).toString(2),r=7-i.length%7;7==r&&(r=0);for(var n="",s=0;s<r;s++)n+="0";for(i=n+i,s=0;s<i.length-1;s+=7){var o=i.substr(s,7);s!=i.length-7&&(o="1"+o),e+=h(parseInt(o,2))}return e}(i[r]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=e},this.setValueName=function(t){var e=P.asn1.x509.OID.name2oid(t);if(""===e)throw"DERObjectIdentifier oidName undefined: "+t;this.setValueOidString(e)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?t.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(t):this.setValueName(t):void 0!==t.oid?this.setValueOidString(t.oid):void 0!==t.hex?this.setValueHex(t.hex):void 0!==t.name&&this.setValueName(t.name))},I.lang.extend(P.asn1.DERObjectIdentifier,P.asn1.ASN1Object),P.asn1.DEREnumerated=function(t){P.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=P.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){t=new d(String(t),10);this.setByBigInteger(t)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},I.lang.extend(P.asn1.DEREnumerated,P.asn1.ASN1Object),P.asn1.DERUTF8String=function(t){P.asn1.DERUTF8String.superclass.constructor.call(this,t),this.hT="0c"},I.lang.extend(P.asn1.DERUTF8String,P.asn1.DERAbstractString),P.asn1.DERNumericString=function(t){P.asn1.DERNumericString.superclass.constructor.call(this,t),this.hT="12"},I.lang.extend(P.asn1.DERNumericString,P.asn1.DERAbstractString),P.asn1.DERPrintableString=function(t){P.asn1.DERPrintableString.superclass.constructor.call(this,t),this.hT="13"},I.lang.extend(P.asn1.DERPrintableString,P.asn1.DERAbstractString),P.asn1.DERTeletexString=function(t){P.asn1.DERTeletexString.superclass.constructor.call(this,t),this.hT="14"},I.lang.extend(P.asn1.DERTeletexString,P.asn1.DERAbstractString),P.asn1.DERIA5String=function(t){P.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="16"},I.lang.extend(P.asn1.DERIA5String,P.asn1.DERAbstractString),P.asn1.DERUTCTime=function(t){P.asn1.DERUTCTime.superclass.constructor.call(this,t),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{12}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date))},I.lang.extend(P.asn1.DERUTCTime,P.asn1.DERAbstractTime),P.asn1.DERGeneralizedTime=function(t){P.asn1.DERGeneralizedTime.superclass.constructor.call(this,t),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{14}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date),!0===t.millis)&&(this.withMillis=!0)},I.lang.extend(P.asn1.DERGeneralizedTime,P.asn1.DERAbstractTime),P.asn1.DERSequence=function(t){P.asn1.DERSequence.superclass.constructor.call(this,t),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++)t+=this.asn1Array[e].getEncodedHex();return this.hV=t,this.hV}},I.lang.extend(P.asn1.DERSequence,P.asn1.DERAbstractStructured),P.asn1.DERSet=function(t){P.asn1.DERSet.superclass.constructor.call(this,t),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var i=this.asn1Array[e];t.push(i.getEncodedHex())}return 1==this.sortFlag&&t.sort(),this.hV=t.join(""),this.hV},void 0!==t&&void 0!==t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},I.lang.extend(P.asn1.DERSet,P.asn1.DERAbstractStructured),P.asn1.DERTaggedObject=function(t){P.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,i){this.hT=e,this.isExplicit=t,this.asn1Object=i,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=i.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.tag&&(this.hT=t.tag),void 0!==t.explicit&&(this.isExplicit=t.explicit),void 0!==t.obj)&&(this.asn1Object=t.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object))},I.lang.extend(P.asn1.DERTaggedObject,P.asn1.ASN1Object),t(ht=C,I=ot=V),ht.prototype=null===I?Object.create(I):(ut.prototype=I.prototype,new ut),C.prototype.parseKey=function(t){try{var e=0,i=0,r=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(t)?function(t){if(void 0===f){var e="0123456789ABCDEF",i=" \f\n\r\t \u2028\u2029";for(f={},o=0;o<16;++o)f[e.charAt(o)]=o;for(e=e.toLowerCase(),o=10;o<16;++o)f[e.charAt(o)]=o;for(o=0;o<i.length;++o)f[i.charAt(o)]=-1}for(var r=[],n=0,s=0,o=0;o<t.length;++o){var h=t.charAt(o);if("="==h)break;if(-1!=(h=f[h])){if(void 0===h)throw new Error("Illegal character at offset "+o);n|=h,2<=++s?(r[r.length]=n,s=n=0):n<<=4}}if(s)throw new Error("Hex encoding incomplete: 4 bits missing");return r}(t):F.unarmor(t),n=z.decode(r);if(9===(n=3===n.sub.length?n.sub[2].sub[0]:n).sub.length){e=n.sub[1].getHexStringValue(),this.n=T(e,16),i=n.sub[2].getHexStringValue(),this.e=parseInt(i,16);var s=n.sub[3].getHexStringValue(),o=(this.d=T(s,16),n.sub[4].getHexStringValue()),h=(this.p=T(o,16),n.sub[5].getHexStringValue()),a=(this.q=T(h,16),n.sub[6].getHexStringValue()),u=(this.dmp1=T(a,16),n.sub[7].getHexStringValue()),c=(this.dmq1=T(u,16),n.sub[8].getHexStringValue());this.coeff=T(c,16)}else{if(2!==n.sub.length)return!1;var l=n.sub[1].sub[0],e=l.sub[0].getHexStringValue();this.n=T(e,16),i=l.sub[1].getHexStringValue(),this.e=parseInt(i,16)}return!0}catch(t){return!1}},C.prototype.getPrivateBaseKey=function(){var t={array:[new P.asn1.DERInteger({int:0}),new P.asn1.DERInteger({bigint:this.n}),new P.asn1.DERInteger({int:this.e}),new P.asn1.DERInteger({bigint:this.d}),new P.asn1.DERInteger({bigint:this.p}),new P.asn1.DERInteger({bigint:this.q}),new P.asn1.DERInteger({bigint:this.dmp1}),new P.asn1.DERInteger({bigint:this.dmq1}),new P.asn1.DERInteger({bigint:this.coeff})]};return new P.asn1.DERSequence(t).getEncodedHex()},C.prototype.getPrivateBaseKeyB64=function(){return _(this.getPrivateBaseKey())},C.prototype.getPublicBaseKey=function(){var t=new P.asn1.DERSequence({array:[new P.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new P.asn1.DERNull]}),e=new P.asn1.DERSequence({array:[new P.asn1.DERInteger({bigint:this.n}),new P.asn1.DERInteger({int:this.e})]}),e=new P.asn1.DERBitString({hex:"00"+e.getEncodedHex()});return new P.asn1.DERSequence({array:[t,e]}).getEncodedHex()},C.prototype.getPublicBaseKeyB64=function(){return _(this.getPublicBaseKey())},C.wordwrap=function(t,e){return t&&(e="(.{1,"+(e=e||64)+"})( +|$\n?)|(.{1,"+e+"})",t.match(RegExp(e,"g")).join("\n"))},C.prototype.getPrivateKey=function(){var t="-----BEGIN RSA PRIVATE KEY-----\n";return(t+=C.wordwrap(this.getPrivateBaseKeyB64())+"\n")+"-----END RSA PRIVATE KEY-----"},C.prototype.getPublicKey=function(){var t="-----BEGIN PUBLIC KEY-----\n";return(t+=C.wordwrap(this.getPublicBaseKeyB64())+"\n")+"-----END PUBLIC KEY-----"},C.hasPublicKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")},C.hasPrivateKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")&&t.hasOwnProperty("d")&&t.hasOwnProperty("p")&&t.hasOwnProperty("q")&&t.hasOwnProperty("dmp1")&&t.hasOwnProperty("dmq1")&&t.hasOwnProperty("coeff")},C.prototype.parsePropertiesFrom=function(t){this.n=t.n,this.e=t.e,t.hasOwnProperty("d")&&(this.d=t.d,this.p=t.p,this.q=t.q,this.dmp1=t.dmp1,this.dmq1=t.dmq1,this.coeff=t.coeff)},C),t=(M.prototype.setKey=function(t){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new at(t)},M.prototype.setPrivateKey=function(t){this.setKey(t)},M.prototype.setPublicKey=function(t){this.setKey(t)},M.prototype.decrypt=function(t){try{return this.getKey().decrypt(function(t){for(var e="",i=0,r=0,n=0;n<t.length&&"="!=t.charAt(n);++n){var s=o.indexOf(t.charAt(n));s<0||(i=0==i?(e+=a(s>>2),r=3&s,1):1==i?(e+=a(r<<2|s>>4),r=15&s,2):2==i?(e=(e+=a(r))+a(s>>2),r=3&s,3):(e=(e+=a(r<<2|s>>4))+a(15&s),0))}return 1==i&&(e+=a(r<<2)),e}(t))}catch(t){return!1}},M.prototype.encrypt=function(t){try{return _(this.getKey().encrypt(t))}catch(t){return!1}},M.prototype.getKey=function(t){if(!this.key){if(this.key=new at,t&&"[object Function]"==={}.toString.call(t))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},M.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},M.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},M.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},M.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},M.version="3.0.0-beta.1",M);function M(t){t=t||{},this.default_key_size=parseInt(t.default_key_size,10)||1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}function C(t){var e=ot.call(this)||this;return t&&("string"==typeof t?e.parseKey(t):(C.hasPrivateKeyProperty(t)||C.hasPublicKeyProperty(t))&&e.parsePropertiesFrom(t)),e}function ut(){this.constructor=ht}window.JSEncrypt=t,j.JSEncrypt=t,j.default=t,Object.defineProperty(j,"__esModule",{value:!0})}(e)},function(t,e,i){i(1),t.exports=i(0)}]);