<?php
/**
 * Admin Vendors View
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

$current_status = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php _e('Vendors', 'vendor'); ?></h1>
    <a href="<?php echo admin_url('user-new.php'); ?>" class="page-title-action"><?php _e('Add New Vendor', 'vendor'); ?></a>
    <hr class="wp-header-end">
    
    <!-- Status Filter -->
    <ul class="subsubsub">
        <li class="all">
            <a href="<?php echo admin_url('admin.php?page=vendor-vendors'); ?>" <?php echo empty($current_status) ? 'class="current"' : ''; ?>>
                <?php _e('All', 'vendor'); ?>
                <span class="count">(<?php echo vendor()->vendor_manager->get_vendor_count(); ?>)</span>
            </a> |
        </li>
        <li class="pending">
            <a href="<?php echo admin_url('admin.php?page=vendor-vendors&status=pending'); ?>" <?php echo $current_status === 'pending' ? 'class="current"' : ''; ?>>
                <?php _e('Pending', 'vendor'); ?>
                <span class="count">(<?php echo vendor()->vendor_manager->get_vendor_count('pending'); ?>)</span>
            </a> |
        </li>
        <li class="active">
            <a href="<?php echo admin_url('admin.php?page=vendor-vendors&status=active'); ?>" <?php echo $current_status === 'active' ? 'class="current"' : ''; ?>>
                <?php _e('Active', 'vendor'); ?>
                <span class="count">(<?php echo vendor()->vendor_manager->get_vendor_count('active'); ?>)</span>
            </a> |
        </li>
        <li class="rejected">
            <a href="<?php echo admin_url('admin.php?page=vendor-vendors&status=rejected'); ?>" <?php echo $current_status === 'rejected' ? 'class="current"' : ''; ?>>
                <?php _e('Rejected', 'vendor'); ?>
                <span class="count">(<?php echo vendor()->vendor_manager->get_vendor_count('rejected'); ?>)</span>
            </a>
        </li>
    </ul>
    
    <div class="vendor-vendors-table">
        <?php if ($vendors) : ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th scope="col" class="manage-column column-cb check-column">
                        <input type="checkbox" />
                    </th>
                    <th scope="col" class="manage-column column-store-name column-primary">
                        <?php _e('Store Name', 'vendor'); ?>
                    </th>
                    <th scope="col" class="manage-column column-owner">
                        <?php _e('Owner', 'vendor'); ?>
                    </th>
                    <th scope="col" class="manage-column column-email">
                        <?php _e('Email', 'vendor'); ?>
                    </th>
                    <th scope="col" class="manage-column column-commission">
                        <?php _e('Commission Rate', 'vendor'); ?>
                    </th>
                    <th scope="col" class="manage-column column-status">
                        <?php _e('Status', 'vendor'); ?>
                    </th>
                    <th scope="col" class="manage-column column-date">
                        <?php _e('Date Created', 'vendor'); ?>
                    </th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($vendors as $vendor_item) : 
                    $user = get_user_by('id', $vendor_item->user_id);
                ?>
                <tr>
                    <th scope="row" class="check-column">
                        <input type="checkbox" name="vendor[]" value="<?php echo esc_attr($vendor_item->id); ?>" />
                    </th>
                    <td class="column-store-name column-primary">
                        <strong>
                            <a href="<?php echo admin_url('admin.php?page=vendor-vendors&action=view&vendor_id=' . $vendor_item->id); ?>">
                                <?php echo esc_html($vendor_item->store_name); ?>
                            </a>
                        </strong>
                        <div class="row-actions">
                            <span class="view">
                                <a href="<?php echo admin_url('admin.php?page=vendor-vendors&action=view&vendor_id=' . $vendor_item->id); ?>">
                                    <?php _e('View', 'vendor'); ?>
                                </a> |
                            </span>
                            <?php if ($vendor_item->status === 'pending') : ?>
                            <span class="approve">
                                <a href="#" class="vendor-approve-vendor" data-vendor-id="<?php echo esc_attr($vendor_item->id); ?>">
                                    <?php _e('Approve', 'vendor'); ?>
                                </a> |
                            </span>
                            <span class="reject">
                                <a href="#" class="vendor-reject-vendor" data-vendor-id="<?php echo esc_attr($vendor_item->id); ?>">
                                    <?php _e('Reject', 'vendor'); ?>
                                </a> |
                            </span>
                            <?php endif; ?>
                            <span class="edit">
                                <a href="<?php echo admin_url('user-edit.php?user_id=' . $vendor_item->user_id); ?>">
                                    <?php _e('Edit User', 'vendor'); ?>
                                </a>
                            </span>
                        </div>
                        <button type="button" class="toggle-row">
                            <span class="screen-reader-text"><?php _e('Show more details', 'vendor'); ?></span>
                        </button>
                    </td>
                    <td class="column-owner" data-colname="<?php _e('Owner', 'vendor'); ?>">
                        <?php if ($user) : ?>
                            <a href="<?php echo admin_url('user-edit.php?user_id=' . $user->ID); ?>">
                                <?php echo esc_html($user->display_name); ?>
                            </a>
                        <?php else : ?>
                            <span class="description"><?php _e('User not found', 'vendor'); ?></span>
                        <?php endif; ?>
                    </td>
                    <td class="column-email" data-colname="<?php _e('Email', 'vendor'); ?>">
                        <?php echo esc_html($vendor_item->store_email ?: ($user ? $user->user_email : '')); ?>
                    </td>
                    <td class="column-commission" data-colname="<?php _e('Commission Rate', 'vendor'); ?>">
                        <?php echo esc_html($vendor_item->commission_rate); ?>%
                    </td>
                    <td class="column-status" data-colname="<?php _e('Status', 'vendor'); ?>">
                        <span class="vendor-status vendor-status-<?php echo esc_attr($vendor_item->status); ?>">
                            <?php echo esc_html(ucfirst($vendor_item->status)); ?>
                        </span>
                    </td>
                    <td class="column-date" data-colname="<?php _e('Date Created', 'vendor'); ?>">
                        <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($vendor_item->created_at))); ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <!-- Pagination -->
        <?php
        $total_vendors = vendor()->vendor_manager->get_vendor_count($current_status);
        $per_page = 20;
        $total_pages = ceil($total_vendors / $per_page);
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        
        if ($total_pages > 1) :
            $page_links = paginate_links(array(
                'base' => add_query_arg('paged', '%#%'),
                'format' => '',
                'prev_text' => __('&laquo;'),
                'next_text' => __('&raquo;'),
                'total' => $total_pages,
                'current' => $current_page
            ));
            
            if ($page_links) :
        ?>
        <div class="tablenav">
            <div class="tablenav-pages">
                <?php echo $page_links; ?>
            </div>
        </div>
        <?php 
            endif;
        endif; 
        ?>
        
        <?php else : ?>
        <div class="vendor-no-vendors">
            <p><?php _e('No vendors found.', 'vendor'); ?></p>
        </div>
        <?php endif; ?>
    </div>
</div>

<style>
.vendor-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.vendor-status-pending {
    background: #fff3cd;
    color: #856404;
}

.vendor-status-active {
    background: #d4edda;
    color: #155724;
}

.vendor-status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.vendor-no-vendors {
    background: #fff;
    border: 1px solid #ddd;
    padding: 20px;
    text-align: center;
}

.vendor-approve-vendor,
.vendor-reject-vendor {
    color: #0073aa;
    text-decoration: none;
}

.vendor-approve-vendor:hover {
    color: #46b450;
}

.vendor-reject-vendor:hover {
    color: #dc3232;
}
</style>

<script>
jQuery(document).ready(function($) {
    $('.vendor-approve-vendor').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm(vendorAdmin.strings.confirm_approve)) {
            return;
        }
        
        var vendorId = $(this).data('vendor-id');
        var $row = $(this).closest('tr');
        
        $.ajax({
            url: vendorAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'vendor_approve_vendor',
                vendor_id: vendorId,
                nonce: vendorAdmin.nonce
            },
            beforeSend: function() {
                $row.addClass('vendor-processing');
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || vendorAdmin.strings.error);
                }
            },
            error: function() {
                alert(vendorAdmin.strings.error);
            },
            complete: function() {
                $row.removeClass('vendor-processing');
            }
        });
    });
    
    $('.vendor-reject-vendor').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm(vendorAdmin.strings.confirm_reject)) {
            return;
        }
        
        var vendorId = $(this).data('vendor-id');
        var $row = $(this).closest('tr');
        
        $.ajax({
            url: vendorAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'vendor_reject_vendor',
                vendor_id: vendorId,
                nonce: vendorAdmin.nonce
            },
            beforeSend: function() {
                $row.addClass('vendor-processing');
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || vendorAdmin.strings.error);
                }
            },
            error: function() {
                alert(vendorAdmin.strings.error);
            },
            complete: function() {
                $row.removeClass('vendor-processing');
            }
        });
    });
});
</script>
