<?php
/**
 * Vendor API
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * API class
 */
class Vendor_API {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('rest_api_init', array($this, 'register_routes'));
    }
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        $namespace = 'vendor/v1';
        
        // Dashboard routes
        register_rest_route($namespace, '/dashboard/stats', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_dashboard_stats'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        // Vendor routes
        register_rest_route($namespace, '/vendor/profile', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_vendor_profile'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        register_rest_route($namespace, '/vendor/profile', array(
            'methods' => 'POST',
            'callback' => array($this, 'update_vendor_profile'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        // Products routes
        register_rest_route($namespace, '/products', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_vendor_products'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        register_rest_route($namespace, '/products/(?P<id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_vendor_product'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        register_rest_route($namespace, '/products', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_product'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        register_rest_route($namespace, '/products/(?P<id>\d+)', array(
            'methods' => 'PUT',
            'callback' => array($this, 'update_product'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        register_rest_route($namespace, '/products/(?P<id>\d+)', array(
            'methods' => 'DELETE',
            'callback' => array($this, 'delete_product'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        // Orders routes
        register_rest_route($namespace, '/orders', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_vendor_orders'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        register_rest_route($namespace, '/orders/(?P<id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_vendor_order'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        // Commissions routes
        register_rest_route($namespace, '/commissions', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_vendor_commissions'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        register_rest_route($namespace, '/commissions/analytics', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_commission_analytics'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        // Withdrawals routes
        register_rest_route($namespace, '/withdrawals', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_vendor_withdrawals'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        register_rest_route($namespace, '/withdrawals', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_withdrawal_request'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        register_rest_route($namespace, '/withdrawals/methods', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_withdrawal_methods'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        // Coupons routes
        register_rest_route($namespace, '/coupons', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_vendor_coupons'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        register_rest_route($namespace, '/coupons', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_coupon'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        register_rest_route($namespace, '/coupons/(?P<id>\d+)', array(
            'methods' => 'PUT',
            'callback' => array($this, 'update_coupon'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        register_rest_route($namespace, '/coupons/(?P<id>\d+)', array(
            'methods' => 'DELETE',
            'callback' => array($this, 'delete_coupon'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        // Reviews routes
        register_rest_route($namespace, '/reviews', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_vendor_reviews'),
            'permission_callback' => array($this, 'check_vendor_permission')
        ));
        
        // Tutor LMS routes (if Tutor is active)
        if (class_exists('TUTOR\Tutor')) {
            register_rest_route($namespace, '/courses', array(
                'methods' => 'GET',
                'callback' => array($this, 'get_vendor_courses'),
                'permission_callback' => array($this, 'check_vendor_permission')
            ));
        }
    }
    
    /**
     * Check vendor permission
     */
    public function check_vendor_permission($request) {
        if (!is_user_logged_in()) {
            return false;
        }
        
        return vendor()->vendor_manager->is_vendor();
    }
    
    /**
     * Get dashboard stats
     */
    public function get_dashboard_stats($request) {
        $vendor = vendor()->vendor_manager->get_current_vendor();
        if (!$vendor) {
            return new WP_Error('vendor_not_found', __('Vendor not found', 'vendor'), array('status' => 404));
        }
        
        $earnings = vendor()->commission->get_vendor_earnings_summary($vendor->id);
        $available_balance = vendor()->commission->get_vendor_available_balance($vendor->id);
        $withdrawal_summary = vendor()->withdrawal->get_withdrawal_summary($vendor->id);
        
        // Get product count
        $product_count = wp_count_posts('product');
        $vendor_products = get_posts(array(
            'post_type' => 'product',
            'meta_key' => '_vendor_id',
            'meta_value' => $vendor->id,
            'posts_per_page' => -1,
            'fields' => 'ids'
        ));
        
        return rest_ensure_response(array(
            'earnings' => array(
                'total_commission' => $earnings->total_commission ?: 0,
                'approved_commission' => $earnings->approved_commission ?: 0,
                'pending_commission' => $earnings->pending_commission ?: 0,
                'available_balance' => $available_balance ?: 0
            ),
            'orders' => array(
                'total_orders' => $earnings->total_orders ?: 0,
                'gross_sales' => $earnings->gross_sales ?: 0
            ),
            'products' => array(
                'total_products' => count($vendor_products)
            ),
            'withdrawals' => array(
                'total_requests' => $withdrawal_summary->total_requests ?: 0,
                'total_amount' => $withdrawal_summary->total_amount ?: 0,
                'pending_amount' => $withdrawal_summary->pending_amount ?: 0,
                'completed_amount' => $withdrawal_summary->completed_amount ?: 0
            )
        ));
    }
    
    /**
     * Get vendor profile
     */
    public function get_vendor_profile($request) {
        $vendor = vendor()->vendor_manager->get_current_vendor();
        if (!$vendor) {
            return new WP_Error('vendor_not_found', __('Vendor not found', 'vendor'), array('status' => 404));
        }
        
        $user = get_user_by('id', $vendor->user_id);
        
        return rest_ensure_response(array(
            'id' => $vendor->id,
            'user_id' => $vendor->user_id,
            'store_name' => $vendor->store_name,
            'store_slug' => $vendor->store_slug,
            'store_description' => $vendor->store_description,
            'store_logo' => $vendor->store_logo,
            'store_banner' => $vendor->store_banner,
            'store_address' => $vendor->store_address,
            'store_phone' => $vendor->store_phone,
            'store_email' => $vendor->store_email,
            'commission_rate' => $vendor->commission_rate,
            'status' => $vendor->status,
            'user' => array(
                'display_name' => $user->display_name,
                'email' => $user->user_email
            )
        ));
    }
    
    /**
     * Update vendor profile
     */
    public function update_vendor_profile($request) {
        $vendor = vendor()->vendor_manager->get_current_vendor();
        if (!$vendor) {
            return new WP_Error('vendor_not_found', __('Vendor not found', 'vendor'), array('status' => 404));
        }
        
        $params = $request->get_params();
        
        $update_data = array();
        $allowed_fields = array('store_name', 'store_description', 'store_logo', 'store_banner', 'store_address', 'store_phone', 'store_email');
        
        foreach ($allowed_fields as $field) {
            if (isset($params[$field])) {
                $update_data[$field] = $params[$field];
            }
        }
        
        if (empty($update_data)) {
            return new WP_Error('no_data', __('No data to update', 'vendor'), array('status' => 400));
        }
        
        $result = vendor()->vendor_manager->update_vendor($vendor->id, $update_data);
        
        if ($result) {
            return rest_ensure_response(array('message' => __('Profile updated successfully', 'vendor')));
        } else {
            return new WP_Error('update_failed', __('Failed to update profile', 'vendor'), array('status' => 500));
        }
    }
    
    /**
     * Get vendor products
     */
    public function get_vendor_products($request) {
        $vendor = vendor()->vendor_manager->get_current_vendor();
        if (!$vendor) {
            return new WP_Error('vendor_not_found', __('Vendor not found', 'vendor'), array('status' => 404));
        }
        
        $page = $request->get_param('page') ?: 1;
        $per_page = $request->get_param('per_page') ?: 20;
        $status = $request->get_param('status') ?: 'any';
        $search = $request->get_param('search') ?: '';
        
        $args = array(
            'post_type' => 'product',
            'post_status' => $status,
            'meta_key' => '_vendor_id',
            'meta_value' => $vendor->id,
            'posts_per_page' => $per_page,
            'paged' => $page
        );
        
        if (!empty($search)) {
            $args['s'] = $search;
        }
        
        $products_query = new WP_Query($args);
        $products = array();
        
        foreach ($products_query->posts as $product_post) {
            $product = wc_get_product($product_post->ID);
            if ($product) {
                $products[] = array(
                    'id' => $product->get_id(),
                    'name' => $product->get_name(),
                    'slug' => $product->get_slug(),
                    'status' => $product->get_status(),
                    'price' => $product->get_price(),
                    'regular_price' => $product->get_regular_price(),
                    'sale_price' => $product->get_sale_price(),
                    'stock_status' => $product->get_stock_status(),
                    'stock_quantity' => $product->get_stock_quantity(),
                    'image' => wp_get_attachment_image_url($product->get_image_id(), 'thumbnail'),
                    'date_created' => $product->get_date_created()->date('Y-m-d H:i:s'),
                    'date_modified' => $product->get_date_modified()->date('Y-m-d H:i:s')
                );
            }
        }
        
        return rest_ensure_response(array(
            'products' => $products,
            'total' => $products_query->found_posts,
            'pages' => $products_query->max_num_pages,
            'current_page' => $page
        ));
    }
    
    /**
     * Get vendor orders
     */
    public function get_vendor_orders($request) {
        $vendor = vendor()->vendor_manager->get_current_vendor();
        if (!$vendor) {
            return new WP_Error('vendor_not_found', __('Vendor not found', 'vendor'), array('status' => 404));
        }
        
        $page = $request->get_param('page') ?: 1;
        $per_page = $request->get_param('per_page') ?: 20;
        $status = $request->get_param('status') ?: 'any';
        
        // Get orders that contain vendor products
        global $wpdb;
        $commissions_table = Vendor_Database::get_commissions_table();
        
        $offset = ($page - 1) * $per_page;
        
        $where = array('vendor_id = %d');
        $values = array($vendor->id);
        
        if ($status !== 'any') {
            $where[] = 'status = %s';
            $values[] = $status;
        }
        
        $where_clause = implode(' AND ', $where);
        
        $order_ids = $wpdb->get_col($wpdb->prepare(
            "SELECT DISTINCT order_id FROM $commissions_table WHERE $where_clause ORDER BY created_at DESC LIMIT %d OFFSET %d",
            array_merge($values, array($per_page, $offset))
        ));
        
        $orders = array();
        foreach ($order_ids as $order_id) {
            $order = wc_get_order($order_id);
            if ($order) {
                $orders[] = array(
                    'id' => $order->get_id(),
                    'order_number' => $order->get_order_number(),
                    'status' => $order->get_status(),
                    'total' => $order->get_total(),
                    'currency' => $order->get_currency(),
                    'date_created' => $order->get_date_created()->date('Y-m-d H:i:s'),
                    'billing' => array(
                        'first_name' => $order->get_billing_first_name(),
                        'last_name' => $order->get_billing_last_name(),
                        'email' => $order->get_billing_email()
                    )
                );
            }
        }
        
        $total_orders = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT order_id) FROM $commissions_table WHERE $where_clause",
            $values
        ));
        
        return rest_ensure_response(array(
            'orders' => $orders,
            'total' => $total_orders,
            'pages' => ceil($total_orders / $per_page),
            'current_page' => $page
        ));
    }
    
    /**
     * Get vendor commissions
     */
    public function get_vendor_commissions($request) {
        $vendor = vendor()->vendor_manager->get_current_vendor();
        if (!$vendor) {
            return new WP_Error('vendor_not_found', __('Vendor not found', 'vendor'), array('status' => 404));
        }
        
        $page = $request->get_param('page') ?: 1;
        $per_page = $request->get_param('per_page') ?: 20;
        $status = $request->get_param('status') ?: '';
        $date_from = $request->get_param('date_from') ?: '';
        $date_to = $request->get_param('date_to') ?: '';
        
        $args = array(
            'limit' => $per_page,
            'offset' => ($page - 1) * $per_page,
            'status' => $status,
            'date_from' => $date_from,
            'date_to' => $date_to
        );
        
        $commissions = vendor()->commission->get_vendor_commissions($vendor->id, $args);
        
        $formatted_commissions = array();
        foreach ($commissions as $commission) {
            $order = wc_get_order($commission->order_id);
            $product = wc_get_product($commission->product_id);
            
            $formatted_commissions[] = array(
                'id' => $commission->id,
                'order_id' => $commission->order_id,
                'product_id' => $commission->product_id,
                'gross_amount' => $commission->gross_amount,
                'commission_amount' => $commission->commission_amount,
                'commission_rate' => $commission->commission_rate,
                'admin_fee' => $commission->admin_fee,
                'status' => $commission->status,
                'created_at' => $commission->created_at,
                'order' => $order ? array(
                    'order_number' => $order->get_order_number(),
                    'status' => $order->get_status()
                ) : null,
                'product' => $product ? array(
                    'name' => $product->get_name(),
                    'image' => wp_get_attachment_image_url($product->get_image_id(), 'thumbnail')
                ) : null
            );
        }
        
        return rest_ensure_response(array(
            'commissions' => $formatted_commissions
        ));
    }
    
    /**
     * Get vendor withdrawals
     */
    public function get_vendor_withdrawals($request) {
        $vendor = vendor()->vendor_manager->get_current_vendor();
        if (!$vendor) {
            return new WP_Error('vendor_not_found', __('Vendor not found', 'vendor'), array('status' => 404));
        }
        
        $page = $request->get_param('page') ?: 1;
        $per_page = $request->get_param('per_page') ?: 20;
        $status = $request->get_param('status') ?: '';
        
        $args = array(
            'limit' => $per_page,
            'offset' => ($page - 1) * $per_page,
            'status' => $status
        );
        
        $withdrawals = vendor()->withdrawal->get_vendor_withdrawals($vendor->id, $args);
        
        $formatted_withdrawals = array();
        foreach ($withdrawals as $withdrawal) {
            $formatted_withdrawals[] = array(
                'id' => $withdrawal->id,
                'amount' => $withdrawal->amount,
                'charges' => $withdrawal->charges,
                'net_amount' => $withdrawal->net_amount,
                'payment_method' => $withdrawal->payment_method,
                'status' => $withdrawal->status,
                'note' => $withdrawal->note,
                'transaction_id' => $withdrawal->transaction_id,
                'requested_at' => $withdrawal->requested_at,
                'processed_at' => $withdrawal->processed_at
            );
        }
        
        return rest_ensure_response(array(
            'withdrawals' => $formatted_withdrawals
        ));
    }
    
    /**
     * Create withdrawal request
     */
    public function create_withdrawal_request($request) {
        $vendor = vendor()->vendor_manager->get_current_vendor();
        if (!$vendor) {
            return new WP_Error('vendor_not_found', __('Vendor not found', 'vendor'), array('status' => 404));
        }
        
        $amount = floatval($request->get_param('amount'));
        $payment_method = sanitize_text_field($request->get_param('payment_method'));
        $payment_details = $request->get_param('payment_details') ?: array();
        
        if ($amount <= 0) {
            return new WP_Error('invalid_amount', __('Invalid amount', 'vendor'), array('status' => 400));
        }
        
        $result = vendor()->withdrawal->create_withdrawal_request($vendor->id, $amount, $payment_method, $payment_details);
        
        if (is_wp_error($result)) {
            return $result;
        }
        
        return rest_ensure_response(array(
            'message' => __('Withdrawal request created successfully', 'vendor'),
            'withdrawal_id' => $result
        ));
    }
    
    /**
     * Get withdrawal methods
     */
    public function get_withdrawal_methods($request) {
        $methods = vendor()->withdrawal->get_payment_methods();
        
        return rest_ensure_response(array(
            'methods' => $methods
        ));
    }
}
