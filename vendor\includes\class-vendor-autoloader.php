<?php
/**
 * Vendor Autoloader
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Vendor autoloader class
 */
class Vendor_Autoloader {
    
    /**
     * Path to the includes directory
     */
    private static $include_path = '';
    
    /**
     * Initialize autoloader
     */
    public static function init() {
        self::$include_path = untrailingslashit(plugin_dir_path(VENDOR_PLUGIN_FILE)) . '/includes/';
        
        if (function_exists('__autoload')) {
            spl_autoload_register('__autoload');
        }
        
        spl_autoload_register(array(__CLASS__, 'autoload'));
    }
    
    /**
     * Auto-load classes on demand
     */
    public static function autoload($class) {
        $class = strtolower($class);
        
        if (0 !== strpos($class, 'vendor_')) {
            return;
        }
        
        $file = self::get_file_name_from_class($class);
        $path = '';
        
        if (0 === strpos($class, 'vendor_admin')) {
            $path = self::$include_path . 'admin/';
        } elseif (0 === strpos($class, 'vendor_api')) {
            $path = self::$include_path . 'api/';
        } elseif (0 === strpos($class, 'vendor_frontend')) {
            $path = self::$include_path . 'frontend/';
        } elseif (0 === strpos($class, 'vendor_gateway')) {
            $path = self::$include_path . 'gateways/';
        } else {
            $path = self::$include_path;
        }
        
        if (empty($path) || !self::load_file($path . $file)) {
            self::load_file(self::$include_path . $file);
        }
    }
    
    /**
     * Take a class name and turn it into a file name
     */
    private static function get_file_name_from_class($class) {
        return 'class-' . str_replace('_', '-', $class) . '.php';
    }
    
    /**
     * Include a class file
     */
    private static function load_file($path) {
        if ($path && is_readable($path)) {
            include_once $path;
            return true;
        }
        return false;
    }
}
