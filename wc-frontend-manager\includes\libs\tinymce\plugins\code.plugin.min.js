!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e[f]=d(a[f]);b.apply(null,e)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("4",tinymce.util.Tools.resolve),g("1",["4"],function(a){return a("tinymce.PluginManager")}),g("8",["4"],function(a){return a("tinymce.dom.DOMUtils")}),g("6",["8"],function(a){var b=function(a){return a.getParam("code_dialog_width",600)},c=function(b){return b.getParam("code_dialog_height",Math.min(a.DOM.getViewPort().h-200,500))};return{getMinWidth:b,getMinHeight:c}}),g("7",[],function(){var a=function(a,b){a.focus(),a.undoManager.transact(function(){a.setContent(b)}),a.selection.setCursorLocation(),a.nodeChanged()},b=function(a){return a.getContent({source_view:!0})};return{setContent:a,getContent:b}}),g("5",["6","7"],function(a,b){var c=function(c){var d=a.getMinWidth(c),e=a.getMinHeight(c),f=c.windowManager.open({title:"Source code",body:{type:"textbox",name:"code",multiline:!0,minWidth:d,minHeight:e,spellcheck:!1,style:"direction: ltr; text-align: left"},onSubmit:function(a){b.setContent(c,a.data.code)}});f.find("#code").value(b.getContent(c))};return{open:c}}),g("2",["5"],function(a){var b=function(b){b.addCommand("mceCodeEditor",function(){a.open(b)})};return{register:b}}),g("3",["5"],function(a){var b=function(b){b.addButton("code",{icon:"code",tooltip:"Source code",onclick:function(){a.open(b)}}),b.addMenuItem("code",{icon:"code",text:"Source code",onclick:function(){a.open(b)}})};return{register:b}}),g("0",["1","2","3"],function(a,b,c){return a.add("code",function(a){return b.register(a),c.register(a),{}}),function(){}}),d("0")()}();