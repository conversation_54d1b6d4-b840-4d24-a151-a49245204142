.wcfm-admin-menu-head {
	padding: 0 2px 0 2px !important;
	height: 32px !important;
}

.wcfm-admin-menu-head img {
	width:30px !important;
}

.wcfm-admin-menu span {
	font-size: 15px;
	padding: 0 4px 0 2px !important;
}

.wcfm_addon_inactive_notice_box {
	background-color: #00002c;
	border-left: 0px;
	padding-left: 97px;
	min-height: 100px;
	position: relative;
}

.wcfm_ultimate_inactive_notice_box {
	background-color: #17a2b8;
	padding-top: 5px;
}

.wcfm_group_inactive_notice_box {
	background-color: #62c4ba;
}

.wcfm_addon_inactive_notice_box h2 {
	font-size: 18px;
	width: 80%;
	color: rgba(250, 250, 250, 1);
	margin-bottom: 8px;
	font-weight: normal;
	margin-top: 15px;
	-webkit-text-shadow: 0.1px 0.1px 0px rgba(250, 250, 250, 0.24);
	-moz-text-shadow: 0.1px 0.1px 0px rgba(250, 250, 250, 0.24);
	-o-text-shadow: 0.1px 0.1px 0px rgba(250, 250, 250, 0.24);
	text-shadow: 0.1px 0.1px 0px rgba(250, 250, 250, 0.24);
	font-weight: 600;
}

.wcfm_ultimate_inactive_notice_box h2 {
  color:#00002c;
}

.wcfm_group_inactive_notice_box h2 {
	color: #13171c;
}

.wcfm_addon_inactive_notice_box a.promo-btn {
	background: transparent;
	text-decoration: none;
	text-shadow: none;
	position: absolute;
	top: 0px;
	right: 0px;
	height: 100%;
	width: 350px;
	text-align: center;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}

.wcfm_ultimate_inactive_notice_box a.promo-btn {
	background: #fff !important;
	border-color: #fafafa #fafafa #fafafa;
	box-shadow: 0 1px 0 #fafafa;
	color: #000 !important;
	text-decoration: none;
	text-shadow: none;
	position: absolute;
	top: 30px !important;
	right: 26px !important;
	height: 40px!important;
	line-height: 40px;
	width: 130px!important;
	text-align: center;
	font-weight: 700;
}

.wcfm_ultimate_inactive_notice_box a.promo-btn:hover {
	background: #000 !important;
	color: #fff !important;
}

.wcfm_addon_inactive_notice_box a.wcfmu_promo_button1 {
	background-image: url('https://wclovers.com/wp-content/uploads/2019/07/wcfm_ultimate_wp_cta-1.png');
}

.wcfm_addon_inactive_notice_box a.wcfmu_promo_button1:hover {
	background-image: url('https://wclovers.com/wp-content/uploads/2019/07/wcfm_ultimate_wp_hover_cta-1.png');
}

.wcfm_addon_inactive_notice_box a.wcfmgs_promo_button {
	background-image: url('https://wclovers.com/wp-content/uploads/2019/07/wcfm_group_wp_cta.png');
}

.wcfm_addon_inactive_notice_box a.wcfmgs_promo_button:hover {
	background-image: url('https://wclovers.com/wp-content/uploads/2019/07/wcfm_group_wp_hover_cta.png');
}

.wcfm_addon_inactive_notice_box img {
	position: absolute;
	width: 80px;
	top: 10px;
	left: 10px;
	border-radius: 5px;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
}

.wcfm_addon_inactive_notice_box .promo-btn img {
	width: 100%;
}

.wcfm_addon_inactive_notice_box h2 span {
	position: relative;
	top: -1px;
}

.wcfm_addon_inactive_notice_box p {
	width: 75%;
	color: #8d8db0;
	font-size: 14px;
	margin-bottom: 10px;
	-webkit-text-shadow: 0.1px 0.1px 0px rgba(250, 250, 250, 0.24);
	-moz-text-shadow: 0.1px 0.1px 0px rgba(250, 250, 250, 0.24);
	-o-text-shadow: 0.1px 0.1px 0px rgba(250, 250, 250, 0.24);
	text-shadow: 0.1px 0.1px 0px rgba(250, 250, 250, 0.24);
}

.wcfm_ultimate_inactive_notice_box p {
	color: #ffffff;
}

.wcfm_group_inactive_notice_box p {
	color: #ffffff;
}

.wcfm_addon_inactive_notice_box p strong.highlight-text {
	color: #fff;
}

.wcfm_addon_inactive_notice_box p a {
	color: #fafafa;
}

.wcfm_addon_inactive_notice_box .notice-dismiss:before {
	color: #fff;
}

.wcfm_addon_inactive_notice_box span.dashicons-megaphone, .wcfm_addon_inactive_notice_box span.dashicons-groups {
	position: absolute;
	top: 22px;
	right: 248px;
	color: rgba(253, 253, 253, 0.29);
	font-size: 96px;
	transform: rotate(-21deg);
}

#wcfm-membership-notice {
	background-color: #b198dc;
}

#wcfm-groups-sttafs-notice {
	background-color: #62c4ba;
}

#wcfm-conflict-notice {
	background-color: #dd4b39;
}

.wcfm_setting_help_box, .wcfm_setting_warning_box {
	background: #fff;
	border-left: 4px solid #fff;
	-webkit-box-shadow: 0 1px 1px 0 rgba(0,0,0,.1);
	box-shadow: 0 1px 1px 0 rgba(0,0,0,.1);
	margin: 5px 1px 2px;
	padding: 1px 12px;
	border-left-color: #46b450;
}

.wcfm_setting_warning_box {
	border-left-color: #ff8761;
}

.wcfm_admin_message_wrapper {
	float: right;
	width: 30%;
}

.wcfm_admin_message {
	background: #fff;
	border-radius: 3px;
	border: 1px solid #ccc;
	-webkit-box-shadow: 0px 2px 1px #555;
	box-shadow: 0px 1px 1px #555;
	padding: 10px;
	margin-bottom: 10px;
}

.wcfm_admin_help_docs a, .wcfm_admin_support_docs a {
	text-decoration: none;
	color: #D15600;
	font-weight: 500;
}

.wcfm_admin_support_docs a {
	color: #FF0084;
}

.wcfm_admin_help_docs a:hover, .wcfm_admin_support_docs a:hover {
	text-decoration: underline;
}

.wcfm_admin_message p {
	padding: 5px;
	font-size: 16px;
	font-weight: 500;
	text-align: center;
	color: #555;
}

.wcfm_admin_message ul li {
}

.wcfm_admin_message .primary {
	background: #17a2b8;
	padding: 5px;
	padding-left: 10px;
	padding-right: 10px;
	margin-left: 6px;
	border-radius: 5px;
	color: #fff;
	border: 1px solid #17a2b8;
	text-decoration: none;
	font-weight: 500;
	letter-spacing: 1px;
	-webkit-box-shadow: 0px 2px 1px #555;
	box-shadow: 0px 2px 1px #555;
	text-align: center;
	display: block;
}

.wcfm_admin_message .membership_btn {
	background-color: #D01F3C; 
	border-color: #D01F3C;
}

.wcfm_admin_message .primary:hover {
	border: 1px solid #2a3344;
  background: #2a3344; 
}

/** Marketplace Double Menu Fix **/
#adminmenu li.toplevel_page_wcfm_settings_form_marketplace_head {
  display: none !important;
}

@media only screen and (max-width: 782px) {
	#wpadminbar #wp-admin-bar-wcfm-menu {
    display: block;
    position: static;
  }
  
  .wcfm-admin-menu-head { 
  	padding: 6px 2px 6px 2px !important;
  }
}