<?php
/**
 * Vendor Frontend
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Frontend class
 */
class Vendor_Frontend {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_shortcode('vendor_dashboard', array($this, 'dashboard_shortcode'));
        add_action('template_redirect', array($this, 'dashboard_template_redirect'));
    }
    
    /**
     * Initialize
     */
    public function init() {
        // Add rewrite rules for vendor dashboard
        add_rewrite_rule('^vendor-dashboard/?$', 'index.php?vendor_dashboard=1', 'top');
        add_rewrite_rule('^vendor-dashboard/([^/]+)/?$', 'index.php?vendor_dashboard=1&vendor_page=$matches[1]', 'top');
        
        // Add query vars
        add_filter('query_vars', array($this, 'add_query_vars'));
    }
    
    /**
     * Add query vars
     */
    public function add_query_vars($vars) {
        $vars[] = 'vendor_dashboard';
        $vars[] = 'vendor_page';
        return $vars;
    }
    
    /**
     * Enqueue scripts
     */
    public function enqueue_scripts() {
        if ($this->is_vendor_dashboard()) {
            // Enqueue React and dependencies
            wp_enqueue_script('vendor-react', VENDOR_PLUGIN_URL . 'assets/js/vendor-dashboard.js', array('wp-element', 'wp-api-fetch'), VENDOR_VERSION, true);
            wp_enqueue_style('vendor-dashboard', VENDOR_PLUGIN_URL . 'assets/css/vendor-dashboard.css', array(), VENDOR_VERSION);
            
            // Localize script
            wp_localize_script('vendor-react', 'vendorDashboard', array(
                'apiUrl' => rest_url('vendor/v1/'),
                'nonce' => wp_create_nonce('wp_rest'),
                'currentUser' => wp_get_current_user(),
                'strings' => array(
                    'dashboard' => __('Dashboard', 'vendor'),
                    'products' => __('Products', 'vendor'),
                    'orders' => __('Orders', 'vendor'),
                    'coupons' => __('Coupons', 'vendor'),
                    'analytics' => __('Analytics', 'vendor'),
                    'reviews' => __('Reviews', 'vendor'),
                    'withdrawals' => __('Withdrawals', 'vendor'),
                    'courses' => __('My Courses', 'vendor'),
                    'announcements' => __('Manage Announcements', 'vendor'),
                    'qa' => __('Q&A', 'vendor'),
                    'quiz_attempts' => __('Quiz Attempts', 'vendor'),
                    'students' => __('Students', 'vendor'),
                    'settings' => __('Settings', 'vendor'),
                    'store' => __('Go to Store', 'vendor'),
                    'store_settings' => __('Store Settings', 'vendor'),
                    'logout' => __('Logout', 'vendor'),
                    'loading' => __('Loading...', 'vendor'),
                    'error' => __('An error occurred', 'vendor'),
                    'success' => __('Success!', 'vendor'),
                    'save' => __('Save', 'vendor'),
                    'cancel' => __('Cancel', 'vendor'),
                    'edit' => __('Edit', 'vendor'),
                    'delete' => __('Delete', 'vendor'),
                    'view' => __('View', 'vendor'),
                    'add_new' => __('Add New', 'vendor'),
                    'search' => __('Search', 'vendor'),
                    'filter' => __('Filter', 'vendor'),
                    'all' => __('All', 'vendor'),
                    'active' => __('Active', 'vendor'),
                    'pending' => __('Pending', 'vendor'),
                    'completed' => __('Completed', 'vendor'),
                    'cancelled' => __('Cancelled', 'vendor')
                ),
                'currency' => array(
                    'symbol' => get_woocommerce_currency_symbol(),
                    'position' => get_option('woocommerce_currency_pos'),
                    'decimal_separator' => wc_get_price_decimal_separator(),
                    'thousand_separator' => wc_get_price_thousand_separator(),
                    'decimals' => wc_get_price_decimals()
                ),
                'dateFormat' => get_option('date_format'),
                'timeFormat' => get_option('time_format'),
                'tutorActive' => class_exists('TUTOR\Tutor')
            ));
        }
    }
    
    /**
     * Check if current page is vendor dashboard
     */
    private function is_vendor_dashboard() {
        global $wp_query;
        
        return isset($wp_query->query_vars['vendor_dashboard']) || 
               is_page(get_option('vendor_dashboard_page_id')) ||
               (isset($_GET['page']) && $_GET['page'] === 'vendor-dashboard');
    }
    
    /**
     * Dashboard shortcode
     */
    public function dashboard_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<p>' . __('Please login to access vendor dashboard.', 'vendor') . '</p>';
        }
        
        if (!vendor()->vendor_manager->is_vendor()) {
            return '<p>' . __('You do not have permission to access vendor dashboard.', 'vendor') . '</p>';
        }
        
        ob_start();
        $this->render_dashboard();
        return ob_get_clean();
    }
    
    /**
     * Dashboard template redirect
     */
    public function dashboard_template_redirect() {
        global $wp_query;
        
        if (isset($wp_query->query_vars['vendor_dashboard'])) {
            if (!is_user_logged_in()) {
                wp_redirect(wp_login_url(get_permalink()));
                exit;
            }
            
            if (!vendor()->vendor_manager->is_vendor()) {
                wp_die(__('You do not have permission to access this page.', 'vendor'));
            }
            
            $this->load_dashboard_template();
        }
    }
    
    /**
     * Load dashboard template
     */
    private function load_dashboard_template() {
        // Try to load custom template from theme
        $template = locate_template('vendor/dashboard.php');
        
        if (!$template) {
            $template = VENDOR_PLUGIN_DIR . 'templates/dashboard.php';
        }
        
        include $template;
        exit;
    }
    
    /**
     * Render dashboard
     */
    private function render_dashboard() {
        ?>
        <div id="vendor-dashboard-root" class="vendor-dashboard-wrapper">
            <div class="vendor-dashboard-loading">
                <div class="vendor-spinner"></div>
                <p><?php _e('Loading dashboard...', 'vendor'); ?></p>
            </div>
        </div>
        <?php
    }
    
    /**
     * Get vendor dashboard URL
     */
    public static function get_dashboard_url($page = '') {
        $dashboard_page_id = get_option('vendor_dashboard_page_id');
        
        if ($dashboard_page_id) {
            $url = get_permalink($dashboard_page_id);
        } else {
            $url = home_url('/vendor-dashboard/');
        }
        
        if (!empty($page)) {
            $url = trailingslashit($url) . $page . '/';
        }
        
        return $url;
    }
    
    /**
     * Get vendor store URL
     */
    public static function get_store_url($vendor_id) {
        $vendor = vendor()->vendor_manager->get_vendor($vendor_id);
        
        if ($vendor && $vendor->store_slug) {
            return home_url('/store/' . $vendor->store_slug . '/');
        }
        
        return home_url();
    }
}
