msgid ""
msgstr ""
"Project-Id-Version: WooCommerce Frontend Manager\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-30 21:42+0000\n"
"PO-Revision-Date: 2018-11-12 14:23+0000\n"
"Last-Translator: admin <<EMAIL>>\n"
"Language-Team: Deutsch\n"
"Language: de_DE\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/"

#: core/class-wcfm-admin.php:100
msgid ""
"<h2>\n"
"\t\t\t\t\t\t\t\t\t\t           Is there anything missing in your front-end "
"dashboard !!!\n"
"\t\t\t\t\t\t\t\t               </h2>"
msgstr ""
"Fehlt Ihnen etwas in Ihrem Front-End Dashboard?!! Dann gehen <PERSON>e doch zu "
"%sWCfM U >>%s"

#: core/class-wcfm-admin.php:106
msgid ""
"<p>WooCommerce Frontend Manage - Ultimate is there to fill up all those for "
"you. Store Invoice, Support Ticket, Shipment Tracking, Direct Messaging, "
"Followers, Badges, Verificaton, Product Importer, Bulk Edit and many more, "
"almost a never ending features list for you.</p>"
msgstr ""

#: core/class-wcfm-admin.php:112
msgid "WCFM U >>"
msgstr "WCFM U >>"

#: core/class-wcfm-admin.php:148
msgid ""
"<h2>\n"
"\t\t\t\t\t\t\t\t\t\t           Now setup your vendor membership subscription "
"in minutes & it's FREE !!!\n"
"\t\t\t\t\t\t\t\t               </h2>"
msgstr ""
"<h2> Richten Sie jetzt Ihr Shopmitgliedschafts-Abonnement in Minuten ein und "
"es ist KOSTENLOS !!! <h2>"

#: core/class-wcfm-admin.php:151
msgid ""
"<p>A simple membership plugin for offering FREE AND PREMIUM SUBSCRIPTION for "
"your multi-vendor marketplace. You may set up unlimited membership levels "
"(example: free, silver, gold etc) with different pricing plan, capabilities "
"and commission. Also you will have Pay for Product option.</p>"
msgstr ""

#: core/class-wcfm-admin.php:157
#: controllers/customers/wcfm-controller-customers-details.php:163
#: controllers/customers/wcfm-controller-customers-details.php:360
#: controllers/customers/wcfm-controller-customers-details.php:533
#: controllers/orders/wcfm-controller-dokan-orders.php:253
#: controllers/orders/wcfm-controller-orders.php:231
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:325
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:307
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:289
#: controllers/orders/wcfm-controller-wcvendors-orders.php:338
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:206
#: views/capability/wcfm-view-capability.php:393
msgid "View Details"
msgstr "Zeige Details"

#: core/class-wcfm-admin.php:193
msgid ""
"<h2>\n"
"\t\t\t\t\t\t\t\t\t\t           Do you want to have different capabilities "
"for each membership levels !!!\n"
"\t\t\t\t\t\t\t\t               </h2>"
msgstr ""
"<h2>Möchten Sie verschiedene Berechtigungen für die einzelnen Stufen der "
"Mitgliedschaft haben!!!</h2>"

#: core/class-wcfm-admin.php:199
msgid ""
"<p>WCFM - Groups & Staffs will empower you to set up totaly different "
"capability rules for your each membership levels very easily.</p>"
msgstr ""
"<p> WCFM - Groups & Staffs wird es Ihnen ermöglichen, völlig "
"unterschiedliche Berechtigungen für Ihre einzelnen Mitgliedschaftsstufen "
"einzurichten. </ p>"

#: core/class-wcfm-admin.php:205
msgid "WCFM GS >>"
msgstr "WCFM GS >>"

#: core/class-wcfm-admin.php:225 core/class-wcfm-admin.php:265
#: core/class-wcfm-admin.php:266 core/class-wcfm-admin.php:267
msgid "WCFM View"
msgstr "WCFM Anzeige"

#: core/class-wcfm-admin.php:284 core/class-wcfm-admin.php:294
#: core/class-wcfm-admin.php:296 core/class-wcfm-admin.php:298
#: core/class-wcfm-wcvendors.php:196 core/class-wcfm-wcvendors.php:196
msgid "WCFM Home"
msgstr "WCFM Startseite"

#: core/class-wcfm-admin.php:343
#, php-format
msgid ""
"WCFM totally works from front-end ... check dashboard settings %shere >>%s"
msgstr ""
"WCFM arbeitet komplett von Front-End ... Dashboard-Einstellungen "
"überprüfen%shier >>%s"

#: core/class-wcfm-admin.php:346
msgid "This page should contain \"[wc_frontend_manager]\" short code"
msgstr "Diese Seite sollte das Codekürzel \"[wc_frontend_manager]\" enthalten"

#: core/class-wcfm-admin.php:382 views/settings/wcfm-view-settings.php:277
msgid ""
"DO NOT USE WCFM DASHBOARD PAGE FOR OTHER PAGE SETTINGS, you will break your "
"site if you do."
msgstr ""
"VERWENDEN SIE KEINE WCFM-DASHBOARD-SEITE FÜR ANDERE SEITENEINSTELLUNGEN. "
"Wenn Sie dies tun, werden Sie Ihre Website beschädigen."

#: core/class-wcfm-ajax.php:273
#, php-format
msgid "A new %s <b>%s</b> added to the store by <b>%s</b>"
msgstr "Ein neuer %s <b>%s </b> wurde dem Shop um <b>%s </b> hinzugefügt"

#: core/class-wcfm-ajax.php:430 core/class-wcfm-ajax.php:464
#, php-format
msgid "<b>%s</b> order status updated to <b>%s</b> by <b>%s</b>"
msgstr ""

#: core/class-wcfm-ajax.php:473
msgid "Order status updated."
msgstr ""

#: core/class-wcfm-ajax.php:645
msgid "Email Verification Code"
msgstr ""

#: core/class-wcfm-ajax.php:646 core/class-wcfm-notification.php:364
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:101
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:184
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:136
msgid "Hi"
msgstr "Hallo"

#: core/class-wcfm-ajax.php:648
#, php-format
msgid "Here is your email verification code - <b>%s</b>"
msgstr ""

#: core/class-wcfm-ajax.php:649 core/class-wcfm-notification.php:371
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:108
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:191
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:144
msgid "Thank You"
msgstr "Vielen Dank"

#: core/class-wcfm-ajax.php:659
msgid "Email verification code send to your email."
msgstr ""

#: core/class-wcfm-ajax.php:661
msgid "Email verification not working right now, please try after sometime."
msgstr ""

#: core/class-wcfm-ajax.php:685
#, php-format
msgid "<b>%s</b> (Store: <b>%s</b>) has been disabled."
msgstr ""

#: core/class-wcfm-ajax.php:689
#, php-format
msgid "Your Store: <b>%s</b> has been disabled."
msgstr ""

#: core/class-wcfm-ajax.php:692
msgid "Vendor successfully disabled."
msgstr ""

#: core/class-wcfm-ajax.php:695
msgid "Vendor can not be disabled right now, please try after sometime."
msgstr ""

#: core/class-wcfm-ajax.php:726
#, php-format
msgid "<b>%s</b> (Store: <b>%s</b>) has been enabled."
msgstr ""

#: core/class-wcfm-ajax.php:730
#, php-format
msgid "Your Store: <b>%s</b> has been enabled."
msgstr ""

#: core/class-wcfm-ajax.php:734
msgid "Vendor successfully enabled."
msgstr ""

#: core/class-wcfm-ajax.php:737
msgid "Vendor can not be enabled right now, please try after sometime."
msgstr ""

#: core/class-wcfm-article.php:71
msgid "Articles Dashboard"
msgstr "Beitragszentrale"

#: core/class-wcfm-article.php:74
msgid "Articles Manager"
msgstr "Beiträge verwalten"

#: core/class-wcfm-article.php:118 views/articles/wcfm-view-articles.php:32
#: views/capability/wcfm-view-capability.php:308
msgid "Articles"
msgstr "Beiträge"

#: core/class-wcfm-capability.php:295
msgid "Products Limit: "
msgstr "Produkte-Limit: "

#: core/class-wcfm-capability.php:303 core/class-wcfm-capability.php:308
#: core/class-wcfm-capability.php:311 core/class-wcfm-capability.php:522
#: core/class-wcfm-capability.php:527 core/class-wcfm-capability.php:531
#: core/class-wcfm-capability.php:847 core/class-wcfm-capability.php:852
#: core/class-wcfm-capability.php:868
msgid "remaining"
msgstr "übrig"

#: core/class-wcfm-capability.php:314 core/class-wcfm-capability.php:534
#: core/class-wcfm-capability.php:871
#: core/class-wcfm-thirdparty-support.php:380
#: views/products-manager/wcfm-view-products-manage-tabs.php:52
msgid "Unlimited"
msgstr "Unbegrenzt"

#: core/class-wcfm-capability.php:518
msgid "Articles Limit: "
msgstr ""

#: core/class-wcfm-capability.php:843
msgid "Customers Limit: "
msgstr ""

#: core/class-wcfm-catalog.php:41 core/class-wcfm.php:736
#: views/capability/wcfm-view-capability.php:212
msgid "Catalog"
msgstr "Katalog"

#: core/class-wcfm-catalog.php:63
msgid "Catalog Mode"
msgstr "Katalog-Modus"

#: core/class-wcfm-catalog.php:68
msgid "Disable Add to Cart?"
msgstr "Die In den Warenkorb Schaltfläche deaktivieren?"

#: core/class-wcfm-catalog.php:69
msgid "Hide Price?"
msgstr "Preis verstecken?"

#: core/class-wcfm-customer.php:85
msgid "Customers Dashboard"
msgstr "Kunden Zentrale"

#: core/class-wcfm-customer.php:88
msgid "Customers Manager"
msgstr "Kunden Manager"

#: core/class-wcfm-customer.php:91
msgid "Customers Details"
msgstr "Kundendetails"

#: core/class-wcfm-customer.php:136
#: views/capability/wcfm-view-capability.php:415
#: views/customers/wcfm-view-customers.php:25
msgid "Customers"
msgstr "Kunden"

#: core/class-wcfm-customer.php:331
msgid "New Customer"
msgstr "Nekunde"

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:145
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:385
#: views/products-manager/wcfm-view-products-manage.php:390
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/products-popup/wcfm-view-product-popup.php:97
#: views/settings/wcfm-view-settings.php:506
msgid "Simple Product"
msgstr "Einfaches Produkt"

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:145
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:385
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/settings/wcfm-view-settings.php:506
msgid "Variable Product"
msgstr "Variables Produkt"

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:145
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:385
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/settings/wcfm-view-settings.php:506
msgid "Grouped Product"
msgstr "Gruppiertes Produkt"

#: core/class-wcfm-customfield-support.php:30
#: views/products/wcfm-view-products.php:145
#: views/products-manager/wcfm-view-customfield-products-manage.php:24
#: views/products-manager/wcfm-view-products-manage.php:385
#: views/products-popup/wcfm-view-product-popup.php:92
#: views/settings/wcfm-view-settings.php:506
msgid "External/Affiliate Product"
msgstr "Affiliate Produkt"

#: core/class-wcfm-customfield-support.php:32
msgid "Do not show"
msgstr "Nicht anzeigen"

#: core/class-wcfm-customfield-support.php:38
#: core/class-wcfm-customfield-support.php:42
msgid "Product Custom Field"
msgstr "Produkt Benutzerdefiniertes Feld"

#: core/class-wcfm-customfield-support.php:47
msgid "Custom Fields"
msgstr "Benutzerdefinierte Felder"

#: core/class-wcfm-customfield-support.php:47
msgid ""
"You can integrate any Third Party plugin using Custom Fields, but you should "
"use the same fields name as used by Third Party plugins."
msgstr ""
"Sie können jedes Drittanbieter-Plugin mit benutzerdefinierten Feldern "
"integrieren, aber Sie sollten denselben Feldernamen verwenden, wie sie von "
"Drittanbieter-Plugins verwendet werden."

#: core/class-wcfm-customfield-support.php:48
#: views/products-manager/wcfm-view-products-manage-tabs.php:237
#: views/settings/wcfm-view-settings.php:420
#: views/settings/wcfm-view-settings.php:482
msgid "Enable"
msgstr "Aktivieren"

#: core/class-wcfm-customfield-support.php:49
msgid "Block Name"
msgstr "Bezeichnung"

#: core/class-wcfm-customfield-support.php:50
msgid "Exlude Product Types"
msgstr ""

#: core/class-wcfm-customfield-support.php:50
msgid "Choose product types for which you want to disable this field block."
msgstr ""

#: core/class-wcfm-customfield-support.php:51
msgid "Visibility"
msgstr "Sichtbarkeit"

#: core/class-wcfm-customfield-support.php:51
msgid ""
"Set where and how you want to visible this custom field block in single "
"product page."
msgstr ""
"Legen Sie fest, wo und wie Sie diesen benutzerdefinierten Feldblock in der "
"einzelnen Produktseite sichtbar machen möchten."

#: core/class-wcfm-customfield-support.php:52
msgid "Fields as Group?"
msgstr "Felder als Gruppe?"

#: core/class-wcfm-customfield-support.php:53
msgid "Group name"
msgstr "Gruppenname"

#: core/class-wcfm-customfield-support.php:54
msgid "Fields"
msgstr "Felder"

#: core/class-wcfm-customfield-support.php:55
#: views/settings/wcfm-view-settings.php:483
msgid "Field Type"
msgstr "Feldtyp"

#: core/class-wcfm-customfield-support.php:56
#: views/settings/wcfm-view-settings.php:421
#: views/settings/wcfm-view-settings.php:484
msgid "Label"
msgstr "Titel"

#: core/class-wcfm-customfield-support.php:57 core/class-wcfm-frontend.php:736
#: views/articles/wcfm-view-articles.php:110
#: views/articles/wcfm-view-articles.php:121
#: views/customers/wcfm-view-customers.php:59
#: views/customers/wcfm-view-customers.php:74
#: views/enquiry/wcfm-view-enquiry-form.php:56
#: views/products/wcfm-view-products.php:198
#: views/products/wcfm-view-products.php:221
#: views/products-manager/wcfm-view-products-manage-tabs.php:47
#: views/products-manager/wcfm-view-products-manage-tabs.php:139
msgid "Name"
msgstr "Name"

#: core/class-wcfm-customfield-support.php:57
msgid ""
"This is will going to use as `meta_key` for storing this field value in "
"database."
msgstr ""
"Dies wird als `meta_key` verwendet, um diesen Feldwert in der Datenbank zu "
"speichern."

#: core/class-wcfm-customfield-support.php:58
#: views/settings/wcfm-view-settings.php:485
msgid "Options"
msgstr "Optionen"

#: core/class-wcfm-customfield-support.php:58
msgid ""
"Insert option values | separated, leave first element empty to show as '-"
"Select-'"
msgstr ""
"Optionswerte | Getrennt, lassen Sie das erste Element leer, um es als "
"'Select-' anzuzeigen'"

#: core/class-wcfm-customfield-support.php:59
#: views/settings/wcfm-view-settings.php:486
msgid "Help Content"
msgstr "Hilfe-Inhalt"

#: core/class-wcfm-customfield-support.php:60
#: views/settings/wcfm-view-settings.php:487
msgid "Required?"
msgstr "Erforderlich?"

#: core/class-wcfm-dokan.php:124 core/class-wcfm-wcfmmarketplace.php:118
#: core/class-wcfm-wcmarketplace.php:136 core/class-wcfm-wcmarketplace.php:137
#: core/class-wcfm-wcpvendors.php:114 core/class-wcfm-wcvendors.php:151
#: views/wcfm-view-header-panels.php:41 views/wcfm-view-menu.php:68
#: views/settings/wcfm-view-settings.php:36
msgid "My Store"
msgstr "Mein Shop"

#: core/class-wcfm-dokan.php:128 core/class-wcfm-vendor-support.php:1050
#: core/class-wcfm-vendor-support.php:1058
#: core/class-wcfm-vendor-support.php:1066
#: core/class-wcfm-vendor-support.php:1077
#: core/class-wcfm-vendor-support.php:1087
#: core/class-wcfm-wcfmmarketplace.php:122
#: core/class-wcfm-wcmarketplace.php:140 core/class-wcfm-wcpvendors.php:118
#: core/class-wcfm-wcvendors.php:155
msgid "Shop"
msgstr "Shop"

#: core/class-wcfm-dokan.php:449 core/class-wcfm-wcfmmarketplace.php:683
#: core/class-wcfm-wcmarketplace.php:683 core/class-wcfm-wcpvendors.php:532
#: core/class-wcfm-wcvendors.php:859
msgid "Total Earning"
msgstr "Gesamteinnahmen"

#: core/class-wcfm-enquiry.php:118
msgid "Enquiry Dashboard"
msgstr "Anfrage Dashboard"

#: core/class-wcfm-enquiry.php:121
msgid "Enquiry Manager"
msgstr "Anfrage Manager"

#: core/class-wcfm-enquiry.php:287 core/class-wcfm-enquiry.php:301
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:68
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:68
msgid "Inquiries"
msgstr ""

#: core/class-wcfm-enquiry.php:309 core/class-wcfm-enquiry.php:417
#: helpers/class-wcfm-install.php:317
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:183
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:190
#: views/enquiry/wcfm-view-enquiry-form.php:36
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:65
msgid "Inquiry"
msgstr ""

#: core/class-wcfm-enquiry.php:366 core/class-wcfm-enquiry.php:450
#: helpers/wcfm-core-functions.php:1109
#: views/enquiry/wcfm-view-enquiry-manage.php:75
#: views/enquiry/wcfm-view-enquiry-manage.php:75
#: views/enquiry/wcfm-view-enquiry-tab.php:52
#: views/enquiry/wcfm-view-enquiry.php:43
msgid "Enquiries"
msgstr "Anfragen"

#: core/class-wcfm-enquiry.php:388
#: controllers/settings/wcfm-controller-settings.php:188
#: includes/shortcodes/class-wcfm-shortcode-enquiry.php:33
#: views/enquiry/wcfm-view-enquiry-tab.php:21
#: views/settings/wcfm-view-settings.php:52
msgid "Ask a Question"
msgstr "Stellen Sie eine Frage"

#: core/class-wcfm-enquiry.php:462 core/class-wcfm-notification.php:216
msgid "Show All"
msgstr "Zeige alle"

#: core/class-wcfm-enquiry.php:465
msgid "There is no enquiry yet!!"
msgstr "Es gibt noch keine Anfrage!!"

#: core/class-wcfm-enquiry.php:494
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:159
msgid "New Enquiry"
msgstr "Neue Anfrage"

#: core/class-wcfm-frontend.php:271 views/dashboard/wcfm-view-dashboard.php:97
#: views/dashboard/wcfm-view-dokan-dashboard.php:123
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:126
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:144
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:148
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:130
#: views/settings/wcfm-view-settings.php:138
#: views/settings/wcfm-view-settings.php:277
msgid "Dashboard"
msgstr "Zentrale"

#: core/class-wcfm-frontend.php:306
#: controllers/articles/wcfm-controller-articles.php:161
#: controllers/articles/wcfm-controller-articles.php:164
#: controllers/coupons/wcfm-controller-coupons.php:98
#: controllers/coupons/wcfm-controller-coupons.php:100
#: controllers/enquiry/wcfm-controller-enquiry.php:167
#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:105
#: controllers/listings/wcfm-controller-listings.php:130
#: controllers/notice/wcfm-controller-notices.php:80
#: controllers/products/wcfm-controller-products.php:361
#: controllers/products/wcfm-controller-products.php:364
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:107
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:110
#: views/notice/wcfm-view-notice-view.php:59
msgid "Edit"
msgstr "Bearbeiten"

#: core/class-wcfm-frontend.php:310
#: controllers/articles/wcfm-controller-articles.php:162
#: controllers/articles/wcfm-controller-articles.php:165
#: controllers/enquiry/wcfm-controller-enquiry.php:170
#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:106
#: controllers/listings/wcfm-controller-listings.php:156
#: controllers/messages/wcfm-controller-messages.php:222
#: controllers/notice/wcfm-controller-notices.php:81
#: controllers/products/wcfm-controller-products.php:362
#: controllers/products/wcfm-controller-products.php:365
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:108
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:111
#: views/enquiry/wcfm-view-enquiry-manage.php:158
#: views/messages/wcfm-view-messages.php:99
msgid "Delete"
msgstr "Löschen"

#: core/class-wcfm-frontend.php:735
#: views/products-manager/wcfm-view-products-manage-tabs.php:138
msgid "Active?"
msgstr "Aktiv?"

#: core/class-wcfm-frontend.php:735
msgid "Check to associate this attribute with the product"
msgstr ""

#: core/class-wcfm-frontend.php:737
#: views/products-manager/wcfm-view-products-manage-tabs.php:140
msgid "Value(s):"
msgstr "Wert(e)"

#: core/class-wcfm-frontend.php:738
#: views/products-manager/wcfm-view-products-manage-tabs.php:141
msgid "Visible on the product page"
msgstr "Auf der Produktseite sichtbar"

#: core/class-wcfm-frontend.php:739
#: views/products-manager/wcfm-view-products-manage-tabs.php:142
msgid "Use as Variation"
msgstr "Verwendung als Produktvariante"

#: core/class-wcfm-library.php:373
msgid "Select an option&hellip;"
msgstr ""

#: core/class-wcfm-library.php:464
msgid "Memebership"
msgstr "Mitgliedschaft"

#: core/class-wcfm-library.php:880
msgid "Processing..."
msgstr "Wird verarbeitet..."

#: core/class-wcfm-library.php:880
msgid "Search:"
msgstr "Suche:"

#: core/class-wcfm-library.php:880
msgid "Show _MENU_ entries"
msgstr "Einträge _MENU_"

#: core/class-wcfm-library.php:880
msgid "Showing _START_ to _END_ of _TOTAL_ entries"
msgstr "Zeige _START_ bis _END_ von _TOTAL_ Einträgen"

#: core/class-wcfm-library.php:880
msgid "Showing 0 to 0 of 0 entries"
msgstr "Zeigt 0 von 0 von 0 Einträgen"

#: core/class-wcfm-library.php:880
msgid "(filtered _MAX_ entries of total)"
msgstr "(gefilterte _MAX_ Einträge gesamt)"

#: core/class-wcfm-library.php:880
msgid "Loading..."
msgstr "Wird geladen..."

#: core/class-wcfm-library.php:880
msgid "No matching records found"
msgstr "Keine Einträge gefunden"

#: core/class-wcfm-library.php:880
msgid "No data in the table"
msgstr "Keine Daten vorhanden"

#: core/class-wcfm-library.php:880
msgid "First"
msgstr "Erster"

#: core/class-wcfm-library.php:880 core/class-wcfm-library.php:1080
#: helpers/wcfm-core-functions.php:969
msgid "Previous"
msgstr "Vorherige"

#: core/class-wcfm-library.php:880 core/class-wcfm-library.php:1086
#: helpers/wcfm-core-functions.php:968
msgid "Next"
msgstr "Nächste"

#: core/class-wcfm-library.php:880
msgid "Last"
msgstr "Letzte"

#: core/class-wcfm-library.php:880
msgid "Print"
msgstr "Drucken"

#: core/class-wcfm-library.php:880
msgid "PDF"
msgstr "PDF"

#: core/class-wcfm-library.php:880
msgid "Excel"
msgstr "Excel"

#: core/class-wcfm-library.php:880
msgid "CSV"
msgstr "CSV"

#: core/class-wcfm-library.php:984
msgid "Choose Media"
msgstr "Medien auswählen"

#: core/class-wcfm-library.php:984
msgid "Choose Image"
msgstr "Wählen Sie ein Bild"

#: core/class-wcfm-library.php:984
msgid "Add to Gallery"
msgstr "Zur Galerie hinzufügen"

#: core/class-wcfm-library.php:1050
msgid "Selected:"
msgstr ""

#: core/class-wcfm-library.php:1051
msgid "Day"
msgstr ""

#: core/class-wcfm-library.php:1052
msgid "Days"
msgstr ""

#: core/class-wcfm-library.php:1053
msgid "Close"
msgstr ""

#: core/class-wcfm-library.php:1054
msgid "mo"
msgstr ""

#: core/class-wcfm-library.php:1055
msgid "tu"
msgstr ""

#: core/class-wcfm-library.php:1056
msgid "we"
msgstr ""

#: core/class-wcfm-library.php:1057
msgid "th"
msgstr ""

#: core/class-wcfm-library.php:1058
msgid "fr"
msgstr ""

#: core/class-wcfm-library.php:1059
msgid "sa"
msgstr ""

#: core/class-wcfm-library.php:1060
msgid "su"
msgstr ""

#: core/class-wcfm-library.php:1061
msgid "W"
msgstr ""

#: core/class-wcfm-library.php:1063
msgid "january"
msgstr ""

#: core/class-wcfm-library.php:1064
msgid "february"
msgstr ""

#: core/class-wcfm-library.php:1065
msgid "march"
msgstr ""

#: core/class-wcfm-library.php:1066
msgid "april"
msgstr ""

#: core/class-wcfm-library.php:1067
msgid "may"
msgstr ""

#: core/class-wcfm-library.php:1068
msgid "june"
msgstr ""

#: core/class-wcfm-library.php:1069
msgid "july"
msgstr ""

#: core/class-wcfm-library.php:1070
msgid "august"
msgstr ""

#: core/class-wcfm-library.php:1071
msgid "september"
msgstr ""

#: core/class-wcfm-library.php:1072
msgid "october"
msgstr ""

#: core/class-wcfm-library.php:1073
msgid "november"
msgstr ""

#: core/class-wcfm-library.php:1074
msgid "december"
msgstr ""

#: core/class-wcfm-library.php:1076
msgid "Shortcuts"
msgstr ""

#: core/class-wcfm-library.php:1077
msgid "Custom Values"
msgstr ""

#: core/class-wcfm-library.php:1078
msgid "Past"
msgstr ""

#: core/class-wcfm-library.php:1079
msgid "Following"
msgstr ""

#: core/class-wcfm-library.php:1081 core/class-wcfm-library.php:1087
msgid "Week"
msgstr ""

#: core/class-wcfm-library.php:1082 core/class-wcfm-library.php:1088
msgid "Month"
msgstr ""

#: core/class-wcfm-library.php:1083
msgid "This Week"
msgstr ""

#: core/class-wcfm-library.php:1084
#: includes/reports/class-wcfm-report-analytics.php:143
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:285
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:261
#: includes/reports/class-wcvendors-report-sales-by-date.php:283
#: views/enquiry/wcfm-view-enquiry.php:25
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:31
#: views/reports/wcfm-view-reports-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:31
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:31
msgid "This Month"
msgstr "Dieser Monat"

#: core/class-wcfm-library.php:1085 core/class-wcfm-library.php:1089
#: includes/reports/class-wcfm-report-analytics.php:141
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:283
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:259
#: includes/reports/class-wcvendors-report-sales-by-date.php:281
#: views/enquiry/wcfm-view-enquiry.php:27
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:29
#: views/reports/wcfm-view-reports-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:29
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:29
msgid "Year"
msgstr "Jahr"

#: core/class-wcfm-library.php:1090
#, php-format
msgid "Date range should not be more than %d days"
msgstr ""

#: core/class-wcfm-library.php:1091
#, php-format
msgid "Date range should not be less than %d days"
msgstr ""

#: core/class-wcfm-library.php:1092
#, php-format
msgid "Please select a date range longer than %d days"
msgstr ""

#: core/class-wcfm-library.php:1093
msgid "Please select a date"
msgstr ""

#: core/class-wcfm-library.php:1094
#, php-format
msgid "Please select a date range less than %d days"
msgstr ""

#: core/class-wcfm-library.php:1095
#, php-format
msgid "Please select a date range between %d and %d days"
msgstr ""

#: core/class-wcfm-library.php:1096
msgid "Please select a date range"
msgstr ""

#: core/class-wcfm-library.php:1097
msgid "Time"
msgstr ""

#: core/class-wcfm-library.php:1098
msgid "Hour"
msgstr ""

#: core/class-wcfm-library.php:1099
msgid "Minute"
msgstr ""

#: core/class-wcfm-library.php:1108
msgid "Choose Data Range"
msgstr ""

#: core/class-wcfm-non-ajax.php:53
msgid "Online"
msgstr "Online"

#: core/class-wcfm-non-ajax.php:53
#: controllers/listings/wcfm-controller-listings.php:25
#: controllers/products/wcfm-controller-products.php:27
#: views/articles/wcfm-view-articles.php:13
#: views/listings/wcfm-view-listings.php:38
#: views/products/wcfm-view-products.php:13
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:44
msgid "Pending"
msgstr "Ausstehend"

#: core/class-wcfm-non-ajax.php:53
#: controllers/products/wcfm-controller-products.php:26
#: views/articles/wcfm-view-articles-manage.php:374
#: views/articles/wcfm-view-articles.php:12
#: views/coupons/wcfm-view-coupons-manage.php:126
#: views/products/wcfm-view-products.php:12
#: views/products-manager/wcfm-view-products-manage.php:785
#: views/products-popup/wcfm-view-product-popup.php:289
msgid "Draft"
msgstr "Entwurf"

#: core/class-wcfm-non-ajax.php:138
msgid "No sales yet ..!!!"
msgstr "Noch keine Verkäufe vorhanden..!!!"

#: core/class-wcfm-non-ajax.php:188
msgid "View WCFM settings"
msgstr "Zeige WCFM Einstellungen"

#: core/class-wcfm-non-ajax.php:188 core/class-wcfm-query.php:169
#: core/class-wcfm.php:642 helpers/class-wcfm-install.php:321
#: views/capability/wcfm-view-capability.php:150
#: views/settings/wcfm-view-dokan-settings.php:129
#: views/settings/wcfm-view-settings.php:83
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:180
#: views/settings/wcfm-view-wcmarketplace-settings.php:136
#: views/settings/wcfm-view-wcpvendors-settings.php:73
#: views/settings/wcfm-view-wcvendors-settings.php:120
msgid "Settings"
msgstr "Einstellungen"

#: core/class-wcfm-non-ajax.php:195 core/class-wcfm-non-ajax.php:222
msgid "Add more power to your WCFM"
msgstr "Mehr Leistung für Ihre WCFM hinzufügen"

#: core/class-wcfm-non-ajax.php:195 core/class-wcfm-non-ajax.php:222
msgid "WCFM Ultimate"
msgstr "WCFM Ultimate"

#: core/class-wcfm-non-ajax.php:213
msgid "View WCFM documentation"
msgstr "Zeige WCFM Dokumente"

#: core/class-wcfm-non-ajax.php:213 views/settings/wcfm-view-settings.php:97
msgid "Documentation"
msgstr "Dokumentation"

#: core/class-wcfm-non-ajax.php:215
msgid "View WCFM Video Tutorial"
msgstr ""

#: core/class-wcfm-non-ajax.php:215 views/settings/wcfm-view-settings.php:96
msgid "Video Tutorial"
msgstr ""

#: core/class-wcfm-non-ajax.php:217
msgid "Any WC help feel free to contact us"
msgstr ""

#: core/class-wcfm-non-ajax.php:217
msgid "Customization Help"
msgstr ""

#: core/class-wcfm-non-ajax.php:277 core/class-wcfm-thirdparty-support.php:311
#: helpers/wcfm-core-functions.php:945 views/wcfm-view-menu.php:128
#: views/articles/wcfm-view-articles-manage.php:141
#: views/articles/wcfm-view-articles.php:65
#: views/coupons/wcfm-view-coupons-manage.php:77
#: views/coupons/wcfm-view-coupons.php:43
#: views/customers/wcfm-view-customers-details.php:107
#: views/customers/wcfm-view-customers-manage.php:113
#: views/customers/wcfm-view-customers.php:42
#: views/knowledgebase/wcfm-view-knowledgebase.php:38
#: views/listings/wcfm-view-listings.php:91
#: views/notice/wcfm-view-notices.php:37
#: views/products/wcfm-view-products-export.php:64
#: views/products/wcfm-view-products.php:115
#: views/products-manager/wcfm-view-products-manage.php:441
#: views/vendors/wcfm-view-vendors-manage.php:189
#: views/vendors/wcfm-view-vendors-new.php:75
#: views/vendors/wcfm-view-vendors.php:28
msgid "Add New"
msgstr "Hinzufügen"

#: core/class-wcfm-notification.php:62
#, php-format
msgid "A new product <b>%s</b> added by <b>%s</b>"
msgstr "Ein neues Produkt <b>%s</b>hinzugefügt von<b>%s</b>"

#: core/class-wcfm-notification.php:85
#, php-format
msgid "Product <b>%s</b> has been approved."
msgstr ""

#: core/class-wcfm-notification.php:105
#, php-format
msgid "Product <b>%s</b> awaiting for review"
msgstr ""

#: core/class-wcfm-notification.php:131
#, php-format
msgid "You have received an Order <b>#%s</b>"
msgstr "Sie haben eine Bestellung <b>#%s</b> erhalten"

#: core/class-wcfm-notification.php:147
#, php-format
msgid "You have received an Order <b>#%s</b> for <b>%s</b>"
msgstr "Sie haben eine Bestellung erhalten <b>#%s</b> für <b>%s</b>"

#: core/class-wcfm-notification.php:196 core/class-wcfm.php:739
#: views/messages/wcfm-view-messages.php:93
msgid "Notifications"
msgstr "Benachrichtigungen"

#: core/class-wcfm-notification.php:219
msgid "There is no notification yet!!"
msgstr "Es gibt noch keine Benachrichtigung !!"

#: core/class-wcfm-notification.php:362 core/class-wcfm-notification.php:377
msgid "Notification"
msgstr ""

#: core/class-wcfm-notification.php:366
msgid "You have received a new notification:"
msgstr ""

#: core/class-wcfm-notification.php:370
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:107
#, php-format
msgid "Check more details %shere%s."
msgstr ""

#: core/class-wcfm-policy.php:63 core/class-wcfm-policy.php:176
msgid "Store Policies"
msgstr ""

#: core/class-wcfm-policy.php:67 core/class-wcfm-policy.php:180
msgid "Policies Setting"
msgstr ""

#: core/class-wcfm-policy.php:79 core/class-wcfm-policy.php:191
#: core/class-wcfm-policy.php:335
msgid "Policy Tab Label"
msgstr "Reiterbeschriftung"

#: core/class-wcfm-policy.php:80 core/class-wcfm-policy.php:192
#: core/class-wcfm-policy.php:336 core/class-wcfm-policy.php:587
#: views/settings/wcfm-view-wcvendors-settings.php:398
msgid "Shipping Policy"
msgstr "Versandbedingungen"

#: core/class-wcfm-policy.php:81 core/class-wcfm-policy.php:193
#: core/class-wcfm-policy.php:337 core/class-wcfm-policy.php:592
#: views/settings/wcfm-view-wcvendors-settings.php:399
msgid "Refund Policy"
msgstr "Rückgaberecht"

#: core/class-wcfm-policy.php:82 core/class-wcfm-policy.php:194
#: core/class-wcfm-policy.php:338
msgid "Cancellation/Return/Exchange Policy"
msgstr ""

#: core/class-wcfm-policy.php:323
msgid "Product Policies"
msgstr ""

#: core/class-wcfm-policy.php:420
msgid "Store Polices"
msgstr ""

#: core/class-wcfm-policy.php:597
msgid "Cancellation / Return / Exchange Policy"
msgstr ""

#: core/class-wcfm-query.php:116
msgid "Products Dashboard"
msgstr "Produkte Hauptmenü"

#: core/class-wcfm-query.php:121
#, php-format
msgid "Product Manager -%s"
msgstr "Produkt Manager -%s"

#: core/class-wcfm-query.php:121
msgid "Product Manager"
msgstr "Produktmanager"

#: core/class-wcfm-query.php:124
msgid "Products Stock Manager"
msgstr "Lagerverwaltung Manager"

#: core/class-wcfm-query.php:127
#: views/products/wcfm-view-products-export.php:58
#: views/products/wcfm-view-products.php:90
#: views/products/wcfm-view-products.php:95
#: views/products/wcfm-view-products.php:95
msgid "Products Import"
msgstr "Produkte importieren"

#: core/class-wcfm-query.php:130
#: views/products/wcfm-view-products-export.php:33
#: views/products/wcfm-view-products.php:82
#: views/products/wcfm-view-products.php:82
msgid "Products Export"
msgstr "Produkte exportieren"

#: core/class-wcfm-query.php:133
msgid "Coupons Dashboard"
msgstr "Gutschein Hauptmenü"

#: core/class-wcfm-query.php:138
#, php-format
msgid "Coupon Manager -%s"
msgstr "Gutschein Verwalter -%s"

#: core/class-wcfm-query.php:138
msgid "Coupon Manager"
msgstr "Gutschein-Verwalter"

#: core/class-wcfm-query.php:141
msgid "Orders Dashboard"
msgstr "Auftrags-Zentrale"

#: core/class-wcfm-query.php:145
#, php-format
msgid "Order Details #%s"
msgstr "Bestell Details #%s"

#: core/class-wcfm-query.php:145 views/orders/wcfm-view-orders-details.php:115
msgid "Order Details"
msgstr "Bestelldetails"

#: core/class-wcfm-query.php:148
msgid "Reports - Sales by Date"
msgstr "Berichte - nach Datum"

#: core/class-wcfm-query.php:151
msgid "Reports - Sales by Product"
msgstr "Berichte - nach Produkt"

#: core/class-wcfm-query.php:154
msgid "Reports - Coupons by Date"
msgstr "Berichte - Gutscheine nach Datum"

#: core/class-wcfm-query.php:157
msgid "Reports - Out of Stock"
msgstr "Berichte - Nicht vorrätig"

#: core/class-wcfm-query.php:160
msgid "Reports - Low in Stock"
msgstr "Berichte - geringer Lagerbestand"

#: core/class-wcfm-query.php:163 helpers/class-wcfm-install.php:334
#: views/settings/wcfm-view-settings.php:204
#: views/settings/wcfm-view-settings.php:210
msgid "Analytics"
msgstr "Analysen"

#: core/class-wcfm-query.php:166 core/class-wcfm.php:741
#: helpers/class-wcfm-install.php:330 views/wcfm-view-header-panels.php:57
#: views/profile/wcfm-view-profile.php:173
#: views/settings/wcfm-view-wcpvendors-settings.php:112
msgid "Profile"
msgstr "Profil"

#: core/class-wcfm-query.php:172 core/class-wcfm.php:740
#: views/wcfm-view-header-panels.php:73
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:66
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:66
#: views/knowledgebase/wcfm-view-knowledgebase.php:26
msgid "Knowledgebase"
msgstr "Wissendatenbank"

#: core/class-wcfm-query.php:175
msgid "Knowledgebase Manager"
msgstr "Wissensdatenbank-Manager"

#: core/class-wcfm-query.php:178
msgid "Notice Dashboard"
msgstr "Notizen Zentrale"

#: core/class-wcfm-query.php:181
msgid "Notice Manager"
msgstr "Hinweis-Manager"

#: core/class-wcfm-query.php:184 core/class-wcfm.php:742
msgid "Notice"
msgstr "Notiz"

#: core/class-wcfm-query.php:187
msgid "Message Dashboard"
msgstr "Nachrichten Dashboard"

#: core/class-wcfm-thirdparty-support.php:157
msgid "Listings Dashboard"
msgstr "Listen Hauptmenü"

#: core/class-wcfm-thirdparty-support.php:212
#: core/class-wcfm-thirdparty-support.php:313
#: helpers/class-wcfm-setup-bak.php:950 helpers/class-wcfm-setup.php:978
#: views/listings/wcfm-view-listings.php:27
msgid "Listings"
msgstr "Jobs"

#: core/class-wcfm-thirdparty-support.php:241
msgid "Rental Product"
msgstr "Mietprodukt"

#: core/class-wcfm-thirdparty-support.php:248
#: core/class-wcfm-thirdparty-support.php:276
#: core/class-wcfm-thirdparty-support.php:462
#: controllers/products/wcfm-controller-products.php:276
msgid "Auction"
msgstr "Auktion"

#: core/class-wcfm-thirdparty-support.php:264
msgid "Listing Package"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:270
#: core/class-wcfm-thirdparty-support.php:412
#: controllers/products/wcfm-controller-products.php:278
msgid "Rental"
msgstr "Mieten"

#: core/class-wcfm-thirdparty-support.php:296
#: core/class-wcfm-thirdparty-support.php:310
#: core/class-wcfm-thirdparty-support.php:313
msgid "Manage Listings"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:302
msgid "Edit Listing"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:304
msgid "Add Listing"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:307
#: views/articles/wcfm-view-articles-manage.php:136
#: views/articles/wcfm-view-articles.php:60
#: views/coupons/wcfm-view-coupons-manage.php:72
#: views/coupons/wcfm-view-coupons.php:39
#: views/customers/wcfm-view-customers-details.php:96
#: views/customers/wcfm-view-customers-manage.php:106
#: views/customers/wcfm-view-customers.php:37
#: views/listings/wcfm-view-listings.php:86
#: views/orders/wcfm-view-orders-details.php:128
#: views/orders/wcfm-view-orders.php:68
#: views/products/wcfm-view-products-export.php:45
#: views/products/wcfm-view-products.php:76
#: views/products-manager/wcfm-view-products-manage.php:436
#: views/reports/wcfm-view-reports-out-of-stock.php:37
#: views/reports/wcfm-view-reports-sales-by-date.php:78
#: views/wc_bookings/wcfm-view-wcbookings-details.php:57
#: views/wc_bookings/wcfm-view-wcbookings.php:64
msgid "WP Admin View"
msgstr "WP Admin Ansicht"

#: core/class-wcfm-thirdparty-support.php:311
#: views/listings/wcfm-view-listings.php:91
msgid "Add New Listing"
msgstr "Neuen Job hinzufügen"

#: core/class-wcfm-thirdparty-support.php:417
msgid "Set Price Type"
msgstr "Festpreis, Prozent oder Kundenkategoriepreis"

#: core/class-wcfm-thirdparty-support.php:417
msgid "General Pricing"
msgstr "Allgemeine Preisgestaltung"

#: core/class-wcfm-thirdparty-support.php:417
msgid "Choose a price type - this controls the schema."
msgstr "Wählen Sie einen Preistyp - das steuert das Schema."

#: core/class-wcfm-thirdparty-support.php:418
msgid "Hourly Price"
msgstr "Stundenpreis"

#: core/class-wcfm-thirdparty-support.php:418
msgid "Hourly price will be applicabe if booking or rental days min 1day"
msgstr "Stundensatz wird bei Buchung oder Mietstunden min. 1 Tag beantragt"

#: core/class-wcfm-thirdparty-support.php:418
#: core/class-wcfm-thirdparty-support.php:419
msgid "Enter price here"
msgstr "Tragen Sie den Preis hier ein"

#: core/class-wcfm-thirdparty-support.php:419
msgid "General Price"
msgstr "Allgemeiner Preis"

#: core/class-wcfm-thirdparty-support.php:425
msgid "Availability"
msgstr "Verfügbarkeit"

#: core/class-wcfm-thirdparty-support.php:430
msgid "Product Availabilities"
msgstr "Produkt Verfügbarkeiten"

#: core/class-wcfm-thirdparty-support.php:430
#, fuzzy
msgid "Please select the date range to be disabled for the product."
msgstr ""
"Bitte wählen Sie den Datumsbereich aus, der für das Produkt deaktiviert "
"werden soll."

#: core/class-wcfm-thirdparty-support.php:431
#: views/coupons/wcfm-view-coupons.php:56
#: views/coupons/wcfm-view-coupons.php:66
#: views/messages/wcfm-view-messages.php:122
#: views/messages/wcfm-view-messages.php:135
#: views/products/wcfm-view-products.php:203
#: views/products/wcfm-view-products.php:226
msgid "Type"
msgstr "Type"

#: core/class-wcfm-thirdparty-support.php:431
msgid "Custom Date"
msgstr "Benutzerdefiniertes Datum"

#: core/class-wcfm-thirdparty-support.php:432
#: views/messages/wcfm-view-messages.php:124
#: views/messages/wcfm-view-messages.php:137
#: views/products-manager/wcfm-view-products-manage-tabs.php:245
#: views/products-manager/wcfm-view-products-manage.php:472
#: views/products-manager/wcfm-view-products-manage.php:472
#: views/products-popup/wcfm-view-product-popup.php:131
#: views/products-popup/wcfm-view-product-popup.php:131
#: views/settings/wcfm-view-dokan-settings.php:490
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:770
#: views/settings/wcfm-view-wcmarketplace-settings.php:746
#: views/settings/wcfm-view-wcpvendors-settings.php:154
#: views/settings/wcfm-view-wcvendors-settings.php:446
msgid "From"
msgstr "Von"

#: core/class-wcfm-thirdparty-support.php:433
#: views/messages/wcfm-view-messages.php:125
#: views/messages/wcfm-view-messages.php:138
#: views/products-manager/wcfm-view-products-manage.php:473
#: views/products-popup/wcfm-view-product-popup.php:132
msgid "To"
msgstr "An"

#: core/class-wcfm-thirdparty-support.php:434
#: core/class-wcfm-wcbookings.php:187
msgid "Bookable"
msgstr "Buchbar"

#: core/class-wcfm-thirdparty-support.php:434
msgid "NO"
msgstr "NEIN"

#: core/class-wcfm-thirdparty-support.php:467
msgid "Auction Date From"
msgstr "Versteigerungsdatum von"

#: core/class-wcfm-thirdparty-support.php:468
msgid "Auction Date To"
msgstr "Versteigerungsdatum bis"

#: core/class-wcfm-thirdparty-support.php:500
msgid "Has Voucher"
msgstr "Hat einen Gutschein"

#: core/class-wcfm-thirdparty-support.php:528
msgid "-- Choose Template --"
msgstr "-- Vorlage wählen --"

#: core/class-wcfm-thirdparty-support.php:534
msgid "Voucher Template"
msgstr "Gutschein Vorlage"

#: core/class-wcfm-thirdparty-support.php:534
msgid "Select a voucher template to make this into a voucher product."
msgstr ""
"Wählen Sie eine Gutscheinvorlage aus, um daraus ein Gutscheinprodukt zu "
"machen."

#: core/class-wcfm-thirdparty-support.php:646
#: core/class-wcfm-thirdparty-support.php:669
msgid "Select Delivery Time"
msgstr "Wählen Sie die Lieferzeit"

#: core/class-wcfm-thirdparty-support.php:738
msgid "Scheduler Config"
msgstr ""

#: core/class-wcfm-thirdparty-support.php:775
#: core/class-wcfm-wcfmmarketplace.php:629
#: core/class-wcfm-wcmarketplace.php:484 core/class-wcfm-wcmarketplace.php:632
#: core/class-wcfm-wcpvendors.php:406 core/class-wcfm-wcpvendors.php:505
#: core/class-wcfm-wcvendors.php:636 core/class-wcfm-wcvendors.php:649
#: core/class-wcfm-wcvendors.php:814 core/class-wcfm-wcvendors.php:837
#: helpers/class-wcfm-install.php:320
#: includes/reports/class-dokan-report-sales-by-date.php:831
#: includes/reports/class-wcfm-report-sales-by-date.php:720
#: views/capability/wcfm-view-capability.php:207
#: views/customers/wcfm-view-customers-manage.php:182
#: views/orders/wcfm-view-orders-details.php:610
#: views/orders/wcfm-view-orders-details.php:860
#: views/products-manager/wcfm-view-products-manage-tabs.php:83
#: views/products-manager/wcfm-view-products-manage-tabs.php:217
#: views/profile/wcfm-view-profile.php:297
#: views/settings/wcfm-view-dokan-settings.php:341
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:690
#: views/settings/wcfm-view-wcmarketplace-settings.php:606
#: views/settings/wcfm-view-wcvendors-settings.php:351
#: views/vendors/wcfm-view-vendors-new.php:131
msgid "Shipping"
msgstr "Versand"

#: core/class-wcfm-thirdparty-support.php:780
msgid "Shipment Origin Information"
msgstr ""

#: core/class-wcfm-vendor-support.php:141
msgid "Vendors Dashboard"
msgstr "Verkäufer Hauptmenü"

#: core/class-wcfm-vendor-support.php:144
msgid "New Vendor"
msgstr ""

#: core/class-wcfm-vendor-support.php:147
msgid "Vendors Manager"
msgstr "Verkäufer Manager"

#: core/class-wcfm-vendor-support.php:150
msgid "Vendors Commission"
msgstr "Verkäufer Provisionen"

#: core/class-wcfm-vendor-support.php:198
#: core/class-wcfm-vendor-support.php:211
#: views/vendors/wcfm-view-vendors-manage.php:186
#: views/vendors/wcfm-view-vendors-new.php:72
#: views/vendors/wcfm-view-vendors.php:17
msgid "Vendors"
msgstr "Verkäufer"

#: core/class-wcfm-vendor-support.php:290
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:79
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:80
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:83
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:125
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:126
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:129
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:130
#: controllers/messages/wcfm-controller-messages.php:155
#: controllers/messages/wcfm-controller-messages.php:183
#: views/enquiry/wcfm-view-enquiry.php:81
#: views/enquiry/wcfm-view-enquiry.php:93
#: views/enquiry/wcfm-view-my-account-enquiry.php:43
#: views/enquiry/wcfm-view-my-account-enquiry.php:56
#: views/products/wcfm-view-products.php:206
#: views/products/wcfm-view-products.php:229
#: views/settings/wcfm-view-dokan-settings.php:159
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:210
#: views/settings/wcfm-view-wcmarketplace-settings.php:166
#: views/settings/wcfm-view-wcvendors-settings.php:150
#: views/vendors/wcfm-view-vendors-manage.php:272
#: views/vendors/wcfm-view-vendors.php:61
#: views/vendors/wcfm-view-vendors.php:82
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:54
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:64
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:71
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:87
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:66
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:78
msgid "Store"
msgstr "Shop"

#: core/class-wcfm-vendor-support.php:293
msgid "Package Qty"
msgstr ""

#: core/class-wcfm-vendor-support.php:338
#: core/class-wcfm-vendor-support.php:492
#: core/class-wcfm-vendor-support.php:531
#: core/class-wcfm-vendor-support.php:547
#: core/class-wcfm-vendor-support.php:576 core/class-wcfm-wcmarketplace.php:479
#: core/class-wcfm-wcpvendors.php:405 core/class-wcfm-wcvendors.php:631
#: views/orders/wcfm-view-orders.php:92 views/orders/wcfm-view-orders.php:112
#: views/settings/wcfm-view-wcpvendors-settings.php:181
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:68
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:80
msgid "Commission"
msgstr "Provision"

#: core/class-wcfm-vendor-support.php:377
#: core/class-wcfm-vendor-support.php:382
#: views/vendors/wcfm-view-vendors.php:60
#: views/vendors/wcfm-view-vendors.php:81
msgid "Vendor"
msgstr "Verkäufer"

#: core/class-wcfm-vendor-support.php:497
#: core/class-wcfm-vendor-support.php:537
#: core/class-wcfm-vendor-support.php:542
#: core/class-wcfm-vendor-support.php:566
#: core/class-wcfm-vendor-support.php:571
msgid "Commission(%)"
msgstr "Provision %"

#: core/class-wcfm-vendor-support.php:538
#: core/class-wcfm-vendor-support.php:567
msgid "Fixed (per transaction)"
msgstr "Festgebühr pro Transaktion"

#: core/class-wcfm-vendor-support.php:543
#: core/class-wcfm-vendor-support.php:572
msgid "Fixed (per unit)"
msgstr "Festgebühr pro Einheit"

#: core/class-wcfm-vendor-support.php:843
#: controllers/messages/wcfm-controller-messages.php:181
#: views/articles/wcfm-view-articles.php:10
#: views/listings/wcfm-view-listings.php:36
#: views/messages/wcfm-view-messages.php:106
#: views/products/wcfm-view-products.php:10
#: views/wc_bookings/wcfm-view-wcbookings.php:19
msgid "All"
msgstr "Alle"

#: core/class-wcfm-vendor-support.php:845 helpers/wcfm-core-functions.php:962
msgid "Choose Vendor ..."
msgstr "Verkäufer auswählen..."

#: core/class-wcfm-vendor-support.php:2265
msgid "Review Product"
msgstr "Produkt bewerten"

#: core/class-wcfm-vendor-support.php:2266 helpers/wcfm-core-functions.php:995
msgid "New Product"
msgstr "Neues Produkt"

#: core/class-wcfm-vendor-support.php:2267 helpers/wcfm-core-functions.php:996
msgid "New Category"
msgstr "Neue Kategorie"

#: core/class-wcfm-vendor-support.php:2274
msgid "You may manage this using WCfM Capability Controller."
msgstr ""

#: core/class-wcfm-vendor-support.php:2275
#, php-format
msgid ""
"Manage vendor backend access from <a href=\"%s\">WCfM Capability "
"Controller</a>."
msgstr ""
"Verwalten Sie den Backend-Zugriff von<a href=\"%s\"> WCfM Capability "
"Controller </a>."

#: core/class-wcfm-wcbookings.php:93
msgid "Bookings Dashboard"
msgstr "Buchungen Dashboard"

#: core/class-wcfm-wcbookings.php:96
#: views/capability/wcfm-view-capability.php:347
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:99
#: views/wc_bookings/wcfm-view-wcbookings-details.php:67
#: views/wc_bookings/wcfm-view-wcbookings.php:36
msgid "Bookings List"
msgstr "Buchungsliste"

#: core/class-wcfm-wcbookings.php:99
msgid "Bookings Resources"
msgstr "Ressourcen für Buchungen"

#: core/class-wcfm-wcbookings.php:102
msgid "Bookings Resources Manage"
msgstr "Buchungsressourcen verwalten"

#: core/class-wcfm-wcbookings.php:105
msgid "Create Bookings"
msgstr "Buchungen erstellen"

#: core/class-wcfm-wcbookings.php:108
#: views/capability/wcfm-view-capability.php:348
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:114
msgid "Bookings Calendar"
msgstr "Buchungskalender"

#: core/class-wcfm-wcbookings.php:111
#, php-format
msgid "Booking Details #%s"
msgstr "Buchungsdetails#%s"

#: core/class-wcfm-wcbookings.php:114
msgid "Bookings settings"
msgstr "Buchungseinstellungen"

#: core/class-wcfm-wcbookings.php:335
msgid "Booking Options"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:188
msgid "Show all"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:189
msgid "Unpaid"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:190
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:46
msgid "Requested"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:191
#: controllers/customers/wcfm-controller-customers-details.php:397
msgid "Paid"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:192
#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/customers/wcfm-controller-customers-details.php:397
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:24
#: views/withdrawal/dokan/wcfm-view-payments.php:54
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:40
#: views/withdrawal/wcfm/wcfm-view-payments.php:56
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:47
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:45
msgid "Cancelled"
msgstr "Storniert"

#: core/class-wcfm-wcfmmarketplace.php:456
#: core/class-wcfm-wcmarketplace.php:477 views/orders/wcfm-view-orders.php:88
#: views/orders/wcfm-view-orders.php:108
msgid "Fees"
msgstr "Gebühren"

#: core/class-wcfm-wcfmmarketplace.php:458
#: includes/reports/class-dokan-report-sales-by-date.php:814
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:541
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:537
#: includes/reports/class-wcpvendors-report-sales-by-date.php:440
#: includes/reports/class-wcvendors-report-sales-by-date.php:550
msgid "Earning"
msgstr "Einnahme"

#: core/class-wcfm-wcfmmarketplace.php:613
msgid "Line Total"
msgstr ""

#: core/class-wcfm-wcfmmarketplace.php:621
#: core/class-wcfm-wcmarketplace.php:490 core/class-wcfm-wcmarketplace.php:643
#: core/class-wcfm-wcpvendors.php:407 core/class-wcfm-wcpvendors.php:514
#: core/class-wcfm-wcvendors.php:642 core/class-wcfm-wcvendors.php:655
#: core/class-wcfm-wcvendors.php:825 core/class-wcfm-wcvendors.php:848
#: views/orders/wcfm-view-orders-details.php:399
#: views/orders/wcfm-view-orders-details.php:400
#: views/products-manager/wcfm-view-products-manage-tabs.php:109
msgid "Tax"
msgstr "Steuer"

#: core/class-wcfm-wcfmmarketplace.php:636
#: core/class-wcfm-wcmarketplace.php:491 core/class-wcfm-wcmarketplace.php:652
#: core/class-wcfm-wcpvendors.php:408 core/class-wcfm-wcpvendors.php:523
msgid "Shipping Tax"
msgstr "Mehrwertsteuer Versand"

#: core/class-wcfm-wcfmmarketplace.php:645
#: core/class-wcfm-wcmarketplace.php:696 core/class-wcfm-wcpvendors.php:541
#: core/class-wcfm-wcvendors.php:869
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:67
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:79
msgid "Gross Total"
msgstr "Gesamtsumme"

#: core/class-wcfm-wcfmmarketplace.php:662
#: views/orders/wcfm-view-orders-details.php:915
msgid "Refunded"
msgstr "Erstattet"

#: core/class-wcfm-wcfmmarketplace.php:670
#: core/class-wcfm-wcmarketplace.php:665 views/vendors/wcfm-view-vendors.php:67
#: views/vendors/wcfm-view-vendors.php:88
msgid "Total Fees"
msgstr "Gesamt Gebühren"

#: core/class-wcfm-wcmarketplace.php:496 core/class-wcfm-wcpvendors.php:409
#: core/class-wcfm-wcvendors.php:662
#: views/orders/wcfm-view-orders-details.php:392
msgid "Total"
msgstr "Gesamtsumme"

#: core/class-wcfm-wcmarketplace.php:538 core/class-wcfm-wcmarketplace.php:626
#: core/class-wcfm-wcmarketplace.php:674 core/class-wcfm-wcmarketplace.php:688
#: controllers/orders/wcfm-controller-orders.php:210
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:279
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:261
msgid "N/A"
msgstr "N/A"

#: core/class-wcfm-wcmarketplace.php:621 core/class-wcfm-wcpvendors.php:496
#: core/class-wcfm-wcvendors.php:803
msgid "Line Commission"
msgstr "Linienprovision"

#: core/class-wcfm-wcsubscriptions.php:55
#: core/class-wcfm-xasubscriptions.php:55
#: views/capability/wcfm-view-capability.php:375
msgid "Subscriptions"
msgstr "Mitgliedschaften"

#: core/class-wcfm-wcsubscriptions.php:56
msgid "Variable Subscriptions"
msgstr "Variable Abonnements"

#: core/class-wcfm-withdrawal.php:63
#: views/withdrawal/dokan/wcfm-view-payments.php:26
#: views/withdrawal/wcfm/wcfm-view-payments.php:28
#: views/withdrawal/wcmp/wcfm-view-payments.php:26
msgid "Payments History"
msgstr "Auszahlungen Verlauf"

#: core/class-wcfm-withdrawal.php:68
#: views/withdrawal/dokan/wcfm-view-payments.php:42
#: views/withdrawal/dokan/wcfm-view-withdrawal.php:53
#: views/withdrawal/wcfm/wcfm-view-payments.php:44
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:110
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:117
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:34
#: views/withdrawal/wcmp/wcfm-view-payments.php:43
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:49
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:28
msgid "Withdrawal Request"
msgstr "Auszahlung beantragen"

#: core/class-wcfm-withdrawal.php:72
msgid "Withdrawal Reverse"
msgstr ""

#: core/class-wcfm-withdrawal.php:76
#: views/capability/wcfm-view-capability.php:268
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:97
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:36
msgid "Transaction Details"
msgstr ""

#: core/class-wcfm-withdrawal.php:109 views/settings/wcfm-view-settings.php:345
msgid "Payments"
msgstr "Zahlungen"

#: core/class-wcfm-withdrawal.php:118 core/class-wcfm-withdrawal.php:129
#: core/class-wcfm.php:746 helpers/class-wcfm-install.php:332
#: includes/reports/class-dokan-report-sales-by-date.php:822
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:551
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:547
#: includes/reports/class-wcpvendors-report-sales-by-date.php:450
#: includes/reports/class-wcvendors-report-sales-by-date.php:559
#: views/capability/wcfm-view-capability.php:262
#: views/settings/wcfm-view-settings.php:351
#: views/vendors/wcfm-view-vendors.php:71
#: views/vendors/wcfm-view-vendors.php:92
#: views/withdrawal/dokan/wcfm-view-payments.php:42
#: views/withdrawal/wcfm/wcfm-view-payments.php:44
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:110
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:117
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:34
#: views/withdrawal/wcmp/wcfm-view-payments.php:43
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:49
msgid "Withdrawal"
msgstr "Auszahlung"

#: core/class-wcfm.php:474 core/class-wcfm.php:502
#: controllers/vendors/wcfm-controller-vendors.php:82
msgid "Disable Vendor"
msgstr ""

#: core/class-wcfm.php:612 views/capability/wcfm-view-capability.php:176
#: views/listings/wcfm-view-listings.php:105
#: views/listings/wcfm-view-listings.php:117
#: views/products/wcfm-view-products.php:37
msgid "Products"
msgstr "Produkte"

#: core/class-wcfm.php:622 views/capability/wcfm-view-capability.php:324
#: views/coupons/wcfm-view-coupons.php:16
msgid "Coupons"
msgstr "Gutscheine"

#: core/class-wcfm.php:632 views/capability/wcfm-view-capability.php:388
#: views/customers/wcfm-view-customers-details.php:262
#: views/customers/wcfm-view-customers.php:63
#: views/customers/wcfm-view-customers.php:78
#: views/orders/wcfm-view-orders.php:30
msgid "Orders"
msgstr "Bestellungen"

#: core/class-wcfm.php:637 views/capability/wcfm-view-capability.php:437
msgid "Reports"
msgstr "Berichte"

#: core/class-wcfm.php:647 helpers/class-wcfm-setup-bak.php:87
#: helpers/class-wcfm-setup-bak.php:925 helpers/class-wcfm-setup.php:87
#: helpers/class-wcfm-setup.php:953 views/settings/wcfm-view-settings.php:117
#: views/settings/wcfm-view-settings.php:425
msgid "Capability"
msgstr "Eigenschaften"

#: core/class-wcfm.php:732
msgid "Popup Add Product"
msgstr ""

#: core/class-wcfm.php:733 views/settings/wcfm-view-settings.php:333
msgid "Menu Manager"
msgstr ""

#: core/class-wcfm.php:734
msgid "Enquiry"
msgstr "Ihre Frage zum Produkt"

#: core/class-wcfm.php:735
msgid "Enquiry Tab"
msgstr "Registerkarte - Anfrage"

#: core/class-wcfm.php:735
msgid ""
"If you just want to hide Single Product page `Enquiry Tab`, but keep enable "
"`Enquiry Module` for `Catalog Mode`."
msgstr ""
"Wenn Sie nur den \"Single-Produktseite Anfrage-Tab \" ausblenden möchten, "
"aber das \" Anfrage-Modul \" für den Katalog-Modus 'benutzen wollen."

#: core/class-wcfm.php:736
msgid ""
"If you disable `Enquiry Module` then `Catalog Module` will stop working "
"automatically."
msgstr ""
"Wenn Sie das `Anfrage-Modul 'deaktivieren, wird das ` Katalog-Modul` nicht "
"mehr automatisch funktionieren."

#: core/class-wcfm.php:737 helpers/class-wcfm-install.php:324
msgid "Article"
msgstr "Blog"

#: core/class-wcfm.php:738 helpers/class-wcfm-install.php:325
#: views/enquiry/wcfm-view-enquiry.php:80
#: views/enquiry/wcfm-view-enquiry.php:92
msgid "Customer"
msgstr "Kunde"

#: core/class-wcfm.php:743
msgid "Policies"
msgstr "Rechtliches"

#: core/class-wcfm.php:744
msgid "Custom Field"
msgstr "Benutzerdefiniertes Feld"

#: core/class-wcfm.php:745
msgid "Sub-menu"
msgstr "Untermenü"

#: core/class-wcfm.php:745
msgid "This will disable `Add New` sub-menus on hover."
msgstr ""
"Dadurch werden die Untermenüs \"Neu hinzufügen\" beim Hover deaktiviert."

#: core/class-wcfm.php:747
msgid "Refund"
msgstr ""

#: core/class-wcfm.php:751
msgid "BuddyPress Integration"
msgstr "BuddyPress Integration"

#: core/class-wcfm.php:774
msgid "Base Highlighter Color"
msgstr "Basis Farbe zur Hervorhebung"

#: core/class-wcfm.php:775
msgid "Top Bar Background Color"
msgstr "Top Bar Hintergrundfarbe einstellen"

#: core/class-wcfm.php:776
msgid "Top Bar Text Color"
msgstr "Top Bar Text Farbe"

#: core/class-wcfm.php:777
msgid "Dashboard Background Color"
msgstr "Dashboard-Hintergrund-Farbe"

#: core/class-wcfm.php:778
msgid "Container Background Color"
msgstr "Hintergrundfarbe für Container"

#: core/class-wcfm.php:779
msgid "Container Head Color"
msgstr "Container Head Farbe"

#: core/class-wcfm.php:780
msgid "Container Head Text Color"
msgstr "Textfarbe für Container Head"

#: core/class-wcfm.php:781
msgid "Container Head Active Color"
msgstr "Aktivierte Head Farbe"

#: core/class-wcfm.php:782
msgid "Container Head Active Text Color"
msgstr "Aktivierte Textfarbe für Container Head"

#: core/class-wcfm.php:783
msgid "Menu Background Color"
msgstr "Hintergrundfarbe für Menü"

#: core/class-wcfm.php:784
msgid "Menu Item Text Color"
msgstr "Textfarbe für Menüelement"

#: core/class-wcfm.php:785
msgid "Menu Active Item Background"
msgstr "Hintergrundfarbe für aktives Menüelement"

#: core/class-wcfm.php:786
msgid "Menu Active Item Text Color"
msgstr "Textfarbe für aktives Menüelement"

#: core/class-wcfm.php:787
msgid "Button Background Color"
msgstr "Hintergrundfarbe für Button"

#: core/class-wcfm.php:788
msgid "Button Text Color"
msgstr "Button Schriftfarbe"

#: helpers/class-wcfm-install.php:102
msgctxt "page_slug"
msgid "store-manager"
msgstr ""

#: helpers/class-wcfm-install.php:102
#: controllers/vendors/wcfm-controller-vendors-new.php:161
msgid "Store Manager"
msgstr ""

#: helpers/class-wcfm-install.php:316
#: views/customers/wcfm-view-customers-details.php:194
#: views/customers/wcfm-view-customers-details.php:205
#: views/enquiry/wcfm-view-enquiry.php:79
#: views/enquiry/wcfm-view-enquiry.php:91
#: views/enquiry/wcfm-view-my-account-enquiry.php:42
#: views/enquiry/wcfm-view-my-account-enquiry.php:53
#: views/wc_bookings/wcfm-view-wcbookings.php:117
#: views/wc_bookings/wcfm-view-wcbookings.php:129
msgid "Product"
msgstr "Produkt"

#: helpers/class-wcfm-install.php:318
#: views/customers/wcfm-view-customers-details.php:195
#: views/customers/wcfm-view-customers-details.php:206
#: views/customers/wcfm-view-customers-details.php:269
#: views/customers/wcfm-view-customers-details.php:279
#: views/orders/wcfm-view-orders.php:82 views/orders/wcfm-view-orders.php:102
#: views/wc_bookings/wcfm-view-wcbookings.php:118
#: views/wc_bookings/wcfm-view-wcbookings.php:130
msgid "Order"
msgstr "Bestellung"

#: helpers/class-wcfm-install.php:319
msgid "Report"
msgstr ""

#: helpers/class-wcfm-install.php:322 views/vendors/wcfm-view-vendors.php:59
#: views/vendors/wcfm-view-vendors.php:80
msgid "Verification"
msgstr ""

#: helpers/class-wcfm-install.php:323
msgid "Support Ticket"
msgstr ""

#: helpers/class-wcfm-install.php:326
msgid "Followers"
msgstr ""

#: helpers/class-wcfm-install.php:327
msgid "Coupon"
msgstr ""

#: helpers/class-wcfm-install.php:328
msgid "Noice"
msgstr ""

#: helpers/class-wcfm-install.php:329 views/settings/wcfm-view-settings.php:120
#: views/vendors/wcfm-view-vendors-manage.php:363
#: views/vendors/wcfm-view-vendors.php:62
#: views/vendors/wcfm-view-vendors.php:83
msgid "Membership"
msgstr "Mitgliedschaft"

#: helpers/class-wcfm-install.php:331
#: views/settings/wcfm-view-dokan-settings.php:252
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:378
#: views/settings/wcfm-view-wcmarketplace-settings.php:303
#: views/settings/wcfm-view-wcpvendors-settings.php:174
#: views/settings/wcfm-view-wcvendors-settings.php:246
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:55
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:65
#: views/withdrawal/wcfm/wcfm-view-payments.php:74
#: views/withdrawal/wcfm/wcfm-view-payments.php:88
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:74
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:90
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:79
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:92
msgid "Payment"
msgstr "Zahlung"

#: helpers/class-wcfm-install.php:333
msgid "General"
msgstr ""

#: helpers/class-wcfm-install.php:335
msgid "Marketing"
msgstr ""

#: helpers/class-wcfm-install.php:336
#: views/settings/wcfm-view-dokan-settings.php:443
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:705
msgid "SEO"
msgstr "Yoast SEO"

#: helpers/class-wcfm-setup-bak.php:52 helpers/class-wcfm-setup.php:52
msgid "Introduction"
msgstr "Einführung"

#: helpers/class-wcfm-setup-bak.php:57 helpers/class-wcfm-setup.php:57
msgid "Dashboard Setup"
msgstr "Dashboard Einstellungen"

#: helpers/class-wcfm-setup-bak.php:62 helpers/class-wcfm-setup.php:62
msgid "Marketplace Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:67 helpers/class-wcfm-setup.php:67
msgid "Commission Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:72 helpers/class-wcfm-setup.php:72
msgid "Withdrawal Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:77 helpers/class-wcfm-setup.php:77
msgid "Registration Setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:82 helpers/class-wcfm-setup.php:82
msgid "Style"
msgstr "Darstellung"

#: helpers/class-wcfm-setup-bak.php:92 helpers/class-wcfm-setup.php:92
msgid "Ready!"
msgstr "Bereit!"

#: helpers/class-wcfm-setup-bak.php:120 helpers/class-wcfm-setup.php:120
msgctxt "enhanced select"
msgid "No matches found"
msgstr "Keine Treffer gefunden"

#: helpers/class-wcfm-setup-bak.php:121 helpers/class-wcfm-setup.php:121
msgctxt "enhanced select"
msgid "Loading failed"
msgstr "Laden fehlgeschlagen"

#: helpers/class-wcfm-setup-bak.php:122 helpers/class-wcfm-setup.php:122
msgctxt "enhanced select"
msgid "Please enter 1 or more characters"
msgstr "Bitte geben Sie mindestens 1 oder mehr Zeichen ein"

#: helpers/class-wcfm-setup-bak.php:123 helpers/class-wcfm-setup.php:123
msgctxt "enhanced select"
msgid "Please enter %qty% or more characters"
msgstr "Bitte geben Sie mindestens %qty% oder mehr Zeichen ein"

#: helpers/class-wcfm-setup-bak.php:124 helpers/class-wcfm-setup.php:124
msgctxt "enhanced select"
msgid "Please delete 1 character"
msgstr "Bitte 1 Zeichen löschen"

#: helpers/class-wcfm-setup-bak.php:125 helpers/class-wcfm-setup.php:125
msgctxt "enhanced select"
msgid "Please delete %qty% characters"
msgstr "Bitte %qty% Zeichen löschen"

#: helpers/class-wcfm-setup-bak.php:126 helpers/class-wcfm-setup.php:126
msgctxt "enhanced select"
msgid "You can only select 1 item"
msgstr "Du kannst nur 1 Produkt auswählen"

#: helpers/class-wcfm-setup-bak.php:127 helpers/class-wcfm-setup.php:127
msgctxt "enhanced select"
msgid "You can only select %qty% items"
msgstr "Du kannst nur %qty% Produkt auswählen"

#: helpers/class-wcfm-setup-bak.php:128 helpers/class-wcfm-setup.php:128
msgctxt "enhanced select"
msgid "Loading more results&hellip;"
msgstr "Mehr Ergebnisse werden geladen;"

#: helpers/class-wcfm-setup-bak.php:129 helpers/class-wcfm-setup.php:129
msgctxt "enhanced select"
msgid "Searching&hellip;"
msgstr "Suche&hellip;"

#: helpers/class-wcfm-setup-bak.php:218 helpers/class-wcfm-setup.php:218
msgid "WCFM &rsaquo; Setup Wizard"
msgstr "WCFM> Einrichtungsassistent"

#: helpers/class-wcfm-setup-bak.php:275 helpers/class-wcfm-setup-bak.php:386
#: helpers/class-wcfm-setup.php:276
msgid "Welcome to WooCommerce Multi-vendor Marketplace!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:276 helpers/class-wcfm-setup.php:277
msgid ""
"Thank you for choosing WCFM Marketplace! This quick setup wizard will help "
"you to configure the basic settings and you will have your marketplace ready "
"in no time."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:278 helpers/class-wcfm-setup.php:279
msgid "Let's experience the best ever WC Frontend Dashboard!!"
msgstr "Erleben Sie das beste WC Frontend Dashboard aller Zeiten!!"

#: helpers/class-wcfm-setup-bak.php:279 helpers/class-wcfm-setup.php:280
msgid ""
"Thank you for choosing WCFM! This quick setup wizard will help you to "
"configure the basic settings and you will have your dashboard ready in no "
"time. <strong>It’s completely optional as WCFM already auto-setup.</strong>"
msgstr ""
"Vielen Dank für Ihre Wahl WCFM zu nutzen! Dieser quick-Setup-Assistent hilft "
"Ihnen, die grundlegenden Einstellungen zu konfigurieren, und Sie haben Ihr "
"Dashboard in kürzester Zeit bereit. <strong>Ist komplett optional zum WCFM "
"Auto-Setup.</strong>"

#: helpers/class-wcfm-setup-bak.php:281 helpers/class-wcfm-setup.php:282
msgid ""
"If you don't want to go through the wizard right now, you can skip and "
"return to the WordPress dashboard. Come back anytime if you change your mind!"
msgstr ""
"Jetzt keine Zeit? Wenn Sie den Wizards nicht verwenden möchten können Sie "
"Überspringen und zum Dashboard vom WordPress zurückgehen. Sie können "
"jederzeit hierher zurückkommen!"

#: helpers/class-wcfm-setup-bak.php:283 helpers/class-wcfm-setup-bak.php:390
#: helpers/class-wcfm-setup.php:284
msgid "Let's go!"
msgstr "Los gehts!"

#: helpers/class-wcfm-setup-bak.php:284 helpers/class-wcfm-setup.php:285
msgid "Not right now"
msgstr "Nicht gerade jetzt"

#: helpers/class-wcfm-setup-bak.php:305 helpers/class-wcfm-setup.php:306
msgid "Dashboard setup"
msgstr "Dashboard setup"

#: helpers/class-wcfm-setup-bak.php:310 helpers/class-wcfm-setup.php:311
msgid "WCFM Full View"
msgstr "WCFM Volle Ansicht"

#: helpers/class-wcfm-setup-bak.php:311 helpers/class-wcfm-setup.php:312
msgid "Theme Header"
msgstr "Thema-Header"

#: helpers/class-wcfm-setup-bak.php:312 helpers/class-wcfm-setup.php:313
msgid "WCFM Slick Menu"
msgstr "WCFM Menü Slick"

#: helpers/class-wcfm-setup-bak.php:313 helpers/class-wcfm-setup.php:314
msgid "WCFM Header Panel"
msgstr "WCFM Kopfleiste"

#: helpers/class-wcfm-setup-bak.php:314 helpers/class-wcfm-setup.php:315
msgid "Welcome Box"
msgstr "Willkommens-Box"

#: helpers/class-wcfm-setup-bak.php:315 helpers/class-wcfm-setup.php:316
msgid "Category Checklist View"
msgstr "Kategorie-Checkliste Ansicht"

#: helpers/class-wcfm-setup-bak.php:315 helpers/class-wcfm-setup.php:316
#: views/settings/wcfm-view-settings.php:160
msgid ""
"Disable this to have Product Manager Category/Custom Taxonomy Selector - "
"Flat View."
msgstr ""
"Deaktivieren Sie dies, um Produktmanager-Kategorie / Custom Taxonomy "
"Selector - Flat Ansicht  zu haben."

#: helpers/class-wcfm-setup-bak.php:316 helpers/class-wcfm-setup.php:317
msgid "Quick Access"
msgstr "Schnellzugriff"

#: helpers/class-wcfm-setup-bak.php:317 helpers/class-wcfm-setup.php:318
#: views/settings/wcfm-view-settings.php:156
msgid "Disable Responsive Float Menu"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:318 helpers/class-wcfm-setup.php:319
msgid "Float Button"
msgstr "Float-Button"

#: helpers/class-wcfm-setup-bak.php:323 helpers/class-wcfm-setup-bak.php:426
#: helpers/class-wcfm-setup-bak.php:747 helpers/class-wcfm-setup-bak.php:863
#: helpers/class-wcfm-setup-bak.php:894 helpers/class-wcfm-setup-bak.php:963
#: helpers/class-wcfm-setup.php:324 helpers/class-wcfm-setup.php:426
#: helpers/class-wcfm-setup.php:747 helpers/class-wcfm-setup.php:891
#: helpers/class-wcfm-setup.php:922 helpers/class-wcfm-setup.php:991
msgid "Continue"
msgstr "Fortfahren"

#: helpers/class-wcfm-setup-bak.php:324 helpers/class-wcfm-setup-bak.php:383
#: helpers/class-wcfm-setup-bak.php:427 helpers/class-wcfm-setup-bak.php:748
#: helpers/class-wcfm-setup-bak.php:864 helpers/class-wcfm-setup-bak.php:895
#: helpers/class-wcfm-setup-bak.php:964 helpers/class-wcfm-setup.php:325
#: helpers/class-wcfm-setup.php:427 helpers/class-wcfm-setup.php:748
#: helpers/class-wcfm-setup.php:892 helpers/class-wcfm-setup.php:923
#: helpers/class-wcfm-setup.php:992
msgid "Skip this step"
msgstr "Schritt überspringen"

#: helpers/class-wcfm-setup-bak.php:376
msgid "Do you want to setup a multi-vendor marketplace!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:378
msgid "Install WCFM Marketplace"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:387
#, php-format
msgid ""
"You have installed <b>%s</b> as your multi-vendor marketplace. Setup multi-"
"vendor setting from plugin setup panel."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:388
#, php-format
msgid ""
"You may switch your multi-vendor to %s for having more features and "
"flexibilities."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:393 helpers/class-wcfm-setup.php:384
msgid "Marketplace setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:520 helpers/class-wcfm-setup-bak.php:684
#: helpers/class-wcfm-setup.php:520 helpers/class-wcfm-setup.php:684
#, php-format
msgid ""
"%1$s could not be installed (%2$s). <a href=\"%3$s\">Please install it "
"manually by clicking here.</a>"
msgstr ""
"%1$s konnte nicht installiert werden (%2$s). <a href=\"%3$s\">Bitte manuell "
"installieren, indem Sie hier klicken.</a>"

#: helpers/class-wcfm-setup-bak.php:540 helpers/class-wcfm-setup-bak.php:704
#: helpers/class-wcfm-setup.php:540 helpers/class-wcfm-setup.php:704
#, php-format
msgid ""
"%1$s was installed but could not be activated. <a href=\"%2$s\">Please "
"activate it manually by clicking here.</a>"
msgstr ""
"%1$s wurde installiert, konnte aber nicht aktiviert werden. <a href=\"%2$s\">"
"Bitte manuell aktivieren, indem Sie hier klicken.</a>"

#: helpers/class-wcfm-setup-bak.php:590 helpers/class-wcfm-setup.php:590
msgid "Setup WCFM Maketplace vendor registration:"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:592 helpers/class-wcfm-setup.php:592
msgid "Setup Registration"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:731 helpers/class-wcfm-setup.php:731
msgid "Commission setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:737 helpers/class-wcfm-setup.php:737
msgid ""
"You may setup more commission rules (By Sales Total and Product Price) from "
"setting panel."
msgstr ""

#: helpers/class-wcfm-setup-bak.php:796 helpers/class-wcfm-setup.php:801
msgid "Withdrawal setup"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:882 helpers/class-wcfm-setup.php:910
#: views/settings/wcfm-view-settings.php:224
msgid "Dashboard Style"
msgstr "Dashboard-Stil"

#: helpers/class-wcfm-setup-bak.php:930 helpers/class-wcfm-setup.php:958
#: views/capability/wcfm-view-capability.php:227
msgid "Backend Access"
msgstr "Backend Zugang"

#: helpers/class-wcfm-setup-bak.php:933 helpers/class-wcfm-setup.php:961
msgid "Submit Products"
msgstr "Produkte senden"

#: helpers/class-wcfm-setup-bak.php:934 helpers/class-wcfm-setup.php:962
#: views/capability/wcfm-view-capability.php:182
msgid "Publish Products"
msgstr "Produkte veröffentlichen"

#: helpers/class-wcfm-setup-bak.php:935 helpers/class-wcfm-setup.php:963
#: views/capability/wcfm-view-capability.php:183
msgid "Edit Live Products"
msgstr "Veröffentliche Produkte bearbeiten"

#: helpers/class-wcfm-setup-bak.php:936 helpers/class-wcfm-setup.php:964
#: views/capability/wcfm-view-capability.php:185
msgid "Delete Products"
msgstr "Produkte löschen"

#: helpers/class-wcfm-setup-bak.php:940 helpers/class-wcfm-setup.php:968
#: views/capability/wcfm-view-capability.php:282
#: views/capability/wcfm-view-capability.php:344
msgid "Manage Bookings"
msgstr "Buchungen verwalten"

#: helpers/class-wcfm-setup-bak.php:945 helpers/class-wcfm-setup.php:973
#: views/capability/wcfm-view-capability.php:294
#: views/capability/wcfm-view-capability.php:378
msgid "Manage Subscriptions"
msgstr "Newsletterabonnements managen"

#: helpers/class-wcfm-setup-bak.php:950 helpers/class-wcfm-setup.php:978
#: views/capability/wcfm-view-capability.php:299
msgid "by WP Job Manager."
msgstr "by Job Manager."

#: helpers/class-wcfm-setup-bak.php:954 helpers/class-wcfm-setup.php:982
#: views/capability/wcfm-view-capability.php:391
msgid "View Orders"
msgstr "Zeige Bestellungen"

#: helpers/class-wcfm-setup-bak.php:955 helpers/class-wcfm-setup.php:983
#: views/capability/wcfm-view-capability.php:392
msgid "Status Update"
msgstr "Status aktualisieren"

#: helpers/class-wcfm-setup-bak.php:958 helpers/class-wcfm-setup.php:986
#: views/capability/wcfm-view-capability.php:440
msgid "View Reports"
msgstr "Berichte anzeigen"

#: helpers/class-wcfm-setup-bak.php:980 helpers/class-wcfm-setup.php:1008
msgid "We are done!"
msgstr "Wir sind fertig!"

#: helpers/class-wcfm-setup-bak.php:983 helpers/class-wcfm-setup.php:1011
msgid ""
"Your marketplace is ready. It's time to experience the things more Easily "
"and Peacefully. Also you will be a bit more relax than ever before, have fun!"
"!"
msgstr ""

#: helpers/class-wcfm-setup-bak.php:985 helpers/class-wcfm-setup.php:1013
msgid ""
"Your front-end dashboard is ready. It's time to experience the things more "
"Easily and Peacefully. Also you will be a bit more relax than ever before, "
"have fun!!"
msgstr ""
"Ihr Frontend-Dashboard ist fertig. Es ist Zeit, die Dinge leichter und "
"einfacher zu erleben. Auch Sie werden sich ein bisschen mehr entspannen als "
"je zuvor, viel Spaß !!"

#: helpers/class-wcfm-setup-bak.php:990 helpers/class-wcfm-setup.php:1018
msgid "Next steps"
msgstr "Nächster Schritt"

#: helpers/class-wcfm-setup-bak.php:992 helpers/class-wcfm-setup.php:1020
msgid "Let's go to Dashboard"
msgstr "Gehen wir zur Zentrale"

#: helpers/class-wcfm-setup-bak.php:996 helpers/class-wcfm-setup.php:1024
msgid "Learn more"
msgstr "Mehr erfahren"

#: helpers/class-wcfm-setup-bak.php:998 helpers/class-wcfm-setup.php:1026
msgid "Watch the tutorial videos"
msgstr "Sehen Sie sich die Tutorial-Videos an"

#: helpers/class-wcfm-setup-bak.php:999 helpers/class-wcfm-setup.php:1027
msgid "WCFM - What & Why?"
msgstr "WCFM - Was und warum?"

#: helpers/class-wcfm-setup-bak.php:1000 helpers/class-wcfm-setup.php:1028
msgid "Choose your multi-vendor plugin"
msgstr "Wähle Dein Multi-Verkäufer Plugin"

#: helpers/class-wcfm-setup-bak.php:1281 helpers/class-wcfm-setup.php:1322
msgid "Return to the WordPress Dashboard"
msgstr "Zur WordPress-Übersichtsseite zurückkehren"

#: helpers/class-wcfm-setup.php:377
msgid "Setup your multi-vendor marketplace in minutes!"
msgstr ""

#: helpers/class-wcfm-setup.php:379
msgid "Setup WCFM Marketplace"
msgstr ""

#: helpers/class-wcfm-setup.php:416
msgid "Single product page related products rule."
msgstr ""

#: helpers/class-wcfm-setup.php:417
msgid "No of products at Store per Page."
msgstr ""

#: helpers/class-wcfm-setup.php:879
msgid "Reverse Withdrawal setup"
msgstr ""

#: helpers/wcfm-core-functions.php:6
#, php-format
msgid ""
"%sWooCommerce Frontend Manager is inactive.%s The %sWooCommerce plugin%s "
"must be active for the WooCommerce Frontend Manager to work. Please "
"%sinstall & activate WooCommerce%s"
msgstr ""
"%sWooCommerce Frontend Manager ist inaktiv.%s Das%sWooCommerce Plugin%s muss "
"für den WooCommerce Frontend Manager aktiv sein. Bitte%sinstallieren & "
"aktivieren WooCommerce%s"

#: helpers/wcfm-core-functions.php:16
#, php-format
msgid ""
"%sOpps ..!!!%s You are using %sWC %s. WCFM works only with %sWC 3.0+%s. "
"PLease upgrade your WooCommerce version now to make your life easier and "
"peaceful by using WCFM."
msgstr ""
"%sOpps .. !!!%s Sie verwenden%sWC%s. WCFM arbeitet nur mit%sWC 3.0  %s. "
"PLease aktualisieren Sie Ihre WooCommerce-Version jetzt, um Ihr Leben "
"einfacher und friedlicher zu machen, indem Sie WCFM verwenden."

#: helpers/wcfm-core-functions.php:44
#, php-format
msgid ""
"%s: You don't have permission to access this page. Please contact your "
"%sStore Admin%s for assistance."
msgstr ""
"%s: Sie haben keine Berechtigung zum Zugriff auf diese Seite. Wenden Sie "
"sich bitte an Ihren %sShop Admin%s."

#: helpers/wcfm-core-functions.php:59 helpers/wcfm-core-functions.php:89
msgid ""
": Please ask your Store Admin to upgrade your dashboard to access this "
"feature."
msgstr "Bitten Sie Ihren Shop Admin, Ihre Funktionen zu erweitern."

#: helpers/wcfm-core-functions.php:64 helpers/wcfm-core-functions.php:94
#, php-format
msgid ""
"%s: Please ask your %sStore Admin%s to upgrade your dashboard to access this "
"feature."
msgstr ""
"%s: Bitte fragen Sie Ihren %sStore Admin %s, aktualisieren Sie Ihr Dashboard,"
" um auf dieses Feature zuzugreifen."

#: helpers/wcfm-core-functions.php:71
msgid ""
": Upgrade your WCFM to WCFM - Ultimate to avail this feature. Disable this "
"notice from settings panel using \"Disable Ultimate Notice\" option."
msgstr ""
": Aktualisieren Sie von WCFM auf WCFM - Ultimate, um diese Funktion nutzen "
"zu können. Deaktivieren Sie diesen Hinweis im Einstellungsfenster mit der "
"Option \"Ultimate Notice deaktivieren\"."

#: helpers/wcfm-core-functions.php:75
#, php-format
msgid ""
"%s: Upgrade your WCFM to %sWCFM - Ultimate%s to access this feature. Disable "
"this notice from settings panel using \"Disable Ultimate Notice\" option."
msgstr ""
"%s: Aktualisieren Sie Ihre WCFM auf %sWCFM - Ultimate %s um auf diese "
"Funktion zugreifen zu können. Deaktivieren Sie diesen Hinweis im "
"Einstellungsfenster mit der Option \"Ultimate Notice deaktivieren\"."

#: helpers/wcfm-core-functions.php:101
msgid ""
": Associate your WCFM with WCFM - Groups & Staffs to avail this feature."
msgstr ""
": Verknüpfen Sie Ihr WCFM mit WCFM - Gruppen & Personal, um diese Funktion "
"zu nutzen."

#: helpers/wcfm-core-functions.php:105
#, php-format
msgid ""
"%s: Associate your WCFM with %sWCFM - Groups & Staffs%s to access this "
"feature."
msgstr ""
"%s: Verknüpfen Sie Ihren WCFM mit %sWCFM - Groups & Staffs%s, um auf diese "
"Funktion zuzugreifen."

#: helpers/wcfm-core-functions.php:119
msgid ": Please contact your Store Admin to access this feature."
msgstr "Bitten Sie Ihren Shop Admin, Ihre Funktionen zu erweitern."

#: helpers/wcfm-core-functions.php:124
#, php-format
msgid "%s: Please contact your %sStore Admin%s to access this feature."
msgstr ""
"%s: Bitte wenden Sie sich an Ihrem %sStore Admin %s um auf diese Funktion "
"zugreifen zu können."

#: helpers/wcfm-core-functions.php:131
msgid ": Associate your WCFM with WCFM - Analytics to access this feature."
msgstr ""
": Verknüpfen Sie Ihre WCFM mit WCFM - Analytics, um auf diese Funktion "
"zuzugreifen."

#: helpers/wcfm-core-functions.php:135
#, php-format
msgid ""
"%s: Associate your WCFM with %sWCFM - Analytics%s to access this feature."
msgstr ""
"%s: Diese Funktion setzt die Verbindung mit %sWCFM - Analytics%s voraus."

#: helpers/wcfm-core-functions.php:776
msgid "Please insert Article Title before submit."
msgstr "Bitte geben Sie den Beitrag vor dem Absenden ein."

#: helpers/wcfm-core-functions.php:777
msgid "Article Successfully Saved."
msgstr "Beitrag Erfolgreich gespeichert."

#: helpers/wcfm-core-functions.php:778
msgid "Article Successfully submitted for moderation."
msgstr "Beitrag Erfolgreich zur Moderation eingereicht."

#: helpers/wcfm-core-functions.php:779
msgid "Article Successfully Published."
msgstr "Beitrag erfolgreich veröffentlicht."

#: helpers/wcfm-core-functions.php:791
msgid "Please insert Product Title before submit."
msgstr "Bitte geben Sie einen Produkttitel ein."

#: helpers/wcfm-core-functions.php:792
msgid "Product SKU must be unique."
msgstr "Produktnummer muss einmalig sein."

#: helpers/wcfm-core-functions.php:793
msgid "Variation SKU must be unique."
msgstr "Varianten Produktnummer muss einmalig sein."

#: helpers/wcfm-core-functions.php:794
msgid "Product Successfully Saved."
msgstr "Produkt erfolgreich gespeichert."

#: helpers/wcfm-core-functions.php:795
msgid "Product Successfully submitted for moderation."
msgstr "Produkt wurde erfolgreich zur Überprüfung gesendet."

#: helpers/wcfm-core-functions.php:796
msgid "Product Successfully Published."
msgstr "Produkt erfolgreich veröffentlicht."

#: helpers/wcfm-core-functions.php:797
msgid "Set Stock"
msgstr ""

#: helpers/wcfm-core-functions.php:798
#: views/products-manager/wcfm-view-products-manage-tabs.php:210
msgid "Increase Stock"
msgstr ""

#: helpers/wcfm-core-functions.php:799
#: views/products-manager/wcfm-view-products-manage-tabs.php:243
#: views/products-manager/wcfm-view-thirdparty-products-manage.php:294
msgid "Regular Price"
msgstr "Regulärer Preis"

#: helpers/wcfm-core-functions.php:800
msgid "Regular price increase by"
msgstr ""

#: helpers/wcfm-core-functions.php:801
msgid "Regular price decrease by"
msgstr ""

#: helpers/wcfm-core-functions.php:802
#: views/products-manager/wcfm-view-products-manage-tabs.php:244
#: views/products-manager/wcfm-view-products-manage.php:471
#: views/products-popup/wcfm-view-product-popup.php:130
msgid "Sale Price"
msgstr "Angebotspreis"

#: helpers/wcfm-core-functions.php:803
msgid "Sale price increase by"
msgstr ""

#: helpers/wcfm-core-functions.php:804
msgid "Sale price decrease by"
msgstr ""

#: helpers/wcfm-core-functions.php:805
#: views/products-manager/wcfm-view-products-manage-tabs.php:90
#: views/products-manager/wcfm-view-products-manage-tabs.php:218
msgid "Length"
msgstr "Länge"

#: helpers/wcfm-core-functions.php:806
#: views/products-manager/wcfm-view-products-manage-tabs.php:91
#: views/products-manager/wcfm-view-products-manage-tabs.php:219
msgid "Width"
msgstr "Breite"

#: helpers/wcfm-core-functions.php:807
#: views/products-manager/wcfm-view-products-manage-tabs.php:92
#: views/products-manager/wcfm-view-products-manage-tabs.php:220
msgid "Height"
msgstr "Höhe"

#: helpers/wcfm-core-functions.php:808
#: views/products-manager/wcfm-view-products-manage-tabs.php:89
#: views/products-manager/wcfm-view-products-manage-tabs.php:221
msgid "Weight"
msgstr "Gewicht"

#: helpers/wcfm-core-functions.php:809
#: views/products-manager/wcfm-view-products-manage-tabs.php:52
msgid "Download Limit"
msgstr ""

#: helpers/wcfm-core-functions.php:810
#: views/products-manager/wcfm-view-products-manage-tabs.php:53
msgid "Download Expiry"
msgstr ""

#: helpers/wcfm-core-functions.php:824
msgid "Please insert atleast Coupon Title before submit."
msgstr ""
"Bitte geben Sie den Titel des Gutscheins ein, bevor Sie ihn einreichen."

#: helpers/wcfm-core-functions.php:825
msgid "Coupon Successfully Saved."
msgstr "Der Gutschein wurde erfolgreich gespeichert."

#: helpers/wcfm-core-functions.php:826
msgid "Coupon Successfully Published."
msgstr "Gutschein erfolgreich veröffentlicht."

#: helpers/wcfm-core-functions.php:838
msgid "Please insert atleast Knowledgebase Title before submit."
msgstr ""
"Bitte geben Sie mindestens einen Wissensdatenbank-Titel vor dem Absenden ein."

#: helpers/wcfm-core-functions.php:839
msgid "Knowledgebase Successfully Saved."
msgstr "Knowledgebase erfolgreich gespeichert."

#: helpers/wcfm-core-functions.php:840
msgid "Knowledgebase Successfully Published."
msgstr "Knowledgebase erfolgreich veröffentlicht."

#: helpers/wcfm-core-functions.php:852
msgid "Please insert atleast Topic Title before submit."
msgstr "Bitte geben Sie vor dem Absenden den Titel des Themas ein."

#: helpers/wcfm-core-functions.php:853
msgid "Topic Successfully Saved."
msgstr "Thema erfolgreich gespeichert."

#: helpers/wcfm-core-functions.php:854
msgid "Topic Successfully Published."
msgstr "Thema erfolgreich veröffentlicht."

#: helpers/wcfm-core-functions.php:866
msgid "Please write something before submit."
msgstr "Bitte schreiben Sie etwas vor dem Abschicken."

#: helpers/wcfm-core-functions.php:867
msgid "Reply send failed, try again."
msgstr "Antwort senden fehlgeschlagen, versuchen Sie es erneut."

#: helpers/wcfm-core-functions.php:868
msgid "Reply Successfully Send."
msgstr "Antwort erfolgreich gesendet."

#: helpers/wcfm-core-functions.php:880
msgid "Name is required."
msgstr "Name erforderlich."

#: helpers/wcfm-core-functions.php:881
msgid "Email is required."
msgstr "Gültige E-Mail erforderlich."

#: helpers/wcfm-core-functions.php:882
msgid "Please insert your enquiry before submit."
msgstr "Bitte geben Sie Ihre Anfrage vor dem Absenden ein."

#: helpers/wcfm-core-functions.php:883
msgid "Please insert your reply before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:884
msgid "Your enquiry successfully sent."
msgstr "Ihre Anfrage wurde erfolgreich gesendet."

#: helpers/wcfm-core-functions.php:885
msgid "Enquiry reply successfully published."
msgstr "Anfrageantwort erfolgreich veröffentlicht."

#: helpers/wcfm-core-functions.php:886
msgid "Your reply successfully sent."
msgstr ""

#: helpers/wcfm-core-functions.php:898
msgid "Please insert Username before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:899
msgid "Please insert Email before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:900
msgid "Please insert Store Name before submit."
msgstr ""

#: helpers/wcfm-core-functions.php:901 helpers/wcfm-core-functions.php:918
msgid "This Username already exists."
msgstr "Dieser Benutzername existiert schon."

#: helpers/wcfm-core-functions.php:902 helpers/wcfm-core-functions.php:919
msgid "This Email already exists."
msgstr "Diese E-Mail existiert bereits."

#: helpers/wcfm-core-functions.php:903
msgid "Vendor Saving Failed."
msgstr ""

#: helpers/wcfm-core-functions.php:904
msgid "Vendor Successfully Saved."
msgstr ""

#: helpers/wcfm-core-functions.php:916
msgid "Please insert Customer Username before submit."
msgstr "Bitte geben Sie den Benutzernamen vor dem Absenden ein."

#: helpers/wcfm-core-functions.php:917
msgid "Please insert Customer Email before submit."
msgstr "Bitte fügen vor dem senden eine Kunden E-Mail Adresse ein."

#: helpers/wcfm-core-functions.php:920
msgid "Customer Saving Failed."
msgstr "Kundenspeichern fehlgeschlagen."

#: helpers/wcfm-core-functions.php:921
msgid "Customer Successfully Saved."
msgstr "Der Kunde wurde erfolgreich gespeichert."

#: helpers/wcfm-core-functions.php:933
msgid "Are you sure and want to approve / publish this 'Product'?"
msgstr ""
"Sind Sie sicher und möchten dieses 'Produkt' genehmigen / veröffentlichen?"

#: helpers/wcfm-core-functions.php:934
msgid ""
"Are you sure and want to delete this 'Article'?\n"
"You can't undo this action ..."
msgstr ""
"Sind Sie sicher und möchten diesen 'Artikel' löschen?\n"
"Sie können diese Aktion nicht rückgängig machen ..."

#: helpers/wcfm-core-functions.php:935
msgid ""
"Are you sure and want to delete this 'Product'?\n"
"You can't undo this action ..."
msgstr ""
"Sind Sie sicher und möchten dieses 'Produkt\" löschen?\n"
"Sie können diese Aktion nicht rückgängig machen ..."

#: helpers/wcfm-core-functions.php:936
msgid ""
"Are you sure and want to delete this 'Message'?\n"
"You can't undo this action ..."
msgstr ""
"Sind Sie sicher und möchten diese 'Nachricht\" löschen?\n"
"Sie können diese Aktion nicht rückgängig machen ..."

#: helpers/wcfm-core-functions.php:937
msgid ""
"Are you sure and want to delete this 'Order'?\n"
"You can't undo this action ..."
msgstr ""
"Sind Sie sicher und möchten diese 'Bestellung' löschen?\n"
"Sie können diese Aktion nicht rückgängig machen ..."

#: helpers/wcfm-core-functions.php:938
msgid ""
"Are you sure and want to delete this 'Enquiry'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:939
msgid ""
"Are you sure and want to delete this 'Support Ticket'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:940
msgid ""
"Are you sure and want to delete this 'Follower'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:941
msgid ""
"Are you sure and want to delete this 'Following'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:942
msgid "Are you sure and want to 'Mark as Complete' this Order?"
msgstr ""
"Sind Sie sicher und möchten diese Bestellung als \"Vollständig\" "
"kennzeichnen?"

#: helpers/wcfm-core-functions.php:943
msgid "Are you sure and want to 'Mark as Confirmed' this Booking?"
msgstr "Sind Sie sicher und möchten diese Buchung als bestätigt markieren?"

#: helpers/wcfm-core-functions.php:944
msgid "Are you sure and want to 'Mark as Complete' this Appointment?"
msgstr ""
"Sind Sie sicher und möchten diesen Termin als 'Als abgeschlossen markieren' "
"markieren?"

#: helpers/wcfm-core-functions.php:946
msgid "Select all"
msgstr "Wählen Sie Alle"

#: helpers/wcfm-core-functions.php:947
msgid "Select none"
msgstr "Nichts ausgewählt"

#: helpers/wcfm-core-functions.php:948
msgid "Any"
msgstr "Irgendein"

#: helpers/wcfm-core-functions.php:949
msgid "Enter a name for the new attribute term:"
msgstr "Geben Sie einen Namen für die neue Eigenschaft ein:"

#: helpers/wcfm-core-functions.php:950
msgid ""
"Please upgrade your WC Frontend Manager to Ultimate version and avail this "
"feature."
msgstr ""
"Bitte aktualisieren Sie Ihren WC Frontend Manager auf die Ultimate Version "
"und nutzen Sie diese Funktion."

#: helpers/wcfm-core-functions.php:951
msgid ""
"Install WC Frontend Manager Ultimate and WooCommerce PDF Invoices & Packing "
"Slips to avail this feature."
msgstr ""
"Installieren Sie WC Frontend Manager Ultimate und WooCommerce PDF-Rechnungen "
"& Lieferscheine, um diese Funktion in Anspruch zu nehmen."

#: helpers/wcfm-core-functions.php:952
msgid "Please select some element first!!"
msgstr "Bitte wählen Sie zuerst ein Element aus !!"

#: helpers/wcfm-core-functions.php:953
msgid ""
"Are you sure and want to do this?\n"
"You can't undo this action ..."
msgstr ""
"Bist du sicher, dass du das tun willst?\n"
" Diese Aktion kann nicht mehr Rückgängig gemacht werden."

#: helpers/wcfm-core-functions.php:954
msgid "Are you sure and want to do this?"
msgstr "Bist du sicher, dass du das tun willst?"

#: helpers/wcfm-core-functions.php:955
#: includes/libs/php/class-wcfm-fields.php:650
msgid "Everywhere Else"
msgstr "Überall sonst"

#: helpers/wcfm-core-functions.php:956 views/profile/wcfm-view-profile.php:223
#: includes/libs/php/class-wcfm-fields.php:82
#: includes/libs/php/class-wcfm-fields.php:84
#: includes/libs/php/class-wcfm-fields.php:87
#: includes/libs/php/class-wcfm-fields.php:148
#: includes/libs/php/class-wcfm-fields.php:219
#: includes/libs/php/class-wcfm-fields.php:261
#: includes/libs/php/class-wcfm-fields.php:327
#: includes/libs/php/class-wcfm-fields.php:389
#: includes/libs/php/class-wcfm-fields.php:449
#: includes/libs/php/class-wcfm-fields.php:554
#: includes/libs/php/class-wcfm-fields.php:632
#: includes/libs/php/class-wcfm-fields.php:702
#: includes/libs/php/class-wcfm-fields.php:767
#: includes/libs/php/class-wcfm-fields.php:770
#: includes/libs/php/class-wcfm-fields.php:833
#: includes/libs/php/class-wcfm-fields.php:882
#: includes/libs/php/class-wcfm-fields.php:976
msgid "This field is required."
msgstr "Dieses Feld ist erforderlich."

#: helpers/wcfm-core-functions.php:957
msgid "Choose "
msgstr "Wähle "

#: helpers/wcfm-core-functions.php:958
msgid "Search for a attribute ..."
msgstr "Suche nach einem Attribut ..."

#: helpers/wcfm-core-functions.php:959
msgid "Search for a product ..."
msgstr "Suche nach einem Produkt..."

#: helpers/wcfm-core-functions.php:960
msgid "Choose Categoies ..."
msgstr "Kategorie wählen..."

#: helpers/wcfm-core-functions.php:961
msgid "Choose Listings ..."
msgstr ""

#: helpers/wcfm-core-functions.php:963
msgid "No categories"
msgstr "Keine Kategorien"

#: helpers/wcfm-core-functions.php:964
msgid "Searching ..."
msgstr ""

#: helpers/wcfm-core-functions.php:965
msgid "No matching result found."
msgstr ""

#: helpers/wcfm-core-functions.php:966
msgid "Loading ..."
msgstr ""

#: helpers/wcfm-core-functions.php:967
msgid "Minimum input character "
msgstr ""

#: helpers/wcfm-core-functions.php:970
msgid "Add New Block"
msgstr ""

#: helpers/wcfm-core-functions.php:971
msgid "Remove Block"
msgstr ""

#: helpers/wcfm-core-functions.php:972
msgid "Toggle Block"
msgstr ""

#: helpers/wcfm-core-functions.php:973
msgid "Drag to re-arrange blocks"
msgstr ""

#: helpers/wcfm-core-functions.php:974
msgid "Please login to the site first!"
msgstr ""

#: helpers/wcfm-core-functions.php:975
msgid "Please select a shipping method"
msgstr ""

#: helpers/wcfm-core-functions.php:976
msgid "Shipping method not found"
msgstr ""

#: helpers/wcfm-core-functions.php:977
msgid "Shipping zone not found"
msgstr ""

#: helpers/wcfm-core-functions.php:978
msgid ""
"Are you sure you want to delete this 'Shipping Method'?\n"
"You can't undo this action ..."
msgstr ""

#: helpers/wcfm-core-functions.php:990
#: views/vendors/wcfm-view-vendors-manage.php:410
msgid "Direct Message"
msgstr "Direktnachricht"

#: helpers/wcfm-core-functions.php:991
msgid "Approve Product"
msgstr "Produkt genehmigen"

#: helpers/wcfm-core-functions.php:992
msgid "Status Updated"
msgstr ""

#: helpers/wcfm-core-functions.php:993
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:23
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:30
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:25
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:32
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:34
msgid "Withdrawal Requests"
msgstr ""

#: helpers/wcfm-core-functions.php:994
#: views/capability/wcfm-view-capability.php:247
msgid "Refund Requests"
msgstr ""

#: helpers/wcfm-core-functions.php:997
msgid "New Order"
msgstr "Neue Bestellung"

#: helpers/wcfm-core-functions.php:1330
msgid "Tutorial"
msgstr ""

#: views/wcfm-view-header-panels.php:46 views/wcfm-view-header-panels.php:49
msgid "Toggle Menu"
msgstr "Menu umschalten"

#: views/wcfm-view-header-panels.php:61
#: includes/shortcodes/class-wcfm-shortcode-notification.php:51
msgid "Notification Board"
msgstr "Nachrichten Board"

#: views/wcfm-view-header-panels.php:65
#: includes/shortcodes/class-wcfm-shortcode-notification.php:55
#: views/enquiry/wcfm-view-enquiry.php:36
msgid "Enquiry Board"
msgstr "Anfragen Board"

#: views/wcfm-view-header-panels.php:69
#: includes/shortcodes/class-wcfm-shortcode-notification.php:59
#: views/notice/wcfm-view-notices.php:26
msgid "Notice Board"
msgstr "Notizen Board"

#: views/wcfm-view-header-panels.php:79 views/wcfm-view-menu.php:145
msgid "Logout"
msgstr "Ausloggen"

#: views/wcfm-view-menu.php:69 views/settings/wcfm-view-settings.php:340
msgid "Home"
msgstr "Home"

#: controllers/articles/wcfm-controller-articles-manage.php:37
#: controllers/coupons/wcfm-controller-coupons-manage.php:37
#: controllers/customers/wcfm-controller-customers-manage.php:37
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:34
#: controllers/knowledgebase/wcfm-controller-knowledgebase-manage.php:37
#: controllers/products-manager/wcfm-controller-products-manage.php:37
#: controllers/profile/wcfm-controller-profile.php:67
#: controllers/settings/wcfm-controller-dokan-settings.php:33
#: controllers/settings/wcfm-controller-settings.php:31
#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:40
#: controllers/settings/wcfm-controller-wcmarketplace-settings.php:33
#: controllers/settings/wcfm-controller-wcpvendors-settings.php:29
#: controllers/settings/wcfm-controller-wcvendors-settings.php:31
#: controllers/vendors/wcfm-controller-vendors-manage.php:29
#: controllers/vendors/wcfm-controller-vendors-new.php:48
#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:29
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:37
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:34
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:137
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:33
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:34
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:93
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse-actions.php:34
#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal-request.php:33
msgid "There has some error in submitted data."
msgstr "In den gesendeten Daten ist ein Fehler aufgetreten."

#: controllers/articles/wcfm-controller-articles.php:138
#: controllers/listings/wcfm-controller-listings.php:24
#: controllers/products/wcfm-controller-products.php:25
#: controllers/products/wcfm-controller-products.php:222
#: views/articles/wcfm-view-articles-manage.php:117
#: views/articles/wcfm-view-articles.php:11
#: views/listings/wcfm-view-listings.php:37
#: views/products/wcfm-view-products.php:11
#: views/products-manager/wcfm-view-products-manage.php:415
msgid "Published"
msgstr "Veröffentlicht"

#: controllers/articles/wcfm-controller-articles.php:158
#: controllers/knowledgebase/wcfm-controller-knowledgebase.php:103
#: controllers/listings/wcfm-controller-listings.php:127
#: controllers/notice/wcfm-controller-notices.php:77
#: controllers/products/wcfm-controller-products.php:327
#: controllers/reports/wcfm-controller-reports-out-of-stock.php:105
#: views/articles/wcfm-view-articles-manage.php:387
#: views/enquiry/wcfm-view-my-account-enquiry.php:60
#: views/notice/wcfm-view-notice-manage.php:59
#: views/products-manager/wcfm-view-products-manage.php:798
#: views/products-popup/wcfm-view-product-popup.php:302
msgid "View"
msgstr "Anzeigen"

#: controllers/capability/wcfm-controller-capability.php:39
msgid "Capability saved successfully"
msgstr "Erfolgreich gespeichert"

#: controllers/customers/wcfm-controller-customers-details.php:96
#: controllers/customers/wcfm-controller-customers-details.php:103
#: controllers/orders/wcfm-controller-dokan-orders.php:161
#: controllers/orders/wcfm-controller-dokan-orders.php:168
#: controllers/orders/wcfm-controller-orders.php:127
#: controllers/orders/wcfm-controller-orders.php:134
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:185
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:192
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:184
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:191
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:154
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:161
#: controllers/orders/wcfm-controller-wcvendors-orders.php:182
#: controllers/orders/wcfm-controller-wcvendors-orders.php:189
#, php-format
msgctxt "full name"
msgid "%1$s %2$s"
msgstr "%1$s %2$s"

#: controllers/customers/wcfm-controller-customers-details.php:107
#: controllers/customers/wcfm-controller-customers-details.php:113
#: controllers/orders/wcfm-controller-dokan-orders.php:172
#: controllers/orders/wcfm-controller-dokan-orders.php:178
#: controllers/orders/wcfm-controller-orders.php:138
#: controllers/orders/wcfm-controller-orders.php:144
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:196
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:202
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:195
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:201
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:165
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:171
#: controllers/orders/wcfm-controller-wcvendors-orders.php:193
#: controllers/orders/wcfm-controller-wcvendors-orders.php:199
msgid "Guest"
msgstr "Gast"

#: controllers/customers/wcfm-controller-customers-details.php:117
#: controllers/customers/wcfm-controller-customers-details.php:119
#: controllers/orders/wcfm-controller-dokan-orders.php:182
#: controllers/orders/wcfm-controller-dokan-orders.php:184
#: controllers/orders/wcfm-controller-orders.php:148
#: controllers/orders/wcfm-controller-orders.php:150
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:206
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:208
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:205
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:207
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:175
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:177
#: controllers/orders/wcfm-controller-wcvendors-orders.php:203
#: controllers/orders/wcfm-controller-wcvendors-orders.php:205
#: views/enquiry/wcfm-view-enquiry-tab.php:64
msgid "by"
msgstr "von"

#: controllers/customers/wcfm-controller-customers-details.php:140
#: controllers/orders/wcfm-controller-dokan-orders.php:205
#: controllers/orders/wcfm-controller-orders.php:171
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:244
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:237
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:233
#: controllers/orders/wcfm-controller-wcvendors-orders.php:220
#, php-format
msgid "%d item"
msgid_plural "%d items"
msgstr[0] "%d Produkt"
msgstr[1] "%d Produkt"

#: controllers/customers/wcfm-controller-customers-details.php:147
#: controllers/orders/wcfm-controller-dokan-orders.php:230
#: controllers/orders/wcfm-controller-orders.php:196
#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:89
#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:139
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:132
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse.php:104
msgid "Via"
msgstr "Via"

#: controllers/customers/wcfm-controller-customers-details.php:159
#: controllers/orders/wcfm-controller-dokan-orders.php:249
#: controllers/orders/wcfm-controller-orders.php:227
#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:321
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:303
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:285
#: controllers/orders/wcfm-controller-wcvendors-orders.php:334
msgid "Mark as Complete"
msgstr "Als erledigt markieren"

#: controllers/customers/wcfm-controller-customers-details.php:167
#: controllers/customers/wcfm-controller-customers-details.php:171
#: controllers/orders/wcfm-controller-orders.php:236
#: views/capability/wcfm-view-capability.php:402
#: views/orders/wcfm-view-orders-details.php:147
#: views/orders/wcfm-view-orders-details.php:149
msgid "PDF Invoice"
msgstr "PDF Rechnung"

#: controllers/customers/wcfm-controller-customers-details.php:168
#: views/capability/wcfm-view-capability.php:408
msgid "PDF Packing Slip"
msgstr "PDF-Lieferschein"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:21
msgid "Paid & Confirmed"
msgstr "Bezahlt & Bestätigt"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/customers/wcfm-controller-customers-details.php:397
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:23
msgid "Pending Confirmation"
msgstr "Bestätigung ausstehend"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/customers/wcfm-controller-customers-details.php:397
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
msgid "Un-paid"
msgstr "Nicht bezahlt"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/customers/wcfm-controller-customers-details.php:397
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:20
msgid "Complete"
msgstr "Komplett"

#: controllers/customers/wcfm-controller-customers-details.php:210
#: controllers/customers/wcfm-controller-customers-details.php:397
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:23
#: views/wc_bookings/wcfm-view-wcbookings.php:22
msgid "Confirmed"
msgstr "Bestätigt"

#: controllers/customers/wcfm-controller-customers-details.php:275
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:114
msgid "#"
msgstr "#"

#: controllers/customers/wcfm-controller-customers-details.php:289
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:128
#, php-format
msgctxt "Guest string with name from booking order in brackets"
msgid "Guest (%s)"
msgstr "Gast (%s)"

#: controllers/customers/wcfm-controller-customers-details.php:357
#: controllers/customers/wcfm-controller-customers-details.php:531
#: controllers/wc_bookings/wcfm-controller-wcbookings.php:203
msgid "Mark as Confirmed"
msgstr "Als bestätigt markieren"

#: controllers/customers/wcfm-controller-customers-details.php:400
msgid "Partial Paid"
msgstr ""

#: controllers/customers/wcfm-controller-customers-details.php:476
#, php-format
msgctxt "Guest string with name from appointment order in brackets"
msgid "Guest (%s)"
msgstr ""

#: controllers/customers/wcfm-controller-customers-manage.php:127
#: controllers/vendors/wcfm-controller-vendors-new.php:155
msgid "Dear"
msgstr "Sehr geehrter"

#: controllers/customers/wcfm-controller-customers-manage.php:129
#: controllers/vendors/wcfm-controller-vendors-new.php:157
msgid ""
"Your account has been created as {user_role}. Follow the bellow details to "
"log into the system"
msgstr ""
"Ihr Konto wurde als {user_role} erstellt. Befolgen Sie die folgenden Details,"
" um sich beim System anzumelden"

#: controllers/customers/wcfm-controller-customers-manage.php:131
msgid "Site"
msgstr "Seite"

#: controllers/customers/wcfm-controller-customers-manage.php:133
msgid "Login"
msgstr "Login"

#: controllers/customers/wcfm-controller-customers-manage.php:135
#: controllers/vendors/wcfm-controller-vendors-new.php:165
#: views/profile/wcfm-view-profile.php:233
msgid "Password"
msgstr "Passwort"

#: controllers/customers/wcfm-controller-customers-manage.php:145
#: controllers/vendors/wcfm-controller-vendors-new.php:176
msgid "New Account"
msgstr "Neuer Account"

#: controllers/customers/wcfm-controller-customers-manage.php:156
#, php-format
msgid "A new customer <b>%s</b> added to the store by <b>%s</b>"
msgstr "Ein neuer Kunde <b>%s</b> wurde dem Shop durch <b>%s </b> hinzugefügt"

#: controllers/customers/wcfm-controller-customers.php:143
#: views/customers/wcfm-view-customers-manage.php:94
msgid "Manage Customer"
msgstr "Kunden verwalten"

#: controllers/customers/wcfm-controller-customers.php:145
#: views/capability/wcfm-view-capability.php:420
#: views/customers/wcfm-view-customers-details.php:103
#: views/customers/wcfm-view-customers-manage.php:101
msgid "Edit Customer"
msgstr "Kunden bearbeiten"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:100
msgid "Reply for your Inquiry"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:103
#, php-format
msgid ""
"We recently have a enquiry from you regarding \"%s\". Please check below for "
"our input for the same: "
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:117
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:183
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:194
msgid "Inquiry Reply"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:124
#, php-format
msgid "New reply posted for Inquiry <b>%s</b>"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:186
msgid ""
"You have received reply for your \"{product_title}\" inquiry. Please check "
"below for the details: "
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:190
msgid "Check more details here"
msgstr "Überprüfen Sie mehr Details hier"

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:202
msgid "Reply to Inquiry"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-manage.php:207
#: controllers/enquiry/wcfm-controller-enquiry-manage.php:221
#, php-format
msgid "New reply received for Inquiry <b>%s</b>"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:46
msgid "Captcha failed, please try again."
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:123
#: views/enquiry/wcfm-view-enquiry-manage.php:97
#: views/enquiry/wcfm-view-enquiry.php:82
#: views/enquiry/wcfm-view-enquiry.php:94
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:85
#: views/orders/wcfm-view-orders.php:94 views/orders/wcfm-view-orders.php:114
#: views/vendors/wcfm-view-vendors.php:73
#: views/vendors/wcfm-view-vendors.php:94
msgid "Additional Info"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:135
msgid "New enquiry for"
msgstr "Neue Anfrage für"

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:138
#, php-format
msgid "You have a recent enquiry for %s."
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:143
#, php-format
msgid "To respond to this Enquiry, please %sClick Here%s"
msgstr ""

#: controllers/enquiry/wcfm-controller-enquiry-tab.php:168
#: controllers/enquiry/wcfm-controller-enquiry-tab.php:186
#, php-format
msgid "New Inquiry <b>%s</b> received for <b>%s</b>"
msgstr ""

#: controllers/listings/wcfm-controller-listings.php:26
#: views/listings/wcfm-view-listings.php:39
msgid "Expired"
msgstr "Abgelaufen"

#: controllers/listings/wcfm-controller-listings.php:27
#: views/articles/wcfm-view-articles-manage.php:128
#: views/articles/wcfm-view-articles-manage.php:381
#: views/listings/wcfm-view-listings.php:40
#: views/products-manager/wcfm-view-products-manage.php:426
#: views/products-manager/wcfm-view-products-manage.php:792
#: views/products-popup/wcfm-view-product-popup.php:296
msgid "Preview"
msgstr "Vorschau"

#: controllers/listings/wcfm-controller-listings.php:28
msgid "Pending Payment"
msgstr ""

#: controllers/messages/wcfm-controller-message-sent.php:47
msgid "Message sent successfully"
msgstr "Nachricht erfolgreich gesendet"

#: controllers/messages/wcfm-controller-messages.php:153
msgid "System"
msgstr "System"

#: controllers/messages/wcfm-controller-messages.php:163
#: controllers/messages/wcfm-controller-messages.php:174
#: controllers/messages/wcfm-controller-messages.php:190
#: controllers/messages/wcfm-controller-messages.php:201
msgid "You"
msgstr "Du"

#: controllers/messages/wcfm-controller-messages.php:213
#: controllers/messages/wcfm-controller-messages.php:215
msgid "Approve / Reject"
msgstr "Genehmigen / Ablehnen"

#: controllers/messages/wcfm-controller-messages.php:217
#: views/messages/wcfm-view-messages.php:100
msgid "Mark Read"
msgstr "Als gelesen markieren"

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:282
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:265
msgid "UNPAID"
msgstr "Unbezahlt"

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:286
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:269
#: controllers/orders/wcfm-controller-wcvendors-orders.php:298
msgid "PAID"
msgstr "BEZAHLT"

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:290
msgid "REQUESTED"
msgstr ""

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:294
msgid "CANCELLED"
msgstr ""

#: controllers/orders/wcfm-controller-wcfmmarketplace-orders.php:330
#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:312
#: controllers/orders/wcfm-controller-wcpvendors-orders.php:293
#: controllers/orders/wcfm-controller-wcvendors-orders.php:342
msgid "Mark Shipped"
msgstr "Als versendet markieren"

#: controllers/orders/wcfm-controller-wcmarketplace-orders.php:273
#: controllers/orders/wcfm-controller-wcvendors-orders.php:302
msgid "REVERSED"
msgstr "Storniert"

#: controllers/orders/wcfm-controller-wcvendors-orders.php:294
msgid "DUE"
msgstr "Fällig"

#: controllers/products/wcfm-controller-products.php:233
#: views/products-manager/wcfm-view-products-manage-tabs.php:27
#: views/products-manager/wcfm-view-products-manage-tabs.php:250
msgid "In stock"
msgstr "Auf Lager"

#: controllers/products/wcfm-controller-products.php:233
#: views/products-manager/wcfm-view-products-manage-tabs.php:27
#: views/products-manager/wcfm-view-products-manage-tabs.php:250
#: views/reports/wcfm-view-reports-menu.php:5
msgid "Out of stock"
msgstr "Ausverkauft"

#: controllers/products/wcfm-controller-products.php:233
#: views/products-manager/wcfm-view-products-manage-tabs.php:27
#: views/products-manager/wcfm-view-products-manage-tabs.php:250
msgid "On backorder"
msgstr "Auf Lieferrückstand"

#: controllers/products/wcfm-controller-products.php:252
#: views/capability/wcfm-view-capability.php:196
msgid "Grouped"
msgstr "Gruppiert"

#: controllers/products/wcfm-controller-products.php:254
msgid "External/Affiliate"
msgstr "Affiliate"

#: controllers/products/wcfm-controller-products.php:258
#: views/products/wcfm-view-products.php:161
#: views/products-manager/wcfm-view-products-manage-tabs.php:238
#: views/products-manager/wcfm-view-products-manage.php:461
#: views/products-popup/wcfm-view-product-popup.php:120
msgid "Virtual"
msgstr "Virtuell"

#: controllers/products/wcfm-controller-products.php:260
#: views/products/wcfm-view-products.php:157
#: views/products-manager/wcfm-view-products-manage-tabs.php:41
#: views/products-manager/wcfm-view-products-manage.php:462
#: views/products-popup/wcfm-view-product-popup.php:121
msgid "Downloadable"
msgstr "Herunterladbar"

#: controllers/products/wcfm-controller-products.php:262
#: views/capability/wcfm-view-capability.php:194
msgid "Simple"
msgstr "Einfach"

#: controllers/products/wcfm-controller-products.php:266
#: views/capability/wcfm-view-capability.php:195
msgid "Variable"
msgstr "Variabel"

#: controllers/products/wcfm-controller-products.php:268
msgid "Subscription"
msgstr "Abonnementen"

#: controllers/products/wcfm-controller-products.php:270
msgid "Variable Subscription"
msgstr "Variables Abonnement"

#: controllers/products/wcfm-controller-products.php:272
msgid "Listings Package"
msgstr ""

#: controllers/products/wcfm-controller-products.php:274
msgid "Resume Package"
msgstr "Lebenslauf-Paket"

#: controllers/products/wcfm-controller-products.php:280
msgid "Accommodation"
msgstr "Unterkunft"

#: controllers/products/wcfm-controller-products.php:282
#: views/customers/wcfm-view-customers.php:65
#: views/customers/wcfm-view-customers.php:80
msgid "Appointment"
msgstr "Termin"

#: controllers/products/wcfm-controller-products.php:284
msgid "Bundle"
msgstr "Bundle"

#: controllers/products/wcfm-controller-products.php:286
msgid "Composite"
msgstr "Komponenten"

#: controllers/products/wcfm-controller-products.php:288
msgid "Lottery"
msgstr "Lotterie"

#: controllers/products/wcfm-controller-products.php:322
msgid "Mark Approve / Publish"
msgstr "Als genehmigt/ veröffentlicht markieren"

#: controllers/products/wcfm-controller-products.php:335
msgid "No Featured"
msgstr "Nicht Hervorgehoben"

#: controllers/products/wcfm-controller-products.php:338
msgid "Mark Featured"
msgstr "Als hervorgehoben markieren"

#: controllers/products/wcfm-controller-products.php:343
msgid ""
"Featured Product: Upgrade your WCFM to WCFM Ultimate to avail this feature."
msgstr ""
"Hervorgehobenes Produkt: Diese Funktion setzt den Kauf von WCFM Ultimate "
"voraus."

#: controllers/products/wcfm-controller-products.php:352
msgid "Duplicate"
msgstr "Duplizieren"

#: controllers/products/wcfm-controller-products.php:355
msgid ""
"Duplicate Product: Upgrade your WCFM to WCFM Ultimate to avail this feature."
msgstr ""
"Produkt duplizieren: Diese Funktion setzt den Kauf von WCFM Ultimate voraus."

#: controllers/profile/wcfm-controller-profile.php:257
msgid "Email verification code invalid."
msgstr ""

#: controllers/profile/wcfm-controller-profile.php:267
#: controllers/vendors/wcfm-controller-vendors-manage.php:49
msgid "Profile saved successfully"
msgstr "Profil erfolgreich gespeichert"

#: controllers/settings/wcfm-controller-dokan-settings.php:93
#: controllers/settings/wcfm-controller-settings.php:232
#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:181
#: controllers/settings/wcfm-controller-wcmarketplace-settings.php:290
#: controllers/settings/wcfm-controller-wcpvendors-settings.php:80
#: controllers/settings/wcfm-controller-wcvendors-settings.php:122
msgid "Settings saved successfully"
msgstr "Einstellungen erfolgreich gespeichert"

#: controllers/settings/wcfm-controller-wcfmmarketplace-settings.php:105
#: controllers/settings/wcfm-controller-wcmarketplace-settings.php:94
msgid "Shop Slug already exists."
msgstr ""

#: controllers/settings/wcfm-controller-wcpvendors-settings.php:82
msgid "Settings failed to save"
msgstr "Einstellungen wurden nicht gespeichert"

#: controllers/vendors/wcfm-controller-vendors-manage.php:81
msgid "Badges saved successfully"
msgstr "Abzeichen erfolgreich gespeichert"

#: controllers/vendors/wcfm-controller-vendors-new.php:159
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:245
msgid "Store Name"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors-new.php:161
msgid "Click here ..."
msgstr ""

#: controllers/vendors/wcfm-controller-vendors-new.php:163
#: views/customers/wcfm-view-customers-manage.php:131
#: views/customers/wcfm-view-customers-manage.php:133
#: views/customers/wcfm-view-customers.php:60
#: views/customers/wcfm-view-customers.php:75
#: views/vendors/wcfm-view-vendors-new.php:90
msgid "Username"
msgstr "Benutzername"

#: controllers/vendors/wcfm-controller-vendors-new.php:183
#, php-format
msgid "A new vendor <b>%s</b> added ."
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:79
msgid "Store Off-line"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:84
msgid "Active Vendor"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:98
#: views/vendors/wcfm-view-vendors-manage.php:160
msgid "Email Verified"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:100
#: views/vendors/wcfm-view-vendors-manage.php:162
msgid "Email Verification Pending"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:131
msgid "Next payment on"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:148
#: controllers/vendors/wcfm-controller-vendors.php:153
#: controllers/vendors/wcfm-controller-vendors.php:161
msgid "Expiry on"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:161
msgid "Never Expire"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:199
#: views/vendors/wcfm-view-vendors-manage.php:304
msgid "Disable Vendor Account"
msgstr ""

#: controllers/vendors/wcfm-controller-vendors.php:201
#: views/vendors/wcfm-view-vendors-manage.php:306
msgid "Enable Vendor Account"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:63
msgid "Booking schedule updated"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:64
msgid "Booking schedule updated."
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:70
msgid "Booking schedule updated successfully"
msgstr ""

#: controllers/wc_bookings/wcfm-controller-wcbookings-schedule-manage.php:72
msgid "Booking schedule updated failed!"
msgstr ""

#: includes/reports/class-dokan-report-sales-by-date.php:573
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:220
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:205
#: includes/reports/class-wcpvendors-report-sales-by-date.php:199
#: includes/reports/class-wcvendors-report-sales-by-date.php:219
#, php-format
msgid "%s total earnings"
msgstr "%s Gesamteinnahmen"

#: includes/reports/class-dokan-report-sales-by-date.php:574
#: includes/reports/class-wcfm-report-analytics.php:127
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:221
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:206
#: includes/reports/class-wcvendors-report-sales-by-date.php:220
msgid ""
"This is the sum of the earned commission including shipping and taxes if "
"applicable."
msgstr ""
"Dies ist die Summe der verdienten Provisionen inklusive Versand und Steuern, "
"falls anwendbar."

#: includes/reports/class-dokan-report-sales-by-date.php:580
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:229
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:214
#: includes/reports/class-wcpvendors-report-sales-by-date.php:208
#: includes/reports/class-wcvendors-report-sales-by-date.php:227
#, php-format
msgid "%s total withdrawal"
msgstr "%s gesamt auszahlung"

#: includes/reports/class-dokan-report-sales-by-date.php:581
msgid ""
"This is the sum of the commission withdraw including shipping and taxes if "
"applicable."
msgstr ""
"Dies ist die Summe der Provisionen inklusive Versand und Steuern, falls "
"anwendbar."

#: includes/reports/class-dokan-report-sales-by-date.php:805
#: includes/reports/class-wcfm-report-sales-by-date.php:712
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:531
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:527
#: includes/reports/class-wcpvendors-report-sales-by-date.php:430
#: includes/reports/class-wcvendors-report-sales-by-date.php:541
#: views/customers/wcfm-view-customers-details.php:271
#: views/customers/wcfm-view-customers-details.php:281
#: views/orders/wcfm-view-orders.php:86 views/orders/wcfm-view-orders.php:106
#: views/vendors/wcfm-view-vendors.php:65
#: views/vendors/wcfm-view-vendors.php:86
msgid "Gross Sales"
msgstr "Bruttoumsatz"

#: includes/reports/class-dokan-report-sales-by-date.php:840
#: includes/reports/class-wcfm-report-sales-by-date.php:728
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:580
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:566
#: includes/reports/class-wcpvendors-report-sales-by-date.php:469
#: includes/reports/class-wcvendors-report-sales-by-date.php:591
msgid "Order Counts"
msgstr "Anzahl der Bestellungen"

#: includes/reports/class-dokan-report-sales-by-date.php:848
#: includes/reports/class-wcfm-report-sales-by-date.php:736
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:588
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:574
#: includes/reports/class-wcpvendors-report-sales-by-date.php:477
#: includes/reports/class-wcvendors-report-sales-by-date.php:599
msgid "Order Item Counts"
msgstr "Anzahl der bestellten Artikel"

#: includes/reports/class-dokan-report-sales-by-date.php:856
#: includes/reports/class-wcfm-report-sales-by-date.php:752
msgid "Coupon Amounts"
msgstr "Gutscheinwert"

#: includes/reports/class-dokan-report-sales-by-date.php:864
#: includes/reports/class-wcfm-report-sales-by-date.php:760
msgid "Refund Amounts"
msgstr "Rückerstattungsbeträge"

#: includes/reports/class-dokan-report-sales-by-date.php:875
#: includes/reports/class-wcfm-report-sales-by-date.php:771
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:599
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:585
#: includes/reports/class-wcpvendors-report-sales-by-date.php:488
#: includes/reports/class-wcvendors-report-sales-by-date.php:610
msgid "Sales Report by Date"
msgstr "Verkaufsbericht - nach Datum"

#: includes/reports/class-dokan-report-sales-by-date.php:893
#: includes/reports/class-wcfm-report-analytics.php:170
#: includes/reports/class-wcfm-report-sales-by-date.php:789
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:319
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:617
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:294
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:603
#: includes/reports/class-wcpvendors-report-sales-by-date.php:506
#: includes/reports/class-wcvendors-report-sales-by-date.php:316
#: includes/reports/class-wcvendors-report-sales-by-date.php:628
#: views/articles/wcfm-view-articles.php:113
#: views/articles/wcfm-view-articles.php:124
#: views/customers/wcfm-view-customers-details.php:272
#: views/customers/wcfm-view-customers-details.php:282
#: views/enquiry/wcfm-view-enquiry.php:84
#: views/enquiry/wcfm-view-enquiry.php:96
#: views/messages/wcfm-view-messages.php:126
#: views/messages/wcfm-view-messages.php:139
#: views/orders/wcfm-view-orders.php:95 views/orders/wcfm-view-orders.php:115
#: views/products/wcfm-view-products.php:205
#: views/products/wcfm-view-products.php:228
#: views/withdrawal/dokan/wcfm-view-payments.php:71
#: views/withdrawal/dokan/wcfm-view-payments.php:80
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:57
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:67
#: views/withdrawal/wcfm/wcfm-view-payments.php:77
#: views/withdrawal/wcfm/wcfm-view-payments.php:91
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:77
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:93
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:71
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:83
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:80
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:93
#: views/withdrawal/wcmp/wcfm-view-payments.php:69
#: views/withdrawal/wcmp/wcfm-view-payments.php:80
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:61
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:70
msgid "Date"
msgstr "Datum"

#: includes/reports/class-dokan-report-sales-by-date.php:902
#: includes/reports/class-wcfm-report-sales-by-date.php:798
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:626
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:612
#: includes/reports/class-wcpvendors-report-sales-by-date.php:515
#: includes/reports/class-wcvendors-report-sales-by-date.php:637
#: views/coupons/wcfm-view-coupons.php:57
#: views/coupons/wcfm-view-coupons.php:67
#: views/withdrawal/dokan/wcfm-view-payments.php:68
#: views/withdrawal/dokan/wcfm-view-payments.php:77
#: views/withdrawal/wcfm/wcfm-view-payments.php:72
#: views/withdrawal/wcfm/wcfm-view-payments.php:86
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:72
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:88
msgid "Amount"
msgstr "Betrag"

#: includes/reports/class-wcfm-report-analytics.php:117
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:179
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:164
#: includes/reports/class-wcvendors-report-sales-by-date.php:201
#, php-format
msgid "%s average daily sales"
msgstr "%s durchschnittlicher Tagesumsatz"

#: includes/reports/class-wcfm-report-analytics.php:121
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:183
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:168
#: includes/reports/class-wcvendors-report-sales-by-date.php:205
#, php-format
msgid "%s average monthly sales"
msgstr "%s durchschnittlichen Monatsumsatz"

#: includes/reports/class-wcfm-report-analytics.php:126
#, php-format
msgid "%s total earned commission"
msgstr "%s Gesamteinnahmen Provisionen"

#: includes/reports/class-wcfm-report-analytics.php:142
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:284
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:260
#: includes/reports/class-wcvendors-report-sales-by-date.php:282
#: views/enquiry/wcfm-view-enquiry.php:26
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:30
#: views/reports/wcfm-view-reports-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:30
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:30
msgid "Last Month"
msgstr "Letzter Monat"

#: includes/reports/class-wcfm-report-analytics.php:144
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:286
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:262
#: includes/reports/class-wcvendors-report-sales-by-date.php:284
#: views/enquiry/wcfm-view-enquiry.php:24
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:32
#: views/reports/wcfm-view-reports-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:32
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:32
msgid "Last 7 Days"
msgstr "Letzte 7 Tage"

#: includes/reports/class-wcfm-report-analytics.php:176
#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:325
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:300
#: includes/reports/class-wcvendors-report-sales-by-date.php:322
#: views/capability/wcfm-view-capability.php:396
msgid "Export CSV"
msgstr "CSV exportieren"

#: includes/reports/class-wcfm-report-analytics.php:260
msgid "Daily Views"
msgstr "Tägliche Aufrufe"

#: includes/reports/class-wcfm-report-analytics.php:270
#: views/dashboard/wcfm-view-dashboard.php:204
#: views/dashboard/wcfm-view-dokan-dashboard.php:208
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:211
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:229
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:232
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:215
msgid "Store Analytics"
msgstr "Shop Analysen"

#: includes/reports/class-wcfm-report-sales-by-date.php:744
msgid "Net Sales"
msgstr "Net Sales"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:189
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:174
#: includes/reports/class-wcvendors-report-sales-by-date.php:211
#, php-format
msgid "%s gross sales in this period"
msgstr "%s Bruttoumsatz in diesem Zeitraum"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:190
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:175
#: includes/reports/class-wcvendors-report-sales-by-date.php:212
msgid ""
"This is the sum of the order totals after any refunds and including shipping "
"and taxes."
msgstr ""
"Entspricht der Summe aller Anfragen (ohne Rückerstattungen) mit "
"Versandkosten und Steuern."

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:202
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:187
#, php-format
msgid "%s total admin fees"
msgstr "%s gesamt admin gebühren"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:203
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:188
msgid ""
"This is the sum of the admin fees including shipping and taxes if applicable."
msgstr ""
"Dies ist die Summe der Verwaltungsgebühren einschließlich Versand und ggf. "
"Steuern."

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:211
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:196
#, php-format
msgid "%s total paid fees"
msgstr "%s gesamt bezahlte gebühren"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:212
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:197
msgid ""
"This is the sum of the admin fees paid including shipping and taxes if "
"applicable."
msgstr ""
"Dies ist die Summe der ausgezahlten Provisionen inklusive Versand und "
"Steuern, falls anwendbar."

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:230
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:215
#: includes/reports/class-wcvendors-report-sales-by-date.php:228
msgid ""
"This is the sum of the commission paid including shipping and taxes if "
"applicable."
msgstr ""
"Dies ist die Summe der bezahlten Provision inklusive Versand und ggf. "
"Steuern."

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:239
#, php-format
msgid "%s total refund"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:240
msgid "This is the sum of the refunds and partial refunds."
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:255
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:231
#: includes/reports/class-wcvendors-report-sales-by-date.php:243
#, php-format
msgid "%s orders placed"
msgstr "%s Bestellungen platziert"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:261
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:237
#: includes/reports/class-wcvendors-report-sales-by-date.php:249
#, php-format
msgid "%s items purchased"
msgstr "gekaufte Produkt %s"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:268
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:244
#: includes/reports/class-wcvendors-report-sales-by-date.php:257
#: includes/reports/class-wcvendors-report-sales-by-date.php:265
#, php-format
msgid "%s charged for shipping"
msgstr "%s berechnet für den Versand"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:541
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:537
msgid "Admin Fees"
msgstr "Admin Gebühren"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:551
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:547
#: views/vendors/wcfm-view-vendors.php:68
#: views/vendors/wcfm-view-vendors.php:89
msgid "Paid Fees"
msgstr "Bezahlte Gebühren"

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:561
msgid "Refunds"
msgstr ""

#: includes/reports/class-wcfmmarketplace-report-sales-by-date.php:571
#: includes/reports/class-wcmarketplace-report-sales-by-date.php:557
#: includes/reports/class-wcpvendors-report-sales-by-date.php:460
#: includes/reports/class-wcvendors-report-sales-by-date.php:570
#: includes/reports/class-wcvendors-report-sales-by-date.php:581
msgid "Shipping Amounts"
msgstr "Liefermengen"

#: includes/shortcodes/class-wcfm-shortcode-follow.php:33
msgid "Follow Me"
msgstr ""

#: views/articles/wcfm-view-articles-manage.php:105
msgid "Manage Article"
msgstr "Beiträge verwalten"

#: views/articles/wcfm-view-articles-manage.php:113
msgid "Edit Article"
msgstr "Beitrag bearbeiten"

#: views/articles/wcfm-view-articles-manage.php:113
msgid "Add Article"
msgstr "Beitrag Hinzufügen"

#: views/articles/wcfm-view-articles-manage.php:122
#: views/articles/wcfm-view-articles.php:112
#: views/articles/wcfm-view-articles.php:123
#: views/listings/wcfm-view-listings.php:107
#: views/listings/wcfm-view-listings.php:119
#: views/products/wcfm-view-products.php:204
#: views/products/wcfm-view-products.php:227
#: views/products-manager/wcfm-view-products-manage.php:420
msgid "Views"
msgstr "Angesehen"

#: views/articles/wcfm-view-articles-manage.php:141
#: views/articles/wcfm-view-articles.php:65
msgid "Add New Article"
msgstr "Neuer Beitrag"

#: views/articles/wcfm-view-articles-manage.php:159
msgid "Article Title"
msgstr "Beitrag Titel"

#: views/articles/wcfm-view-articles-manage.php:167
#: views/articles/wcfm-view-articles-manage.php:262
#: views/articles/wcfm-view-articles-manage.php:262
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:89
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:89
#: views/products-manager/wcfm-view-products-manage.php:482
#: views/products-manager/wcfm-view-products-manage.php:482
#: views/products-manager/wcfm-view-products-manage.php:604
#: views/products-manager/wcfm-view-products-manage.php:604
#: views/products-popup/wcfm-view-product-popup.php:149
#: views/products-popup/wcfm-view-product-popup.php:149
#: views/settings/wcfm-view-settings.php:512
#: views/settings/wcfm-view-settings.php:512
msgid "Categories"
msgstr "Kategorien"

#: views/articles/wcfm-view-articles-manage.php:212
#: views/articles/wcfm-view-articles-manage.php:310
#: views/products-manager/wcfm-view-products-manage.php:531
#: views/products-manager/wcfm-view-products-manage.php:713
#: views/products-popup/wcfm-view-product-popup.php:198
msgid "Tags"
msgstr "Schlagworte"

#: views/articles/wcfm-view-articles-manage.php:212
#: views/articles/wcfm-view-articles-manage.php:310
msgid "Separate Article Tags with commas"
msgstr "Trennen Sie Beitrag-Tags durch Kommas"

#: views/articles/wcfm-view-articles-manage.php:212
#: views/articles/wcfm-view-articles-manage.php:310
#: views/products-manager/wcfm-view-products-manage.php:531
#: views/products-manager/wcfm-view-products-manage.php:713
msgid "Choose from the most used tags"
msgstr "Wählen Sie aus den am häufigsten verwendeten Tags"

#: views/articles/wcfm-view-articles-manage.php:224
#: views/articles/wcfm-view-articles-manage.php:322
#: views/products-manager/wcfm-view-products-manage.php:544
#: views/products-manager/wcfm-view-products-manage.php:726
#: views/products-popup/wcfm-view-product-popup.php:211
msgid " with commas"
msgstr " mit Kommata"

#: views/articles/wcfm-view-articles-manage.php:239
#: views/articles/wcfm-view-articles-manage.php:343
#: views/products-manager/wcfm-view-products-manage.php:566
#: views/products-manager/wcfm-view-products-manage.php:754
#: views/products-popup/wcfm-view-product-popup.php:264
msgid "Short Description"
msgstr "Kurzbeschreibung"

#: views/articles/wcfm-view-articles-manage.php:240
#: views/articles/wcfm-view-articles-manage.php:344
#: views/coupons/wcfm-view-coupons-manage.php:93
#: views/products-manager/wcfm-view-products-manage.php:567
#: views/products-manager/wcfm-view-products-manage.php:755
#: views/products-popup/wcfm-view-product-popup.php:265
msgid "Description"
msgstr "Beschreibung"

#: views/articles/wcfm-view-articles-manage.php:369
#: views/articles/wcfm-view-articles-manage.php:371
#: views/coupons/wcfm-view-coupons-manage.php:120
#: views/coupons/wcfm-view-coupons-manage.php:122
#: views/customers/wcfm-view-customers-manage.php:207
#: views/enquiry/wcfm-view-enquiry-form.php:151
#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:132
#: views/notice/wcfm-view-notice-manage.php:95
#: views/products-manager/wcfm-view-products-manage.php:780
#: views/products-manager/wcfm-view-products-manage.php:782
#: views/products-popup/wcfm-view-product-popup.php:284
#: views/products-popup/wcfm-view-product-popup.php:286
#: views/vendors/wcfm-view-vendors-new.php:156
msgid "Submit"
msgstr "Senden"

#: views/articles/wcfm-view-articles-manage.php:369
#: views/articles/wcfm-view-articles-manage.php:371
#: views/coupons/wcfm-view-coupons-manage.php:120
#: views/coupons/wcfm-view-coupons-manage.php:122
#: views/products-manager/wcfm-view-products-manage.php:780
#: views/products-manager/wcfm-view-products-manage.php:782
#: views/products-popup/wcfm-view-product-popup.php:284
#: views/products-popup/wcfm-view-product-popup.php:286
msgid "Submit for Review"
msgstr "Zur Überprüfung einreichen"

#: views/articles/wcfm-view-articles.php:83
msgid "Select a category"
msgstr "Kategorie auswählen"

#: views/articles/wcfm-view-articles.php:109
#: views/articles/wcfm-view-articles.php:120
#: views/products/wcfm-view-products.php:197
#: views/products/wcfm-view-products.php:220
#: views/products-manager/wcfm-view-products-manage-tabs.php:241
msgid "Image"
msgstr "Bild"

#: views/articles/wcfm-view-articles.php:111
#: views/articles/wcfm-view-articles.php:122
#: views/customers/wcfm-view-customers-details.php:192
#: views/customers/wcfm-view-customers-details.php:203
#: views/customers/wcfm-view-customers-details.php:230
#: views/customers/wcfm-view-customers-details.php:241
#: views/customers/wcfm-view-customers-details.php:268
#: views/customers/wcfm-view-customers-details.php:278
#: views/listings/wcfm-view-listings.php:104
#: views/listings/wcfm-view-listings.php:116
#: views/orders/wcfm-view-orders.php:81 views/orders/wcfm-view-orders.php:101
#: views/products/wcfm-view-products.php:200
#: views/products/wcfm-view-products.php:223
#: views/products-manager/wcfm-view-products-manage-tabs.php:185
#: views/vendors/wcfm-view-vendors.php:58
#: views/vendors/wcfm-view-vendors.php:79
#: views/wc_bookings/wcfm-view-wcbookings.php:115
#: views/wc_bookings/wcfm-view-wcbookings.php:127
#: views/withdrawal/dokan/wcfm-view-payments.php:67
#: views/withdrawal/dokan/wcfm-view-payments.php:76
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:53
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:63
#: views/withdrawal/wcfm/wcfm-view-payments.php:68
#: views/withdrawal/wcfm/wcfm-view-payments.php:82
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:139
#: views/withdrawal/wcmp/wcfm-view-payments.php:63
#: views/withdrawal/wcmp/wcfm-view-payments.php:74
msgid "Status"
msgstr "Status"

#: views/articles/wcfm-view-articles.php:114
#: views/articles/wcfm-view-articles.php:125
msgid "Author"
msgstr "Autor"

#: views/articles/wcfm-view-articles.php:115
#: views/articles/wcfm-view-articles.php:126
#: views/customers/wcfm-view-customers-details.php:198
#: views/customers/wcfm-view-customers-details.php:209
#: views/customers/wcfm-view-customers-details.php:273
#: views/customers/wcfm-view-customers-details.php:283
#: views/customers/wcfm-view-customers.php:69
#: views/customers/wcfm-view-customers.php:84
#: views/enquiry/wcfm-view-enquiry.php:85
#: views/enquiry/wcfm-view-enquiry.php:97
#: views/enquiry/wcfm-view-my-account-enquiry.php:44
#: views/enquiry/wcfm-view-my-account-enquiry.php:59
#: views/knowledgebase/wcfm-view-knowledgebase.php:73
#: views/knowledgebase/wcfm-view-knowledgebase.php:80
#: views/messages/wcfm-view-messages.php:127
#: views/messages/wcfm-view-messages.php:140
#: views/notice/wcfm-view-notices.php:52 views/notice/wcfm-view-notices.php:58
#: views/orders/wcfm-view-orders.php:96 views/orders/wcfm-view-orders.php:116
#: views/products/wcfm-view-products.php:208
#: views/products/wcfm-view-products.php:231
#: views/reports/wcfm-view-reports-out-of-stock.php:54
#: views/reports/wcfm-view-reports-out-of-stock.php:63
#: views/wc_bookings/wcfm-view-wcbookings.php:122
#: views/wc_bookings/wcfm-view-wcbookings.php:134
msgid "Actions"
msgstr "Aktionen"

#: views/capability/wcfm-view-capability.php:140
#: views/settings/wcfm-view-settings.php:117
msgid "Capability Controller"
msgstr "Optionen verwalten"

#: views/capability/wcfm-view-capability.php:147
msgid "Capability Settings"
msgstr "Einstellungen Zusatzoptionen"

#: views/capability/wcfm-view-capability.php:150
msgid "Dashboard Settings"
msgstr "Dashboard Einstellungen"

#: views/capability/wcfm-view-capability.php:166
#: views/vendors/wcfm-view-vendors-manage.php:336
msgid "Vendors Capability"
msgstr "Shop Optionen"

#: views/capability/wcfm-view-capability.php:171
msgid "Configure what to hide from all Vendors"
msgstr "Festlegen, was einem Verkäufer nicht gezeigt werden soll"

#: views/capability/wcfm-view-capability.php:180
msgid "Manage Products"
msgstr "Produkte verwalten"

#: views/capability/wcfm-view-capability.php:181
msgid "Add Products"
msgstr "Produkte hinzufügen"

#: views/capability/wcfm-view-capability.php:184
msgid "Auto Publish Live Products"
msgstr "Sofort freischalten"

#: views/capability/wcfm-view-capability.php:190
msgid "Types"
msgstr "Arten"

#: views/capability/wcfm-view-capability.php:197
msgid "External / Affiliate"
msgstr "Externes/Affiliate Produkt"

#: views/capability/wcfm-view-capability.php:202
msgid "Panels"
msgstr "Bereiche"

#: views/capability/wcfm-view-capability.php:206
#: views/products-manager/wcfm-view-products-manage-tabs.php:18
#: views/products-manager/wcfm-view-products-manage-tabs.php:206
msgid "Inventory"
msgstr "Inventar"

#: views/capability/wcfm-view-capability.php:208
msgid "Taxes"
msgstr "Steuern"

#: views/capability/wcfm-view-capability.php:209
#: views/products-manager/wcfm-view-products-manage-tabs.php:265
msgid "Linked"
msgstr "Verlinkung"

#: views/capability/wcfm-view-capability.php:210
#: views/products-manager/wcfm-view-products-manage-tabs.php:129
#: views/products-manager/wcfm-view-products-manage-tabs.php:136
msgid "Attributes"
msgstr "Eigenschaften"

#: views/capability/wcfm-view-capability.php:211
msgid "Advanced"
msgstr "Erweitert"

#: views/capability/wcfm-view-capability.php:223
msgid "Access"
msgstr "Zugriff"

#: views/capability/wcfm-view-capability.php:233
msgid "Marketplace"
msgstr ""

#: views/capability/wcfm-view-capability.php:238
msgid "Show Email"
msgstr ""

#: views/capability/wcfm-view-capability.php:239
msgid "Show Phone"
msgstr ""

#: views/capability/wcfm-view-capability.php:240
msgid "Show Address"
msgstr ""

#: views/capability/wcfm-view-capability.php:241
msgid "Show Map"
msgstr ""

#: views/capability/wcfm-view-capability.php:242
msgid "Show Social"
msgstr ""

#: views/capability/wcfm-view-capability.php:243
msgid "Show Follower"
msgstr ""

#: views/capability/wcfm-view-capability.php:244
msgid "Show Policy"
msgstr ""

#: views/capability/wcfm-view-capability.php:245
msgid "Store Hours"
msgstr ""

#: views/capability/wcfm-view-capability.php:246
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:732
#: views/settings/wcfm-view-wcmarketplace-settings.php:699
msgid "Customer Support"
msgstr "Kunden Support"

#: views/capability/wcfm-view-capability.php:248
msgid "Reviews Manage"
msgstr ""

#: views/capability/wcfm-view-capability.php:249
msgid "Ledger Book"
msgstr ""

#: views/capability/wcfm-view-capability.php:250
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:232
msgid "Video Banner"
msgstr ""

#: views/capability/wcfm-view-capability.php:251
msgid "Slider Banner"
msgstr ""

#: views/capability/wcfm-view-capability.php:266
msgid "Withdrwal Request"
msgstr ""

#: views/capability/wcfm-view-capability.php:267
#: views/withdrawal/dokan/wcfm-view-withdrawal.php:64
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:57
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:42
msgid "Transactions"
msgstr ""

#: views/capability/wcfm-view-capability.php:278
msgid "Integrations"
msgstr ""

#: views/capability/wcfm-view-capability.php:282
msgid "Install WC Bookings to enable this feature."
msgstr "WC Bookings muss installiert sein, um diese Funktion nutzen zu können."

#: views/capability/wcfm-view-capability.php:288
#: views/capability/wcfm-view-capability.php:361
msgid "Manage Appointments"
msgstr "Termine verwalten"

#: views/capability/wcfm-view-capability.php:288
msgid "Install WC Appointments to enable this feature."
msgstr "Installieren Sie WC-Termine, um diese Funktion zu aktivieren."

#: views/capability/wcfm-view-capability.php:294
msgid "Install WC Subscriptions to enable this feature."
msgstr "Installieren Sie WC-Abonnements, um diese Funktion zu aktivieren."

#: views/capability/wcfm-view-capability.php:299
#: views/capability/wcfm-view-capability.php:302
msgid "Associate Listings"
msgstr "Partnerlisten"

#: views/capability/wcfm-view-capability.php:302
msgid "Install WP Job Manager to enable this feature."
msgstr "Installieren Sie den WP Job Manager, um diese Funktion zu aktivieren."

#: views/capability/wcfm-view-capability.php:311
msgid "Manage Articles"
msgstr "Beiträge verwalten"

#: views/capability/wcfm-view-capability.php:312
msgid "Add Articles"
msgstr "Neuer Beitrag"

#: views/capability/wcfm-view-capability.php:313
msgid "Publish Articles"
msgstr "Beitrag veröffentlichen"

#: views/capability/wcfm-view-capability.php:314
msgid "Edit Live Articles"
msgstr "Beitrag live bearbeiten"

#: views/capability/wcfm-view-capability.php:315
msgid "Auto Publish Live Articles"
msgstr "Live-Beitrag automatisch veröffentlichen"

#: views/capability/wcfm-view-capability.php:316
msgid "Delete Articles"
msgstr "Beitrag löschen"

#: views/capability/wcfm-view-capability.php:327
msgid "Manage Coupons"
msgstr "Gutscheine verwalten"

#: views/capability/wcfm-view-capability.php:328
msgid "Add Coupons"
msgstr "Gutscheine hinzufügen"

#: views/capability/wcfm-view-capability.php:329
msgid "Publish Coupons"
msgstr "Gutscheine publizieren"

#: views/capability/wcfm-view-capability.php:330
msgid "Edit Live Coupons"
msgstr "Veröffentliche Gutscheine bearbeiten"

#: views/capability/wcfm-view-capability.php:331
msgid "Auto Publish Live Coupons"
msgstr "Veröffentliche Gutscheine Automatisch ohne Überprüfung freischalten"

#: views/capability/wcfm-view-capability.php:332
msgid "Delete Coupons"
msgstr "Gutscheine löschen"

#: views/capability/wcfm-view-capability.php:333
msgid "Allow Free Shipping"
msgstr "Kostenloser Versand aktivieren"

#: views/capability/wcfm-view-capability.php:341
#: views/customers/wcfm-view-customers-details.php:186
#: views/customers/wcfm-view-customers.php:64
#: views/customers/wcfm-view-customers.php:79
#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:24
msgid "Bookings"
msgstr "Buchungen"

#: views/capability/wcfm-view-capability.php:345
msgid "Manual Booking"
msgstr ""

#: views/capability/wcfm-view-capability.php:346
msgid "Manage Resource"
msgstr ""

#: views/capability/wcfm-view-capability.php:358
#: views/customers/wcfm-view-customers-details.php:224
msgid "Appointments"
msgstr "Termine"

#: views/capability/wcfm-view-capability.php:362
msgid "Manual Appointment"
msgstr ""

#: views/capability/wcfm-view-capability.php:363
msgid "Manage Staff"
msgstr ""

#: views/capability/wcfm-view-capability.php:364
msgid "Appointments List"
msgstr ""

#: views/capability/wcfm-view-capability.php:365
msgid "Appointments Calendar"
msgstr ""

#: views/capability/wcfm-view-capability.php:379
msgid "Subscriptions List"
msgstr ""

#: views/capability/wcfm-view-capability.php:380
msgid "Subscription Details"
msgstr ""

#: views/capability/wcfm-view-capability.php:381
msgid "Subscription Status Update"
msgstr ""

#: views/capability/wcfm-view-capability.php:382
msgid "Subscription Schedule Update"
msgstr ""

#: views/capability/wcfm-view-capability.php:394
msgid "View Comments"
msgstr "Zeige Kommentare"

#: views/capability/wcfm-view-capability.php:395
msgid "Submit Comments"
msgstr "Kommentare senden"

#: views/capability/wcfm-view-capability.php:406
msgid "Store Invoice"
msgstr ""

#: views/capability/wcfm-view-capability.php:407
msgid "Commission Invoice"
msgstr ""

#: views/capability/wcfm-view-capability.php:418
#: views/customers/wcfm-view-customers-details.php:100
#: views/customers/wcfm-view-customers-manage.php:110
#: views/customers/wcfm-view-customers.php:32
msgid "Manage Customers"
msgstr "Kunden verwalten"

#: views/capability/wcfm-view-capability.php:419
#: views/customers/wcfm-view-customers-manage.php:101
msgid "Add Customer"
msgstr "Hinzufügen"

#: views/capability/wcfm-view-capability.php:421
msgid "View Customer"
msgstr "Kunden anzeigen"

#: views/capability/wcfm-view-capability.php:422
msgid "View Customer Orders"
msgstr "Kunden Bestellungen anzeigen"

#: views/capability/wcfm-view-capability.php:423
msgid "View Customer Name"
msgstr ""

#: views/capability/wcfm-view-capability.php:424
msgid "View Customer Email"
msgstr "Kunden-E-Mail anzeigen"

#: views/capability/wcfm-view-capability.php:425
#: views/orders/wcfm-view-orders.php:84 views/orders/wcfm-view-orders.php:104
msgid "Billing Address"
msgstr "Rechnungsadresse"

#: views/capability/wcfm-view-capability.php:426
#: views/orders/wcfm-view-orders.php:85 views/orders/wcfm-view-orders.php:105
msgid "Shipping Address"
msgstr "Versandadresse"

#: views/capability/wcfm-view-capability.php:430
msgid "Customer Limit"
msgstr ""

#: views/capability/wcfm-view-capability.php:454
msgid "Advanced Capability"
msgstr "Erweiterte Eigenschaften"

#: views/capability/wcfm-view-capability.php:471
#: views/capability/wcfm-view-capability.php:481
msgid "Shop Managers Capability"
msgstr "Shop Manager Optionen"

#: views/capability/wcfm-view-capability.php:493
#: views/capability/wcfm-view-capability.php:503
msgid "Shop Staffs Capability"
msgstr "Shop Mitarbeiter Optionen"

#: views/capability/wcfm-view-capability.php:512
msgid "*** Vendor Managers are treated as Shop Staff for a Vendor Store."
msgstr ""
"*** Shop Managers werden als Shop Mitarbeiter für einen Verkäufer Shop "
"behandelt."

#: views/capability/wcfm-view-capability.php:524
#: views/profile/wcfm-view-profile.php:350
#: views/settings/wcfm-view-dokan-settings.php:511
#: views/settings/wcfm-view-settings.php:536
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:791
#: views/settings/wcfm-view-wcmarketplace-settings.php:766
#: views/settings/wcfm-view-wcpvendors-settings.php:459
#: views/settings/wcfm-view-wcvendors-settings.php:464
msgid "Save"
msgstr "Speichern"

#: views/coupons/wcfm-view-coupons-manage.php:55
msgid "Manage Coupon"
msgstr "Gutschein verwalten"

#: views/coupons/wcfm-view-coupons-manage.php:62
msgid "Edit Coupon"
msgstr "Gutschein bearbeiten"

#: views/coupons/wcfm-view-coupons-manage.php:62
msgid "Add Coupon"
msgstr "Gutschein hinzufügen"

#: views/coupons/wcfm-view-coupons-manage.php:77
#: views/coupons/wcfm-view-coupons.php:43
msgid "Add New Coupon"
msgstr "Neuen Gutschein hinzufügen"

#: views/coupons/wcfm-view-coupons-manage.php:92
#: views/coupons/wcfm-view-coupons.php:55
#: views/coupons/wcfm-view-coupons.php:65
msgid "Code"
msgstr "Code"

#: views/coupons/wcfm-view-coupons-manage.php:94
msgid "Discount Type"
msgstr "Rabatt-Typ"

#: views/coupons/wcfm-view-coupons-manage.php:94
msgid "Percentage discount"
msgstr "Rabatt in Prozent"

#: views/coupons/wcfm-view-coupons-manage.php:94
msgid "Fixed Cart Discount"
msgstr "Warenkorbrabatt"

#: views/coupons/wcfm-view-coupons-manage.php:94
msgid "Fixed Product Discount"
msgstr "Fester Produktrabatt"

#: views/coupons/wcfm-view-coupons-manage.php:95
msgid "Coupon Amount"
msgstr "Gutscheinwert"

#: views/coupons/wcfm-view-coupons-manage.php:96
msgid "Coupon expiry date"
msgstr "Verfallsdatum des Gutscheins"

#: views/coupons/wcfm-view-coupons-manage.php:100
msgid "Allow free shipping"
msgstr "Kostenlosen Versand erlauben"

#: views/coupons/wcfm-view-coupons-manage.php:100
msgid ""
"Check this box if the coupon grants free shipping. The free shipping method "
"must be enabled and be set to require \"a valid free shipping coupon\" (see "
"the \"Free Shipping Requires\" setting)."
msgstr ""
"Aktivieren Sie dieses Kontrollkästchen, wenn der Gutschein kostenlosen "
"Versand erlauben soll. Kostenloser Versand muss aktiviert sein und es muss "
"ausgewählt werden das ein Gutschein erforderlich ist. (siehe „Kostenloser "
"Versand“ Einstellung)."

#: views/coupons/wcfm-view-coupons.php:24
msgid "Coupons Listing"
msgstr "Gutscheinliste"

#: views/coupons/wcfm-view-coupons.php:35
#: views/listings/wcfm-view-listings.php:82
#: views/orders/wcfm-view-orders.php:64
#: views/products/wcfm-view-products.php:72
#: views/wc_bookings/wcfm-view-wcbookings.php:63
msgid "Screen Manager"
msgstr "Bildschirm Manager"

#: views/coupons/wcfm-view-coupons.php:58
#: views/coupons/wcfm-view-coupons.php:68
msgid "Usage Limit"
msgstr "Nutzungslimit"

#: views/coupons/wcfm-view-coupons.php:59
#: views/coupons/wcfm-view-coupons.php:69
msgid "Expiry date"
msgstr "Verfallsdatum"

#: views/coupons/wcfm-view-coupons.php:60
#: views/coupons/wcfm-view-coupons.php:70
#: views/listings/wcfm-view-listings.php:110
#: views/listings/wcfm-view-listings.php:122
#: views/vendors/wcfm-view-vendors.php:74
#: views/vendors/wcfm-view-vendors.php:95
msgid "Action"
msgstr "Aktion"

#: views/customers/wcfm-view-customers-details.php:48
msgid "Customer Details"
msgstr "Kundendetails"

#: views/customers/wcfm-view-customers-details.php:107
#: views/customers/wcfm-view-customers-manage.php:113
#: views/customers/wcfm-view-customers.php:42
msgid "Add New Customer"
msgstr "Hinzufügen"

#: views/customers/wcfm-view-customers-details.php:128
msgid "total money spent"
msgstr "Insgesamt ausgegebenes Geld"

#: views/customers/wcfm-view-customers-details.php:140
#, php-format
msgid "<strong>%s order</strong><br />"
msgid_plural "<strong>%s orders</strong><br />"
msgstr[0] "<strong>%s Bestellung</strong><br />"
msgstr[1] "<strong>%s Bestellungen</strong><br />"

#: views/customers/wcfm-view-customers-details.php:142
msgid "total order placed"
msgstr "Gesamtauftrag platziert"

#: views/customers/wcfm-view-customers-details.php:160
#: views/customers/wcfm-view-customers-manage.php:137
#: views/customers/wcfm-view-customers.php:61
#: views/customers/wcfm-view-customers.php:76
#: views/enquiry/wcfm-view-enquiry-form.php:61
#: views/profile/wcfm-view-profile.php:206
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:739
#: views/settings/wcfm-view-wcmarketplace-settings.php:714
#: views/vendors/wcfm-view-vendors-manage.php:284
#: views/vendors/wcfm-view-vendors-new.php:92
msgid "Email"
msgstr "Email"

#: views/customers/wcfm-view-customers-details.php:161
#: views/customers/wcfm-view-customers-manage.php:138
#: views/customers/wcfm-view-customers-manage.php:167
#: views/customers/wcfm-view-customers-manage.php:185
#: views/profile/wcfm-view-profile.php:204
#: views/profile/wcfm-view-profile.php:285
#: views/profile/wcfm-view-profile.php:301
#: views/vendors/wcfm-view-vendors-manage.php:285
#: views/vendors/wcfm-view-vendors-manage.php:328
#: views/vendors/wcfm-view-vendors-new.php:93
#: views/vendors/wcfm-view-vendors-new.php:116
#: views/vendors/wcfm-view-vendors-new.php:134
msgid "First Name"
msgstr "Vorname"

#: views/customers/wcfm-view-customers-details.php:162
#: views/customers/wcfm-view-customers-manage.php:139
#: views/customers/wcfm-view-customers-manage.php:168
#: views/customers/wcfm-view-customers-manage.php:186
#: views/profile/wcfm-view-profile.php:205
#: views/profile/wcfm-view-profile.php:286
#: views/profile/wcfm-view-profile.php:302
#: views/vendors/wcfm-view-vendors-manage.php:286
#: views/vendors/wcfm-view-vendors-manage.php:329
#: views/vendors/wcfm-view-vendors-new.php:94
#: views/vendors/wcfm-view-vendors-new.php:117
#: views/vendors/wcfm-view-vendors-new.php:135
msgid "Last Name"
msgstr "Nachname"

#: views/customers/wcfm-view-customers-details.php:193
#: views/customers/wcfm-view-customers-details.php:204
#: views/wc_bookings/wcfm-view-wcbookings-details.php:147
#: views/wc_bookings/wcfm-view-wcbookings.php:116
#: views/wc_bookings/wcfm-view-wcbookings.php:128
msgid "Booking"
msgstr "Buchung"

#: views/customers/wcfm-view-customers-details.php:196
#: views/customers/wcfm-view-customers-details.php:207
#: views/wc_bookings/wcfm-view-wcbookings.php:119
#: views/wc_bookings/wcfm-view-wcbookings.php:131
msgid "Start Date"
msgstr "Start Datum"

#: views/customers/wcfm-view-customers-details.php:197
#: views/customers/wcfm-view-customers-details.php:208
#: views/wc_bookings/wcfm-view-wcbookings.php:120
#: views/wc_bookings/wcfm-view-wcbookings.php:132
msgid "End Date"
msgstr "Enddatum"

#: views/customers/wcfm-view-customers-details.php:270
#: views/customers/wcfm-view-customers-details.php:280
#: views/orders/wcfm-view-orders.php:83 views/orders/wcfm-view-orders.php:103
msgid "Purchased"
msgstr "Anzahl"

#: views/customers/wcfm-view-customers-manage.php:159
#: views/orders/wcfm-view-orders-details.php:292
#: views/orders/wcfm-view-orders-details.php:294
#: views/orders/wcfm-view-orders-details.php:331
#: views/orders/wcfm-view-orders-details.php:333
#: views/profile/wcfm-view-profile.php:278
#: views/vendors/wcfm-view-vendors-new.php:108
msgid "Address"
msgstr "Adresse"

#: views/customers/wcfm-view-customers-manage.php:164
#: views/profile/wcfm-view-profile.php:282
#: views/vendors/wcfm-view-vendors-new.php:113
msgid "Billing"
msgstr "Rechnung"

#: views/customers/wcfm-view-customers-manage.php:169
#: views/profile/wcfm-view-profile.php:238
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:738
#: views/settings/wcfm-view-wcmarketplace-settings.php:713
#: views/vendors/wcfm-view-vendors-new.php:118
msgid "Phone"
msgstr "Telefon"

#: views/customers/wcfm-view-customers-manage.php:170
#: views/customers/wcfm-view-customers-manage.php:187
#: views/profile/wcfm-view-profile.php:287
#: views/profile/wcfm-view-profile.php:303
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:740
#: views/settings/wcfm-view-wcmarketplace-settings.php:229
#: views/settings/wcfm-view-wcmarketplace-settings.php:715
#: views/settings/wcfm-view-wcvendors-settings.php:219
#: views/settings/wcfm-view-wcvendors-settings.php:408
#: views/vendors/wcfm-view-vendors-new.php:119
#: views/vendors/wcfm-view-vendors-new.php:136
msgid "Address 1"
msgstr "Adresszeile 1"

#: views/customers/wcfm-view-customers-manage.php:171
#: views/customers/wcfm-view-customers-manage.php:188
#: views/profile/wcfm-view-profile.php:288
#: views/profile/wcfm-view-profile.php:304
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:741
#: views/settings/wcfm-view-wcmarketplace-settings.php:230
#: views/settings/wcfm-view-wcmarketplace-settings.php:716
#: views/settings/wcfm-view-wcvendors-settings.php:220
#: views/settings/wcfm-view-wcvendors-settings.php:409
#: views/vendors/wcfm-view-vendors-new.php:120
#: views/vendors/wcfm-view-vendors-new.php:137
msgid "Address 2"
msgstr "Adresszeile 2"

#: views/customers/wcfm-view-customers-manage.php:172
#: views/customers/wcfm-view-customers-manage.php:189
#: views/profile/wcfm-view-profile.php:289
#: views/profile/wcfm-view-profile.php:305
#: views/settings/wcfm-view-dokan-settings.php:204
#: views/settings/wcfm-view-dokan-settings.php:416
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:308
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:742
#: views/settings/wcfm-view-wcmarketplace-settings.php:231
#: views/settings/wcfm-view-wcmarketplace-settings.php:717
#: views/settings/wcfm-view-wcvendors-settings.php:221
#: views/settings/wcfm-view-wcvendors-settings.php:382
#: views/settings/wcfm-view-wcvendors-settings.php:410
#: views/vendors/wcfm-view-vendors-new.php:121
#: views/vendors/wcfm-view-vendors-new.php:138
msgid "Country"
msgstr "Land"

#: views/customers/wcfm-view-customers-manage.php:173
#: views/customers/wcfm-view-customers-manage.php:190
#: views/profile/wcfm-view-profile.php:290
#: views/profile/wcfm-view-profile.php:306
#: views/settings/wcfm-view-dokan-settings.php:202
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:306
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:743
#: views/settings/wcfm-view-wcmarketplace-settings.php:233
#: views/settings/wcfm-view-wcmarketplace-settings.php:718
#: views/settings/wcfm-view-wcvendors-settings.php:222
#: views/settings/wcfm-view-wcvendors-settings.php:411
#: views/vendors/wcfm-view-vendors-new.php:122
#: views/vendors/wcfm-view-vendors-new.php:139
msgid "City/Town"
msgstr "Stadt/Ort"

#: views/customers/wcfm-view-customers-manage.php:174
#: views/customers/wcfm-view-customers-manage.php:191
#: views/profile/wcfm-view-profile.php:291
#: views/profile/wcfm-view-profile.php:307
#: views/settings/wcfm-view-dokan-settings.php:205
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:309
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:744
#: views/settings/wcfm-view-wcmarketplace-settings.php:232
#: views/settings/wcfm-view-wcmarketplace-settings.php:719
#: views/settings/wcfm-view-wcvendors-settings.php:223
#: views/settings/wcfm-view-wcvendors-settings.php:412
#: views/vendors/wcfm-view-vendors-new.php:123
#: views/vendors/wcfm-view-vendors-new.php:140
msgid "State/County"
msgstr "Bundesland"

#: views/customers/wcfm-view-customers-manage.php:175
#: views/customers/wcfm-view-customers-manage.php:192
#: views/profile/wcfm-view-profile.php:292
#: views/profile/wcfm-view-profile.php:308
#: views/settings/wcfm-view-dokan-settings.php:203
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:307
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:745
#: views/settings/wcfm-view-wcmarketplace-settings.php:234
#: views/settings/wcfm-view-wcmarketplace-settings.php:720
#: views/settings/wcfm-view-wcvendors-settings.php:224
#: views/settings/wcfm-view-wcvendors-settings.php:413
#: views/vendors/wcfm-view-vendors-new.php:124
#: views/vendors/wcfm-view-vendors-new.php:141
msgid "Postcode/Zip"
msgstr "Postleitzahl"

#: views/customers/wcfm-view-customers.php:62
#: views/customers/wcfm-view-customers.php:77
msgid "Location"
msgstr "Standort"

#: views/customers/wcfm-view-customers.php:66
#: views/customers/wcfm-view-customers.php:81
msgid "Money Spent"
msgstr "Umsatz"

#: views/customers/wcfm-view-customers.php:67
#: views/customers/wcfm-view-customers.php:82
msgid "Last Order"
msgstr "Letzte Bestellung"

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:48
#, php-format
msgid "Welcome to %s Dashboard"
msgstr "Willkommen im %s Hauptmenü"

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:62
msgid "Last Login:"
msgstr ""

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:69
#: views/vendors/wcfm-view-vendors.php:63
#: views/vendors/wcfm-view-vendors.php:84
msgid "Product Limit Stats"
msgstr ""

#: views/dashboard/wcfm-view-dashboard-welcome-box.php:70
#: views/vendors/wcfm-view-vendors.php:64
#: views/vendors/wcfm-view-vendors.php:85
msgid "Disk Space Usage Stats"
msgstr ""

#: views/dashboard/wcfm-view-dashboard.php:115
msgid "gross sales in last 7 days"
msgstr "Bruttomsatz der letzten 7 Tage"

#: views/dashboard/wcfm-view-dashboard.php:146
msgid "admin fees in last 7 days"
msgstr "admin gebühr letzten 7 tage"

#: views/dashboard/wcfm-view-dashboard.php:146
msgid "commission in last 7 days"
msgstr "Provision der letzten 7 Tage"

#: views/dashboard/wcfm-view-dashboard.php:154
#: views/dashboard/wcfm-view-dokan-dashboard.php:161
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:163
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:181
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:185
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:168
#, php-format
msgid "<strong>%s item</strong>"
msgid_plural "<strong>%s items</strong>"
msgstr[0] ""
msgstr[1] ""

#: views/dashboard/wcfm-view-dashboard.php:155
msgid "sold in last 7 days"
msgstr "in den letzten 7 Tagen verkauft"

#: views/dashboard/wcfm-view-dashboard.php:167
#: views/dashboard/wcfm-view-dokan-dashboard.php:172
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:174
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:192
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:196
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:179
#, php-format
msgid "<strong>%s order</strong>"
msgid_plural "<strong>%s orders</strong>"
msgstr[0] ""
msgstr[1] ""

#: views/dashboard/wcfm-view-dashboard.php:168
msgid "received in last 7 days"
msgstr "in den letzten 7 tagen erhalten"

#: views/dashboard/wcfm-view-dashboard.php:228
#: views/dashboard/wcfm-view-dokan-dashboard.php:232
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:235
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:253
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:256
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:239
msgid "Product Stats"
msgstr "Produktstatistik"

#: views/dashboard/wcfm-view-dashboard.php:250
#: views/dashboard/wcfm-view-dokan-dashboard.php:252
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:255
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:273
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:276
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:259
msgid "Store Stats"
msgstr "Shop Statistiken"

#: views/dashboard/wcfm-view-dashboard.php:261
#: views/dashboard/wcfm-view-dokan-dashboard.php:264
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:267
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:285
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:271
#, php-format
msgid "%s top seller in last 7 days (sold %d)"
msgstr "%s Top Seller der letzten 7 Tage (verkauft %d)"

#: views/dashboard/wcfm-view-dashboard.php:274
#: views/dashboard/wcfm-view-dokan-dashboard.php:278
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:281
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:299
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:307
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:285
#, php-format
msgid "<strong>%s order</strong> - processing"
msgid_plural "<strong>%s orders</strong> - processing"
msgstr[0] "<strong>%s Bestellung</strong> - in Bearbeitung"
msgstr[1] "<strong>%s Bestellungen</strong> - in Bearbeitung"

#: views/dashboard/wcfm-view-dashboard.php:280
#, php-format
msgid "<strong>%s order</strong> - on-hold"
msgid_plural "<strong>%s orders</strong> - on-hold"
msgstr[0] "<strong>%s-Bestellung</strong> - in Wartestellung"
msgstr[1] "<strong>%s-Bestellungen</strong> - in Wartestellung"

#: views/dashboard/wcfm-view-dashboard.php:290
#: views/dashboard/wcfm-view-dokan-dashboard.php:297
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:300
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:318
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:326
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:304
#, php-format
msgid "<strong>%s product</strong> - low in stock"
msgid_plural "<strong>%s products</strong> - low in stock"
msgstr[0] "<strong>%s Produkt</strong> - geringer Lagerbestand"
msgstr[1] "<strong>%s Produkt</strong> - geringer Lagerbestand"

#: views/dashboard/wcfm-view-dashboard.php:296
#: views/dashboard/wcfm-view-dokan-dashboard.php:303
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:306
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:324
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:332
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:310
#, php-format
msgid "<strong>%s product</strong> - out of stock"
msgid_plural "<strong>%s products</strong> - out of stock"
msgstr[0] "<strong>%s Produkt</strong> - ausverkauft"
msgstr[1] "<strong>%s Produkte</strong> - ausverkauft"

#: views/dashboard/wcfm-view-dashboard.php:317
#: views/dashboard/wcfm-view-dokan-dashboard.php:324
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:328
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:346
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:353
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:331
msgid "Sales by Product"
msgstr "Verkäufe nach Produkt"

#: views/dashboard/wcfm-view-dashboard.php:332
#: views/dashboard/wcfm-view-dokan-dashboard.php:340
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:344
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:362
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:369
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:347
msgid "Top Regions"
msgstr "Top Regionen"

#: views/dashboard/wcfm-view-dashboard.php:352
#: views/dashboard/wcfm-view-dokan-dashboard.php:360
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:364
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:382
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:389
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:367
msgid "Latest Topics"
msgstr "Neueste Themen"

#: views/dashboard/wcfm-view-dashboard.php:374
#: views/dashboard/wcfm-view-dokan-dashboard.php:382
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:386
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:404
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:411
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:389
msgid "There is no topic yet!!"
msgstr "Es gibt noch kein neues Thema!!!"

#: views/dashboard/wcfm-view-dokan-dashboard.php:143
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:145
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:163
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:167
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:150
#: views/vendors/wcfm-view-vendors-manage.php:210
msgid "gross sales in this month"
msgstr "Bruttoumsatz in diesem Monat"

#: views/dashboard/wcfm-view-dokan-dashboard.php:153
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:155
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:173
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:177
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:160
#: views/vendors/wcfm-view-vendors-manage.php:228
msgid "earnings in this month"
msgstr "Einnahmen in diesem Monat"

#: views/dashboard/wcfm-view-dokan-dashboard.php:162
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:164
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:182
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:186
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:169
#: views/vendors/wcfm-view-vendors-manage.php:257
msgid "sold in this month"
msgstr "verkauft in diesem Monat"

#: views/dashboard/wcfm-view-dokan-dashboard.php:173
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:175
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:193
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:197
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:180
msgid "received in this month"
msgstr "in diesem monat erhalten"

#: views/dashboard/wcfm-view-dokan-dashboard.php:285
#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:288
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:306
#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:314
#: views/dashboard/wcfm-view-wcvendors-dashboard.php:292
#, php-format
msgid "<strong>%s product</strong> - awaiting fulfillment"
msgid_plural "<strong>%s products</strong> - awaiting fulfillment"
msgstr[0] "<strong>%s produkt</strong> - in Wartestellung"
msgstr[1] "<strong>%s produkte</strong> - in Wartestellung"

#: views/dashboard/wcfm-view-wcfmmarketplace-dashboard.php:155
#: views/dashboard/wcfm-view-wcmarketplace-dashboard.php:173
#: views/vendors/wcfm-view-vendors-manage.php:228
msgid "admin fees in this month"
msgstr "Admin Gebühren in diesem Monat"

#: views/dashboard/wcfm-view-wcpvendors-dashboard.php:296
#, php-format
msgid "%s top seller this month (sold %d)"
msgstr "%s-Top-Seller des Monats (verkaufte %d)"

#: views/enquiry/wcfm-view-enquiry-form.php:46
msgid "Your email address will not be published."
msgstr "Ihre E-Mail Adresse wird nicht veröffentlicht."

#: views/enquiry/wcfm-view-enquiry-form.php:50
msgid "Your enquiry"
msgstr "Ihre Frage"

#: views/enquiry/wcfm-view-enquiry-manage.php:65
msgid "Manage Enquiry"
msgstr "Anfrage verwalten"

#: views/enquiry/wcfm-view-enquiry-manage.php:72
msgid "Edit Enquiry"
msgstr "Anfrage bearbeiten"

#: views/enquiry/wcfm-view-enquiry-manage.php:72
msgid "Add Enquiry"
msgstr "Anfrage hinzufügen"

#: views/enquiry/wcfm-view-enquiry-manage.php:76
msgid "View Product"
msgstr "Produkt anzeigen"

#: views/enquiry/wcfm-view-enquiry-manage.php:122
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:110
#: views/notice/wcfm-view-notice-view.php:104
msgid "Replies"
msgstr "Antworten"

#: views/enquiry/wcfm-view-enquiry-manage.php:175
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:157
#: views/notice/wcfm-view-notice-view.php:152
msgid "New Reply"
msgstr "Neue Antwort"

#: views/enquiry/wcfm-view-enquiry-manage.php:192
msgid "Stick at Product Page"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:192
msgid "Enable to stick this reply at product page"
msgstr ""

#: views/enquiry/wcfm-view-enquiry-manage.php:205
#: views/enquiry/wcfm-view-my-account-enquiry-manage.php:177
#: views/messages/wcfm-view-messages.php:77
#: views/notice/wcfm-view-notice-view.php:170
#: views/vendors/wcfm-view-vendors-manage.php:402
msgid "Send"
msgstr "Gesendet"

#: views/enquiry/wcfm-view-enquiry-tab.php:31
msgid "General Enquiries"
msgstr "Allgemeine Anfragen"

#: views/enquiry/wcfm-view-enquiry-tab.php:36
msgid "There are no enquiries yet."
msgstr "Bisher gibt es keine Anfragen."

#: views/enquiry/wcfm-view-enquiry-tab.php:73
msgid "Reply by"
msgstr ""

#: views/enquiry/wcfm-view-enquiry.php:78
#: views/enquiry/wcfm-view-enquiry.php:90
#: views/enquiry/wcfm-view-my-account-enquiry.php:41
#: views/enquiry/wcfm-view-my-account-enquiry.php:50
#: views/enquiry/wcfm-view-my-account-enquiry.php:65
msgid "Query"
msgstr "Abfrage"

#: views/enquiry/wcfm-view-enquiry.php:83
#: views/enquiry/wcfm-view-enquiry.php:95
msgid "Reply"
msgstr "Antwort"

#: views/enquiry/wcfm-view-my-account-enquiry.php:66
msgid "You do not have any enquiry yet!"
msgstr ""

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:56
msgid "Manage Knowledgebase"
msgstr "Knowledgebase verwalten"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:63
msgid "Edit Knowledgebase"
msgstr "Knowledgebase bearbeiten"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:63
msgid "Add Knowledgebase"
msgstr "Fügen Sie eine neue Wissensdatenbank hinzu"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:81
#: views/knowledgebase/wcfm-view-knowledgebase.php:71
#: views/knowledgebase/wcfm-view-knowledgebase.php:78
#: views/notice/wcfm-view-notice-manage.php:78
#: views/notice/wcfm-view-notices.php:51 views/notice/wcfm-view-notices.php:57
#: views/products-manager/wcfm-view-thirdparty-products-manage.php:164
msgid "Title"
msgstr "Titel"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:82
msgid "Details"
msgstr ""

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:100
#: views/products-manager/wcfm-view-products-manage.php:619
msgid "Add new category"
msgstr "Neue Kategorie hinzufügen"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:106
#: views/products-manager/wcfm-view-products-manage.php:625
msgid "-- Parent category --"
msgstr "-- Eltern-Kategorie --"

#: views/knowledgebase/wcfm-view-knowledgebase-manage.php:116
#: views/products-manager/wcfm-view-products-manage-tabs.php:154
#: views/products-manager/wcfm-view-products-manage.php:635
#: views/products-manager/wcfm-view-products-manage.php:696
msgid "Add"
msgstr "Hinzufügen"

#: views/knowledgebase/wcfm-view-knowledgebase.php:33
msgid "Guidelines for Store Users"
msgstr "Richtlinien für Shopbenutzer"

#: views/knowledgebase/wcfm-view-knowledgebase.php:38
msgid "Add New Knowledgebase"
msgstr "Neu zu Wissensdatenbank hinzufügen"

#: views/knowledgebase/wcfm-view-knowledgebase.php:57
#: views/products/wcfm-view-products.php:136
msgid "Show all category"
msgstr ""

#: views/knowledgebase/wcfm-view-knowledgebase.php:72
#: views/knowledgebase/wcfm-view-knowledgebase.php:79
msgid "Category"
msgstr ""

#: views/listings/wcfm-view-listings.php:103
#: views/listings/wcfm-view-listings.php:115
msgid "Listing"
msgstr "Job"

#: views/messages/wcfm-view-messages.php:36
msgid "Notification Dashboard"
msgstr "Nachrichten-Dashboard"

#: views/messages/wcfm-view-messages.php:48
msgid "To Store Admin"
msgstr "An den Shop Admin"

#: views/messages/wcfm-view-messages.php:48
msgid "To Store Vendors"
msgstr "Zum Verkäufer Shop"

#: views/messages/wcfm-view-messages.php:71
msgid "Direct TO:"
msgstr "Direkt an:"

#: views/messages/wcfm-view-messages.php:102
msgid "Only Unread"
msgstr "Ungelesen"

#: views/messages/wcfm-view-messages.php:103
msgid "Only Read"
msgstr "Gelesen"

#: views/messages/wcfm-view-messages.php:120
#: views/messages/wcfm-view-messages.php:133
msgid "Select all for mark as read or delete"
msgstr ""

#: views/messages/wcfm-view-messages.php:123
#: views/messages/wcfm-view-messages.php:136
msgid "Message"
msgstr "Nachricht"

#: views/notice/wcfm-view-notice-manage.php:47
msgid "Manage Topic"
msgstr "Thema verwalten"

#: views/notice/wcfm-view-notice-manage.php:55
#: views/notice/wcfm-view-notice-view.php:59
msgid "Edit Topic"
msgstr "Thema bearbeiten"

#: views/notice/wcfm-view-notice-manage.php:55
msgid "Add Topic"
msgstr "Thema hinzufügen"

#: views/notice/wcfm-view-notice-manage.php:58
#: views/notice/wcfm-view-notice-view.php:57
#: views/notice/wcfm-view-notice-view.php:57
#: views/notice/wcfm-view-notices.php:33
msgid "Topics"
msgstr "Beiträge"

#: views/notice/wcfm-view-notice-manage.php:59
msgid "View Topic"
msgstr "Zeige Thema"

#: views/notice/wcfm-view-notice-manage.php:79
msgid "Allow Reply"
msgstr "Antwort erlauben"

#: views/notice/wcfm-view-notice-manage.php:80
msgid "Close for New Reply"
msgstr "Schließen für neue Antwort"

#: views/notice/wcfm-view-notice-manage.php:81
#: views/products-manager/wcfm-view-thirdparty-products-manage.php:165
#: views/products-popup/wcfm-view-product-popup.php:251
msgid "Content"
msgstr "Content"

#: views/notice/wcfm-view-notice-view.php:47
msgid "Topic"
msgstr "Thema"

#: views/notice/wcfm-view-notices.php:37
msgid "Add New Topic"
msgstr "Neues Thema hinzufügen"

#: views/orders/wcfm-view-orders-details-fedex-dhl-express.php:49
msgid "Fedex"
msgstr "FedEx"

#: views/orders/wcfm-view-orders-details-fedex-dhl-express.php:163
msgid "DHL Express"
msgstr "DHL Express"

#: views/orders/wcfm-view-orders-details.php:70
#: views/products-manager/wcfm-view-products-manage.php:355
#: views/products-manager/wcfm-view-products-manage.php:356
#: views/products-popup/wcfm-view-product-popup.php:68
#: views/products-popup/wcfm-view-product-popup.php:69
msgid "Standard"
msgstr "Standard"

#: views/orders/wcfm-view-orders-details.php:122
msgid "Order #"
msgstr "Bestellung #"

#: views/orders/wcfm-view-orders-details.php:137
msgid "CSV Export"
msgstr "CVS Export"

#: views/orders/wcfm-view-orders-details.php:166
msgid "Order date:"
msgstr "Bestell Datum:"

#: views/orders/wcfm-view-orders-details.php:173
msgid "Order status:"
msgstr "Bestellstatus:"

#: views/orders/wcfm-view-orders-details.php:179
msgid "Customer payment page"
msgstr "Kunden-Bezahlseite"

#: views/orders/wcfm-view-orders-details.php:196
#: views/vendors/wcfm-view-vendors-manage.php:344
#: views/wc_bookings/wcfm-view-wcbookings-details.php:124
#: views/wc_bookings/wcfm-view-wcbookings-details.php:266
msgid "Update"
msgstr "Aktualisieren"

#: views/orders/wcfm-view-orders-details.php:208
msgid "Customer:"
msgstr "Kunde:"

#: views/orders/wcfm-view-orders-details.php:216
msgid "View other orders"
msgstr "Zeige weitere Bestellungen"

#: views/orders/wcfm-view-orders-details.php:240
#, php-format
msgid "<label for=\"order_payment_via\">Payment via: </label> %s"
msgstr "<label for=\"order_payment_via\">Zahlung per: </label> %s"

#: views/orders/wcfm-view-orders-details.php:259
msgid "Customer IP"
msgstr "Kunden-IP"

#: views/orders/wcfm-view-orders-details.php:271
msgid "Billing Details"
msgstr "Rechnungsdetails"

#: views/orders/wcfm-view-orders-details.php:277
msgid "Shipping Details"
msgstr "Versanddetails"

#: views/orders/wcfm-view-orders-details.php:294
msgid "No billing address set."
msgstr "Keine Rechnungsadresse hinterlegt."

#: views/orders/wcfm-view-orders-details.php:333
msgid "No shipping address set."
msgstr "Keine Versandadresse angegeben."

#: views/orders/wcfm-view-orders-details.php:358
msgid "Customer Provided Note"
msgstr "Kundennotiz"

#: views/orders/wcfm-view-orders-details.php:380
msgid "Order Items"
msgstr "Bestellte Produkt"

#: views/orders/wcfm-view-orders-details.php:387
msgid "Item"
msgstr "Produkt"

#: views/orders/wcfm-view-orders-details.php:389
#: views/settings/wcfm-view-dokan-settings.php:417
#: views/settings/wcfm-view-dokan-settings.php:420
msgid "Cost"
msgstr "Kosten"

#: views/orders/wcfm-view-orders-details.php:390
msgid "Qty"
msgstr "Menge"

#: views/orders/wcfm-view-orders-details.php:444
msgid "SKU:"
msgstr "Produktnummer:"

#: views/orders/wcfm-view-orders-details.php:448
msgid "Variation ID:"
msgstr "Variations-ID:"

#: views/orders/wcfm-view-orders-details.php:452
msgid "No longer exists"
msgstr "Existiert nicht länger"

#: views/orders/wcfm-view-orders-details.php:717
#: views/withdrawal/wcmp/wcfm-view-payments.php:66
#: views/withdrawal/wcmp/wcfm-view-payments.php:77
msgid "Fee"
msgstr "Gebühr"

#: views/orders/wcfm-view-orders-details.php:831
msgid "Coupon(s) Used"
msgstr "Gutschein/e Gebraucht"

#: views/orders/wcfm-view-orders-details.php:848
msgid "This is the total discount. Discounts are defined per line item."
msgstr ""
"Das ist der Gesamtrabatt. Einzelne Rabattbeträge sind pro Produkt angegeben."

#: views/orders/wcfm-view-orders-details.php:848
msgid "Discount"
msgstr "Rabatt"

#: views/orders/wcfm-view-orders-details.php:860
msgid "This is the shipping and handling total costs for the order."
msgstr "Das sind die Gesamtkosten für Versand und Bearbeitung."

#: views/orders/wcfm-view-orders-details.php:900
msgid "Order Total"
msgstr "Gesamtbetrag"

#: views/orders/wcfm-view-orders.php:40
msgid "Orders Listing"
msgstr "Auftragsliste"

#: views/orders/wcfm-view-orders.php:90 views/orders/wcfm-view-orders.php:110
#: views/vendors/wcfm-view-vendors.php:70
#: views/vendors/wcfm-view-vendors.php:91
msgid "Earnings"
msgstr ""

#: views/products/wcfm-view-products-export.php:64
#: views/products/wcfm-view-products.php:115
#: views/products-manager/wcfm-view-products-manage.php:441
#: views/products-popup/wcfm-view-product-popup-button.php:2
msgid "Add New Product"
msgstr "Neues Produkt hinzufügen"

#: views/products/wcfm-view-products.php:104
#: views/products/wcfm-view-products.php:109
#: views/products/wcfm-view-products.php:109
msgid "Stock Manager"
msgstr "Lagerverwaltung"

#: views/products/wcfm-view-products.php:147
msgid "Show all product types"
msgstr "Alle Produktarten"

#: views/products/wcfm-view-products.php:192
#: views/products/wcfm-view-products.php:215
msgid "Select all for bulk edit"
msgstr "Alle für die Massenbearbeitung auswählen"

#: views/products/wcfm-view-products.php:199
#: views/products/wcfm-view-products.php:222
#: views/products-manager/wcfm-view-products-manage-tabs.php:23
#: views/products-manager/wcfm-view-products-manage-tabs.php:249
msgid "SKU"
msgstr "Artikelnummer"

#: views/products/wcfm-view-products.php:201
#: views/products/wcfm-view-products.php:224
#: views/products-manager/wcfm-view-products-manage-tabs.php:209
msgid "Stock"
msgstr "Lager"

#: views/products/wcfm-view-products.php:202
#: views/products/wcfm-view-products.php:225
#: views/products-manager/wcfm-view-products-manage.php:470
#: views/products-popup/wcfm-view-product-popup.php:129
msgid "Price"
msgstr "Preis"

#: views/products-manager/wcfm-view-customfield-products-manage.php:130
msgid "-Select-"
msgstr "-Wähle-"

#: views/products-manager/wcfm-view-epeken-products-manage.php:42
msgid "Epeken Product Config"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:23
msgid ""
"SKU refers to a Stock-keeping unit, a unique identifier for each distinct "
"product and service that can be purchased."
msgstr ""
"Die Produktnummer bezieht sich auf eine Lagerhaltungseinheit und eine "
"eindeutige Kennung für jedes einzelne Produkt und Service, die gekauft "
"werden können."

#: views/products-manager/wcfm-view-products-manage-tabs.php:24
msgid "Manage Stock?"
msgstr "Lagerbestand verwalten?"

#: views/products-manager/wcfm-view-products-manage-tabs.php:24
msgid "Enable stock management at product level"
msgstr "Aktiviere Bestandsverwaltung auf Produktebene"

#: views/products-manager/wcfm-view-products-manage-tabs.php:25
#: views/products-manager/wcfm-view-products-manage-tabs.php:247
msgid "Stock Qty"
msgstr "Lagerbestand"

#: views/products-manager/wcfm-view-products-manage-tabs.php:25
msgid ""
"Stock quantity. If this is a variable product this value will be used to "
"control stock for all variations, unless you define stock at variation level."
msgstr ""
"Lagerbestand. Wenn dies ein variables Produkt ist, wird dieser Wert "
"verwendet, um den Bestand für alle Varianten zu verwenden (sofern du auf "
"Varianten-Ebene keinen Lagerbestand festgelegt hast)."

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
msgid "Allow Backorders?"
msgstr "Lieferrückestände zulassen?"

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
#: views/products-manager/wcfm-view-products-manage-tabs.php:248
msgid "Do not Allow"
msgstr "Nicht erlauben"

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
#: views/products-manager/wcfm-view-products-manage-tabs.php:248
msgid "Allow, but notify customer"
msgstr "Zulassen, aber Kunde benachrichtigen"

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
#: views/products-manager/wcfm-view-products-manage-tabs.php:248
msgid "Allow"
msgstr "Zulassen"

#: views/products-manager/wcfm-view-products-manage-tabs.php:26
msgid ""
"If managing stock, this controls whether or not backorders are allowed. If "
"enabled, stock quantity can go below 0."
msgstr ""
"Stellt ein, ob bei der Lagerverwaltung Lieferrückstände erlaubt sind. Wenn "
"aktiviert, dann kann der Lagerbestand unter 0 fallen."

#: views/products-manager/wcfm-view-products-manage-tabs.php:27
#: views/products-manager/wcfm-view-products-manage-tabs.php:250
msgid "Stock status"
msgstr "Lagerbestand"

#: views/products-manager/wcfm-view-products-manage-tabs.php:27
msgid ""
"Controls whether or not the product is listed as \"in stock\" or \"out of "
"stock\" on the frontend."
msgstr ""
"Zeigt im Frontend an, ob das Produkt „auf Lager“ oder „ausverkauft“ ist."

#: views/products-manager/wcfm-view-products-manage-tabs.php:28
msgid "Sold Individually"
msgstr "Nur einzeln verkaufen"

#: views/products-manager/wcfm-view-products-manage-tabs.php:28
msgid ""
"Enable this to only allow one of this item to be bought in a single order"
msgstr ""
"Aktivieren Sie diese Option, wenn dieser Produkt nur einmal pro Bestellung "
"gekauft werden darf"

#: views/products-manager/wcfm-view-products-manage-tabs.php:46
msgid "Files"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:48
msgid "File"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:53
msgid "Never"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:66
msgid "Grouped Products"
msgstr "Gruppierte Produkte"

#: views/products-manager/wcfm-view-products-manage-tabs.php:71
msgid "Grouped products"
msgstr "Gruppierte Produkte"

#: views/products-manager/wcfm-view-products-manage-tabs.php:71
msgid "This lets you choose which products are part of this group."
msgstr "Hier können Sie auswählen, welche Produkte Teil dieser Gruppe sind."

#: views/products-manager/wcfm-view-products-manage-tabs.php:90
msgid "Dimensions"
msgstr "Abmessungen"

#: views/products-manager/wcfm-view-products-manage-tabs.php:93
msgid "Shipping class"
msgstr "Versandklasse"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgid "Tax Status"
msgstr "Steuerstatus"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgid "Taxable"
msgstr "Besteuerbar"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgid "Shipping only"
msgstr "Nur Versand"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgctxt "Tax status"
msgid "None"
msgstr "Keine"

#: views/products-manager/wcfm-view-products-manage-tabs.php:114
msgid ""
"Define whether or not the entire product is taxable, or just the cost of "
"shipping it."
msgstr ""
"Definiere ob das ganze Produkt oder lediglich dessen Versandkosten zu "
"versteuern sind."

#: views/products-manager/wcfm-view-products-manage-tabs.php:115
msgid "Tax Class"
msgstr "Steuerklasse"

#: views/products-manager/wcfm-view-products-manage-tabs.php:115
msgid ""
"Choose a tax class for this product. Tax classes are used to apply different "
"tax rates specific to certain types of product."
msgstr ""
"Wähle eine Steuerklasse für dieses Produkt. Steuerklassen werden verwendet, "
"um unterschiedliche Steuersätze auf bestimmte Produktarten anzuwenden."

#: views/products-manager/wcfm-view-products-manage-tabs.php:140
#, php-format
msgid "Enter some text, some attributes by \"%s\" separating values."
msgstr ""
"Geben Sie einen Text oder einige Attribute mit \"%s\" Trennungswerten ein."

#: views/products-manager/wcfm-view-products-manage-tabs.php:152
msgid "Add attribute"
msgstr "Attribute hinzufügen"

#: views/products-manager/wcfm-view-products-manage-tabs.php:168
msgid "Variations"
msgstr "Variationen"

#: views/products-manager/wcfm-view-products-manage-tabs.php:171
#, php-format
msgid ""
"Before you can add a variation you need to add some variation attributes on "
"the Attributes tab. %sLearn more%s"
msgstr ""
"Bevor Sie eine Variation hinzufügen können, müssen Sie einige "
"Variationsattribute auf der Registerkarte Attribute hinzufügen. %sWeitere "
"Informationen%s"

#: views/products-manager/wcfm-view-products-manage-tabs.php:176
msgid "Default Form Values:"
msgstr "Standardformular Werte:"

#: views/products-manager/wcfm-view-products-manage-tabs.php:181
#: views/products-manager/wcfm-view-products-manage-tabs.php:182
msgid "Variations Bulk Options"
msgstr "Varianten Bulk Optionen"

#: views/products-manager/wcfm-view-products-manage-tabs.php:184
msgid "Choose option"
msgstr "Option auswählen"

#: views/products-manager/wcfm-view-products-manage-tabs.php:186
msgid "Enable all Variations"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:187
msgid "Disable all Variations"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:189
msgid "Set variations \"Downloadable\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:190
msgid "Set variations \"Non-Downloadable\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:193
msgid "Set variations \"Virtual\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:194
msgid "Set variations \"Non-Virtual\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:197
msgid "Pricing"
msgstr "Preisgestaltung"

#: views/products-manager/wcfm-view-products-manage-tabs.php:198
msgid "Regular prices"
msgstr "Reguläre Preise"

#: views/products-manager/wcfm-view-products-manage-tabs.php:199
msgid "Regular price increase"
msgstr "Reguläre Preiserhöhung"

#: views/products-manager/wcfm-view-products-manage-tabs.php:200
msgid "Regular price decrease"
msgstr "Reguläre Preissenkung"

#: views/products-manager/wcfm-view-products-manage-tabs.php:201
msgid "Sale prices"
msgstr "Sonderangebots Preis"

#: views/products-manager/wcfm-view-products-manage-tabs.php:202
msgid "Sale price increase"
msgstr "Verkaufspreiserhöhung"

#: views/products-manager/wcfm-view-products-manage-tabs.php:203
msgid "Sale price decrease"
msgstr "Verkaufspreisverfall"

#: views/products-manager/wcfm-view-products-manage-tabs.php:207
msgid "ON \"Manage stock\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:208
msgid "OFF \"Manage stock\""
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:211
msgid "Set Status - In stock"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:212
msgid "Set Status - Out of stock"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:213
msgid "Set Status - On backorder"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:225
msgid "Downloadable products"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:226
msgid "Download limit"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:227
msgid "Download expiry"
msgstr ""

#: views/products-manager/wcfm-view-products-manage-tabs.php:239
msgid "Manage Stock"
msgstr "Lagerverwaltung"

#: views/products-manager/wcfm-view-products-manage-tabs.php:246
#: views/products-manager/wcfm-view-products-manage.php:473
#: views/products-popup/wcfm-view-product-popup.php:132
#: views/settings/wcfm-view-dokan-settings.php:491
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:771
#: views/settings/wcfm-view-wcmarketplace-settings.php:747
#: views/settings/wcfm-view-wcpvendors-settings.php:155
#: views/settings/wcfm-view-wcvendors-settings.php:447
msgid "Upto"
msgstr "Bis zu"

#: views/products-manager/wcfm-view-products-manage-tabs.php:248
msgid "Backorders?"
msgstr "Nachbestellungen?"

#: views/products-manager/wcfm-view-products-manage-tabs.php:270
msgid "Up-sells"
msgstr "Upselling (Zusatzverkäufe)"

#: views/products-manager/wcfm-view-products-manage-tabs.php:270
msgid ""
"Up-sells are products which you recommend instead of the currently viewed "
"product, for example, products that are more profitable or better quality or "
"more expensive."
msgstr ""
"Up-Sells sind Produkte, die Sie anstelle des aktuell angezeigten Produkt "
"empfehlen, zum Beispiel Produkte, die mehr rentabel oder qualitativ "
"hochwertiger oder teurer sind."

#: views/products-manager/wcfm-view-products-manage-tabs.php:271
msgid "Cross-sells"
msgstr "Cross-Sells (Querverkäufe)"

#: views/products-manager/wcfm-view-products-manage-tabs.php:271
msgid ""
"Cross-sells are products which you promote in the cart, based on the current "
"product."
msgstr ""
"Cross-Selling sind Produkte, die Sie im Warenkorb basierend auf dem "
"aktuellen Produkt bewerben."

#: views/products-manager/wcfm-view-products-manage.php:35
msgid "You have reached product limit!"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:342
#: views/products-manager/wcfm-view-products-manage.php:354
#: views/products-popup/wcfm-view-product-popup.php:55
#: views/products-popup/wcfm-view-product-popup.php:67
msgid "Same as parent"
msgstr "Gleiche wie übergeordnet"

#: views/products-manager/wcfm-view-products-manage.php:343
#: views/products-popup/wcfm-view-product-popup.php:56
msgid "No shipping class"
msgstr "Keine Versandklasse"

#: views/products-manager/wcfm-view-products-manage.php:400
msgid "Manage Product"
msgstr "Produkt verwalten"

#: views/products-manager/wcfm-view-products-manage.php:409
msgid "Edit Product"
msgstr "Produkt bearbeiten"

#: views/products-manager/wcfm-view-products-manage.php:409
#: views/products-popup/wcfm-view-product-popup.php:113
msgid "Add Product"
msgstr "Produkt hinzufügen"

#: views/products-manager/wcfm-view-products-manage.php:463
#: views/products-popup/wcfm-view-product-popup.php:122
msgid "Product Title"
msgstr "Produkttitel"

#: views/products-manager/wcfm-view-products-manage.php:468
#: views/products-popup/wcfm-view-product-popup.php:127
#: views/settings/wcfm-view-settings.php:424
msgid "URL"
msgstr "URL"

#: views/products-manager/wcfm-view-products-manage.php:468
#: views/products-popup/wcfm-view-product-popup.php:127
msgid "Enter the external URL to the product."
msgstr "Geben Sie die externe URL zum Produkt."

#: views/products-manager/wcfm-view-products-manage.php:469
#: views/products-popup/wcfm-view-product-popup.php:128
msgid "Button Text"
msgstr "Button Text"

#: views/products-manager/wcfm-view-products-manage.php:469
#: views/products-popup/wcfm-view-product-popup.php:128
msgid "This text will be shown on the button linking to the external product."
msgstr ""
"Dieser Text erscheint auf dem Button, der zum auswärtigen Produkt führt."

#: views/products-manager/wcfm-view-products-manage.php:471
#: views/products-popup/wcfm-view-product-popup.php:130
msgid "schedule"
msgstr "Zeitplan"

#: views/products-manager/wcfm-view-products-manage.php:531
#: views/products-manager/wcfm-view-products-manage.php:713
#: views/products-popup/wcfm-view-product-popup.php:198
msgid "Separate Product Tags with commas"
msgstr "Trenne Produkt-Schlagworte durch Kommas"

#: views/products-manager/wcfm-view-products-manage.php:680
msgid "Add new"
msgstr ""

#: views/products-manager/wcfm-view-products-manage.php:686
msgid "-- Parent taxonomy --"
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:140
msgid "Yoast SEO"
msgstr "Yoast SEO"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:145
msgid "Enter a focus keyword"
msgstr "Geben Sie ein Schlagwort ein"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:145
msgid "It should appear in title and first paragraph of the copy."
msgstr "Es sollte im Titel und im ersten Absatz der Kopie erscheinen."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:146
msgid "Meta description"
msgstr "Meta-Beschreibung"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:146
msgid "It should not be more than 156 characters."
msgstr "Es sollte nicht mehr als 156 Zeichen lang sein."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:158
msgid "Custom Tabs"
msgstr "Benutzerdefinierte Reiter"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:163
msgid "Tabs"
msgstr "Tabs"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:164
msgid "Required for tab to be visible"
msgstr "Erforderlich, damit die Registerkarte sichtbar ist"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:165
msgid "HTML or Text to display ..."
msgstr "HTML oder Text zum Anzeigen..."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:178
msgid "Barcode & ISBN"
msgstr "Barcode & ISBN"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:183
msgid "Barcode"
msgstr "Barcode"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:184
msgid "ISBN"
msgstr "ISBN"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:197
msgid "MSRP Pricing"
msgstr "UVP-Preise"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:202
msgid "MSRP Price"
msgstr "UVP Preis"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:215
msgid "Quantities and Units"
msgstr "Mengen und Einheiten"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:220
msgid "Deactivate Quantity Rules"
msgstr "Mengenregeln deaktivieren"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:221
msgid "Override Quantity Rules"
msgstr "Mengenregeln überschreiben"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:222
msgid "Step Value"
msgstr "Schritt-Wert"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:223
msgid "Minimum Quantity"
msgstr "Mindestmenge"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:224
msgid "Maximum Quantity"
msgstr "Maximale Menge"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:225
msgid "Out of Stock Minimum"
msgstr "Schwellwert für geringer Lagerbestand"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:226
msgid "Out of Stock Maximum"
msgstr "Schwellwert für nicht vorrätig"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:227
msgid "Unit"
msgstr "Einheit"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:240
msgid "Product Fees"
msgstr "Produkt Gebühren"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:245
msgid "Fee Name"
msgstr "Gebühr Name"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:245
msgid "This will be shown at the checkout description the added fee."
msgstr ""

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:246
msgid "Fee Amount"
msgstr "Gebührenbetrag"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:246
msgid ""
"Enter a monetary decimal without any currency symbols or thousand separator. "
"This field also accepts percentages."
msgstr ""
"Geben Sie ein monetäres Dezimalzeichen ohne Währungssymbol oder "
"Tausendertrennzeichen ein. Dieses Feld akzeptiert auch Prozentsätze."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:247
msgid "Multiple Fee by Quantity"
msgstr "Mehrfachgebühr nach Anzahl"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:247
msgid ""
"Multiply the fee by the quantity of this product that is added to the cart."
msgstr ""
"Multiplizieren Sie die Gebühr mit der Menge dieses Produkts, die in den "
"Warenkorb gelegt wird."

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:260
msgid "Bulk Discount"
msgstr "Mengenrabatt"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:265
msgid "Bulk Discount enabled"
msgstr "Mengenrabatt aktiviert"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:266
msgid "Bulk discount special offer text in product description"
msgstr "Mengenrabatt-Text in Produkt-Beschreibung"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:267
msgid "Discount Rules"
msgstr "Rabattregeln"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:268
msgid "Quantity (min.)"
msgstr "Menge (minimum)"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:269
msgid "Discount (%)"
msgstr "Rabatt (%)"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:283
msgid "Role Based Price"
msgstr "Rollen Basierter Preis"

#: views/products-manager/wcfm-view-thirdparty-products-manage.php:295
msgid "Selling Price"
msgstr "Angebotspreis"

#: views/products-popup/wcfm-view-product-popup.php:142
msgid "Taxonomies"
msgstr ""

#: views/products-popup/wcfm-view-product-popup.php:227
msgid "Image Gallery"
msgstr "Bildergalerie"

#: views/profile/wcfm-view-profile.php:180
msgid "Profile Manager"
msgstr "Profil-Manager"

#: views/profile/wcfm-view-profile.php:196
msgid "Personal"
msgstr "Personal"

#: views/profile/wcfm-view-profile.php:203
msgid "Avatar"
msgstr "Avatar"

#: views/profile/wcfm-view-profile.php:214
msgid "Email already verified"
msgstr ""

#: views/profile/wcfm-view-profile.php:223
msgid "Email Verification Code: "
msgstr ""

#: views/profile/wcfm-view-profile.php:223
msgid "Verification Code"
msgstr ""

#: views/profile/wcfm-view-profile.php:224
msgid "Get Code"
msgstr ""

#: views/profile/wcfm-view-profile.php:233
msgid "Set New Password - Keep it blank for not to update"
msgstr ""

#: views/profile/wcfm-view-profile.php:252
msgid "Site Default"
msgstr "Seiten Standard"

#: views/profile/wcfm-view-profile.php:260
msgid "Language"
msgstr "Sprache"

#: views/profile/wcfm-view-profile.php:266
msgid "About"
msgstr "Über"

#: views/profile/wcfm-view-profile.php:300
msgid "Same as Billing"
msgstr ""

#: views/profile/wcfm-view-profile.php:321
#: views/settings/wcfm-view-dokan-settings.php:141
#: views/settings/wcfm-view-dokan-settings.php:141
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:192
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:192
#: views/settings/wcfm-view-wcmarketplace-settings.php:148
#: views/settings/wcfm-view-wcmarketplace-settings.php:148
#: views/settings/wcfm-view-wcpvendors-settings.php:85
#: views/settings/wcfm-view-wcpvendors-settings.php:85
#: views/settings/wcfm-view-wcvendors-settings.php:132
#: views/settings/wcfm-view-wcvendors-settings.php:132
msgid "Social"
msgstr "Soziale Netzwerke"

#: views/profile/wcfm-view-profile.php:327
msgid "Twitter"
msgstr "Twitter"

#: views/profile/wcfm-view-profile.php:328
msgid "Facebook"
msgstr "Facebook"

#: views/profile/wcfm-view-profile.php:329
msgid "Instagram"
msgstr "Instagram"

#: views/profile/wcfm-view-profile.php:330
msgid "Youtube"
msgstr "YouTube Link"

#: views/profile/wcfm-view-profile.php:331
msgid "Linkedin"
msgstr "Linkedin"

#: views/profile/wcfm-view-profile.php:332
msgid "Google Plus"
msgstr "Google Plus"

#: views/profile/wcfm-view-profile.php:333
msgid "Snapchat"
msgstr "Snapchat"

#: views/profile/wcfm-view-profile.php:334
msgid "Pinterest"
msgstr "Pinterest"

#: views/reports/wcfm-html-report-sales-by-date.php:31
msgid "Custom:"
msgstr ""

#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:63
#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:65
#: views/reports/wcfm-view-reports-sales-by-date.php:63
#: views/reports/wcfm-view-reports-sales-by-date.php:65
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:64
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:61
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:63
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:64
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:61
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:63
msgid "Sales BY Date"
msgstr "Verkäufe nach Datum"

#: views/reports/wcfm-view-reports-dokan-sales-by-date.php:63
#: views/reports/wcfm-view-reports-sales-by-date.php:63
#: views/reports/wcfm-view-reports-wcfmmarketplace-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcmarketplace-sales-by-date.php:61
#: views/reports/wcfm-view-reports-wcpvendors-sales-by-date.php:62
#: views/reports/wcfm-view-reports-wcvendors-sales-by-date.php:61
#, php-format
msgctxt "start date and end date"
msgid "From %s to %s"
msgstr "Von %s bis %s"

#: views/reports/wcfm-view-reports-menu.php:4
msgid "Sales by date"
msgstr "Verkäufe nach Datum"

#: views/reports/wcfm-view-reports-out-of-stock.php:26
msgid "Out of Stock"
msgstr "Ausverkuft"

#: views/reports/wcfm-view-reports-out-of-stock.php:50
#: views/reports/wcfm-view-reports-out-of-stock.php:59
msgid "product"
msgstr "produkt"

#: views/reports/wcfm-view-reports-out-of-stock.php:51
#: views/reports/wcfm-view-reports-out-of-stock.php:60
msgid "Parent"
msgstr "Übergeordnet"

#: views/reports/wcfm-view-reports-out-of-stock.php:52
#: views/reports/wcfm-view-reports-out-of-stock.php:61
msgid "Unit in stock"
msgstr "Auf Lager"

#: views/reports/wcfm-view-reports-out-of-stock.php:53
#: views/reports/wcfm-view-reports-out-of-stock.php:62
msgid "Stock Status"
msgstr "Lagerbestand"

#: views/settings/wcfm-view-dokan-settings.php:136
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:187
#: views/settings/wcfm-view-wcmarketplace-settings.php:143
#: views/settings/wcfm-view-wcpvendors-settings.php:80
#: views/settings/wcfm-view-wcpvendors-settings.php:103
#: views/settings/wcfm-view-wcvendors-settings.php:127
msgid "Store Settings"
msgstr "Shop Einstellungen"

#: views/settings/wcfm-view-dokan-settings.php:165
msgid "Profile Image"
msgstr "Profilbild"

#: views/settings/wcfm-view-dokan-settings.php:166
#: views/settings/wcfm-view-wcmarketplace-settings.php:207
#: views/settings/wcfm-view-wcvendors-settings.php:195
msgid "Banner"
msgstr "Banner"

#: views/settings/wcfm-view-dokan-settings.php:167
#: views/settings/wcfm-view-wcmarketplace-settings.php:173
#: views/settings/wcfm-view-wcpvendors-settings.php:110
#: views/settings/wcfm-view-wcvendors-settings.php:157
msgid "Shop Name"
msgstr "Shopname"

#: views/settings/wcfm-view-dokan-settings.php:168
msgid "Store Product Per Page"
msgstr "Anzahl Produkte pro Seite"

#: views/settings/wcfm-view-dokan-settings.php:169
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:247
#: views/settings/wcfm-view-wcvendors-settings.php:197
msgid "Store Phone"
msgstr "Shop Telefon"

#: views/settings/wcfm-view-dokan-settings.php:170
msgid "Show email in store"
msgstr "E-Mail-Adresse im Shop zeigen"

#: views/settings/wcfm-view-dokan-settings.php:171
msgid "Show tab on product single page view"
msgstr "Tab auf Produkt-Einzelseiten anzeigen"

#: views/settings/wcfm-view-dokan-settings.php:196
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:300
#: views/settings/wcfm-view-wcmarketplace-settings.php:225
#: views/settings/wcfm-view-wcvendors-settings.php:215
msgid "Store Address"
msgstr "Shop Adresse"

#: views/settings/wcfm-view-dokan-settings.php:200
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:304
msgid "Street"
msgstr "Strasse"

#: views/settings/wcfm-view-dokan-settings.php:200
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:304
msgid "Street adress"
msgstr "Straße Adresse"

#: views/settings/wcfm-view-dokan-settings.php:201
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:305
msgid "Street 2"
msgstr "Strasse 2"

#: views/settings/wcfm-view-dokan-settings.php:201
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:305
msgid "Apartment, suit, unit etc. (optional)"
msgstr "Appartement, Wohnung, Einheit etc. (optional)"

#: views/settings/wcfm-view-dokan-settings.php:202
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:306
msgid "Town / City"
msgstr "Stadt/Ort"

#: views/settings/wcfm-view-dokan-settings.php:203
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:307
msgid "Postcode / Zip"
msgstr "Postleitzahl"

#: views/settings/wcfm-view-dokan-settings.php:211
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:319
#: views/settings/wcfm-view-wcmarketplace-settings.php:252
msgid "Store Location"
msgstr "Shopstandort"

#: views/settings/wcfm-view-dokan-settings.php:259
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:387
#: views/settings/wcfm-view-wcmarketplace-settings.php:310
#: views/settings/wcfm-view-wcvendors-settings.php:252
msgid "PayPal Email"
msgstr "PayPal Email"

#: views/settings/wcfm-view-dokan-settings.php:260
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:388
msgid "Skrill Email"
msgstr "Skrill E-Mail"

#: views/settings/wcfm-view-dokan-settings.php:271
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:396
msgid "Bank Details"
msgstr "Bankverbindung"

#: views/settings/wcfm-view-dokan-settings.php:275
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:400
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:76
msgid "Account Name"
msgstr "Kontenname"

#: views/settings/wcfm-view-dokan-settings.php:276
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:401
#: views/settings/wcfm-view-wcmarketplace-settings.php:312
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:77
msgid "Account Number"
msgstr "Konto Nummer"

#: views/settings/wcfm-view-dokan-settings.php:277
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:402
#: views/settings/wcfm-view-wcmarketplace-settings.php:313
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:78
msgid "Bank Name"
msgstr "Name der Bank"

#: views/settings/wcfm-view-dokan-settings.php:278
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:403
#: views/settings/wcfm-view-wcmarketplace-settings.php:315
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:79
msgid "Bank Address"
msgstr "Bankadresse"

#: views/settings/wcfm-view-dokan-settings.php:279
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:404
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:80
msgid "Routing Number"
msgstr "Bankleitzahl"

#: views/settings/wcfm-view-dokan-settings.php:280
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:405
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:405
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:81
msgid "IBAN"
msgstr "IBAN"

#: views/settings/wcfm-view-dokan-settings.php:281
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:406
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:82
msgid "Swift Code"
msgstr "Swift Code/BIC"

#: views/settings/wcfm-view-dokan-settings.php:296
msgid "Stripe Connect"
msgstr "Stripe Connect"

#: views/settings/wcfm-view-dokan-settings.php:364
msgid "If you want to use Country-State wise Shipping system then"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:374
msgid ""
"A shipping zone is a geographic region where a certain set of shipping "
"methods are offered. System will match a customer to a single zone using "
"their shipping address and present the shipping methods within that zone to "
"them."
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:388
msgid "Enable Shipping"
msgstr "Versand aktivieren"

#: views/settings/wcfm-view-dokan-settings.php:415
#: views/settings/wcfm-view-wcvendors-settings.php:381
msgid "Shipping Rates"
msgstr "Versandkosten"

#: views/settings/wcfm-view-dokan-settings.php:415
msgid ""
"Add the countries you deliver your products to. You can specify states as "
"well. If the shipping price is same except some countries/states, there is "
"an option Everywhere Else, you can use that."
msgstr ""
"Fügen Sie die Länder hinzu, in die Sie Ihre Produkte liefern. Sie können "
"auch Versandklassen angeben. Wenn der Versandpreis bis auf einige Länder / "
"Staaten gleich ist, gibt es eine Option, die Sie überall verwenden können."

#: views/settings/wcfm-view-dokan-settings.php:418
msgid "State Shipping Rates"
msgstr "Versandkosten nach Ländern"

#: views/settings/wcfm-view-dokan-settings.php:419
#: views/settings/wcfm-view-wcvendors-settings.php:383
msgid "State"
msgstr "Bundesland"

#: views/settings/wcfm-view-dokan-settings.php:432
msgid "Dokan Pro Shipping Settings"
msgstr "Dokan Pro Versandeinstellungen"

#: views/settings/wcfm-view-dokan-settings.php:464
msgid "Dokan Pro SEO Settings"
msgstr "Dokan-Pro-SEO-Einstellungen"

#: views/settings/wcfm-view-dokan-settings.php:481
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:761
#: views/settings/wcfm-view-wcmarketplace-settings.php:737
#: views/settings/wcfm-view-wcpvendors-settings.php:144
#: views/settings/wcfm-view-wcpvendors-settings.php:160
#: views/settings/wcfm-view-wcvendors-settings.php:437
msgid "Vacation Mode"
msgstr "Abwesenheitsmodus"

#: views/settings/wcfm-view-dokan-settings.php:487
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:767
#: views/settings/wcfm-view-wcmarketplace-settings.php:743
#: views/settings/wcfm-view-wcpvendors-settings.php:151
#: views/settings/wcfm-view-wcvendors-settings.php:443
msgid "Enable Vacation Mode"
msgstr "Urlaubsdmodus aktivieren"

#: views/settings/wcfm-view-dokan-settings.php:488
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:768
#: views/settings/wcfm-view-wcmarketplace-settings.php:744
#: views/settings/wcfm-view-wcpvendors-settings.php:152
#: views/settings/wcfm-view-wcvendors-settings.php:444
msgid "Disable Purchase During Vacation"
msgstr "Deaktivieren Sie Kaufen während des Urlaubs"

#: views/settings/wcfm-view-dokan-settings.php:489
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:769
#: views/settings/wcfm-view-wcmarketplace-settings.php:745
#: views/settings/wcfm-view-wcpvendors-settings.php:153
#: views/settings/wcfm-view-wcvendors-settings.php:445
msgid "Vacation Type"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:489
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:769
#: views/settings/wcfm-view-wcmarketplace-settings.php:745
#: views/settings/wcfm-view-wcpvendors-settings.php:153
#: views/settings/wcfm-view-wcvendors-settings.php:445
msgid "Instantly Close"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:489
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:769
#: views/settings/wcfm-view-wcmarketplace-settings.php:745
#: views/settings/wcfm-view-wcpvendors-settings.php:153
#: views/settings/wcfm-view-wcvendors-settings.php:445
msgid "Date wise close"
msgstr ""

#: views/settings/wcfm-view-dokan-settings.php:492
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:772
#: views/settings/wcfm-view-wcmarketplace-settings.php:748
#: views/settings/wcfm-view-wcpvendors-settings.php:156
#: views/settings/wcfm-view-wcvendors-settings.php:448
msgid "Vacation Message"
msgstr "Urlaubsnachricht"

#: views/settings/wcfm-view-settings.php:92
msgid "WCfM Settings"
msgstr "WCfM Einstellungen"

#: views/settings/wcfm-view-settings.php:103
msgid "Bookings Global Settings"
msgstr "Globale Einstellungen für Buchungen"

#: views/settings/wcfm-view-settings.php:111
msgid "Appointments Global Settings"
msgstr "Termine Globale Einstellungen"

#: views/settings/wcfm-view-settings.php:120
msgid "Membership Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:142
msgid "WCfM Dashboard Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:146
#: views/settings/wcfm-view-wcmarketplace-settings.php:172
#: views/settings/wcfm-view-wcpvendors-settings.php:109
#: views/settings/wcfm-view-wcvendors-settings.php:156
msgid "Logo"
msgstr "Logo"

#: views/settings/wcfm-view-settings.php:147
msgid "Quick access icon"
msgstr "Zeichen für Zugangsabkürzung"

#: views/settings/wcfm-view-settings.php:148
msgid "My Store Label"
msgstr ""

#: views/settings/wcfm-view-settings.php:149
msgid "Disable Quick Access"
msgstr "Abkürzungszugang deaktivieren"

#: views/settings/wcfm-view-settings.php:151
msgid "Disable Welcome Box"
msgstr "Willkommen-Box deaktivieren"

#: views/settings/wcfm-view-settings.php:152
msgid "Disable WCFM Menu"
msgstr "WCFM Menü deaktivieren"

#: views/settings/wcfm-view-settings.php:153
msgid "Disable Theme Header"
msgstr "Header deaktivieren"

#: views/settings/wcfm-view-settings.php:154
msgid "Disable WCFM Full View"
msgstr "WCFM Vollanzeige deaktivieren"

#: views/settings/wcfm-view-settings.php:155
msgid "Disable WCFM Slick Menu"
msgstr "WCFM Slick Menü deaktivieren"

#: views/settings/wcfm-view-settings.php:157
msgid "Disable WCFM Header Panel"
msgstr "WCFM Header Bereich deaktivieren"

#: views/settings/wcfm-view-settings.php:158
msgid "Disable Float Button"
msgstr "Float-Button deaktivieren"

#: views/settings/wcfm-view-settings.php:159
msgid "Disable Ask a Question Button"
msgstr ""

#: views/settings/wcfm-view-settings.php:160
msgid "Disable Category Checklist View"
msgstr "Kategorie Übersicht deaktivieren"

#: views/settings/wcfm-view-settings.php:161
msgid "Disable Ultimate Notice"
msgstr "Letzte Neuigkeiten deaktivieren"

#: views/settings/wcfm-view-settings.php:175
msgid "Modules"
msgstr "Module"

#: views/settings/wcfm-view-settings.php:179
msgid "Module Controller"
msgstr ""

#: views/settings/wcfm-view-settings.php:181
msgid "Configure what to hide from your dashboard"
msgstr "Konfigurieren Sie, was Sie in Ihrem Dashboard verbergen möchten"

#: views/settings/wcfm-view-settings.php:228
msgid "Dashboard Display Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:240
msgid "Reset to Default"
msgstr "Zurücksetzen auf Grundeinstellungen"

#: views/settings/wcfm-view-settings.php:252
msgid "Dashboard Pages"
msgstr ""

#: views/settings/wcfm-view-settings.php:256
msgid "Dashboard Page/Endpoint Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:261
msgid "-- Select a page --"
msgstr "-- Eine Seite auswählen --"

#: views/settings/wcfm-view-settings.php:276
msgid "Refresh Permalink"
msgstr "Permalink aktualisieren"

#: views/settings/wcfm-view-settings.php:276
msgid ""
"Check to refresh WCfM page permalinks. Only apply if you are getting error "
"(e.g. 404 not found) for any pages."
msgstr ""
"Aktivieren Sie diese Option, um WCFM-Seitenpermalinks zu aktualisieren. Nur "
"anwenden, wenn Sie einen Fehler für Seiten (z. B. 404 nicht gefunden) "
"erhalten."

#: views/settings/wcfm-view-settings.php:277
msgid "This page should have shortcode - wc_frontend_manager"
msgstr "Diese Seite sollte diesen Shortcode haben - wc_frontend_manager"

#: views/settings/wcfm-view-settings.php:282
msgid "WCFM Endpoints"
msgstr "ECDM Endpunkte"

#: views/settings/wcfm-view-settings.php:286
msgid "Dashboard End Points"
msgstr ""

#: views/settings/wcfm-view-settings.php:295
msgid "My Account End Points"
msgstr ""

#: views/settings/wcfm-view-settings.php:337
msgid "Dashboard Menu Manager"
msgstr ""

#: views/settings/wcfm-view-settings.php:418
msgid "Home Menu Label"
msgstr ""

#: views/settings/wcfm-view-settings.php:422
msgid "Icon"
msgstr ""

#: views/settings/wcfm-view-settings.php:422
msgid "Insert a valid Font-awesome icon class."
msgstr ""

#: views/settings/wcfm-view-settings.php:423
msgid "Slug"
msgstr ""

#: views/settings/wcfm-view-settings.php:426
msgid "Has New?"
msgstr ""

#: views/settings/wcfm-view-settings.php:427
#: views/settings/wcfm-view-settings.php:429
msgid "New Menu Class"
msgstr ""

#: views/settings/wcfm-view-settings.php:428
msgid "New Menu URL"
msgstr ""

#: views/settings/wcfm-view-settings.php:430
msgid "Sub Menu Capability"
msgstr ""

#: views/settings/wcfm-view-settings.php:431
msgid "Menu For"
msgstr ""

#: views/settings/wcfm-view-settings.php:431
msgid "All Users"
msgstr ""

#: views/settings/wcfm-view-settings.php:431
msgid "Only Admin"
msgstr ""

#: views/settings/wcfm-view-settings.php:431
msgid "Only Vendors"
msgstr ""

#: views/settings/wcfm-view-settings.php:432
msgid "Open in new tab?"
msgstr ""

#: views/settings/wcfm-view-settings.php:448
msgid "Email Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:452
msgid "WCfM Email Setting"
msgstr ""

#: views/settings/wcfm-view-settings.php:456
msgid "Email from name"
msgstr ""

#: views/settings/wcfm-view-settings.php:456
msgid ""
"Notification emails will be triggered with this name. By default Site Name "
"will be used"
msgstr ""

#: views/settings/wcfm-view-settings.php:457
msgid "Email from address"
msgstr ""

#: views/settings/wcfm-view-settings.php:457
msgid ""
"Notification emails will be triggered from this email address. By default "
"Site Admin Email will be used"
msgstr ""

#: views/settings/wcfm-view-settings.php:458
msgid "CC Email address"
msgstr ""

#: views/settings/wcfm-view-settings.php:458
msgid "Notification emails will be CC to this email address."
msgstr ""

#: views/settings/wcfm-view-settings.php:459
msgid "BCC Email address"
msgstr ""

#: views/settings/wcfm-view-settings.php:459
msgid "Notification emails will be BCC to this email address."
msgstr ""

#: views/settings/wcfm-view-settings.php:470
msgid "Inquiry Settings"
msgstr ""

#: views/settings/wcfm-view-settings.php:474
msgid "Inquiry Module"
msgstr ""

#: views/settings/wcfm-view-settings.php:480
msgid "Inquiry Button Label"
msgstr ""

#: views/settings/wcfm-view-settings.php:485
msgid "Insert option values | separated"
msgstr ""

#: views/settings/wcfm-view-settings.php:499
msgid "Product Type Categories"
msgstr "Produktkategorien"

#: views/settings/wcfm-view-settings.php:503
msgid "Product Type Specific Category Setup"
msgstr ""

#: views/settings/wcfm-view-settings.php:524
msgid ""
"Create group of your Store Categories as per Product Types. Product Manager "
"will work according to that."
msgstr ""
"Gruppiere Deine Shop Kategorien nach Produkttypen. Der Produkt Manger wird "
"ebenfalls so funktionieren."

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:170
#, php-format
msgid "Upload a banner for your store. Banner size is (%sx%s) pixels."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:215
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:216
msgid "Static Image"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:215
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:233
msgid "Slider"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:215
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:216
msgid "Video"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:228
msgid "Store Logo"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:228
msgid "Preferred  size is (125x125) pixels."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:230
msgid "Store Banner Type"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:231
msgid "Store Banner"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:232
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:241
msgid "Insert YouTube video URL."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:235
msgid "Slider Hyperlink"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:239
msgid "Store List Banner Type"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:240
msgid "Store List Banner"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:240
msgid "This Banner will be visible at Store List Page."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:241
msgid "Store List Video Banner"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:243
msgid "Mobile Banner"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:243
msgid "This Banner will be visible when someone browse store from Mobile."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:246
msgid "Store Slug"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:248
#: views/settings/wcfm-view-wcmarketplace-settings.php:175
#: views/settings/wcfm-view-wcvendors-settings.php:159
msgid "Shop Description"
msgstr "Shop-Beschreibung"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:248
#: views/settings/wcfm-view-wcmarketplace-settings.php:175
#: views/settings/wcfm-view-wcvendors-settings.php:159
msgid "This is displayed on your shop page."
msgstr "Dies wird auf der Shop Seite angezeigt."

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:249
msgid "Select Shipping Countries"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:323
#: views/settings/wcfm-view-wcmarketplace-settings.php:258
msgid "Find Location"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:323
#: views/settings/wcfm-view-wcmarketplace-settings.php:258
msgid "Type an address to find"
msgstr "Geben Sie eine Adresse ein"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:343
msgid "Store Visibility"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:347
msgid "Store name position at you Store Page."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:348
msgid "No of products at you Store Page."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:349
msgid "Hide Email from Store"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:350
msgid "Hide Phone from Store"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:351
msgid "Hide Address from Store"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:386
msgid "Prefered Payment Method"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:400
msgid "Your bank account name"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:401
msgid "Your bank account number"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:402
msgid "Name of bank"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:403
msgid "Address of your bank"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:404
msgid "Routing number"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:406
msgid "Swift code"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:407
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:83
msgid "IFSC Code"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:407
msgid "IFSC code"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:469
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:496
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:517
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:583
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:634
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:656
#: views/settings/wcfm-view-wcmarketplace-settings.php:382
#: views/settings/wcfm-view-wcmarketplace-settings.php:409
#: views/settings/wcfm-view-wcmarketplace-settings.php:430
#: views/settings/wcfm-view-wcmarketplace-settings.php:503
#: views/settings/wcfm-view-wcmarketplace-settings.php:550
#: views/settings/wcfm-view-wcmarketplace-settings.php:572
#: controllers/withdrawal/dokan/wcfm-controller-payments.php:90
#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:82
msgid "Stripe"
msgstr "Stripe"

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:472
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:586
#: views/settings/wcfm-view-wcmarketplace-settings.php:385
#: views/settings/wcfm-view-wcmarketplace-settings.php:506
msgid "You are connected with Stripe"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:478
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:592
#: views/settings/wcfm-view-wcmarketplace-settings.php:391
#: views/settings/wcfm-view-wcmarketplace-settings.php:512
msgid "Disconnect Stripe Account"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:499
#: views/settings/wcfm-view-wcfmmarketplace-settings.php:520
#: views/settings/wcfm-view-wcmarketplace-settings.php:412
#: views/settings/wcfm-view-wcmarketplace-settings.php:433
msgid "Please Retry!!!"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:565
msgid "Unable to disconnect your account pleease try again"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:636
#: views/settings/wcfm-view-wcmarketplace-settings.php:552
msgid "You are not connected with stripe."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:658
#: views/settings/wcfm-view-wcmarketplace-settings.php:574
msgid "Please connected with stripe again."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:674
msgid "Stripe not setup properly, please contact your site admin."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:711
msgid "SEO Title"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:711
msgid "SEO Title is shown as the title of your store page"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:712
msgid "Meta Description"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:712
msgid ""
"The meta description is often shown as the black text under the title in a "
"search result. For this to work it has to contain the keyword that was "
"searched for and should be less than 156 chars."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:713
msgid "Meta Keywords"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:713
msgid ""
"Insert some comma separated keywords for better ranking of your store page."
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:714
msgid "Facebook Title"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:715
msgid "Facebook Description"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:716
msgid "Facebook Image"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:717
msgid "Twitter Title"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:718
msgid "Twitter Description"
msgstr ""

#: views/settings/wcfm-view-wcfmmarketplace-settings.php:719
msgid "Twitter Image"
msgstr ""

#: views/settings/wcfm-view-wcmarketplace-settings.php:172
msgid "Preferred logo should be 200x200 px."
msgstr "Das bevorzugte Logo sollte 200 x 200 Pixel groß sein."

#: views/settings/wcfm-view-wcmarketplace-settings.php:173
#: views/settings/wcfm-view-wcpvendors-settings.php:110
#: views/settings/wcfm-view-wcvendors-settings.php:157
msgid "Your shop name is public and must be unique."
msgstr "Ihr Shop Name ist öffentlich und muss eindeutig sein."

#: views/settings/wcfm-view-wcmarketplace-settings.php:174
msgid "Shop Slug"
msgstr "Ihre Shop-Slug ist öffentlich und muss einzigartig sein"

#: views/settings/wcfm-view-wcmarketplace-settings.php:174
msgid "Your shop slug is public and must be unique."
msgstr "Ihre Shop-Slug ist öffentlich und muss einzigartig sein."

#: views/settings/wcfm-view-wcmarketplace-settings.php:201
#: views/settings/wcfm-view-wcvendors-settings.php:187
msgid "Branding"
msgstr "Branding"

#: views/settings/wcfm-view-wcmarketplace-settings.php:207
msgid "Preferred banner should be 1200x245 px."
msgstr "Das bevorzugte Banner sollte 1200 x 245 px sein."

#: views/settings/wcfm-view-wcmarketplace-settings.php:209
msgid "Shop Phone"
msgstr "Shop Telefonnummer"

#: views/settings/wcfm-view-wcmarketplace-settings.php:209
msgid "Your store phone no."
msgstr "Ihre Shop Telefonnummer."

#: views/settings/wcfm-view-wcmarketplace-settings.php:244
#: views/settings/wcfm-view-wcmarketplace-settings.php:245
#: views/settings/wcfm-view-wcpvendors-settings.php:130
#: views/settings/wcfm-view-wcpvendors-settings.php:131
msgid "Timezone"
msgstr "Zeitzone"

#: views/settings/wcfm-view-wcmarketplace-settings.php:244
#: views/settings/wcfm-view-wcpvendors-settings.php:130
msgid "Set the local timezone."
msgstr "Lokale Zeitzone festlegen."

#: views/settings/wcfm-view-wcmarketplace-settings.php:270
msgid "Please contact your administrator to enable Google map feature."
msgstr ""
"Bitte kontaktieren Sie Ihren Administrator, um diese Funktion zu verwenden."

#: views/settings/wcfm-view-wcmarketplace-settings.php:278
msgid "Shop Template"
msgstr "Shop Vorlage"

#: views/settings/wcfm-view-wcmarketplace-settings.php:311
msgid "Account Type"
msgstr "Kontotyp"

#: views/settings/wcfm-view-wcmarketplace-settings.php:311
msgid "Current"
msgstr "Aktuell"

#: views/settings/wcfm-view-wcmarketplace-settings.php:311
msgid "Savings"
msgstr "Ersparnisse"

#: views/settings/wcfm-view-wcmarketplace-settings.php:314
msgid "ABA Routing Number"
msgstr "Bankleitzahl"

#: views/settings/wcfm-view-wcmarketplace-settings.php:316
msgid "Destination Currency"
msgstr "Zielwährung"

#: views/settings/wcfm-view-wcmarketplace-settings.php:317
msgid "Account IBAN"
msgstr "Kontonummer (IBAN)"

#: views/settings/wcfm-view-wcmarketplace-settings.php:318
msgid "Account Holder Name"
msgstr "Name des Kontoinhabers"

#: views/settings/wcfm-view-wcmarketplace-settings.php:635
#: views/settings/wcfm-view-wcmarketplace-settings.php:644
msgid "Shipping Zone"
msgstr "Versand Zone"

#: views/settings/wcfm-view-wcmarketplace-settings.php:665
msgid "Shipping Rules"
msgstr "Versandbedingungen"

#: views/settings/wcfm-view-wcmarketplace-settings.php:684
msgid ""
"There is no shipping zone or Flat Rate shipping method not associated for "
"the zones to set shipping prices, kindly contact your Store Admin."
msgstr ""

#: views/settings/wcfm-view-wcpvendors-settings.php:111
msgid "Vendor Email"
msgstr "Verkäufer E-Mail-Adresse"

#: views/settings/wcfm-view-wcpvendors-settings.php:111
msgid ""
"Enter the email for this vendor. This is the email where all notifications "
"will be send such as new orders and customer inquiries. You may enter more "
"than one email separating each with a comma."
msgstr ""
"Gib eine E-Mail-Adresse für diesen Verkäufer ein. An diese E-Mail-Adresse "
"werden alle Benachrichtigungen wie neue Bestellungen und Kundenanfragen "
"gesendet. Du kannst mehr als eine E-Mail-Adresse eingeben, die jeweils durch "
"ein Komma getrennt ist."

#: views/settings/wcfm-view-wcpvendors-settings.php:112
msgid "Enter the profile information you would like for customer to see."
msgstr ""
"Geben Sie bitte die Profilinformationen ein, die für Kunden sichtbar sein "
"werden."

#: views/settings/wcfm-view-wcpvendors-settings.php:180
msgid "Paypal Email"
msgstr "Paypal E-Mail-Adresse"

#: views/settings/wcfm-view-wcpvendors-settings.php:180
msgid "PayPal email account where you will receive your commission."
msgstr "PayPal Emailadresse für die Auszahlung Ihrer Umsätze."

#: views/settings/wcfm-view-wcpvendors-settings.php:181
msgid ""
"Default commission you will receive per product sale. Please note product "
"level commission can override this. Check your product to confirm."
msgstr ""
"Standardprovision, die Sie pro Verkauf erhalten. Bitte beachte, dass der "
"Prozentsatz pro Produkt unterschiedlich sein kann. Prüfen Sie ihr Produkt, "
"um dies zu bestätigen."

#: views/settings/wcfm-view-wcvendors-settings.php:158
#: views/vendors/wcfm-view-vendors-manage.php:293
msgid "Seller Info"
msgstr "Verkäufer Info"

#: views/settings/wcfm-view-wcvendors-settings.php:158
msgid "This is displayed on each of your products."
msgstr "Dies wird bei jedem Ihrer Produkte angezeigt."

#: views/settings/wcfm-view-wcvendors-settings.php:196
msgid "Store Website / Blog URL"
msgstr "Shop Website / Blog URL"

#: views/settings/wcfm-view-wcvendors-settings.php:196
msgid "Your company/blog URL here."
msgstr "Ihre Firmen-/Blog-URL hier."

#: views/settings/wcfm-view-wcvendors-settings.php:197
msgid "This is your store contact number."
msgstr "Dies ist Ihre Shop Kontakttelefonnummer."

#: views/settings/wcfm-view-wcvendors-settings.php:232
#: views/settings/wcfm-view-wcvendors-settings.php:420
msgid "WCV Pro Settings"
msgstr "WCV Pro Einstellungen"

#: views/settings/wcfm-view-wcvendors-settings.php:252
msgid "Your PayPal address is used to send you your commission."
msgstr "Ihre PayPal-Adresse wird verwendet um Ihnen Ihre Provision zu senden."

#: views/settings/wcfm-view-wcvendors-settings.php:305
msgid "Bank Payment (Mangopay)"
msgstr "Banküberweisung (Mangopay)"

#: views/settings/wcfm-view-wcvendors-settings.php:318
msgid "CHECKING"
msgstr "Checken"

#: views/settings/wcfm-view-wcvendors-settings.php:318
msgid "SAVINGS"
msgstr "Ersparnisse"

#: views/settings/wcfm-view-wcvendors-settings.php:368
#: views/settings/wcfm-view-wcvendors-settings.php:375
msgid "Charge once per product"
msgstr "Nur einmal pro Produkt berechnen"

#: views/settings/wcfm-view-wcvendors-settings.php:384
msgid "Postcode"
msgstr "Postleitzahl"

#: views/settings/wcfm-view-wcvendors-settings.php:385
msgid "Shipping Fee"
msgstr "Versandkosten"

#: views/settings/wcfm-view-wcvendors-settings.php:386
msgid "Override Qty"
msgstr "Menge überschreiben"

#: views/settings/wcfm-view-wcvendors-settings.php:392
msgid "Min Charge Order"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:392
msgid "The minimum shipping fee charged for an order."
msgstr "Die Mindestversandgebühr, die für eine Bestellung berechnet wird."

#: views/settings/wcfm-view-wcvendors-settings.php:393
msgid "Max Charge Order"
msgstr "Max. Gebührenordnung"

#: views/settings/wcfm-view-wcvendors-settings.php:393
msgid "The maximum shipping fee charged for an order."
msgstr "Die maximale Versandkostenpauschale für eine Bestellung."

#: views/settings/wcfm-view-wcvendors-settings.php:394
msgid "Free Shipping Order"
msgstr "Versandkostenfreie Bestellung"

#: views/settings/wcfm-view-wcvendors-settings.php:394
msgid ""
"Free shipping for order spends over this amount. This will override the max "
"shipping charge above."
msgstr ""
"Kostenloser Versand, wenn die Bestellung über diesem Betrag ist. Dies "
"überschreibt die max. Versandkosten oben."

#: views/settings/wcfm-view-wcvendors-settings.php:395
msgid "Max Charge Product"
msgstr ""

#: views/settings/wcfm-view-wcvendors-settings.php:395
msgid "The maximum shipping charged per product no matter the quantity."
msgstr ""
"Die maximalen Versandkosten berechnet pro Produkt unabhängig von der Menge."

#: views/settings/wcfm-view-wcvendors-settings.php:396
msgid "Free Shipping Product"
msgstr "Versandkostenfreier Produkt"

#: views/settings/wcfm-view-wcvendors-settings.php:396
msgid ""
"Free shipping if the spend per product is over this amount. This will "
"override the max shipping charge above."
msgstr ""
"Kostenloser Versand, wenn die Ausgaben pro Produkt über diesem Betrag ist. "
"Dies überschreibt die max. Versandkosten oben."

#: views/settings/wcfm-view-wcvendors-settings.php:397
msgid "Product handling fee"
msgstr "Produkt-Bearbeitungsgebühr"

#: views/settings/wcfm-view-wcvendors-settings.php:397
msgid "Leave empty to disable"
msgstr "'Leer lassen' deaktivieren"

#: views/settings/wcfm-view-wcvendors-settings.php:397
msgid ""
"The product handling fee, this can be overridden on a per product basis. "
"Amount (5.00) or Percentage (5%)."
msgstr ""
"Die Bearbeitungsgebühr, dies kann auf Produktbasis überschrieben werden. "
"Betrag (5.00) oder Prozentsatz (5 %)."

#: views/settings/wcfm-view-wcvendors-settings.php:404
msgid "From Address"
msgstr "Absender-Adresse"

#: views/vendors/wcfm-view-vendors-manage.php:145
#: views/vendors/wcfm-view-vendors-new.php:62
msgid "Manage Vendor"
msgstr "Verkäufer verwalten"

#: views/vendors/wcfm-view-vendors-manage.php:189
#: views/vendors/wcfm-view-vendors-new.php:75
#: views/vendors/wcfm-view-vendors.php:28
msgid "Add New Vendor"
msgstr ""

#: views/vendors/wcfm-view-vendors-manage.php:241
#, php-format
msgid "<strong>%s product</strong><br />"
msgid_plural "<strong>%s products</strong><br />"
msgstr[0] "<strong>%s produkt</strong><br />"
msgstr[1] "<strong>%s produkte</strong><br />"

#: views/vendors/wcfm-view-vendors-manage.php:243
msgid "total products posted"
msgstr "Gesamt Produkte veröffentlicht"

#: views/vendors/wcfm-view-vendors-manage.php:255
#, php-format
msgid "<strong>%s item</strong><br />"
msgid_plural "<strong>%s items</strong><br />"
msgstr[0] "<strong>%s Produkt</strong><br />"
msgstr[1] "<strong>%s Produkt</strong><br />"

#: views/vendors/wcfm-view-vendors-manage.php:279
#: views/vendors/wcfm-view-vendors-manage.php:281
msgid "Store Admin"
msgstr "Shop-Admin"

#: views/vendors/wcfm-view-vendors-manage.php:321
msgid "Profile Update"
msgstr "Profilaktualisierung"

#: views/vendors/wcfm-view-vendors-manage.php:371
msgid "Vendor not yet subscribed for a membership!"
msgstr "Verkäufer hat noch keine Mitgliedschaft abonniert!"

#: views/vendors/wcfm-view-vendors-manage.php:384
msgid "Send Message"
msgstr "Nachricht senden"

#: views/vendors/wcfm-view-vendors-new.php:69
msgid "Edit Vendor"
msgstr ""

#: views/vendors/wcfm-view-vendors-new.php:69
msgid "Add Vendor"
msgstr ""

#: views/vendors/wcfm-view-vendors.php:25
msgid "Vendors Listing"
msgstr "Verkäufer Listen"

#: views/vendors/wcfm-view-vendors.php:29
msgid "Pending Vendors"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:52
#: views/wc_bookings/wcfm-view-wcbookings.php:70
msgid "Create Booking"
msgstr "Buchung erstellen"

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:64
#: views/wc_bookings/wcfm-view-wcbookings-details.php:76
#: views/wc_bookings/wcfm-view-wcbookings.php:81
msgid "Create Bookable"
msgstr "Buchung erstellen"

#: views/wc_bookings/wcfm-view-wcbookings-dashboard.php:81
#: views/wc_bookings/wcfm-view-wcbookings-details.php:71
#: views/wc_bookings/wcfm-view-wcbookings.php:76
msgid "Manage Resources"
msgstr "Ressourcen verwalten"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:45
msgid "Booking Details"
msgstr "Buchungsdetails"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:52
msgid "Booking #"
msgstr "Buchung #"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:63
#: views/wc_bookings/wcfm-view-wcbookings.php:86
msgid "Calendar View"
msgstr "Kalender Ansicht"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:87
msgid "Overview"
msgstr "Übersicht"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:93
msgid "Booking Created:"
msgstr "Buchung erstellt:"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:98
msgid "Order Number:"
msgstr "Bestellnummer:"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:130
msgid "Confirm"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:131
msgid "Decline"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:167
msgid "Resource:"
msgstr ""

#: views/wc_bookings/wcfm-view-wcbookings-details.php:346
msgid "Billing Email:"
msgstr "Rechnung Email:"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:352
msgid "Billing Phone:"
msgstr "Rechnung Telefon:"

#: views/wc_bookings/wcfm-view-wcbookings-details.php:363
msgid "View Order"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:88
#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:80
msgid "PayPal"
msgstr "PayPal"

#: controllers/withdrawal/dokan/wcfm-controller-payments.php:92
msgid "Bank Transfer"
msgstr "Bank Überweisung"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:61
#: controllers/withdrawal/wcmp/wcfm-controller-withdrawal-request.php:46
msgid "Request successfully sent"
msgstr "Anfrage erfolgreich abgeschickt"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-request.php:63
msgid "Something went wrong please try again later"
msgstr "Irgendwas ging schief. Bitte versuchen Sie es später erneut"

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:57
msgid "Seller account balance not enough for this withdrawal."
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:106
msgid "Withdrawal Requests successfully approved."
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:108
msgid "No withdrawals selected for approve."
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:170
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:109
msgid "Withdrawal Requests successfully cancelled."
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests-actions.php:172
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:111
msgid "No withdrawals selected for cancel."
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:77
msgid "Withdrawal Approved"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:79
#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:100
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:95
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse.php:91
msgid "Withdrawal Cancelled"
msgstr ""

#: controllers/withdrawal/dokan/wcfm-controller-withdrawal-requests.php:81
msgid "Withdrawal Pending"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:98
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:93
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse.php:89
msgid "Withdrawal Completed"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:102
#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests.php:97
msgid "Withdrawal Processing"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:153
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:162
msgid "Auto Withdrawal"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:156
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:165
msgid "By Payment Type"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:158
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:167
msgid "By Request"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-payments.php:160
msgid "Split Pay"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:83
msgid "Withdrawal Request successfully processed."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:85
msgid "Withdrawal Request processing failed, please contact Store Admin."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:90
#, php-format
msgid "Vendor <b>%s</b> has placed a Withdrawal Request #%s."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:92
msgid "Your withdrawal request successfully sent."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:97
msgid "Your withdrawal request failed, please try after sometime."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:100
msgid "No payment method selected for withdrawal commission"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-request.php:103
msgid "No commission selected for withdrawal"
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:59
msgid "Withdrawal Requests successfully processed."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:61
msgid "Withdrawal Requests partially processed, check log for more details."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-requests-actions.php:64
msgid "No withdrawals selected for approval."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse-actions.php:46
msgid "Reverse Withdrawal Requests successfully approveed."
msgstr ""

#: controllers/withdrawal/wcfm/wcfm-controller-withdrawal-reverse-actions.php:48
msgid "No reverse withdrawals selected for approve."
msgstr ""

#: controllers/withdrawal/wcmp/wcfm-controller-payments.php:84
msgid "Direct Bank Transfer"
msgstr "Banküberweisung"

#: includes/libs/php/class-wcfm-fields.php:649
#: includes/libs/php/class-wcfm-fields.php:653
msgid "-Select a location-"
msgstr "-Ort auswählen-"

#: views/withdrawal/dokan/wcfm-view-payments.php:34
#: views/withdrawal/wcfm/wcfm-view-payments.php:36
#: views/withdrawal/wcmp/wcfm-view-payments.php:34
msgid "Transactions for: "
msgstr "Transaktionen für : "

#: views/withdrawal/dokan/wcfm-view-payments.php:51
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:37
#: views/withdrawal/wcfm/wcfm-view-payments.php:53
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:44
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:42
msgid "Show all .."
msgstr "Alle Anzeigen..."

#: views/withdrawal/dokan/wcfm-view-payments.php:52
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:38
#: views/withdrawal/wcfm/wcfm-view-payments.php:54
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:45
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:43
msgid "Approved"
msgstr "Genehmigt"

#: views/withdrawal/dokan/wcfm-view-payments.php:53
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:39
#: views/withdrawal/wcfm/wcfm-view-payments.php:55
msgid "Processing"
msgstr "In Bearbeitung"

#: views/withdrawal/dokan/wcfm-view-payments.php:69
#: views/withdrawal/dokan/wcfm-view-payments.php:78
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:159
#: views/withdrawal/wcmp/wcfm-view-payments.php:68
#: views/withdrawal/wcmp/wcfm-view-payments.php:79
msgid "Pay Mode"
msgstr "Zahlungsmethode"

#: views/withdrawal/dokan/wcfm-view-payments.php:70
#: views/withdrawal/dokan/wcfm-view-payments.php:79
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:56
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:66
#: views/withdrawal/wcfm/wcfm-view-payments.php:76
#: views/withdrawal/wcfm/wcfm-view-payments.php:90
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:76
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:92
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:70
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:82
msgid "Note"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:52
#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:62
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:64
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:76
msgid "Requests"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:80
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:106
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:96
msgid "Note to Vendor(s)"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:87
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:113
msgid "Cancel"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal-requests.php:88
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:114
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:103
msgid "Approve"
msgstr ""

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:64
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:113
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:57
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:53
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:42
msgid "Transaction History"
msgstr "Transaktionsverlauf"

#: views/withdrawal/dokan/wcfm-view-withdrawal.php:105
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:112
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:88
msgid "Request"
msgstr "Anfordern"

#: views/withdrawal/wcfm/wcfm-view-payments.php:69
#: views/withdrawal/wcfm/wcfm-view-payments.php:83
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:69
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:85
#: views/withdrawal/wcmp/wcfm-view-payments.php:64
#: views/withdrawal/wcmp/wcfm-view-payments.php:75
msgid "Transc.ID"
msgstr "Transaktions-ID"

#: views/withdrawal/wcfm/wcfm-view-payments.php:70
#: views/withdrawal/wcfm/wcfm-view-payments.php:84
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:70
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:86
msgid "Order IDs"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-payments.php:71
#: views/withdrawal/wcfm/wcfm-view-payments.php:85
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:75
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:91
#: views/withdrawal/wcmp/wcfm-view-payments.php:65
#: views/withdrawal/wcmp/wcfm-view-payments.php:76
msgid "Commission IDs"
msgstr "Kommissions-IDs"

#: views/withdrawal/wcfm/wcfm-view-payments.php:73
#: views/withdrawal/wcfm/wcfm-view-payments.php:87
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:73
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:89
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:78
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:91
msgid "Charges"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-payments.php:75
#: views/withdrawal/wcfm/wcfm-view-payments.php:89
msgid "Mode"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:84
msgid "Payment Received Email"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:85
#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:88
msgid "Transaction ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:86
msgid "Transaction Status"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:87
msgid "Request ID"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:89
msgid "Transaction Ref"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:90
msgid "Transfer Mode"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:104
#: views/withdrawal/wcmp/wcfm-view-transaction-details.php:43
msgid "Transaction #"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:136
msgid "Order ID(s)"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:137
msgid "Commission ID(s)"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:138
msgid "Payment Method"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:169
msgid "By Split Pay"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:175
msgid "Total Amount"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:179
msgid "Withdrawal Charges"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:183
msgid "Paid Amount"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-transaction-details.php:183
msgid "Payable Amount"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:35
msgid "Reverse Withdrawal"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:67
#: views/withdrawal/wcfm/wcfm-view-withdrawal-requests.php:83
msgid "Select all to approve or cancel"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:25
msgid "Reverse Withdrawals"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:32
msgid "Reverse Withdrawal Requests"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:65
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:77
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:75
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:88
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:58
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:67
msgid "Order ID"
msgstr "Bestellnummer"

#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:69
#: views/withdrawal/wcfm/wcfm-view-withdrawal-reverse.php:81
msgid "Balance"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:42
msgid "Pending Withdrawals: "
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:46
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:36
msgid "Threshold for withdrawals: "
msgstr "Erforderlicher Kontostand zur Auszahlung: "

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:51
msgid "Reverse pay balance "
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:52
#, php-format
msgid "Thresold Limit: %s"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:73
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:86
msgid "Select all to send request"
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:76
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:89
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:59
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:68
msgid "Commission ID"
msgstr "Kommission ID"

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:77
#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:90
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:60
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:69
msgid "My Earnings"
msgstr "Meine Einnahmen"

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:102
msgid ""
"Withdrawal charges will be re-calculated depending upon total withdrawal "
"amount."
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:114
msgid "Withdrawal disable due to high reverse balance."
msgstr ""

#: views/withdrawal/wcfm/wcfm-view-withdrawal.php:117
msgid "Withdrawal disable due to low account balance."
msgstr ""

#: views/withdrawal/wcmp/wcfm-view-payments.php:67
#: views/withdrawal/wcmp/wcfm-view-payments.php:78
msgid "Net Earnings"
msgstr "Nettoeinnahmen"

#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:57
#: views/withdrawal/wcmp/wcfm-view-withdrawal.php:66
msgid "Send Request"
msgstr "Anfrage abschicken"

#. Name of the plugin
msgid "WooCommerce Frontend Manager"
msgstr "WooCommerce-Frontend-Manager"

#. Description of the plugin
msgid ""
"WooCommerce is really Easy and Beautiful. We are here to make your life much "
"more Easier and Peaceful."
msgstr ""
"WooCommerce ist wirklich einfach und gut. Mit unserem Frontend noch "
"einfacher und übersichtlicher."

#. Author URI of the plugin
msgid "https://wclovers.com"
msgstr "https://wclovers.com"

#. Author of the plugin
msgid "WC Lovers"
msgstr "WC Lovers"
