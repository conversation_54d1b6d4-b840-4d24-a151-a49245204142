<?php
/**
 * Vendor Database Manager
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Database manager class
 */
class Vendor_Database {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'check_version'));
    }
    
    /**
     * Check database version and update if needed
     */
    public function check_version() {
        $current_version = get_option('vendor_db_version', '0');
        
        if (version_compare($current_version, VENDOR_VERSION, '<')) {
            $this->create_tables();
            update_option('vendor_db_version', VENDOR_VERSION);
        }
    }
    
    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Vendors table
        $vendors_table = $wpdb->prefix . 'vendor_vendors';
        $vendors_sql = "CREATE TABLE $vendors_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            store_name varchar(255) NOT NULL,
            store_slug varchar(255) NOT NULL,
            store_description longtext,
            store_logo varchar(255),
            store_banner varchar(255),
            store_address longtext,
            store_phone varchar(50),
            store_email varchar(100),
            commission_rate decimal(5,2) DEFAULT 10.00,
            status varchar(20) DEFAULT 'pending',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_id (user_id),
            UNIQUE KEY store_slug (store_slug),
            KEY status (status)
        ) $charset_collate;";
        
        // Commissions table
        $commissions_table = $wpdb->prefix . 'vendor_commissions';
        $commissions_sql = "CREATE TABLE $commissions_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            vendor_id bigint(20) NOT NULL,
            order_id bigint(20) NOT NULL,
            product_id bigint(20) NOT NULL,
            order_item_id bigint(20) NOT NULL,
            gross_amount decimal(10,2) NOT NULL,
            commission_amount decimal(10,2) NOT NULL,
            commission_rate decimal(5,2) NOT NULL,
            admin_fee decimal(10,2) DEFAULT 0.00,
            tax_amount decimal(10,2) DEFAULT 0.00,
            status varchar(20) DEFAULT 'pending',
            withdrawal_id bigint(20) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY vendor_id (vendor_id),
            KEY order_id (order_id),
            KEY product_id (product_id),
            KEY status (status),
            KEY withdrawal_id (withdrawal_id)
        ) $charset_collate;";
        
        // Withdrawals table
        $withdrawals_table = $wpdb->prefix . 'vendor_withdrawals';
        $withdrawals_sql = "CREATE TABLE $withdrawals_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            vendor_id bigint(20) NOT NULL,
            amount decimal(10,2) NOT NULL,
            charges decimal(10,2) DEFAULT 0.00,
            net_amount decimal(10,2) NOT NULL,
            payment_method varchar(50) NOT NULL,
            payment_details longtext,
            status varchar(20) DEFAULT 'pending',
            note longtext,
            transaction_id varchar(255),
            requested_at datetime DEFAULT CURRENT_TIMESTAMP,
            processed_at datetime DEFAULT NULL,
            PRIMARY KEY (id),
            KEY vendor_id (vendor_id),
            KEY status (status),
            KEY requested_at (requested_at)
        ) $charset_collate;";
        
        // Vendor settings table
        $settings_table = $wpdb->prefix . 'vendor_settings';
        $settings_sql = "CREATE TABLE $settings_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            vendor_id bigint(20) NOT NULL,
            setting_key varchar(255) NOT NULL,
            setting_value longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY vendor_setting (vendor_id, setting_key),
            KEY vendor_id (vendor_id)
        ) $charset_collate;";
        
        // Vendor analytics table
        $analytics_table = $wpdb->prefix . 'vendor_analytics';
        $analytics_sql = "CREATE TABLE $analytics_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            vendor_id bigint(20) NOT NULL,
            date date NOT NULL,
            orders_count int(11) DEFAULT 0,
            products_sold int(11) DEFAULT 0,
            gross_sales decimal(10,2) DEFAULT 0.00,
            commission_earned decimal(10,2) DEFAULT 0.00,
            visitors_count int(11) DEFAULT 0,
            page_views int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY vendor_date (vendor_id, date),
            KEY vendor_id (vendor_id),
            KEY date (date)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        dbDelta($vendors_sql);
        dbDelta($commissions_sql);
        dbDelta($withdrawals_sql);
        dbDelta($settings_table);
        dbDelta($analytics_sql);
        
        // Create indexes
        $this->create_indexes();
    }
    
    /**
     * Create additional indexes
     */
    private function create_indexes() {
        global $wpdb;
        
        // Add any additional indexes here if needed
    }
    
    /**
     * Get vendors table name
     */
    public static function get_vendors_table() {
        global $wpdb;
        return $wpdb->prefix . 'vendor_vendors';
    }
    
    /**
     * Get commissions table name
     */
    public static function get_commissions_table() {
        global $wpdb;
        return $wpdb->prefix . 'vendor_commissions';
    }
    
    /**
     * Get withdrawals table name
     */
    public static function get_withdrawals_table() {
        global $wpdb;
        return $wpdb->prefix . 'vendor_withdrawals';
    }
    
    /**
     * Get settings table name
     */
    public static function get_settings_table() {
        global $wpdb;
        return $wpdb->prefix . 'vendor_settings';
    }
    
    /**
     * Get analytics table name
     */
    public static function get_analytics_table() {
        global $wpdb;
        return $wpdb->prefix . 'vendor_analytics';
    }
}
