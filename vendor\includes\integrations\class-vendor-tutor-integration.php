<?php
/**
 * Vendor Tutor LMS Integration
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Tutor LMS integration class
 */
class Vendor_Tutor_Integration {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Only initialize if Tutor LMS is active
        if (!class_exists('TUTOR\Tutor')) {
            return;
        }
        
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Initialize integration
     */
    public function init() {
        // Hook into course creation
        add_action('tutor_course_created', array($this, 'assign_course_to_vendor'));
        
        // Hook into course enrollment
        add_action('tutor_after_enrolled', array($this, 'process_course_commission'), 10, 2);
        
        // Add vendor meta to courses
        add_action('add_meta_boxes', array($this, 'add_course_vendor_meta_box'));
        
        // Filter course queries for vendors
        add_action('pre_get_posts', array($this, 'filter_vendor_courses'));
        
        // Add course management capabilities to vendor role
        add_action('init', array($this, 'add_course_capabilities'));
    }
    
    /**
     * Add course capabilities to vendor role
     */
    public function add_course_capabilities() {
        $vendor_role = get_role('vendor');
        
        if ($vendor_role) {
            $vendor_role->add_cap('edit_tutor_courses');
            $vendor_role->add_cap('read_tutor_courses');
            $vendor_role->add_cap('delete_tutor_courses');
            $vendor_role->add_cap('edit_published_tutor_courses');
            $vendor_role->add_cap('delete_published_tutor_courses');
            $vendor_role->add_cap('edit_tutor_lessons');
            $vendor_role->add_cap('read_tutor_lessons');
            $vendor_role->add_cap('delete_tutor_lessons');
            $vendor_role->add_cap('edit_tutor_quizzes');
            $vendor_role->add_cap('read_tutor_quizzes');
            $vendor_role->add_cap('delete_tutor_quizzes');
        }
    }
    
    /**
     * Assign course to vendor on creation
     */
    public function assign_course_to_vendor($course_id) {
        if (!vendor()->vendor_manager->is_vendor()) {
            return;
        }
        
        $vendor = vendor()->vendor_manager->get_current_vendor();
        if ($vendor) {
            update_post_meta($course_id, '_vendor_id', $vendor->id);
            update_post_meta($course_id, '_course_vendor', $vendor->id); // Tutor compatibility
        }
    }
    
    /**
     * Process course commission on enrollment
     */
    public function process_course_commission($course_id, $student_id) {
        $vendor_id = get_post_meta($course_id, '_vendor_id', true);
        
        if (!$vendor_id) {
            return;
        }
        
        $vendor = vendor()->vendor_manager->get_vendor($vendor_id);
        if (!$vendor) {
            return;
        }
        
        // Get course price
        $course_price = get_post_meta($course_id, '_tutor_course_price', true);
        if (!$course_price || $course_price <= 0) {
            return; // Free course, no commission
        }
        
        // Create a virtual order for commission tracking
        $order_id = $this->create_virtual_order($course_id, $student_id, $course_price);
        
        if ($order_id) {
            // Create commission
            $this->create_course_commission($vendor_id, $order_id, $course_id, $course_price);
        }
    }
    
    /**
     * Create virtual order for course enrollment
     */
    private function create_virtual_order($course_id, $student_id, $price) {
        global $wpdb;
        
        $commissions_table = Vendor_Database::get_commissions_table();
        
        // Check if commission already exists for this enrollment
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $commissions_table WHERE product_id = %d AND order_id LIKE %s",
            $course_id,
            'course_enrollment_%'
        ));
        
        if ($existing) {
            return false; // Commission already created
        }
        
        // Create virtual order ID
        $virtual_order_id = 'course_enrollment_' . $course_id . '_' . $student_id . '_' . time();
        
        return $virtual_order_id;
    }
    
    /**
     * Create course commission
     */
    private function create_course_commission($vendor_id, $order_id, $course_id, $price) {
        global $wpdb;
        
        $vendor = vendor()->vendor_manager->get_vendor($vendor_id);
        if (!$vendor) {
            return false;
        }
        
        $table = Vendor_Database::get_commissions_table();
        
        // Calculate commission
        $commission_rate = $vendor->commission_rate;
        $commission_amount = ($price * $commission_rate) / 100;
        $admin_fee = $price - $commission_amount;
        
        $data = array(
            'vendor_id' => $vendor_id,
            'order_id' => $order_id,
            'product_id' => $course_id,
            'order_item_id' => 0, // No item ID for courses
            'gross_amount' => $price,
            'commission_amount' => $commission_amount,
            'commission_rate' => $commission_rate,
            'admin_fee' => $admin_fee,
            'status' => 'approved' // Auto-approve course commissions
        );
        
        $result = $wpdb->insert($table, $data);
        
        if ($result) {
            $commission_id = $wpdb->insert_id;
            do_action('vendor_course_commission_created', $commission_id, $vendor_id, $course_id);
            return $commission_id;
        }
        
        return false;
    }
    
    /**
     * Add vendor meta box to courses
     */
    public function add_course_vendor_meta_box() {
        add_meta_box(
            'vendor-course-meta',
            __('Vendor Information', 'vendor'),
            array($this, 'course_vendor_meta_box_callback'),
            'courses',
            'side',
            'default'
        );
    }
    
    /**
     * Course vendor meta box callback
     */
    public function course_vendor_meta_box_callback($post) {
        $vendor_id = get_post_meta($post->ID, '_vendor_id', true);
        $vendor = $vendor_id ? vendor()->vendor_manager->get_vendor($vendor_id) : null;
        
        echo '<p>';
        if ($vendor) {
            printf(__('Vendor: %s', 'vendor'), esc_html($vendor->store_name));
        } else {
            _e('No vendor assigned', 'vendor');
        }
        echo '</p>';
    }
    
    /**
     * Filter courses for vendors
     */
    public function filter_vendor_courses($query) {
        if (!is_admin() || !$query->is_main_query()) {
            return;
        }
        
        if (!vendor()->vendor_manager->is_vendor()) {
            return;
        }
        
        $post_type = $query->get('post_type');
        if ($post_type !== 'courses') {
            return;
        }
        
        $vendor = vendor()->vendor_manager->get_current_vendor();
        if ($vendor) {
            $query->set('meta_key', '_vendor_id');
            $query->set('meta_value', $vendor->id);
        }
    }
    
    /**
     * Get vendor courses
     */
    public function get_vendor_courses($vendor_id, $args = array()) {
        $defaults = array(
            'post_type' => 'courses',
            'post_status' => 'any',
            'meta_key' => '_vendor_id',
            'meta_value' => $vendor_id,
            'posts_per_page' => 20,
            'paged' => 1
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $query = new WP_Query($args);
        
        $courses = array();
        foreach ($query->posts as $course_post) {
            $course_data = array(
                'id' => $course_post->ID,
                'title' => $course_post->post_title,
                'slug' => $course_post->post_name,
                'status' => $course_post->post_status,
                'date_created' => $course_post->post_date,
                'date_modified' => $course_post->post_modified,
                'price' => get_post_meta($course_post->ID, '_tutor_course_price', true),
                'students_count' => tutor_utils()->count_enrolled_users_by_course($course_post->ID),
                'lessons_count' => tutor_utils()->get_lesson_count_by_course($course_post->ID),
                'thumbnail' => get_the_post_thumbnail_url($course_post->ID, 'medium'),
                'permalink' => get_permalink($course_post->ID)
            );
            
            $courses[] = $course_data;
        }
        
        return array(
            'courses' => $courses,
            'total' => $query->found_posts,
            'pages' => $query->max_num_pages,
            'current_page' => $args['paged']
        );
    }
    
    /**
     * Get course analytics for vendor
     */
    public function get_course_analytics($vendor_id, $period = 'month') {
        global $wpdb;
        
        $commissions_table = Vendor_Database::get_commissions_table();
        
        $date_condition = '';
        switch ($period) {
            case 'week':
                $date_condition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)';
                break;
            case 'month':
                $date_condition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)';
                break;
            case 'year':
                $date_condition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)';
                break;
        }
        
        $query = $wpdb->prepare(
            "SELECT 
                COUNT(*) as enrollments,
                SUM(gross_amount) as total_revenue,
                SUM(commission_amount) as total_commission
            FROM $commissions_table 
            WHERE vendor_id = %d 
            AND order_id LIKE 'course_enrollment_%'
            $date_condition",
            $vendor_id
        );
        
        return $wpdb->get_row($query);
    }
    
    /**
     * Get vendor students
     */
    public function get_vendor_students($vendor_id) {
        global $wpdb;
        
        // Get all courses by this vendor
        $course_ids = get_posts(array(
            'post_type' => 'courses',
            'meta_key' => '_vendor_id',
            'meta_value' => $vendor_id,
            'posts_per_page' => -1,
            'fields' => 'ids'
        ));
        
        if (empty($course_ids)) {
            return array();
        }
        
        $course_ids_str = implode(',', $course_ids);
        
        // Get enrolled students
        $students = $wpdb->get_results(
            "SELECT DISTINCT u.ID, u.display_name, u.user_email, u.user_registered
            FROM {$wpdb->users} u
            INNER JOIN {$wpdb->usermeta} um ON u.ID = um.user_id
            WHERE um.meta_key = '_tutor_enrolled_course_ids_for_user'
            AND um.meta_value REGEXP '(^|,)($course_ids_str)(,|$)'"
        );
        
        return $students;
    }
}
