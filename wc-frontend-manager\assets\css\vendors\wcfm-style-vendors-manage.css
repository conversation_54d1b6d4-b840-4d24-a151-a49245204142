.wcfm-collapse .wcfm-container .wcfm-content {
	min-height: 100px;
}

#wcfm-main-contentainer #vendors_manage_general_expander input[type="text"].wcfm-text, 
#wcfm-main-contentainer #wcfm_vendor_manage_form_verification_expander input[type="text"].wcfm-text, 
#wcfm-main-contentainer #vendors_manage_general_expander textarea {
	border: 0px !important;
}

#wcfm-main-contentainer #vendors_manage_general_expander textarea {
	resize: vertical;
}

.wcfmvm_verification_details {
	display: table;
	width: 99%;
}

.wcfmvm_verification_address_details, .wcfmvm_verification_identity_details {
	display: table-cell;
	width: 49%;
}

.wcfmvm_verification_details .button {
	visibility: hidden !important;
}

.wcfmvm_change_next_renewal { 
	margin-left: 10px;
	cursor: pointer;
	color: #17a2b8;
}

#wcfm-main-contentainer input[type="checkbox"].wcfm-checkbox {
	margin-right: 40%;
}

#wcfm-main-contentainer img.vendor_store_logo {
	display: table-cell;
	height: 40px;
	float: left;
	margin-right: 10px;
	max-width: 50px;
}

#wcfm-main-contentainer .wcfm_vendor_manage_change_vendor {
	display: inline-block;
	float: none;
	margin-left: -100px;
}

#wcfm-main-contentainer .wcfm_vendor_manage_change_vendor .select2-container { margin-bottom: 10px; }
#wcfm-main-contentainer .wcfm_vendor_manage_change_vendor .select2-container, #wcfm-main-contentainer .wcfm_vendor_manage_change_vendor .select2-container .select2-selection { min-height: 35px !important; }
#wcfm-main-contentainer .wcfm_vendor_manage_change_vendor .select2-container--default .select2-selection--single .select2-selection__rendered, #wcfm-main-contentainer .select2-search input { padding-top: 0px !important; padding-bottom: 0px !important; line-height: 2.407em !important; }

#wcfm-main-contentainer .store_address p.wcfm_title, .wcfm_setting_indent_block p.wcfm_title {
	margin-left: 3%;
	width: 32%;
}

#wcfm-main-contentainer .store_address .multi_input_holder, #wcfm-main-contentainer .store_address p.instructions {
	margin-left: 3%;
}

/***** Shipping Settings *****/
.shipping_hide {
  display: none !important;
}
.wcfmmp-zone-method-heading {
  overflow: hidden;
}

.wcfmmp-zone-method-heading span {
  line-height: 35px;
  margin-left: 15px;
}

.wcfmmp_shipping_classes h3 {
  margin-bottom: 0;
  line-height: 1;
}
.wcfmmp_shipping_classes .description {
  font-size: 12px;
  font-style: italic;
  color: #7b7b7b;
  margin-top: 3px;
  margin-bottom: 15px;
}

/**** ADD Shipping METHOD POPUP ******/

#cboxLoadedContent {
	padding: 5px !important;
	margin: 5px !important;
}

#wcfm-main-contentainer #wcfmmp_shipping_method_add_container,
#wcfm-main-contentainer #wcfmmp_shipping_method_edit_container {
	display: none;
}

#wcfmmp_shipping_method_add_container.collapse {
	display: block;
	height: auto;
}

#wcfmmp_shipping_method_edit_container.collapse   {
	display: block;
	height: auto;
}

#wcfmmp_shipping_method_add_container #wcfmmp_shipping_method_add-main-contentainer {
	margin-bottom: 0px;
}

#wcfmmp_shipping_method_add_container .wcfm-content {
	min-height: 300px;
	height: 300px;
	max-height: 310px;
	overflow: auto;
}

#wcfmmp_shipping_method_add_form_general_head, #wcfmmp_shipping_method_edit_general_head {
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 10px;
}

.modal_footer {
  margin-top: 10px;
  padding: 20px 0 0;
  border-top: 1px solid #dfdfdf;
  box-shadow: 0 -4px 4px -4px rgba(0,0,0,.1);  
}

.select2-container {
	width: 60% !important;
}

#groups_manage_capability_expander .select2-container {
	width: 44% !important;
}

.wcfm-top-element-container .select2-container {
	width: 250px !important;
	margin-bottom: 10px !important;
}

.wcfm_vendor_manage_change_vendor .select2-container {
	width: 250px !important;
}

.wcfm-wp-fields-uploader {
	display: inline-block;
	vertical-align: middle;
	margin-right: 25%;
	margin-bottom: 10px;
}

#wcfm-main-contentainer #wcfm_modify_vendor_membership {
	float: right;
	margin-top: 2px;
	margin-left: 10px;
	
	background: #1a1a1a none repeat scroll 0 0;
	border: 0 none;
	border-radius: 2px;
	color: #fff;
	font-family: Montserrat,"Helvetica Neue",sans-serif;
	font-weight: 500;
	letter-spacing: 0.046875em;
	line-height: 1;
	padding: 0.84375em 0.875em 0.78125em !important;
	text-transform: uppercase;
}

#wcfm-main-contentainer #wcfm_modify_vendor_membership:hover {
	background: #17a2b8 none repeat scroll 0 0;
}

#wcfm-main-contentainer #wcfm_modify_vendor_membership {
	color: #ffffff !important;
	float: none;
	padding: 4px;
}

.wcfm_vendor_store a {
	color: #FF7400;
	font-weight: 500;
}

.wcfm_vendor_badges_manage {
	display: none;
}

.wcfm_vendor_badges_manage_link {
	display: block;
	text-align: right;
	text-decoration: underline;
}

#wcfm-main-contentainer p.banner.wcfm_title {
  background: transparent;
}

/** Banner Slider **/

#banner_slider { 
	width: 360px;
	margin-left: 35% !important;
}

#banner_slider .multi_input_block {
	border: 0px;
  display: inline-block;
  margin-bottom: 0px;
  padding: 5px 12.5px;
  width: auto;
}

#banner_slider .multi_input_block:nth-child(odd) { padding-left: 0px; padding-right: 25px; }
#banner_slider .multi_input_block:nth-child(even) {  padding-left: 25px; padding-right: 0px; }

#banner_slider .wcfm-wp-fields-uploader {
	vertical-align: top;
	width: 150px;
	height: 150px;
	text-align: center;
	border-radius: 3px;
	display: block;
}
                                                                                                                       
#banner_slider .wcfm-wp-fields-uploader .placeHolder, #banner_slider .wcfm-wp-fields-uploader img {
	width: 150px;
	height: 150px;
}

#wcfm-main-contentainer #banner_slider input[type="text"].banner_type_slilder_link {
	width: 120px;
	padding: 0px;
	margin: 0px;
}

#banner_slider .multi_input_block .multi_input_block_manupulate {
	font-size: 15px;
}

#wcfm-main-contentainer #banner_slider input.remove_button_bak {
	display: none !important;
}

#wcfm-main-contentainer .wcfm_vendor_settings_heading {
	margin-top: 50px;
	margin-bottom: 25px;
}

/** WCFM Marketplace Store Hours Start **/ 
#wcfm-main-contentainer .wcfm_store_hours_fields {
	margin-bottom: 25px;
}
#wcfm-main-contentainer .wcfm_store_hours_label {
  width: 15% !important;
}
#wcfm-main-contentainer .wcfm_store_hours_field {
	width: 20% !important;
	margin-right: 5% !important;
}
/** WCFM Marketplace Store Hours End **/

input[type="text"].wcfm-text, 
select.wcfm-select, 
input[type="number"].wcfm-text, 
.wp-picker-container {
    padding: 5px;
    width: 60%;
    margin-bottom: 10px;
    font-size: 15px;
    display: inline-block;
    background-color: #fff !important;
    border: 1px solid #555 !important;
    box-shadow: none;
}

textarea.wcfm-textarea {
    width: 60%;
    font-size: 15px;
    margin-bottom: 10px;
    display: inline-block;
    background-color: #fff !important;
    border: 1px solid #555 !important;
    box-shadow: none;
    resize: vertical;
}

#wcfm-marketplace-map {
	width: 450px; 
	height: 300px; 
	border: 1px solid #DFDFDF; 
	margin-left: 35%;
}

div.wcfm-collapse-content .wcfm-content h2{float:none !important;}

@media only screen and (max-width: 640px) {
	#wcfm-main-contentainer .wcfm_vendor_manage_change_vendor {
		margin-left: -50px;
	}
	
	#wcfm-main-contentainer input[type="checkbox"].wcfm-checkbox {
		margin-right: 5%;
	}
	
	.wcfmvm_verification_details, .wcfmvm_verification_address_details, .wcfmvm_verification_identity_details {
		display: block;
		width: 99%;
	}
	
	#banner_slider {
		margin-left: 0% !important;
	}
	
	#wcfm-marketplace-map { 
		width: 325px;
		float: none;
	}
}

@media only screen and (max-width: 640px) {
	div.wcfm-collapse-content h2 {
		display: table-cell;
		width: auto;
	}
	
	#wcfm-main-contentainer .wcfm_vendor_manage_change_vendor {
		display: block;
		width: 100%;
		margin-left: 0px;
	}
}

@media only screen and (max-width: 414px) {
	div.wcfm-collapse-content h2 {
		display: table-cell;
		width: auto;
	}
}