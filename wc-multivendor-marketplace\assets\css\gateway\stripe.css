.wc-wcfmmp-stripe-split-pay-elements-field { border:1px solid #ddd; margin:5px 0; padding:5px; background-color:#fff; outline:0; }

#add_payment_method .woocommerce-PaymentMethod label { margin-left: 10px; }
#add_payment_method li { clear: right; }
form#order_review .payment_methods label { margin-left: 10px; }
form#order_review li { clear: right; }
form#order_review #wc-stripe_sepa-form { padding: 10px; }
.wc_payment_method .payment_box label { display: inline; }

.woocommerce-checkout #payment .payment_method_stripe_split,
#add_payment_method #payment .payment_method_stripe_split { position: relative; }

#add_payment_method #payment input#payment_method_stripe_split { position: absolute; top: 6px; }

.woocommerce-checkout #payment ul.payment_methods li img.stripe-icon,
#add_payment_method #payment ul.payment_methods li img.stripe-icon { float: right; max-width: 40px; padding-left: 3px; margin: 0; }

.woocommerce-checkout #payment ul.payment_methods li .stripe-credit-card-brand,
#add_payment_method #payment ul.payment_methods li .stripe-credit-card-brand { position: absolute; top: 50%; margin-top: -10px; right: 10px; background: no-repeat url( '../../images/gateway/credit-card.svg' ); display: block; width: 30px; height: 24px; }

.woocommerce-checkout #payment ul.payment_methods li .stripe-visa-brand,
#add_payment_method #payment ul.payment_methods li .stripe-visa-brand { position: absolute; top: 50%; margin-top: -10px; right: 10px; background: no-repeat url( '../../images/gateway/visa.svg' ); display: block; width: 30px; height: 24px; }

.woocommerce-checkout #payment ul.payment_methods li .stripe-amex-brand,
#add_payment_method #payment ul.payment_methods li .stripe-amex-brand { position: absolute; top: 50%; margin-top: -10px; right: 10px; background: no-repeat url( '../../images/gateway/amex.svg' ); display: block; width: 30px; height: 24px; }

.woocommerce-checkout #payment ul.payment_methods li .stripe-diners-brand,
#add_payment_method #payment ul.payment_methods li .stripe-diners-brand { position: absolute; top: 50%; margin-top: -10px; right: 10px; background: no-repeat url( '../../images/gateway/diners.svg' ); display: block; width: 30px; height: 24px; }

.woocommerce-checkout #payment ul.payment_methods li .stripe-discover-brand,
#add_payment_method #payment ul.payment_methods li .stripe-discover-brand { position: absolute; top: 50%; margin-top: -10px; right: 10px; background: no-repeat url( '../../images/gateway/discover.svg' ); display: block; width: 30px; height: 24px; }

.woocommerce-checkout #payment ul.payment_methods li .stripe-jcb-brand,
#add_payment_method #payment ul.payment_methods li .stripe-jcb-brand { position: absolute; top: 50%; margin-top: -10px; right: 10px; background: no-repeat url( '../../images/gateway/jcb.svg' ); display: block; width: 30px; height: 24px; }

.woocommerce-checkout #payment ul.payment_methods li .stripe-maestro-brand,
#add_payment_method #payment ul.payment_methods li .stripe-maestro-brand { position: absolute; top: 50%; margin-top: -10px; right: 10px; background: no-repeat url( '../../images/gateway/maestro.svg' ); display: block; width: 30px; height: 24px; }

.woocommerce-checkout #payment ul.payment_methods li .stripe-mastercard-brand,
#add_payment_method #payment ul.payment_methods li .stripe-mastercard-brand { position: absolute; top: 50%; margin-top: -10px; right: 10px; background: no-repeat url( '../../images/gateway/mastercard.svg' ); display: block; width: 30px; height: 24px; }

.woocommerce-checkout #payment ul.payment_methods .stripe-marketpace-card-group,
#add_payment_method #payment ul.payment_methods .stripe-marketpace-card-group { position: relative; }
