/*! DO NOT EDIT THIS FILE. This file is a auto generated on 2023-03-25 */
jQuery(document).ready(function(a){var o,t,e,r,n,s,i,c=a(".wcfmmp-product-geolocate-search-form");function m(r){var c,m,o;$icon_width=parseInt(wcfmmp_product_list_options.icon_width),$icon_height=parseInt(wcfmmp_product_list_options.icon_height),"google"==wcfm_maps.lib?(c=new google.maps.LatLngBounds,m=new google.maps.InfoWindow,a.each(r,function(o,t){var e,a=new google.maps.LatLng(t.lat,t.lang),r=(c.extend(a),{url:t.icon,scaledSize:new google.maps.Size($icon_width,$icon_height)}),a=new google.maps.Marker({position:a,map:s,animation:google.maps.Animation.DROP,title:t.name,icon:r,zIndex:o}),i=t.info_window_content;google.maps.event.addListener(a,"click",(e=a,function(){m.setContent(i),m.open(s,e)})),s.setCenter(a.getPosition()),n.push(a)}),wcfmmp_product_list_options.is_cluster&&(o=wcfmmp_product_list_options.cluster_image,markerClusterer&&markerClusterer.clearMarkers(),markerClusterer=new MarkerClusterer(s,n,{imagePath:o})),$auto_zoom&&0<r.length&&s.fitBounds(c)):a.each(r,function(o,t){var e=L.icon({iconUrl:t.icon,iconSize:[$icon_width,$icon_height]}),e=L.marker([t.lat,t.lang],{icon:e}).bindPopup(t.info_window_content),a=(n.push(e),L.featureGroup(n).addTo(s));$auto_zoom&&0<r.length&&setTimeout(function(){s.fitBounds(a.getBounds())},1e3)})}function l(){if("google"==wcfm_maps.lib)for(var o=0;o<n.length;o++)n[o].setMap(null);n=[]}0<a(".wcfmmp-product-geolocate-search-form").length&&0<a("#wcfmmp_radius_addr").length&&(o=parseInt(wcfmmp_product_list_options.max_radius),i=document.getElementById("wcfmmp_radius_addr"),"google"==wcfm_maps.lib?(t=new google.maps.Geocoder,(e=new google.maps.places.Autocomplete(i)).addListener("place_changed",function(){var o=e.getPlace();a("#wcfmmp_radius_lat").val(o.geometry.location.lat()),a("#wcfmmp_radius_lng").val(o.geometry.location.lng())})):r=new L.Control.Search({container:"wcfm_radius_filter_container",url:"https://nominatim.openstreetmap.org/search?format=json&q={s}",jsonpParam:"json_callback",propertyName:"display_name",propertyLoc:["lat","lon"],marker:L.marker([0,0]),moveToLocation:function(o,t,e){a("#wcfmmp_radius_lat").val(o.lat),a("#wcfmmp_radius_lng").val(o.lng)},initial:!1,collapsed:!1,autoType:!1,minLength:2}),a("#wcfmmp_radius_range").on("input",function(){a(".wcfmmp_radius_range_cur").html(this.value+wcfmmp_product_list_options.radius_unit),wcfmmp_product_list_options.is_rtl?a(".wcfmmp_radius_range_cur").css("right",this.value/o*a(".wcfm_radius_slidecontainer").outerWidth()-7.5+"px"):a(".wcfmmp_radius_range_cur").css("left",this.value/o*a(".wcfm_radius_slidecontainer").outerWidth()-7.5+"px"),$wcfmmp_radius_lat=a("#wcfmmp_radius_lat").val()}),wcfmmp_product_list_options.is_rtl?a(".wcfmmp_radius_range_cur").css("right",a("#wcfmmp_radius_range").val()/o*a(".wcfm_radius_slidecontainer").outerWidth()-7.5+"px"):a(".wcfmmp_radius_range_cur").css("left",a("#wcfmmp_radius_range").val()/o*a(".wcfm_radius_slidecontainer").outerWidth()-7.5+"px"),navigator.geolocation)&&(a(".wcfmmmp_locate_icon").on("click",function(){navigator.geolocation.getCurrentPosition(function(e){console.log(e.coords.latitude,e.coords.longitude),"google"==wcfm_maps.lib?t.geocode({location:{lat:e.coords.latitude,lng:e.coords.longitude}},function(o,t){"OK"===t&&(a("#wcfmmp_radius_addr").val(o[0].formatted_address),a("#wcfmmp_radius_lat").val(e.coords.latitude),a("#wcfmmp_radius_lng").val(e.coords.longitude),c.submit())}):a.get("https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat="+e.coords.latitude+"&lon="+e.coords.longitude,function(o){a("#wcfm_radius_filter_container").find(".search-input").val(o.address.road),a("#wcfmmp_radius_lat").val(e.coords.latitude),a("#wcfmmp_radius_lng").val(e.coords.longitude),c.submit()})})}),wcfmmp_product_list_options.is_geolocate)&&!a("#wcfmmp_radius_lat").val()&&a(".wcfmmmp_locate_icon").click(),0<a(".wcfmmp-product-list-map").length&&(a(".wcfmmp-product-list-map").css("height",a(".wcfmmp-product-list-map").outerWidth()/2),n=[],s=markerClusterer="",i=wcfmmp_product_list_options.is_poi?[]:[{featureType:"poi",elementType:"labels",stylers:[{visibility:"off"}]}],"google"==wcfm_maps.lib?(i={zoom:$map_zoom,center:new google.maps.LatLng(wcfmmp_product_list_options.default_lat,wcfmmp_product_list_options.default_lng,13),mapTypeId:wcfm_maps.map_type,styles:i},s=new google.maps.Map(document.getElementById("wcfmmp-product-list-map"),i)):(s=L.map("wcfmmp-product-list-map",{center:[wcfmmp_product_list_options.default_lat,wcfmmp_product_list_options.default_lng],minZoom:2,zoom:13,zoomAnimation:!1}),wcfmmp_product_list_options.is_allow_scroll_zoom||s.scrollWheelZoom.disable(),L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{subdomains:["a","b","c"]}).addTo(s),r&&0<a("#wcfmmp_radius_addr").length&&(s.addControl(r),a("#wcfm_radius_filter_container").find(".search-input").addClass("wcfmmp-radius-addr").attr("id","wcfmmp_radius_addr").attr("name","radius_addr").css("float","none").attr("placeholder",wcfmmp_product_list_options.search_location).val(a("#wcfmmp_radius_addr").val()),a("#wcfmmp_radius_addr").remove())),0<a(".wcfmmp-product-list-map").length)&&(l(),i={search_term:"",wcfmmp_store_category:"",wcfmmp_store_country:"",wcfmmp_store_state:"",action:"wcfmmp_stores_list_map_markers",pagination_base:1,paged:1,per_row:$per_row,per_page:$per_page,includes:$includes,excludes:$excludes,has_product:$has_product,has_orderby:$has_orderby,sidebar:$sidebar,theme:$theme,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce,search_data:jQuery(".wcfmmp-product-geolocate-search-form").serialize()},a.post(wcfm_params.ajax_url,i,function(o){o.success&&(o=o.data,m(a.parseJSON(o)))}))});