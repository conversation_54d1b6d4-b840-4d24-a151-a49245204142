<?php
/**
 * Vendor PayPal Gateway
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayPal gateway class
 */
class Vendor_Gateway_Paypal {
    
    /**
     * Gateway ID
     */
    public $id = 'paypal';
    
    /**
     * Gateway title
     */
    public $title = 'PayPal';
    
    /**
     * Gateway description
     */
    public $description = 'Send payments via PayPal';
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_settings();
    }
    
    /**
     * Initialize settings
     */
    private function init_settings() {
        $this->settings = get_option('vendor_paypal_settings', array(
            'enabled' => 'no',
            'sandbox' => 'yes',
            'client_id' => '',
            'client_secret' => '',
            'sandbox_client_id' => '',
            'sandbox_client_secret' => ''
        ));
    }
    
    /**
     * Check if gateway is available
     */
    public function is_available() {
        return $this->settings['enabled'] === 'yes' && $this->has_credentials();
    }
    
    /**
     * Check if credentials are set
     */
    private function has_credentials() {
        if ($this->settings['sandbox'] === 'yes') {
            return !empty($this->settings['sandbox_client_id']) && !empty($this->settings['sandbox_client_secret']);
        } else {
            return !empty($this->settings['client_id']) && !empty($this->settings['client_secret']);
        }
    }
    
    /**
     * Process payment
     */
    public function process_payment($withdrawal_id, $vendor_id, $amount, $vendor_email) {
        try {
            $access_token = $this->get_access_token();
            if (!$access_token) {
                return array(
                    'success' => false,
                    'message' => 'Failed to get PayPal access token'
                );
            }
            
            $payout_data = array(
                'sender_batch_header' => array(
                    'sender_batch_id' => 'vendor_withdrawal_' . $withdrawal_id . '_' . time(),
                    'email_subject' => 'You have a payout!',
                    'email_message' => 'You have received a payout from ' . get_bloginfo('name')
                ),
                'items' => array(
                    array(
                        'recipient_type' => 'EMAIL',
                        'amount' => array(
                            'value' => number_format($amount, 2, '.', ''),
                            'currency' => get_woocommerce_currency()
                        ),
                        'receiver' => $vendor_email,
                        'note' => 'Vendor withdrawal payment',
                        'sender_item_id' => 'withdrawal_' . $withdrawal_id
                    )
                )
            );
            
            $response = $this->make_api_request('payments/payouts', $payout_data, $access_token);
            
            if ($response && isset($response['batch_header']['payout_batch_id'])) {
                return array(
                    'success' => true,
                    'transaction_id' => $response['batch_header']['payout_batch_id'],
                    'message' => 'Payment sent successfully'
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Failed to process PayPal payout'
                );
            }
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'PayPal error: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Get access token
     */
    private function get_access_token() {
        $client_id = $this->settings['sandbox'] === 'yes' ? $this->settings['sandbox_client_id'] : $this->settings['client_id'];
        $client_secret = $this->settings['sandbox'] === 'yes' ? $this->settings['sandbox_client_secret'] : $this->settings['client_secret'];
        
        $base_url = $this->settings['sandbox'] === 'yes' ? 'https://api.sandbox.paypal.com' : 'https://api.paypal.com';
        
        $response = wp_remote_post($base_url . '/v1/oauth2/token', array(
            'headers' => array(
                'Accept' => 'application/json',
                'Accept-Language' => 'en_US',
                'Authorization' => 'Basic ' . base64_encode($client_id . ':' . $client_secret)
            ),
            'body' => 'grant_type=client_credentials'
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        return isset($data['access_token']) ? $data['access_token'] : false;
    }
    
    /**
     * Make API request
     */
    private function make_api_request($endpoint, $data, $access_token) {
        $base_url = $this->settings['sandbox'] === 'yes' ? 'https://api.sandbox.paypal.com' : 'https://api.paypal.com';
        
        $response = wp_remote_post($base_url . '/v1/' . $endpoint, array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $access_token
            ),
            'body' => json_encode($data)
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        return json_decode($body, true);
    }
    
    /**
     * Get payment fields for frontend
     */
    public function get_payment_fields() {
        return array(
            'paypal_email' => array(
                'type' => 'email',
                'label' => __('PayPal Email', 'vendor'),
                'required' => true,
                'description' => __('Enter your PayPal email address', 'vendor')
            )
        );
    }
    
    /**
     * Validate payment details
     */
    public function validate_payment_details($details) {
        if (empty($details['paypal_email']) || !is_email($details['paypal_email'])) {
            return new WP_Error('invalid_email', __('Please enter a valid PayPal email address', 'vendor'));
        }
        
        return true;
    }
    
    /**
     * Get settings fields for admin
     */
    public function get_settings_fields() {
        return array(
            'enabled' => array(
                'title' => __('Enable PayPal', 'vendor'),
                'type' => 'checkbox',
                'default' => 'no'
            ),
            'sandbox' => array(
                'title' => __('Sandbox Mode', 'vendor'),
                'type' => 'checkbox',
                'default' => 'yes',
                'description' => __('Enable sandbox mode for testing', 'vendor')
            ),
            'client_id' => array(
                'title' => __('Live Client ID', 'vendor'),
                'type' => 'text',
                'description' => __('Your PayPal live client ID', 'vendor')
            ),
            'client_secret' => array(
                'title' => __('Live Client Secret', 'vendor'),
                'type' => 'password',
                'description' => __('Your PayPal live client secret', 'vendor')
            ),
            'sandbox_client_id' => array(
                'title' => __('Sandbox Client ID', 'vendor'),
                'type' => 'text',
                'description' => __('Your PayPal sandbox client ID', 'vendor')
            ),
            'sandbox_client_secret' => array(
                'title' => __('Sandbox Client Secret', 'vendor'),
                'type' => 'password',
                'description' => __('Your PayPal sandbox client secret', 'vendor')
            )
        );
    }
    
    /**
     * Update settings
     */
    public function update_settings($settings) {
        $this->settings = wp_parse_args($settings, $this->settings);
        update_option('vendor_paypal_settings', $this->settings);
    }
}
