/*! DO NOT EDIT THIS FILE. This file is a auto generated on 2023-03-25 */
jQuery(document).ready(function(s){$refunds_vendor="",$status_type="requested",$wcfm_refund_requests_table=s("#wcfm-refund-requests").DataTable({processing:!0,serverSide:!0,pageLength:parseInt(dataTables_config.pageLength),bFilter:!1,dom:"Bfrtip",responsive:!0,language:s.parseJSON(dataTables_language),buttons:$wcfm_datatable_button_args,columns:[{responsivePriority:1},{responsivePriority:5},{responsivePriority:2},{responsivePriority:1},{responsivePriority:3},{responsivePriority:4},{responsivePriority:6},{responsivePriority:7},{responsivePriority:8}],columnDefs:[{targets:0,orderable:!1},{targets:1,orderable:!1},{targets:2,orderable:!1},{targets:3,orderable:!1},{targets:4,orderable:!1},{targets:5,orderable:!1},{targets:6,orderable:!1},{targets:7,orderable:!1},{targets:8,orderable:!1}],ajax:{type:"POST",url:wcfm_params.ajax_url,data:function(e){e.action="wcfm_ajax_controller",e.controller="wcfm-refund-requests",e.refund_vendor=$refunds_vendor,e.status_type=$status_type,e.transaction_id=GetURLParameter("request_id"),e.order="asc",e.wcfm_ajax_nonce=wcfm_params.wcfm_ajax_nonce},complete:function(){initiateTip(),s(document.body).trigger("updated_wcfm-refund-requests")}}}),s("#wcfm_refund_requests_approve_button").click(function(e){e.preventDefault(),s("#wcfm_refund_requests_approve_button").hide(),s("#wcfm_refund_requests_cancel_button").hide(),s("#wcfm-content").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});e={action:"wcfm_ajax_controller",controller:"wcfm-refund-requests-approve",wcfm_refund_manage_form:s("#wcfm_refund_requests_manage_form").serialize(),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce,status:"submit"};s.post(wcfm_params.ajax_url,e,function(e){e&&($response_json=s.parseJSON(e),s(".wcfm-message").html("").removeClass("wcfm-success").removeClass("wcfm-error").slideUp(),wcfm_notification_sound.play(),$response_json.status?(s("#wcfm_refund_requests_manage_form .wcfm-message").html('<span class="wcicon-status-completed"></span>'+$response_json.message).addClass("wcfm-success").slideDown(),$wcfm_refund_requests_table.ajax.reload()):s("#wcfm_refund_requests_manage_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+$response_json.message).addClass("wcfm-error").slideDown(),s("#wcfm-content").unblock(),s("#wcfm_refund_requests_approve_button").show(),s("#wcfm_refund_requests_cancel_button").show())})}),s("#wcfm_refund_requests_cancel_button").click(function(e){e.preventDefault(),s("#wcfm_refund_requests_approve_button").hide(),s("#wcfm_refund_requests_cancel_button").hide(),s("#wcfm-content").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});e={action:"wcfm_ajax_controller",controller:"wcfm-refund-requests-cancel",wcfm_refund_manage_form:s("#wcfm_refund_requests_manage_form").serialize(),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce,status:"submit"};s.post(wcfm_params.ajax_url,e,function(e){e&&($response_json=s.parseJSON(e),s(".wcfm-message").html("").removeClass("wcfm-success").removeClass("wcfm-error").slideUp(),wcfm_notification_sound.play(),$response_json.status?(s("#wcfm_refund_requests_manage_form .wcfm-message").html('<span class="wcicon-status-completed"></span>'+$response_json.message).addClass("wcfm-success").slideDown(),$wcfm_refund_requests_table.ajax.reload()):s("#wcfm_refund_requests_manage_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+$response_json.message).addClass("wcfm-error").slideDown(),s("#wcfm-content").unblock(),s("#wcfm_refund_requests_approve_button").show(),s("#wcfm_refund_requests_cancel_button").show())})}),0<s("#dropdown_vendor").length&&s("#dropdown_vendor").on("change",function(){$refunds_vendor=s("#dropdown_vendor").val(),$wcfm_refund_requests_table.ajax.reload()}).select2($wcfm_vendor_select_args),s("#dropdown_status_type").change(function(){$status_type=s(this).val(),$wcfm_refund_requests_table.ajax.reload()}),0<s(".wcfm_filters_wrap").length&&(s(".dataTable").before(s(".wcfm_filters_wrap")),s(".wcfm_filters_wrap").css("display","inline-block")),s(document.body).on("updated_wcfm-refund-requests",function(){s.each(wcfm_refund_screen_manage,function(e,s){$wcfm_refund_requests_table.column(e).visible(!1)})})});