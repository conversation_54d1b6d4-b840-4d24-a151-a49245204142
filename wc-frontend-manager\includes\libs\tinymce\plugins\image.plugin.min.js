!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e[f]=d(a[f]);b.apply(null,e)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("5",tinymce.util.Tools.resolve),g("1",["5"],function(a){return a("tinymce.PluginManager")}),g("o",[],function(){return"undefined"!=typeof window?window:Function("return this;")()}),g("n",["o"],function(a){var b=function(b,c){for(var d=void 0!==c?c:a,e=0;e<b.length&&void 0!==d&&null!==d;++e)d=d[b[e]];return d},c=function(a,c){var d=a.split(".");return b(d,c)},d=function(a,b){return void 0!==a[b]&&null!==a[b]||(a[b]={}),a[b]},e=function(b,c){for(var e=void 0!==c?c:a,f=0;f<b.length;++f)e=d(e,b[f]);return e},f=function(a,b){var c=a.split(".");return e(c,b)};return{path:b,resolve:c,forge:e,namespace:f}}),g("j",["n"],function(a){var b=function(b,c){return a.resolve(b,c)},c=function(a,c){var d=b(a,c);if(void 0===d)throw a+" not available on this browser";return d};return{getOrDie:c}}),g("8",["j"],function(a){var b=function(){return a.getOrDie("URL")},c=function(a){return b().createObjectURL(a)},d=function(a){b().revokeObjectURL(a)};return{createObjectURL:c,revokeObjectURL:d}}),h("9",document),h("a",Math),h("b",RegExp),g("c",["5"],function(a){return a("tinymce.Env")}),g("d",["5"],function(a){return a("tinymce.ui.Factory")}),g("e",["5"],function(a){return a("tinymce.util.JSON")}),g("7",["5"],function(a){return a("tinymce.util.Tools")}),g("f",["5"],function(a){return a("tinymce.util.XHR")}),g("g",[],function(){var a=function(a){return a.getParam("image_dimensions",!0)},b=function(a){return a.getParam("image_advtab",!1)},c=function(a){return a.getParam("image_prepend_url","")},d=function(a){return a.getParam("image_class_list")},e=function(a){return a.getParam("image_description",!0)},f=function(a){return a.getParam("image_title",!1)},g=function(a){return a.getParam("image_caption",!1)},h=function(a){return a.getParam("image_list",!1)};return{hasDimensions:a,hasAdvTab:b,getPrependUrl:c,getClassList:d,hasDescription:e,hasImageTitle:f,hasImageCaption:g,getImageList:h}}),g("k",["j"],function(a){return function(){var b=a.getOrDie("XMLHttpRequest");return new b}}),h("l",window),g("m",["5"],function(a){return a("tinymce.util.Promise")}),g("h",["k","9","l","m","7"],function(a,b,c,d,e){var f=function(){},g=function(a,b){return a?a.replace(/\/$/,"")+"/"+b.replace(/^\//,""):b};return function(b){var h=function(d,e,f,h){var i,j;i=new a,i.open("POST",b.url),i.withCredentials=b.credentials,i.upload.onprogress=function(a){h(a.loaded/a.total*100)},i.onerror=function(){f("Image upload failed due to a XHR Transport error. Code: "+i.status)},i.onload=function(){var a;return i.status<200||i.status>=300?void f("HTTP Error: "+i.status):(a=JSON.parse(i.responseText),a&&"string"==typeof a.location?void e(g(b.basePath,a.location)):void f("Invalid JSON: "+i.responseText))},j=new c.FormData,j.append("file",d.blob(),d.filename()),i.send(j)},i=function(a,b){return new d(function(c,d){try{b(a,c,d,f)}catch(a){d(a.message)}})},j=function(a){return a===h},k=function(a){return!b.url&&j(b.handler)?d.reject("Upload url missng from the settings."):i(a,b.handler)};return b=e.extend({credentials:!1,handler:h},b),{upload:k}}}),g("i",["7","a","9"],function(a,b,c){var d=function(a,d){function e(a,b){f.parentNode&&f.parentNode.removeChild(f),d({width:a,height:b})}var f=c.createElement("img");f.onload=function(){e(b.max(f.width,f.clientWidth),b.max(f.height,f.clientHeight))},f.onerror=function(){e()};var g=f.style;g.visibility="hidden",g.position="fixed",g.bottom=g.left=0,g.width=g.height="auto",c.body.appendChild(f),f.src=a},e=function(b,c,d){function e(b,d){return d=d||[],a.each(b,function(a){var b={text:a.text||a.title};a.menu?b.menu=e(a.menu):(b.value=a.value,c(b)),d.push(b)}),d}return e(b,d||[])},f=function(a){return a&&(a=a.replace(/px$/,"")),a},g=function(a){return a.length>0&&/^[0-9]+$/.test(a)&&(a+="px"),a},h=function(a){if(a.margin){var b=a.margin.split(" ");switch(b.length){case 1:a["margin-top"]=a["margin-top"]||b[0],a["margin-right"]=a["margin-right"]||b[0],a["margin-bottom"]=a["margin-bottom"]||b[0],a["margin-left"]=a["margin-left"]||b[0];break;case 2:a["margin-top"]=a["margin-top"]||b[0],a["margin-right"]=a["margin-right"]||b[1],a["margin-bottom"]=a["margin-bottom"]||b[0],a["margin-left"]=a["margin-left"]||b[1];break;case 3:a["margin-top"]=a["margin-top"]||b[0],a["margin-right"]=a["margin-right"]||b[1],a["margin-bottom"]=a["margin-bottom"]||b[2],a["margin-left"]=a["margin-left"]||b[1];break;case 4:a["margin-top"]=a["margin-top"]||b[0],a["margin-right"]=a["margin-right"]||b[1],a["margin-bottom"]=a["margin-bottom"]||b[2],a["margin-left"]=a["margin-left"]||b[3]}delete a.margin}return a};return{getImageSize:d,buildListItems:e,removePixelSuffix:f,addPixelSuffix:g,mergeMargins:h}}),g("6",["8","9","a","b","c","d","e","7","f","g","h","i"],function(a,b,c,d,e,f,g,h,i,j,k,l){return function(b){function e(a){var c=j.getImageList(b);"string"==typeof c?i.send({url:c,success:function(b){a(g.parse(b))}}):"function"==typeof c?c(a):a(c)}function m(e){function g(){var c=f.get("Throbber"),d=new c(t.getEl()),e=this.value(),g=new k({url:C.images_upload_url,basePath:C.images_upload_base_path,credentials:C.images_upload_credentials,handler:C.images_upload_handler}),h=b.editorUpload.blobCache.create({blob:e,name:e.name?e.name.replace(/\.[^\.]+$/,""):null,base64:"data:image/fake;base64,="}),i=function(){d.hide(),a.revokeObjectURL(h.blobUri())};return d.show(),g.upload(h).then(function(a){var b=t.find("#src");return b.value(a),t.find("tabpanel")[0].activateTab(0),b.fire("change"),i(),a},function(a){b.windowManager.alert(a),i()})}function i(a){return b.schema.getTextBlockElements()[a.nodeName]}function m(){var a,b,d,e;a=t.find("#width")[0],b=t.find("#height")[0],a&&b&&(d=a.value(),e=b.value(),t.find("#constrain")[0].checked()&&w&&x&&d&&e&&(w!==d?(e=c.round(d/w*e),isNaN(e)||b.value(e)):(d=c.round(e/x*d),isNaN(d)||a.value(d))),w=d,x=e)}function n(){if(j.hasAdvTab(b)){var a=t.toJSON(),c=B.parseStyle(a.style);c=l.mergeMargins(c),a.vspace&&(c["margin-top"]=c["margin-bottom"]=l.addPixelSuffix(a.vspace)),a.hspace&&(c["margin-left"]=c["margin-right"]=l.addPixelSuffix(a.hspace)),a.border&&(c["border-width"]=l.addPixelSuffix(a.border)),t.find("#style").value(B.serializeStyle(B.parseStyle(B.serializeStyle(c))))}}function o(){if(j.hasAdvTab(b)){var a=t.toJSON(),c=B.parseStyle(a.style);t.find("#vspace").value(""),t.find("#hspace").value(""),c=l.mergeMargins(c),(c["margin-top"]&&c["margin-bottom"]||c["margin-right"]&&c["margin-left"])&&(c["margin-top"]===c["margin-bottom"]?t.find("#vspace").value(l.removePixelSuffix(c["margin-top"])):t.find("#vspace").value(""),c["margin-right"]===c["margin-left"]?t.find("#hspace").value(l.removePixelSuffix(c["margin-right"])):t.find("#hspace").value("")),c["border-width"]&&t.find("#border").value(l.removePixelSuffix(c["border-width"])),t.find("#style").value(B.serializeStyle(B.parseStyle(B.serializeStyle(c))))}}function p(a){function c(){a.onload=a.onerror=null,b.selection&&(b.selection.select(a),b.nodeChanged())}a.onload=function(){A.width||A.height||!D||B.setAttribs(a,{width:a.clientWidth,height:a.clientHeight}),c()},a.onerror=c}function q(){var a,c;n(),m(),A=h.extend(A,t.toJSON()),A.alt||(A.alt=""),A.title||(A.title=""),""===A.width&&(A.width=null),""===A.height&&(A.height=null),A.style||(A.style=null),A={src:A.src,alt:A.alt,title:A.title,width:A.width,height:A.height,style:A.style,caption:A.caption,"class":A["class"]},b.undoManager.transact(function(){if(A.src){if(""===A.title&&(A.title=null),u?B.setAttribs(u,A):(A.id="__mcenew",b.focus(),b.selection.setContent(B.createHTML("img",A)),u=B.get("__mcenew"),B.setAttrib(u,"id",null)),b.editorUpload.uploadImagesAuto(),A.caption===!1&&B.is(u.parentNode,"figure.image")&&(a=u.parentNode,B.setAttrib(u,"contenteditable",null),B.insertAfter(u,a),B.remove(a),b.selection.select(u),b.nodeChanged()),A.caption!==!0)p(u);else if(!B.is(u.parentNode,"figure.image")){c=u,u=u.cloneNode(!0),u.contentEditable=!0,a=B.create("figure",{"class":"image"}),a.appendChild(u),a.appendChild(B.create("figcaption",{contentEditable:!0},"Caption")),a.contentEditable=!1;var d=B.getParent(c,i);d?B.split(d,c,a):B.replace(a,c),b.selection.select(a)}}else if(u){var e=B.is(u.parentNode,"figure.image")?u.parentNode:u;B.remove(e),b.focus(),b.nodeChanged(),B.isEmpty(b.getBody())&&(b.setContent(""),b.selection.setCursorLocation())}})}function r(a){var c,e,f,g=a.meta||{};y&&y.value(b.convertURL(this.value(),"src")),h.each(g,function(a,b){t.find("#"+b).value(a)}),g.width||g.height||(c=b.convertURL(this.value(),"src"),e=j.getPrependUrl(b),f=new d("^(?:[a-z]+:)?//","i"),e&&!f.test(c)&&c.substring(0,e.length)!==e&&(c=e+c),this.value(c),l.getImageSize(b.documentBaseURI.toAbsolute(this.value()),function(a){a.width&&a.height&&D&&(w=a.width,x=a.height,t.find("#width").value(w),t.find("#height").value(x))}))}function s(a){a.meta=t.toJSON()}var t,u,v,w,x,y,z,A={},B=b.dom,C=b.settings,D=j.hasDimensions(b);u=b.selection.getNode(),v=B.getParent(u,"figure.image"),v&&(u=B.select("img",v)[0]),u&&("IMG"!==u.nodeName||u.getAttribute("data-mce-object")||u.getAttribute("data-mce-placeholder"))&&(u=null),u&&(w=B.getAttrib(u,"width"),x=B.getAttrib(u,"height"),A={src:B.getAttrib(u,"src"),alt:B.getAttrib(u,"alt"),title:B.getAttrib(u,"title"),"class":B.getAttrib(u,"class"),width:w,height:x,caption:!!v}),e&&(y={type:"listbox",label:"Image list",values:l.buildListItems(e,function(a){a.value=b.convertURL(a.value||a.url,"src")},[{text:"None",value:""}]),value:A.src&&b.convertURL(A.src,"src"),onselect:function(a){var b=t.find("#alt");(!b.value()||a.lastControl&&b.value()===a.lastControl.text())&&b.value(a.control.text()),t.find("#src").value(a.control.value()).fire("change")},onPostRender:function(){y=this}}),j.getClassList(b)&&(z={name:"class",type:"listbox",label:"Class",values:l.buildListItems(j.getClassList(b),function(a){a.value&&(a.textStyle=function(){return b.formatter.getCssText({inline:"img",classes:[a.value]})})})});var E=[{name:"src",type:"filepicker",filetype:"image",label:"Source",autofocus:!0,onchange:r,onbeforecall:s},y];if(j.hasDescription(b)&&E.push({name:"alt",type:"textbox",label:"Image description"}),j.hasImageTitle(b)&&E.push({name:"title",type:"textbox",label:"Image Title"}),D&&E.push({type:"container",label:"Dimensions",layout:"flex",direction:"row",align:"center",spacing:5,items:[{name:"width",type:"textbox",maxLength:5,size:3,onchange:m,ariaLabel:"Width"},{type:"label",text:"x"},{name:"height",type:"textbox",maxLength:5,size:3,onchange:m,ariaLabel:"Height"},{name:"constrain",type:"checkbox",checked:!0,text:"Constrain proportions"}]}),E.push(z),j.hasImageCaption(b)&&E.push({name:"caption",type:"checkbox",label:"Caption"}),j.hasAdvTab(b)||b.settings.images_upload_url){var F=[{title:"General",type:"form",items:E}];if(j.hasAdvTab(b)&&(u&&(u.style.marginLeft&&u.style.marginRight&&u.style.marginLeft===u.style.marginRight&&(A.hspace=l.removePixelSuffix(u.style.marginLeft)),u.style.marginTop&&u.style.marginBottom&&u.style.marginTop===u.style.marginBottom&&(A.vspace=l.removePixelSuffix(u.style.marginTop)),u.style.borderWidth&&(A.border=l.removePixelSuffix(u.style.borderWidth)),A.style=b.dom.serializeStyle(b.dom.parseStyle(b.dom.getAttrib(u,"style")))),F.push({title:"Advanced",type:"form",pack:"start",items:[{label:"Style",name:"style",type:"textbox",onchange:o},{type:"form",layout:"grid",packV:"start",columns:2,padding:0,alignH:["left","right"],defaults:{type:"textbox",maxWidth:50,onchange:n},items:[{label:"Vertical space",name:"vspace"},{label:"Horizontal space",name:"hspace"},{label:"Border",name:"border"}]}]})),b.settings.images_upload_url){var G=".jpg,.jpeg,.png,.gif",H={title:"Upload",type:"form",layout:"flex",direction:"column",align:"stretch",padding:"20 20 20 20",items:[{type:"container",layout:"flex",direction:"column",align:"center",spacing:10,items:[{text:"Browse for an image",type:"browsebutton",accept:G,onchange:g},{text:"OR",type:"label"}]},{text:"Drop an image here",type:"dropzone",accept:G,height:100,onchange:g}]};F.push(H)}t=b.windowManager.open({title:"Insert/edit image",data:A,bodyType:"tabpanel",body:F,onSubmit:q})}else t=b.windowManager.open({title:"Insert/edit image",data:A,body:E,onSubmit:q})}function n(){e(m)}return{open:n}}}),g("2",["6"],function(a){var b=function(b){b.addCommand("mceImage",a(b).open)};return{register:b}}),g("3",["7"],function(a){var b=function(a){var b=a.attr("class");return b&&/\bimage\b/.test(b)},c=function(c){return function(d){for(var e,f=d.length,g=function(a){a.attr("contenteditable",c?"true":null)};f--;)e=d[f],b(e)&&(e.attr("contenteditable",c?"false":null),a.each(e.getAll("figcaption"),g),a.each(e.getAll("img"),g))}},d=function(a){a.on("preInit",function(){a.parser.addNodeFilter("figure",c(!0)),a.serializer.addNodeFilter("figure",c(!1))})};return{setup:d}}),g("4",["6"],function(a){var b=function(b){b.addButton("image",{icon:"image",tooltip:"Insert/edit image",onclick:a(b).open,stateSelector:"img:not([data-mce-object],[data-mce-placeholder]),figure.image"}),b.addMenuItem("image",{icon:"image",text:"Image",onclick:a(b).open,context:"insert",prependToContext:!0})};return{register:b}}),g("0",["1","2","3","4"],function(a,b,c,d){return a.add("image",function(a){c.setup(a),d.register(a),b.register(a)}),function(){}}),d("0")()}();