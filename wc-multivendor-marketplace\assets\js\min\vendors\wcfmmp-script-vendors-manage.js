/*! DO NOT EDIT THIS FILE. This file is a auto generated on 2023-03-25 */
$filter_by_date=$order_status=$wcfm_orders_table="",$order_product="";var orderTableRefrsherTime=$commission_status="";jQuery(document).ready(function(r){function a(){wcfm_orders_auto_refresher.is_allow&&(clearTimeout(orderTableRefrsherTime),orderTableRefrsherTime=setTimeout(function(){$wcfm_orders_table.ajax.reload(),a()},3e4))}dataTables_config.is_allow_hidden_export&&($wcfm_datatable_button_args=[{extend:"print"},{extend:"pdfHtml5"},{extend:"excelHtml5"},{extend:"csv"}]),$wcfm_orders_table=r("#wcfm-orders").DataTable({processing:!0,serverSide:!0,responsive:!0,bFilter:!1,pageLength:parseInt(dataTables_config.pageLength),dom:"Bfrtip",language:r.parseJSON(dataTables_language),buttons:$wcfm_datatable_button_args,columns:r.parseJSON(wcfm_datatable_columns.priority),columnDefs:r.parseJSON(wcfm_datatable_columns.defs),ajax:{type:"POST",url:wcfm_params.ajax_url,data:function(e){e.action="wcfm_ajax_controller",e.controller="wcfm-vendor-orders",e.order_status=GetURLParameter("order_status"),e.filter_date_form=$filter_date_form,e.filter_date_to=$filter_date_to,e.order_product=$order_product,e.commission_status=$commission_status,e.vendor_id=r("#wcfmmp_vendor_manager_id").val(),e.wcfm_ajax_nonce=wcfm_params.wcfm_ajax_nonce},complete:function(){initiateTip(),r(".show_order_items").click(function(e){return e.preventDefault(),r(this).next("div.order_items").toggleClass("order_items_visible"),!1}),r(document.body).trigger("updated_wcfm-orders")}}}),r(document.body).on("wcfm-date-range-refreshed",function(){$wcfm_orders_table.ajax.reload()}),0<r("#order_product").length&&r("#order_product").on("change",function(){$order_product=r("#order_product").val(),$wcfm_orders_table.ajax.reload()}).select2($wcfm_product_select_args),0<r("#commission-status").length&&r("#commission-status").on("change",function(){$commission_status=r("#commission-status").val(),$wcfm_orders_table.ajax.reload()}),a(),r(document.body).on("updated_wcfm-orders",function(){r(".wcfm_order_mark_complete").each(function(){r(this).click(function(e){e.preventDefault();e=confirm(wcfm_dashboard_messages.order_mark_complete_confirm);if(e){e=r(this);clearTimeout(orderTableRefrsherTime),r("#wcfm-orders_wrapper").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),e={action:"wcfm_order_mark_complete",orderid:e.data("orderid"),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce},r.ajax({type:"POST",url:wcfm_params.ajax_url,data:e,success:function(e){$wcfm_orders_table.ajax.reload(),r("#wcfm-orders_wrapper").unblock(),a()}})}return!1})})}),r(document.body).on("updated_wcfm-orders",function(){r.each(wcfm_orders_screen_manage,function(e,r){$wcfm_orders_table.column(e).visible(!1)})}),r(document.body).on("updated_wcfm-orders",function(){r.each(wcfm_orders_screen_manage_hidden,function(e,r){$wcfm_orders_table.column(e).visible(!1)})}),0<r(".wcfm_filters_wrap").length&&(r(".dataTable").before(r(".wcfm_filters_wrap")),r(".wcfm_filters_wrap").css("display","inline-block"))});