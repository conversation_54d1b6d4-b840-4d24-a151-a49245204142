$wcfm_enquiry_table="";var enquiryBoardRefrsherTime="";jQuery(document).ready(function(e){$enquiry_product="",$enquiry_vendor="",$report_for="",$wcfm_enquiry_table=e("#wcfm-enquiry").DataTable({processing:!0,serverSide:!0,responsive:!0,pageLength:parseInt(dataTables_config.pageLength),language:e.parseJSO<PERSON>(dataTables_language),columns:[{responsivePriority:1},{responsivePriority:2},{responsivePriority:3},{responsivePriority:4},{responsivePriority:5},{responsivePriority:6},{responsivePriority:7},{responsivePriority:2}],columnDefs:[{targets:0,orderable:!1},{targets:1,orderable:!1},{targets:2,orderable:!1},{targets:3,orderable:!1},{targets:4,orderable:!1},{targets:5,orderable:!1},{targets:6,orderable:!1},{targets:7,orderable:!1}],ajax:{type:"POST",url:wcfm_params.ajax_url,data:function(e){e.action="wcfm_ajax_controller",e.controller="wcfm-enquiry",e.enquiry_product=$enquiry_product,e.enquiry_vendor=$enquiry_vendor,e.filter_date_form=$filter_date_form,e.filter_date_to=$filter_date_to,e.wcfm_ajax_nonce=wcfm_params.wcfm_ajax_nonce},complete:function(){initiateTip(),e(document.body).trigger("updated_wcfm-enquiry")}}}),e(document.body).on("updated_wcfm-enquiry",function(){e.each(wcfm_enquiry_screen_manage,function(e,r){$wcfm_enquiry_table.column(e).visible(!1)})}),e(document.body).on("wcfm-date-range-refreshed",function(){$wcfm_enquiry_table.ajax.reload()}),e("#enquiry_product").length>0&&e("#enquiry_product").on("change",function(){$enquiry_product=e("#enquiry_product").val(),$wcfm_enquiry_table.ajax.reload()}).select2($wcfm_product_select_args),e("#dropdown_vendor").length>0&&e("#dropdown_vendor").on("change",function(){$enquiry_vendor=e("#dropdown_vendor").val(),$wcfm_enquiry_table.ajax.reload()}).select2($wcfm_vendor_select_args),e(document.body).on("updated_wcfm-enquiry",function(){e(".wcfm_enquiry_delete").each(function(){e(this).click(function(r){return r.preventDefault(),confirm(wcfm_dashboard_messages.enquiry_delete_confirm)&&function(e){jQuery("#wcfm_enquiry_listing_expander").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var r={action:"delete_wcfm_enquiry",enquiryid:e.data("enquiryid"),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:r,success:function(e){$wcfm_enquiry_table&&$wcfm_enquiry_table.ajax.reload(),jQuery("#wcfm_enquiry_listing_expander").unblock()}})}(e(this)),!1})})}),function e(){wcfm_enquiry_auto_refresher.is_allow&&(clearTimeout(enquiryBoardRefrsherTime),enquiryBoardRefrsherTime=setTimeout(function(){$wcfm_enquiry_table.ajax.reload(),e()},wcfm_enquiry_auto_refresher.duration))}(),e(".wcfm_filters_wrap").length>0&&(e(".dataTable").before(e(".wcfm_filters_wrap")),e(".wcfm_filters_wrap").css("display","inline-block"))});