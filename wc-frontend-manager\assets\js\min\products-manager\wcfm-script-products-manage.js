var removed_variations=[],removed_person_types=[],product_form_is_valid=!0,product_variation_auto_generate="",product_manage_from_popup="";jQuery(document).ready((function(e){if(e(".wcfm-tabWrap .page_collapsible").collapsible({defaultOpen:"wcfm_products_manage_form_inventory_head",speed:"slow",loadOpen:function(e){e.next().show()},loadClose:function(e,t){e.next().hide()},animateOpen:function(t,a){e(".collapse-open").addClass("collapse-close").removeClass("collapse-open"),t.addClass("collapse-open"),e(".collapse-close").find("span").removeClass("fa-arrow-alt-circle-right block-indicator"),t.find("span").addClass("fa-arrow-alt-circle-right block-indicator"),e(".wcfm-tabWrap").find(".wcfm-container").stop(!0,!0).slideUp(a.speed),t.next().stop(!0,!0).slideDown(a.speed),e(document.body).trigger("wcfm_product_tab_changed",t)},animateClose:function(e,t){e.find("span").removeClass("fa-arrow-circle-up block-indicator"),e.next().stop(!0,!0).slideUp(t.speed)}}),e(".wcfm-tabWrap .page_collapsible").each((function(){e(this).html('<div class="page_collapsible_content_holder">'+e(this).html()+"</div>"),e(this).find(".page_collapsible_content_holder").after(e(this).find("span"))})),e(".wcfm-tabWrap .page_collapsible").find("span").addClass("wcfmfa"),e(".collapse-open").addClass("collapse-close").removeClass("collapse-open"),e(".wcfm-tabWrap").find(".wcfm-container").hide(),e(".wcfm-tabWrap").find(".page_collapsible:visible:first").click(),e("#product_cats").length>0&&e("#product_cats").select2({placeholder:wcfm_dashboard_messages.choose_category_select2}),e(".product_taxonomies").length>0&&e(".product_taxonomies").each((function(){e("#"+e(this).attr("id")).select2({placeholder:wcfm_dashboard_messages.choose_select2+" "+e(".taxonomy_"+e(this).attr("id")).text()+" ..."})})),e(".product_tags_as_dropdown").length>0&&e(".product_tags_as_dropdown").select2({placeholder:wcfm_dashboard_messages.choose_tags_select2}),e("#upsell_ids").length>0&&e("#upsell_ids").select2($wcfm_product_select_args),e("#crosssell_ids").length>0&&e("#crosssell_ids").select2($wcfm_product_select_args),e("#grouped_products").length>0&&e("#grouped_products").select2($wcfm_product_select_args),e("#wcfm_associate_vendor").length>0&&e("#wcfm_associate_vendor").select2($wcfm_vendor_select_args),e("#wcfm_coupon_title").length>0&&e("#wcfm_coupon_title").select2(),e("#_restricted_countries").length>0&&e("#_restricted_countries").select2(),e(".wcfm_multi_select").length>0&&e(".wcfm_multi_select").select2({placeholder:wcfm_dashboard_messages.choose_select2+" ..."}),e("#product_cats_checklist").length>0&&(e(".sub_checklist_toggler").each((function(){e(this).parent().find(".product_taxonomy_sub_checklist").length>0&&e(this).css("visibility","visible"),e(this).click((function(){e(this).toggleClass("fa-arrow-circle-down"),e(this).parent().find(".product_taxonomy_sub_checklist").toggleClass("product_taxonomy_sub_checklist_visible")}))})),e(".product_cats_checklist_item_hide_by_cap").attr("disabled",!0)),e("#product_type").length>0){var t=["simple","variable","grouped","external","booking"];e("#product_type").change((function(){var a=e(this).val();$product_type_org=a,setTimeout((function(){e(".wcfm-tabWrap .page_collapsible:not(.wcfm_head_hide):first").click(),e.each(wcfm_product_type_default_tab,(function(t,a){t==$product_type_org&&"wcfm_products_manage_form_inventory_head"!=a&&e("#"+a).length>0&&e("#"+a).click()}))}),700),e("#product_cats").length>0&&($has_cat=!1,e("#product_cats").find("option").attr("disabled",!0).css("display","none"),e.each(wcfm_product_type_categories,(function(t,i){a==t&&e.each(i,(function(t,a){e("#product_cats").find(".wcfm_cat_option_"+a).attr("disabled",!1).css("display","block"),$has_cat=!0}))})),$has_cat||e("#product_cats").find("option").attr("disabled",!1).css("display","block"),e("#product_cats").select2("destroy"),e("#product_cats").select2({placeholder:wcfm_dashboard_messages.choose_category_select2,maximumSelectionLength:e("#product_cats").data("catlimit")})),e("#product_cats_checklist").length>0&&($has_cat=!1,e("#product_cats_checklist").find(".product_cats_checklist_item").addClass("product_cats_checklist_item_hide"),e.each(wcfm_product_type_categories,(function(t,i){a==t&&e.each(i,(function(t,a){e("#product_cats_checklist").find(".checklist_item_"+a).removeClass("product_cats_checklist_item_hide"),e("#product_cats_checklist").find(".checklist_item_"+a).find(".product_taxonomy_sub_checklist").find(".product_cats_checklist_item").removeClass("product_cats_checklist_item_hide"),$has_cat=!0}))})),$has_cat||e("#product_cats_checklist").find(".product_cats_checklist_item").removeClass("product_cats_checklist_item_hide"),e(".product_cats_checklist_item_hide_by_cap").attr("disabled",!0)),e("#wcfm_products_manage_form .page_collapsible").addClass("wcfm_head_hide"),e("#wcfm_products_manage_form .wcfm-container").addClass("wcfm_block_hide"),e(".wcfm_ele").addClass("wcfm_ele_hide"),e("."+a).removeClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"),-1==e.inArray(a,t)&&(e(".simple").removeClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"),e(".non-"+a).addClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"),a="simple"),"simple"!=a&&e("#is_downloadable").attr("checked",!1),e("#is_downloadable").change(),e("#is_catalog").change(),e("#is_virtual").change(),e(document.body).trigger("wcfm_product_type_changed"),e(".wcaddons").length>0&&e(".wcaddons").removeClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"),collapsHeight=0,e(".wcfm-tabWrap .page_collapsible").each((function(){e(this).hasClass("wcfm_head_hide")||(collapsHeight+=e(this).height()+21)})),setTimeout((function(){resetCollapsHeight(e(".collapse-open").next(".wcfm-container").find(".wcfm_ele:not(.wcfm_title):first"))}),600)})).change(),e("#is_downloadable").change((function(){e(this).is(":checked")?(e(".downlodable").removeClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"),resetCollapsHeight(e(".collapse-open").next(".wcfm-container").find(".wcfm_ele:not(.wcfm_title):first"))):(e(".downlodable").addClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"),"variable"==e("#product_type").val()&&e(".variable").removeClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"))})).change(),e(".is_downloadable_hidden").change((function(){"enable"==e(this).val()?(e(".downlodable").removeClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"),resetCollapsHeight(e(".collapse-open").next(".wcfm-container").find(".wcfm_ele:not(.wcfm_title):first"))):e(".downlodable").addClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide")})).change(),0==e("#is_downloadable").length&&e(".downlodable").addClass("downloadable_ele_hide"),e("#is_virtual").change((function(){e(this).hasClass("wcfm_ele_hide")||(e(this).is(":checked")?(e(".nonvirtual").addClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"),e(".non-virtual").addClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide")):(e(".nonvirtual").removeClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"),e(".non-virtual").removeClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide")))})).change(),e(".is_virtual_hidden").change((function(){"enable"==e(this).val()?(e(".nonvirtual").addClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"),e(".non-virtual").addClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide")):(e(".nonvirtual").removeClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"),e(".non-virtual").removeClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"))})).change(),e("#is_catalog").change((function(){e(this).is(":checked")?(e(".catalog_options").removeClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide"),resetCollapsHeight(e(".collapse-open").next(".wcfm-container").find(".wcfm_ele:not(.wcfm_title):first"))):e(".catalog_options").addClass("wcfm_ele_hide wcfm_block_hide wcfm_head_hide")})).change()}else e("#wcfm_products_manage_form .page_collapsible").addClass("wcfm_head_hide"),e("#wcfm_products_manage_form .wcfm-container").addClass("wcfm_block_hide"),e(".wcfm_ele").addClass("wcfm_ele_hide");function a(){e(".variation_manage_stock_ele").each((function(){e(this).off("change").on("change",(function(){e(this).is(":checked")?(e(this).parent().find(".variation_non_manage_stock_ele").removeClass("non_stock_ele_hide"),e(this).parent().find(".variation_stock_status_ele").addClass("non_stock_ele_hide"),resetCollapsHeight(e("#variations"))):(e(this).parent().find(".variation_non_manage_stock_ele").addClass("non_stock_ele_hide"),e(this).parent().find(".variation_stock_status_ele").removeClass("non_stock_ele_hide"),resetCollapsHeight(e("#variations")))})).change()})),e(".variation_is_virtual_ele").each((function(){e(this).off("change").on("change",(function(){e(this).is(":checked")?(e(this).parent().find(".variation_non_virtual_ele").addClass("non_virtual_ele_hide"),resetCollapsHeight(e("#variations"))):(e(this).parent().find(".variation_non_virtual_ele").removeClass("non_virtual_ele_hide"),resetCollapsHeight(e("#variations")))})).change()})),e(".variation_is_downloadable_ele").each((function(){e(this).off("change").on("change",(function(){e(this).is(":checked")?(e(this).parent().find(".variation_downloadable_ele").removeClass("downloadable_ele_hide"),e(this).parent().find(".variation_downloadable_ele").next(".upload_button").removeClass("downloadable_ele_hide"),resetCollapsHeight(e("#variations"))):(e(this).parent().find(".variation_downloadable_ele").addClass("downloadable_ele_hide"),e(this).parent().find(".variation_downloadable_ele").next(".upload_button").addClass("downloadable_ele_hide"),resetCollapsHeight(e("#variations")))})).change()})),e(".variation_tiered_price_rules_type").each((function(){e(this).off("change").on("change",(function(){e(this).parent().find(".tiered_price_rule_type").addClass("wcfm_custom_hide"),e(this).parent().find(".tiered_price_rule_type_"+e(this).val()).removeClass("wcfm_custom_hide"),resetCollapsHeight(e("#variations"))})).change()}))}function i(e){var t=parseInt(2,10),a=Math.pow(10,t),i=parseFloat(e);return(Math.round(Math.round(i*a*10)/10)/a).toFixed(2)}function s(t){var i=t.data("limit");void 0===i&&(i=-1),1==t.children(".multi_input_block").length&&t.children(".multi_input_block").children(".remove_multi_input_block").css("display","none"),t.children(".multi_input_block").length==i?t.find(".add_multi_input_block").hide():t.find(".add_multi_input_block").show(),t.children(".multi_input_block").each((function(){e(this)[0]!=t.children(".multi_input_block:last")[0]&&e(this).children(".add_multi_input_block").remove(),e(this).children(".add_multi_input_block").attr("title",wcfm_dashboard_messages.wcfm_multiblick_addnew_help),e(this).children(".remove_multi_input_block").attr("title",wcfm_dashboard_messages.wcfm_multiblick_remove_help)}));var s=t.data("has-dummy");s&&t.find(".add_multi_input_block").hide(),t.children(".multi_input_block").children(".add_multi_input_block").off("click").on("click",(function(){var c=t.attr("id"),n=t.data("name"),o=t.data("length");o++;var l=t.children(".multi_input_block:first").clone(!1);l.find("textarea,input:not(input[type=button],input[type=submit],input[type=checkbox],input[type=radio])").val(""),l.find("input[type=checkbox]").attr("checked",!1),l.find(".select2-container").remove(),l.find("select").select2(),l.find("select").select2("destroy"),l.removeClass("multi_input_block_dummy"),l.children(".wcfm-wp-fields-uploader,.wp-picker-container,.multi_input_block_element:not(.multi_input_holder)").each((function(){var t=e(this),a=t.data("name");if(t.hasClass("wcfm-wp-fields-uploader")){var i=t;a=i.find(".multi_input_block_element").data("name"),mime_type=i.find(".remove_button").data("mime"),"image"==mime_type?i.find("img").attr("src",i.find("img").data("placeholder")).attr("id",c+"_"+a+"_"+o+"_display").addClass("placeHolder"):(i.find("a").attr("href","#").attr("id",c+"_"+a+"_"+o+"_display"),i.find("a span").hide()),i.find(".multi_input_block_element").attr("id",c+"_"+a+"_"+o).attr("name",n+"["+o+"]["+a+"]"),i.find(".upload_button").attr("id",c+"_"+a+"_"+o+"_button").show(),i.find(".remove_button").attr("id",c+"_"+a+"_"+o+"_remove_button").hide(),i.hasClass("wcfm_gallery_upload")?addWCFMMultiUploaderProperty(i):addWCFMUploaderProperty(i)}else t.hasClass("wp-picker-container")?($new_ele=t.find(".multi_input_block_element"),t.replaceWith($new_ele),a=$new_ele.data("name"),$new_ele.attr("name",n+"["+o+"]["+a+"]"),$new_ele.attr("id",c+"_"+a+"_"+o),$new_ele.removeClass("wp-color-picker").wpColorPicker()):(t.attr("name",n+"["+o+"]["+a+"]"),t.attr("id",c+"_"+a+"_"+o));t.hasClass("wcfm_datepicker")?t.removeClass("hasDatepicker").datepicker({dateFormat:t.data("date_format"),closeText:wcfm_datepicker_params.closeText,currentText:wcfm_datepicker_params.currentText,monthNames:wcfm_datepicker_params.monthNames,monthNamesShort:wcfm_datepicker_params.monthNamesShort,dayNames:wcfm_datepicker_params.dayNames,dayNamesShort:wcfm_datepicker_params.dayNamesShort,dayNamesMin:wcfm_datepicker_params.dayNamesMin,firstDay:wcfm_datepicker_params.firstDay,isRTL:wcfm_datepicker_params.isRTL,changeMonth:!0,changeYear:!0}):t.hasClass("time_picker")&&(e(".time_picker").timepicker("remove").timepicker({step:15}),t.timepicker("remove").timepicker({step:15}))})),l.children(".multi_input_holder").each((function(){_(e(this),c,n,o)})),l.children(".remove_multi_input_block").off("click").on("click",(function(){if(confirm(wcfm_dashboard_messages.multiblock_delete_confirm)){var a=e(this).parent().parent(),c=a.children(".multi_input_block").children(".add_multi_input_block").clone(!0);e(this).parent().remove(),a.children(".multi_input_block").children(".add_multi_input_block").remove(),a.children(".multi_input_block:last").append(c),a.children(".multi_input_block").length==i?a.find(".add_multi_input_block").hide():a.find(".add_multi_input_block").show(),s&&t.find(".add_multi_input_block").hide(),1==a.children(".multi_input_block").length&&a.children(".multi_input_block").children(".remove_multi_input_block").css("display","none"),t.hasClass("wcfm_additional_variation_images")||t.hasClass("wcfm_per_product_shipping_variation_fields")||t.hasClass("wcfm_wcaddons_fields")||resetCollapsHeight(t)}})),l.children(".add_multi_input_block").remove(),t.append(l),t.children(".multi_input_block:last").find(".wcfm-select2").select2({placeholder:wcfm_dashboard_messages.choose_select2+" ..."}),t.children(".multi_input_block:last").append(e(this)),t.children(".multi_input_block").length>1&&t.children(".multi_input_block").children(".remove_multi_input_block").css("display","block"),t.children(".multi_input_block").length==i?t.find(".add_multi_input_block").hide():t.find(".add_multi_input_block").show(),s&&t.find(".add_multi_input_block").hide(),t.data("length",o),a(),t.hasClass("wcfm_additional_variation_images")||t.hasClass("wcfm_per_product_shipping_variation_fields")||t.hasClass("wcfm_wcaddons_fields")?(t.hasClass("wcfm_per_product_shipping_variation_fields")||t.hasClass("wcfm_wcaddons_fields"))&&resetCollapsHeight(t.parent()):resetCollapsHeight(t)})),t.hasClass("multi_input_block_element"),t.children(".multi_input_block").children(".multi_input_holder").length,t.children(".multi_input_block").children(".remove_multi_input_block").off("click").on("click",(function(){if(confirm(wcfm_dashboard_messages.multiblock_delete_confirm)){var a=e(this).parent().parent(),c=a.children(".multi_input_block").children(".add_multi_input_block").clone(!0);e(this).parent().find(1==e('input[data-name="is_taxonomy"]').data("name"))&&($taxonomy=e(this).parent().find(e('input[data-name="tax_name"]')).val(),e("select.wcfm_attribute_taxonomy").find('option[value="'+$taxonomy+'"]').removeAttr("disabled")),e(this).parent().remove(),a.children(".multi_input_block").children(".add_multi_input_block").remove(),a.children(".multi_input_block:last").append(c),1==a.children(".multi_input_block").length&&a.children(".multi_input_block").children(".remove_multi_input_block").css("display","none"),a.children(".multi_input_block").length==i?a.find(".add_multi_input_block").hide():a.find(".add_multi_input_block").show(),s&&t.find(".add_multi_input_block").hide(),t.hasClass("wcfm_additional_variation_images")||t.hasClass("wcfm_per_product_shipping_variation_fields")||t.hasClass("wcfm_wcaddons_fields")||resetCollapsHeight(t)}})),wcfm_params.is_mobile||t.sortable({update:function(e,a){c(t)}}).disableSelection()}function c(t){var a=t.attr("id"),i=t.data("name"),s=0;t.children(".multi_input_block").each((function(){e(this).children(".wcfm-wp-fields-uploader,.multi_input_block_element:not(.multi_input_holder)").each((function(){var t=e(this),c=t.data("name");if(t.hasClass("wcfm-wp-fields-uploader")){var _=t;c=_.find(".multi_input_block_element").data("name"),_.find("img").attr("id",a+"_"+c+"_"+s+"_display"),_.find(".multi_input_block_element").attr("id",a+"_"+c+"_"+s).attr("name",i+"["+s+"]["+c+"]"),_.find(".upload_button").attr("id",a+"_"+c+"_"+s+"_button"),_.find(".remove_button").attr("id",a+"_"+c+"_"+s+"_remove_button")}else{var n=t.attr("multiple");void 0!==n&&!1!==n?t.attr("name",i+"["+s+"]["+c+"][]"):t.attr("name",i+"["+s+"]["+c+"]"),t.attr("id",a+"_"+c+"_"+s)}})),s++}))}function _(t,a,i,c){t.children(".multi_input_block:not(:last)").remove();for(var n=t.attr("id"),o=(n=n.replace(a+"_","")).split("_"),l="",r=0;r<o.length-1;r++)""!=l&&(l+="_"),l+=o[r];t.attr("data-name",i+"["+c+"]["+l+"]"),t.attr("id",a+"_"+l+"_"+c);t.children(".multi_input_block").children(".wcfm-wp-fields-uploader,.multi_input_block_element:not(.multi_input_holder)").each((function(){var t=e(this),s=t.data("name");if(t.hasClass("wcfm-wp-fields-uploader")){var _=t;s=_.find(".multi_input_block_element").data("name"),mime_type=_.find(".remove_button").data("mime"),"image"==mime_type?_.find("img").attr("id",a+"_"+l+"_"+c+"_"+s+"_0_display"):(_.find("a").attr("id",a+"_"+l+"_"+c+"_"+s+"_0_display"),_.find("a span").hide()),_.find(".multi_input_block_element").attr("id",a+"_"+l+"_"+c+"_"+s+"_0").attr("name",i+"["+c+"]["+l+"][0]["+s+"]"),_.find(".upload_button").attr("id",a+"_"+l+"_"+c+"_"+s+"_0_button").attr("name",a+"_"+l+"_"+c+"_"+s+"_0_button"),_.find(".remove_button").attr("id",a+"_"+l+"_"+c+"_"+s+"_0_remove_button").attr("name",a+"_"+l+"_"+c+"_"+s+"_0_remove_button"),_.hasClass("wcfm_gallery_upload")?addWCFMMultiUploaderProperty(_):addWCFMUploaderProperty(_)}else{var n=t.attr("multiple");void 0!==n&&!1!==n?t.attr("name",i+"["+c+"]["+l+"][0]["+s+"][]"):t.attr("name",i+"["+c+"]["+l+"][0]["+s+"]"),t.attr("id",a+"_"+l+"_"+c+"_"+s+"_0")}t.hasClass("wcfm_datepicker")?t.removeClass("hasDatepicker").datepicker({dateFormat:t.data("date_format"),closeText:wcfm_datepicker_params.closeText,currentText:wcfm_datepicker_params.currentText,monthNames:wcfm_datepicker_params.monthNames,monthNamesShort:wcfm_datepicker_params.monthNamesShort,dayNames:wcfm_datepicker_params.dayNames,dayNamesShort:wcfm_datepicker_params.dayNamesShort,dayNamesMin:wcfm_datepicker_params.dayNamesMin,firstDay:wcfm_datepicker_params.firstDay,isRTL:wcfm_datepicker_params.isRTL,changeMonth:!0,changeYear:!0}):t.hasClass("time_picker")&&(e(".time_picker").timepicker("remove").timepicker({step:15}),t.timepicker("remove").timepicker({step:15}))})),s(t),t.children(".multi_input_block").children(".multi_input_holder").length>0&&t.children(".multi_input_block").css("padding-bottom","40px"),t.children(".multi_input_block").children(".multi_input_holder").each((function(){_(e(this),a+"_"+l+"_0",i+"["+c+"]["+l+"]",0),e(this).find(".multi_input_block_manupulate").each((function(){e(this).off("click").on("click",(function(){resetCollapsHeight(t)}))}))}))}function n(t){e("#attributes").children(".multi_input_block").children(".attributes_collapser").each((function(){t&&e(this).addClass("fa-arrow-circle-up"),e(this).off("click").on("click",(function(){e(this).parent().find(".wcfm_ele:not(.attribute_ele), .select2, .wcfm_add_attribute_term").toggleClass("variation_ele_hide"),e(this).parent().find('input[type="checkbox"]').toggleClass("collapsed_checkbox"),e(this).toggleClass("fa-arrow-circle-up"),resetCollapsHeight(e("#attributes"))})).click()}))}function o(){e("#wcfm_products_manage_form_variations_empty_expander").removeClass("wcfm_custom_hide"),e("#wcfm_products_manage_form_variations_expander").addClass("wcfm_custom_hide"),e("#variations").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var t={action:"wcfm_generate_variation_attributes",wcfm_products_manage_form:e("#wcfm_products_manage_form").serialize(),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};e.ajax({type:"POST",url:wcfm_params.ajax_url,data:t,success:function(t){if(t){var a="";e.each(e.parseJSON(t),(function(t,i){var s='<select name="default_attributes[attribute_'+t.toLowerCase()+']" class="wcfm-select wcfm_ele wcfm_half_ele default_attribute_ele attribute_ele attribute_ele_new variable" data-name="default_attribute_'+t.toLowerCase()+'"><option value="">'+wcfm_dashboard_messages.any_attribute+" "+i.name+" ..</option>";e.each(i.data,(function(e,t){s+='<option value="'+e+'">'+t+"</option>"})),s+="</select>",e(".default_attributes_holder").each((function(){e(this).find('select[data-name="default_attribute_'+t.toLowerCase()+'"]').length>0?($attr_selected_val=e(this).find('select[data-name="default_attribute_'+t.toLowerCase()+'"]').val(),e(this).find('select[data-name="default_attribute_'+t.toLowerCase()+'"]').replaceWith(e(s)),e(this).find('select[data-name="default_attribute_'+t.toLowerCase()+'"]').val($attr_selected_val)):e(this).find('input[data-name="default_attribute_'+t.toLowerCase()+'"]').length>0?($attr_selected_val=e(this).find('input[data-name="default_attribute_'+t.toLowerCase()+'"]').val(),e(this).find('input[data-name="default_attribute_'+t.toLowerCase()+'"]').replaceWith(e(s)),e(this).find('select[data-name="default_attribute_'+t.toLowerCase()+'"]').val($attr_selected_val)):e(this).append(s)})),a='<select name="attribute_'+t.toLowerCase()+'" class="wcfm-select wcfm_ele wcfm_half_ele attribute_ele attribute_ele_new variable multi_input_block_element" data-name="attribute_'+t.toLowerCase()+'"><option value="">'+wcfm_dashboard_messages.any_attribute+" "+i.name+" ..</option>",e.each(i.data,(function(e,t){a+='<option value="'+e+'">'+t+"</option>"})),a+="</select>",e("#variations").children(".multi_input_block").each((function(){e(this).find('select[data-name="attribute_'+t.toLowerCase()+'"]').length>0?($attr_selected_val=e(this).find('select[data-name="attribute_'+t.toLowerCase()+'"]').val(),e(this).find('select[data-name="attribute_'+t.toLowerCase()+'"]').replaceWith(e(a)),e(this).find('select[data-name="attribute_'+t.toLowerCase()+'"]').val($attr_selected_val)):e(this).find('input[data-name="attribute_'+t.toLowerCase()+'"]').length>0?($attr_selected_val=e(this).find('input[data-name="attribute_'+t.toLowerCase()+'"]').val(),e(this).find('input[data-name="attribute_'+t.toLowerCase()+'"]').replaceWith(e(a)),e(this).find('select[data-name="attribute_'+t.toLowerCase()+'"]').val($attr_selected_val)):e(this).prepend(a)}))})),e(".default_attribute_ele").change(),e(".attribute_ele_old").remove(),e(".attribute_ele_new").addClass("attribute_ele_old").removeClass("attribute_ele_new"),c(e("#variations")),a.length>0&&(e("#wcfm_products_manage_form_variations_empty_expander").addClass("wcfm_custom_hide"),e("#wcfm_products_manage_form_variations_expander").removeClass("wcfm_custom_hide"))}e("#variations").unblock(),"variable"!=e("#product_type").val()&&"variable-subcription"!=e("#product_type").val()||!e(".variations").hasClass("collapse-open")||resetCollapsHeight(e("#variations"))},dataType:"html"})}if(e(".variations").click((function(){e(this).hasClass("collapse-open")&&o()})),e(document.body).on("wcfm_product_popup_variations_option",(function(){o()})),e("#tiered_price_rules_type").length>0&&(e("#tiered_price_rules_type").change((function(){e(".tiered_price_rule_type").addClass("wcfm_custom_hide"),e(".tiered_price_rule_type_"+e(this).val()).removeClass("wcfm_custom_hide")})),e(".tiered_price_rule_type").addClass("wcfm_custom_hide"),e(".tiered_price_rule_type_"+e("#tiered_price_rules_type").val()).removeClass("wcfm_custom_hide")),a(),e(".manage_stock_ele").change((function(){e(this).is(":checked")?(e(this).parent().find(".non_manage_stock_ele").removeClass("non_stock_ele_hide wcfm_custom_hide"),e(this).parent().find(".stock_status_ele").addClass("non_stock_ele_hide wcfm_custom_hide"),resetCollapsHeight(e("#manage_stock"))):(e(this).parent().find(".non_manage_stock_ele").addClass("non_stock_ele_hide wcfm_custom_hide"),e(this).parent().find(".stock_status_ele").removeClass("non_stock_ele_hide wcfm_custom_hide"))})).change(),e(".policy_override_ele").change((function(){e(this).is(":checked")?(e(this).parent().find(".non_policy_override_ele").removeClass("wcfm_custom_hide"),e(this).parent().find(".non_policy_override_ele.wcfm_wpeditor").parents(".wp-editor-wrap").removeClass("wcfm_custom_hide"),resetCollapsHeight(e("#wcfm_policy_override"))):(e(this).parent().find(".non_policy_override_ele").addClass("wcfm_custom_hide"),e(this).parent().find(".non_policy_override_ele.wcfm_wpeditor").parents(".wp-editor-wrap").addClass("wcfm_custom_hide"))})).change(),resetCollapsHeight(e(".collapse-open").next(".wcfm-container").find(".wcfm_ele:not(.wcfm_title):first")),e(".sales_schedule").click((function(){e(".sales_schedule_ele").toggleClass("sales_schedule_ele_show")})),e("#sale_date_from").length>0&&(e("#sale_date_from").datepicker({changeMonth:!0,changeYear:!0,dateFormat:"yy-mm-dd",onClose:function(t){e("#sale_date_upto").datepicker("option","minDate",t)}}),e("#sale_date_upto").datepicker({changeMonth:!0,changeYear:!0,dateFormat:"yy-mm-dd",onClose:function(t){e("#sale_date_from").datepicker("option","maxDate",t)}})),e("#_unit_price_auto").length>0&&(e("#_unit_price_auto").is(":checked")?(e("#_unit_price_regular").attr("readonly",!0),e("#_unit_price_sale").attr("readonly",!0)):(e("#_unit_price_regular").removeAttr("readonly"),e("#_unit_price_sale").removeAttr("readonly")),e("#_unit_price_auto").click((function(){e("#_unit_price_auto").is(":checked")?(e("#_unit_price_regular").attr("readonly",!0),e("#_unit_price_sale").attr("readonly",!0)):(e("#_unit_price_regular").removeAttr("readonly"),e("#_unit_price_sale").removeAttr("readonly"))})),e("#regular_price, #sale_price, #_unit_base").focusout((function(){e("#_unit_price_auto").is(":checked")&&($_unit_base=1,$_unit_product=$_unit_base,$regular_price=parseFloat(e("#regular_price").val()),$sale_price=parseFloat(e("#sale_price").val()),e("#_unit_base").val()&&($_unit_base=e("#_unit_base").val()),e("#_unit_product").val()?$_unit_product=parseFloat(e("#_unit_product").val().replace(",",".")):($_unit_product=parseFloat($_unit_base),$_unit_base=1),$_unit_base&&$regular_price?e("#_unit_price_regular").val(i($regular_price/$_unit_product*$_unit_base)):e("#_unit_price_regular").val(""),$_unit_base&&$sale_price?e("#_unit_price_sale").val(i($sale_price/$_unit_product*$_unit_base)):e("#_unit_price_sale").val(""))})).focusout()),e(".multi_input_holder").each((function(){s(e(this))})),e("button.wcfm_add_attribute").on("click",(function(){return e("select.wcfm_attribute_taxonomy").val()&&(e("#attributes").children(".multi_input_block").children(".add_multi_input_block").click(),e("#attributes").find(".remove_multi_input_block").remove(),e("#attributes").find(".multi_input_block").each((function(){e(this).find('input[data-name="is_variation"]').off("change").on("change",(function(){o()}))})),c(e("#attributes")),n(!1),e(".attributes_collapser").click(),e("#attributes").children(".multi_input_block:last").find('input[data-name="is_active"]').click(),e("#attributes").children(".multi_input_block:last").find('input[type="checkbox"]').attr("checked",!0),e("#attributes").children(".multi_input_block:last").find(".attributes_collapser").click(),e("#attributes").children(".multi_input_block:last").find(".attribute_ele").focus()),!1})),e(".wcfm_category_attributes_mapping_msg").length>0&&e("#attributes").append(e(".wcfm_category_attributes_mapping_msg")),e(".wcfm_select_attributes").length>0&&(e(".wcfm_select_attributes").each((function(){e("#attributes").append(e(this).html()),e(this).remove()})),s(e("#attributes")),c(e("#attributes")),initiateTip(),e("#attributes").find(".remove_multi_input_block").remove()),e("#text_attributes").length>0&&(e("#attributes").append(e("#text_attributes").html()),e("#text_attributes").remove(),s(e("#attributes")),c(e("#attributes")),initiateTip(),e("#attributes").find(".remove_multi_input_block").remove()),e("#attributes").find(".multi_input_block").each((function(){$multi_input_block=e(this),$multi_input_block.prepend('<span class="fields_collapser attributes_collapser wcfmfa fa-arrow-circle-down" title="'+wcfm_dashboard_messages.wcfm_multiblick_collapse_help+'"></span>'),$multi_input_block.find(1==e('input[data-name="is_taxonomy"]').data("name"))&&($taxonomy=$multi_input_block.find('input[data-name="tax_name"]').val(),e("select.wcfm_attribute_taxonomy").find('option[value="'+$taxonomy+'"]').attr("disabled","disabled")),$multi_input_block.find('input[data-name="is_variation"]').off("change").on("change",(function(){o()})),$multi_input_block.find('input[data-name="is_active"]').off("change").on("change",(function(){e(this).is(":checked")?(e(this).parent().find(".wcfm_ele:not(.attribute_ele), .select2, .wcfm_add_attribute_term").removeClass("variation_ele_hide"),e(this).parent().find('input[type="checkbox"]').attr("checked",!0).removeClass("collapsed_checkbox"),e(this).parent().find(".attributes_collapser").addClass("fa-arrow-circle-up")):(e(this).parent().find(".wcfm_ele:not(.attribute_ele), .select2, .wcfm_add_attribute_term").addClass("variation_ele_hide"),e(this).parent().find('input[type="checkbox"]').attr("checked",!1).addClass("collapsed_checkbox"),e(this).parent().find(".wcfm_select_no_attributes").click(),e(this).parent().find(".attributes_collapser").removeClass("fa-arrow-circle-up")),resetCollapsHeight(e("#attributes"))})),$multi_input_block.find("select").length>0&&($attrlimit=$multi_input_block.find("select").data("attrlimit"),1!=$attrlimit&&($multi_input_block.find("select").after(e('<div class="wcfm-clearfix"></div>')),$multi_input_block.find("select").after(e('<button type="button" class="button wcfm_add_attribute_term wcfm_select_all_attributes">'+wcfm_dashboard_messages.select_all+"</button>")),$multi_input_block.find("select").after(e('<button type="button" class="button wcfm_add_attribute_term wcfm_select_no_attributes">'+wcfm_dashboard_messages.select_none+"</button>")),$multi_input_block.find("select").hasClass("allow_add_term")&&$multi_input_block.find("select").after(e('<button type="button" class="button wcfm_add_attribute_term wcfm_add_attributes_new_term">'+wcfm_dashboard_messages.add_new+"</button>")),$multi_input_block.find("select").after(e('<div class="wcfm-clearfix"></div>'))),$multi_input_block.find("select").each((function(){e(this).select2({placeholder:wcfm_dashboard_messages.search_attribute_select2,maximumSelectionLength:$attrlimit})})))})),n(!0),e(".wcfm_select_all_attributes").each((function(){e(this).on("click",(function(){e(this).parent().find("select option").attr("selected","selected"),e(this).parent().find("select").change()}))})),e(".wcfm_select_no_attributes").each((function(){e(this).on("click",(function(){e(this).parent().find("select option").removeAttr("selected"),e(this).parent().find("select").change()}))})),o(),$default_attributes=e('input[data-name="default_attributes_hidden"]'),$default_attributes.length>0&&($default_attributes_val=$default_attributes.val(),$default_attributes_val.length>0&&e.each(e.parseJSON($default_attributes_val),(function(t,a){$regex=/"/g,a=a.replace($regex,"&quot;"),$regex=/'/g,a=a.replace($regex,"&#039;"),e(".default_attributes_holder").append('<input type="hidden" name="default_attribute_'+t+'" data-name="default_attribute_'+t+'" value="'+a+'" />')}))),e("#variations").children(".multi_input_block").each((function(){$multi_input_block=e(this),$multi_input_block.prepend('<div class="wcfm_clearfix"></div>'),$multi_input_block.prepend('<span class="fields_collapser variations_collapser wcfmfa fa-arrow-circle-down" title="'+wcfm_dashboard_messages.wcfm_multiblick_collapse_help+'"></span>'),$attributes=$multi_input_block.find('input[data-name="attributes"]'),$attributes_val=$attributes.val(),$attributes_val.length>0&&e.each(e.parseJSON($attributes_val),(function(e,t){$regex=/"/g,t=t.replace($regex,"&quot;"),$regex=/'/g,t=t.replace($regex,"&#039;"),$multi_input_block.prepend('<input type="hidden" name="'+e+'" data-name="'+e+'" value="'+t+'" />')})),$multi_input_block.find(".var_sale_date_from").datepicker({changeMonth:!0,changeYear:!0,dateFormat:"yy-mm-dd",onClose:function(e){$multi_input_block.find(".var_sale_date_upto").datepicker("option","minDate",e)}}),$multi_input_block.find(".var_sale_date_upto").datepicker({changeMonth:!0,changeYear:!0,dateFormat:"yy-mm-dd",onClose:function(e){$multi_input_block.find(".var_sale_date_from").datepicker("option","maxDate",e)}}),$multi_input_block.find(".var_sales_schedule").click((function(){e(this).parent().find(".var_sales_schedule_ele").toggleClass("var_sales_schedule_ele_show")}))})),e("#variations").children(".multi_input_block").children(".add_multi_input_block").click((function(){e("#variations").children(".multi_input_block").children(".variations_collapser").each((function(){e(this).off("click").on("click",(function(){e(this).parent().find(".wcfm_ele:not(.attribute_ele), .wcfm_title").toggleClass("variation_ele_hide"),e(this).toggleClass("fa-arrow-circle-up"),resetCollapsHeight(e("#variations"))})),e(this).parent().find(".wcfm_ele:not(.attribute_ele), .wcfm_title").addClass("variation_ele_hide"),e(this).removeClass("fa-arrow-circle-up"),resetCollapsHeight(e("#variations"))})),e("#variations").children(".multi_input_block:last").children(".variations_collapser").click(),e("#variations").children(".multi_input_block:last").find('input[type="checkbox"]:first').attr("checked",!0),e("#variations").children(".multi_input_block:last").find(".var_sales_schedule_ele").removeClass("var_sales_schedule_ele_show"),e("#variations").children(".multi_input_block:last").find(".var_sales_schedule").click((function(){e(this).parent().find(".var_sales_schedule_ele").toggleClass("var_sales_schedule_ele_show")})),e("#variations").children(".multi_input_block:last").find(".var_sale_date_from").removeClass("hasDatepicker").datepicker({changeMonth:!0,changeYear:!0,dateFormat:"yy-mm-dd",onClose:function(t){e("#variations").children(".multi_input_block:last").find(".var_sale_date_upto").datepicker("option","minDate",t)}}),e("#variations").children(".multi_input_block:last").find(".var_sale_date_upto").removeClass("hasDatepicker").datepicker({changeMonth:!0,changeYear:!0,dateFormat:"yy-mm-dd",onClose:function(t){e("#variations").children(".multi_input_block:last").find(".var_sale_date_from").datepicker("option","maxDate",t)}})})),e("#variations").children(".multi_input_block").children(".variations_collapser").each((function(){e(this).addClass("fa-arrow-circle-up"),e(this).off("click").on("click",(function(){e(this).parent().find(".wcfm_ele:not(.attribute_ele), .wcfm_title").toggleClass("variation_ele_hide"),e(this).toggleClass("fa-arrow-circle-up"),resetCollapsHeight(e("#variations"))})),$pro_id=e("#pro_id").val(),$pro_id&&0!=$pro_id&&e(this).click()})),e("#variations").children(".multi_input_block").children(".remove_multi_input_block").click((function(){removed_variations.push(e(this).parent().find(".variation_id").val())})),e("#variations_options").change((function(){if($variations_option=e(this).val(),$variations_option){switch($variations_option){case"on_enabled":e("#variations").find('input[data-name="enable"]').each((function(){e(this).attr("checked",!0)}));break;case"off_enabled":e("#variations").find('input[data-name="enable"]').each((function(){e(this).attr("checked",!1)}));break;case"on_downloadable":e("#variations").find('input[data-name="is_downloadable"]').each((function(){e(this).attr("checked",!0).change()}));break;case"off_downloadable":e("#variations").find('input[data-name="is_downloadable"]').each((function(){e(this).attr("checked",!1).change()}));break;case"on_virtual":e("#variations").find('input[data-name="is_virtual"]').each((function(){e(this).attr("checked",!0).change()}));break;case"off_virtual":e("#variations").find('input[data-name="is_virtual"]').each((function(){e(this).attr("checked",!1).change()}));break;case"on_manage_stock":e("#variations").find('input[data-name="manage_stock"]').each((function(){e(this).attr("checked",!0).change()}));break;case"off_manage_stock":e("#variations").find('input[data-name="manage_stock"]').each((function(){e(this).attr("checked",!1).change()}));break;case"variable_stock":null!=(t=prompt(wcfm_products_manage_messages.set_stock))&&e("#variations").find('input[data-name="stock_qty"]').each((function(){isNaN(parseFloat(t))||e(this).val(parseFloat(t))}));break;case"variable_increase_stock":var t;null!=(t=prompt(wcfm_products_manage_messages.increase_stock))&&e("#variations").find('input[data-name="stock_qty"]').each((function(){isNaN(parseFloat(t))||(e(this).val().length>0?e(this).val(parseFloat(e(this).val())+parseFloat(t)):e(this).val(parseFloat(t)))}));break;case"variable_stock_status_instock":e("#variations").find('select[data-name="stock_status"]').each((function(){e(this).val("instock")}));break;case"variable_stock_status_outofstock":e("#variations").find('select[data-name="stock_status"]').each((function(){e(this).val("outofstock")}));break;case"variable_stock_status_onbackorder":e("#variations").find('select[data-name="stock_status"]').each((function(){e(this).val("onbackorder")}));break;case"set_regular_price":null!=(a=prompt(wcfm_products_manage_messages.regular_price))&&e("#variations").find('input[data-name="regular_price"]').each((function(){isNaN(parseFloat(a))||e(this).val(parseFloat(a))}));break;case"regular_price_increase":null!=(a=prompt(wcfm_products_manage_messages.regular_price_increase))&&e("#variations").find('input[data-name="regular_price"]').each((function(){isNaN(parseFloat(a))||(e(this).val().length>0?e(this).val(parseFloat(e(this).val())+parseFloat(a)):e(this).val(parseFloat(a)))}));break;case"regular_price_decrease":var a;null!=(a=prompt(wcfm_products_manage_messages.regular_price_decrease))&&e("#variations").find('input[data-name="regular_price"]').each((function(){isNaN(parseFloat(a))||(e(this).val().length>0?e(this).val(parseFloat(e(this).val())-parseFloat(a)):e(this).val(parseFloat(a)))}));break;case"set_sale_price":null!=(i=prompt(wcfm_products_manage_messages.sales_price))&&e("#variations").find('input[data-name="sale_price"]').each((function(){isNaN(parseFloat(i))||e(this).val(parseFloat(i))}));break;case"sale_price_increase":null!=(i=prompt(wcfm_products_manage_messages.sales_price_increase))&&e("#variations").find('input[data-name="sale_price"]').each((function(){isNaN(parseFloat(i))||(e(this).val().length>0?e(this).val(parseFloat(e(this).val())+parseFloat(i)):e(this).val(parseFloat(i)))}));break;case"sale_price_decrease":var i;null!=(i=prompt(wcfm_products_manage_messages.sales_price_decrease))&&e("#variations").find('input[data-name="sale_price"]').each((function(){isNaN(parseFloat(i))||(e(this).val().length>0?e(this).val(parseFloat(e(this).val())-parseFloat(i)):e(this).val(parseFloat(i)))}));break;case"set_length":var s=prompt(wcfm_products_manage_messages.length);null!=s&&e("#variations").find('input[data-name="length"]').each((function(){isNaN(parseFloat(s))||e(this).val(parseFloat(s))}));break;case"set_width":var c=prompt(wcfm_products_manage_messages.width);null!=c&&e("#variations").find('input[data-name="width"]').each((function(){isNaN(parseFloat(c))||e(this).val(parseFloat(c))}));break;case"set_height":var _=prompt(wcfm_products_manage_messages.height);null!=_&&e("#variations").find('input[data-name="height"]').each((function(){isNaN(parseFloat(_))||e(this).val(parseFloat(_))}));break;case"set_weight":var n=prompt(wcfm_products_manage_messages.weight);null!=n&&e("#variations").find('input[data-name="weight"]').each((function(){isNaN(parseFloat(n))||e(this).val(parseFloat(n))}));break;case"variable_download_limit":var o=prompt(wcfm_products_manage_messages.download_limit);null!=o&&e("#variations").find('input[data-name="download_limit"]').each((function(){isNaN(parseFloat(o))||e(this).val(parseFloat(o))}));break;case"variable_download_expiry":var l=prompt(wcfm_products_manage_messages.download_expiry);null!=l&&e("#variations").find('input[data-name="download_expiry"]').each((function(){isNaN(parseFloat(l))||e(this).val(parseFloat(l))}));break;case"variation_auto_generate":confirm(wcfm_dashboard_messages.variation_auto_generate_confirm)&&(product_variation_auto_generate="yes",e("#wcfm_products_simple_draft_button").click())}e(this).val("")}})),e(".wcfm_add_new_taxonomy").each((function(){e(this).on("click",(function(){e(this).parent().find(".wcfm_add_new_taxonomy_form").toggleClass("wcfm_add_new_taxonomy_form_hide")}))})),e(".wcfm_add_taxonomy_bt").each((function(){e(this).on("click",(function(){if($wrapper=e(this).parent(),$wrapper.find(".wcfm_new_tax_ele").val()){$taxonomy=e(this).data("taxonomy"),$new_term=$wrapper.find(".wcfm_new_tax_ele").val(),$parent_term=$wrapper.find(".wcfm_new_parent_taxt_ele").val();var t={action:"wcfm_add_taxonomy_new_term",taxonomy:$taxonomy,new_term:$new_term,parent_term:$parent_term,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};$wrapper.block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),e.ajax({type:"POST",url:wcfm_params.ajax_url,data:t,success:function(t){t&&(t.error?window.alert(t.error):(e(".product_taxonomy_checklist_"+$taxonomy).prepend(t),$wrapper.find(".wcfm_new_tax_ele").val(""),$wrapper.find(".wcfm_new_parent_taxt_ele").val(0)),$wrapper.toggleClass("wcfm_add_new_taxonomy_form_hide"),$wrapper.unblock())}})}}))})),e(".wcfm_fetch_tag_cloud").length>0&&($wcfm_tag_cloud_fetched=!1,e(".wcfm_fetch_tag_cloud").click((function(){if(!$wcfm_tag_cloud_fetched){var t={action:"get-tagcloud",tax:"product_tag",wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};e.post(wcfm_params.ajax_url,t,(function(t){t&&(e(".wcfm_fetch_tag_cloud").html(t),$wcfm_tag_cloud_fetched=!0,e(".tag-cloud-link").each((function(){e(this).click((function(t){t.preventDefault(),$tag=e(this).text(),$tags=e("#product_tags").val(),$tags.length>0?$tags+=","+$tag:$tags=$tag,e("#product_tags").val($tags)}))})))}))}}))),"undefined"!=typeof gmw_forms){if("undefined"!=typeof tinymce&&tinymce.PluginManager.add("geomywp",(function(e,t){e.addButton("geomywp",{text:"GMW Form",icon:!1,onclick:function(){e.windowManager.open({title:"GMW Form",body:[{type:"listbox",name:"form_type",label:"Form Type",values:[{text:"Form",value:"form"},{text:"Map",value:"map"},{text:"Results",value:"results"}]},{type:"listbox",name:"gmw_forms",label:"Select Form",values:gmw_forms}],onsubmit:function(t){"results"==t.data.form_type?e.insertContent('[gmw form="results"]'):"map"==t.data.form_type?e.insertContent('[gmw map="'+t.data.gmw_forms+'"]'):e.insertContent('[gmw form="'+t.data.gmw_forms+'"]')}})}})})),tinyMce_toolbar+=" | geomywp",e("#excerpt").hasClass("rich_editor")&&"undefined"!=typeof tinymce)tinymce.init({selector:"#excerpt",height:75,menubar:!1,plugins:["advlist autolink lists link charmap print preview anchor","searchreplace visualblocks code fullscreen","insertdatetime image media table paste code geomywp directionality"],toolbar:tinyMce_toolbar,content_css:"//www.tinymce.com/css/codepen.min.css",statusbar:!1,browser_spellcheck:!0,entity_encoding:"raw"});if(e("#description").hasClass("rich_editor")&&"undefined"!=typeof tinymce)tinymce.init({selector:"#description",height:75,menubar:!1,plugins:["advlist autolink lists link charmap print preview anchor","searchreplace visualblocks code fullscreen","insertdatetime image media table paste code geomywp directionality","autoresize"],toolbar:tinyMce_toolbar,content_css:"//www.tinymce.com/css/codepen.min.css",statusbar:!1,browser_spellcheck:!0,entity_encoding:"raw"})}else{if(e("#excerpt").hasClass("rich_editor")&&"undefined"!=typeof tinymce)tinymce.init({selector:"#excerpt",height:75,menubar:!1,plugins:["advlist autolink lists link charmap print preview anchor","searchreplace visualblocks code fullscreen","insertdatetime image media table paste code directionality"],toolbar:tinyMce_toolbar,content_css:"//www.tinymce.com/css/codepen.min.css",statusbar:!1,browser_spellcheck:!0,entity_encoding:"raw"});if(e("#description").hasClass("rich_editor")&&"undefined"!=typeof tinymce)tinymce.init({selector:"#description",menubar:!1,plugins:["advlist autolink lists link charmap print preview anchor","searchreplace visualblocks code fullscreen","insertdatetime image media table paste code directionality","autoresize"],toolbar:tinyMce_toolbar,content_css:"//www.tinymce.com/css/codepen.min.css",statusbar:!1,browser_spellcheck:!0,entity_encoding:"raw"})}function l(t){product_form_is_valid=!0,e(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp();var a=e.trim(e("#wcfm_products_manage_form").find("#pro_title").val());return e("#wcfm_products_manage_form").find("#pro_title").removeClass("wcfm_validation_failed").addClass("wcfm_validation_success"),0==a.length&&(e("#wcfm_products_manage_form").find("#pro_title").removeClass("wcfm_validation_success").addClass("wcfm_validation_failed"),product_form_is_valid=!1,e("#wcfm_products_manage_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+wcfm_products_manage_messages.no_title).addClass("wcfm-error").slideDown(),wcfm_notification_sound.play()),e(".wcfm_custom_field_editor").length>0&&e(".wcfm_custom_field_editor").each((function(){$wcfm_custom_field_editor=e(this),($wcfm_custom_field_editor.hasClass("rich_editor")||$wcfm_custom_field_editor.hasClass("wcfm_wpeditor"))&&e("#"+$wcfm_custom_field_editor.attr("id")).val(getWCFMEditorContent($wcfm_custom_field_editor.attr("id")))})),t&&(e(document.body).trigger("wcfm_products_manage_form_validate",e("#wcfm_products_manage_form")),$wcfm_is_valid_form=product_form_is_valid,e(document.body).trigger("wcfm_form_validate",e("#wcfm_products_manage_form")),product_form_is_valid=$wcfm_is_valid_form),product_form_is_valid}e(".wcfm_custom_field_editor").each((function(){if($wcfm_custom_field_editor=e(this),$wcfm_custom_field_editor.hasClass("rich_editor")&&"undefined"!=typeof tinymce)tinymce.init({selector:"#"+$wcfm_custom_field_editor.attr("id"),height:150,menubar:!1,plugins:["advlist autolink lists link charmap print preview anchor","searchreplace visualblocks code fullscreen","insertdatetime image media table paste code directionality"],toolbar:tinyMce_toolbar,content_css:"//www.tinymce.com/css/codepen.min.css",statusbar:!1,browser_spellcheck:!0,entity_encoding:"raw"})})),e("#wcfm_products_simple_draft_button").click((function(t){t.preventDefault(),e(".wcfm_submit_button").hide();var a=getWCFMEditorContent("excerpt"),i=getWCFMEditorContent("description"),s=getWCFMEditorContent("_ticket_content"),c=getWCFMEditorContent("_ticket_email_html");if($is_valid=l(!1),$is_valid){e("#wcfm_products_manage_form").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var _={action:"wcfm_ajax_controller",controller:"wcfm-products-manage",wcfm_products_manage_form:e("#wcfm_products_manage_form").serialize(),excerpt:a,description:i,status:"draft",removed_variations:removed_variations,removed_person_types:removed_person_types,ticket_content:s,ticket_email_html:c,product_manage_from_popup:product_manage_from_popup,variation_auto_generate:product_variation_auto_generate,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};e.post(wcfm_params.ajax_url,_,(function(t){t&&($response_json=e.parseJSON(t),e(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp(),wcfm_notification_sound.play(),$response_json.status?e("#wcfm_products_manage_form .wcfm-message").html('<span class="wcicon-status-completed"></span>'+$response_json.message).addClass("wcfm-success").slideDown("slow",(function(){$response_json.redirect&&(window.location=$response_json.redirect)})):e("#wcfm_products_manage_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+$response_json.message).addClass("wcfm-error").slideDown(),$response_json.id&&e("#pro_id").val($response_json.id),wcfmMessageHide(),e("#wcfm_products_manage_form").unblock(),e(".wcfm_submit_button").show())}))}else e(".wcfm_submit_button").show()})),e("#wcfm_products_simple_submit_button").click((function(t){t.preventDefault(),e(".wcfm_submit_button").hide();var a=getWCFMEditorContent("excerpt"),i=getWCFMEditorContent("description"),s=getWCFMEditorContent("_ticket_content"),c=getWCFMEditorContent("_ticket_email_html");if($is_valid=l(!0),$is_valid){e("#wcfm_products_manage_form").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var _={action:"wcfm_ajax_controller",controller:"wcfm-products-manage",wcfm_products_manage_form:e("#wcfm_products_manage_form").serialize(),excerpt:a,description:i,status:"submit",removed_variations:removed_variations,removed_person_types:removed_person_types,ticket_content:s,ticket_email_html:c,product_manage_from_popup:product_manage_from_popup,variation_auto_generate:product_variation_auto_generate,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};e.post(wcfm_params.ajax_url,_,(function(t){t&&($response_json=e.parseJSON(t),e(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp(),wcfm_notification_sound.play(),$response_json.status?e("#wcfm_products_manage_form .wcfm-message").html('<span class="wcicon-status-completed"></span>'+$response_json.message).addClass("wcfm-success").slideDown("slow",(function(){$response_json.redirect&&(window.location=$response_json.redirect)})):e("#wcfm_products_manage_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+$response_json.message).addClass("wcfm-error").slideDown(),$response_json.id&&e("#pro_id").val($response_json.id),e("#wcfm_products_manage_form").unblock(),e(".wcfm_submit_button").show())}))}else e(".wcfm_submit_button").show()})),e("#wcfm_products_simple_reject_button").click((function(t){t.preventDefault(),e(".wcfm_submit_button").hide();var a=getWCFMEditorContent("excerpt"),i=getWCFMEditorContent("description"),s=getWCFMEditorContent("_ticket_content"),c=getWCFMEditorContent("_ticket_email_html");if($is_valid=l(!1),$is_valid){var _=prompt(wcfm_dashboard_messages.product_reject_confirm);if(_){e("#wcfm_products_manage_form").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var n={action:"wcfm_ajax_controller",controller:"wcfm-products-manage",wcfm_products_manage_form:e("#wcfm_products_manage_form").serialize(),excerpt:a,description:i,status:"draft",removed_variations:removed_variations,removed_person_types:removed_person_types,ticket_content:s,ticket_email_html:c,product_manage_from_popup:product_manage_from_popup,variation_auto_generate:product_variation_auto_generate,reject_reason:_,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};e.post(wcfm_params.ajax_url,n,(function(t){t&&($response_json=e.parseJSON(t),e(".wcfm-message").html("").removeClass("wcfm-error").removeClass("wcfm-success").slideUp(),wcfm_notification_sound.play(),$response_json.status?e("#wcfm_products_manage_form .wcfm-message").html('<span class="wcicon-status-completed"></span>'+$response_json.message).addClass("wcfm-success").slideDown("slow",(function(){$response_json.redirect&&(window.location=$response_json.redirect)})):e("#wcfm_products_manage_form .wcfm-message").html('<span class="wcicon-status-cancelled"></span>'+$response_json.message).addClass("wcfm-error").slideDown(),$response_json.id&&e("#pro_id").val($response_json.id),wcfmMessageHide(),e("#wcfm_products_manage_form").unblock(),e(".wcfm_submit_button").show())}))}}else e(".wcfm_submit_button").show()})),wcfm_auto_product_suggest.allow&&($pro_id=e("#pro_id").val(),0==$pro_id&&(e("#wcfm-main-contentainer #pro_title").after('<div id="wcfm_auto_suggest_product_title"></div>'),e("#wcfm-main-contentainer #pro_title").on("keyup focus",(function(t){var a=e(this).val();if(a.length>=3){var i={action:"wcfm_auto_search_product",protitle:a,wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};e.post(wcfm_params.ajax_url,i,(function(t){t?(e("#wcfm_auto_suggest_product_title").html(t).addClass("wcfm_auto_suggest_product_title_show"),e(".wcfm_product_multi_seller_associate").each((function(){e(this).click((function(t){t.preventDefault(),e("#wcfm-content").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var a={action:"wcfm_product_multi_seller_associate",proid:e(this).data("proid"),wcfm_ajax_nonce:wcfm_params.wcfm_ajax_nonce};return jQuery.ajax({type:"POST",url:wcfm_params.ajax_url,data:a,success:function(t){t&&($response_json=e.parseJSON(t),$response_json.status&&$response_json.redirect&&(window.location=$response_json.redirect))}}),!1}))}))):e("#wcfm_auto_suggest_product_title").html("").removeClass("wcfm_auto_suggest_product_title_show")}))}else 0==a.length&&e("#wcfm_auto_suggest_product_title").html("").removeClass("wcfm_auto_suggest_product_title_show")})),e("body").click((function(t){"wcfm_auto_suggest_product_title"!=t.target.id&&e("#wcfm_auto_suggest_product_title").removeClass("wcfm_auto_suggest_product_title_show")}))))}));