<?php
/**
 * Basic Vendor Plugin Tests
 *
 * @package Vendor
 */

class Test_Vendor_Basic extends WP_UnitTestCase {
    
    /**
     * Test plugin activation
     */
    public function test_plugin_activation() {
        // Test that plugin is loaded
        $this->assertTrue(class_exists('Vendor'));
        
        // Test that main instance is available
        $this->assertInstanceOf('Vendor', vendor());
        
        // Test that database tables are created
        global $wpdb;
        
        $vendors_table = $wpdb->prefix . 'vendor_vendors';
        $this->assertEquals($vendors_table, $wpdb->get_var("SHOW TABLES LIKE '$vendors_table'"));
        
        $commissions_table = $wpdb->prefix . 'vendor_commissions';
        $this->assertEquals($commissions_table, $wpdb->get_var("SHOW TABLES LIKE '$commissions_table'"));
        
        $withdrawals_table = $wpdb->prefix . 'vendor_withdrawals';
        $this->assertEquals($withdrawals_table, $wpdb->get_var("SHOW TABLES LIKE '$withdrawals_table'"));
    }
    
    /**
     * Test vendor role creation
     */
    public function test_vendor_role_creation() {
        $vendor_role = get_role('vendor');
        $this->assertNotNull($vendor_role);
        
        // Test vendor capabilities
        $this->assertTrue($vendor_role->has_cap('read'));
        $this->assertTrue($vendor_role->has_cap('edit_products'));
        $this->assertTrue($vendor_role->has_cap('edit_shop_orders'));
        $this->assertTrue($vendor_role->has_cap('edit_shop_coupons'));
    }
    
    /**
     * Test vendor creation
     */
    public function test_vendor_creation() {
        // Create test user
        $user_id = $this->factory->user->create(array(
            'user_login' => 'testvendor',
            'user_email' => '<EMAIL>',
            'display_name' => 'Test Vendor'
        ));
        
        // Create vendor
        $vendor_data = array(
            'store_name' => 'Test Store',
            'store_slug' => 'test-store',
            'store_description' => 'Test store description',
            'store_email' => '<EMAIL>',
            'commission_rate' => 15.00,
            'status' => 'active'
        );
        
        $vendor_id = vendor()->vendor_manager->create_vendor($user_id, $vendor_data);
        
        $this->assertNotFalse($vendor_id);
        $this->assertIsInt($vendor_id);
        
        // Test vendor retrieval
        $vendor = vendor()->vendor_manager->get_vendor($vendor_id);
        $this->assertNotNull($vendor);
        $this->assertEquals('Test Store', $vendor->store_name);
        $this->assertEquals('test-store', $vendor->store_slug);
        $this->assertEquals(15.00, $vendor->commission_rate);
        $this->assertEquals('active', $vendor->status);
        
        // Test user role update
        $user = get_user_by('id', $user_id);
        $this->assertTrue(in_array('vendor', $user->roles));
    }
    
    /**
     * Test commission creation
     */
    public function test_commission_creation() {
        // Create vendor first
        $user_id = $this->factory->user->create();
        $vendor_data = array(
            'store_name' => 'Commission Test Store',
            'store_slug' => 'commission-test',
            'commission_rate' => 20.00,
            'status' => 'active'
        );
        $vendor_id = vendor()->vendor_manager->create_vendor($user_id, $vendor_data);
        
        // Create mock order item
        $order_item = new stdClass();
        $order_item->total = 100.00;
        
        // Create commission
        $commission_id = vendor()->commission->create_commission(
            $vendor_id,
            123, // order_id
            456, // product_id
            789, // order_item_id
            $order_item
        );
        
        $this->assertNotFalse($commission_id);
        
        // Test commission retrieval
        $commission = vendor()->commission->get_commission($commission_id);
        $this->assertNotNull($commission);
        $this->assertEquals($vendor_id, $commission->vendor_id);
        $this->assertEquals(123, $commission->order_id);
        $this->assertEquals(456, $commission->product_id);
        $this->assertEquals(100.00, $commission->gross_amount);
        $this->assertEquals(20.00, $commission->commission_amount); // 20% of 100
        $this->assertEquals(20.00, $commission->commission_rate);
        $this->assertEquals('pending', $commission->status);
    }
    
    /**
     * Test withdrawal creation
     */
    public function test_withdrawal_creation() {
        // Create vendor with approved commission
        $user_id = $this->factory->user->create();
        $vendor_data = array(
            'store_name' => 'Withdrawal Test Store',
            'store_slug' => 'withdrawal-test',
            'commission_rate' => 10.00,
            'status' => 'active'
        );
        $vendor_id = vendor()->vendor_manager->create_vendor($user_id, $vendor_data);
        
        // Create approved commission
        global $wpdb;
        $commissions_table = Vendor_Database::get_commissions_table();
        $wpdb->insert($commissions_table, array(
            'vendor_id' => $vendor_id,
            'order_id' => 123,
            'product_id' => 456,
            'order_item_id' => 789,
            'gross_amount' => 100.00,
            'commission_amount' => 10.00,
            'commission_rate' => 10.00,
            'admin_fee' => 90.00,
            'status' => 'approved'
        ));
        
        // Test withdrawal creation
        $withdrawal_id = vendor()->withdrawal->create_withdrawal_request(
            $vendor_id,
            10.00,
            'paypal',
            array('paypal_email' => '<EMAIL>')
        );
        
        $this->assertNotInstanceOf('WP_Error', $withdrawal_id);
        $this->assertIsInt($withdrawal_id);
        
        // Test withdrawal retrieval
        $withdrawal = vendor()->withdrawal->get_withdrawal($withdrawal_id);
        $this->assertNotNull($withdrawal);
        $this->assertEquals($vendor_id, $withdrawal->vendor_id);
        $this->assertEquals(10.00, $withdrawal->amount);
        $this->assertEquals('paypal', $withdrawal->payment_method);
        $this->assertEquals('pending', $withdrawal->status);
    }
    
    /**
     * Test insufficient balance withdrawal
     */
    public function test_insufficient_balance_withdrawal() {
        // Create vendor without commission
        $user_id = $this->factory->user->create();
        $vendor_data = array(
            'store_name' => 'No Balance Store',
            'store_slug' => 'no-balance',
            'commission_rate' => 10.00,
            'status' => 'active'
        );
        $vendor_id = vendor()->vendor_manager->create_vendor($user_id, $vendor_data);
        
        // Try to withdraw more than available
        $result = vendor()->withdrawal->create_withdrawal_request(
            $vendor_id,
            100.00,
            'paypal',
            array('paypal_email' => '<EMAIL>')
        );
        
        $this->assertInstanceOf('WP_Error', $result);
        $this->assertEquals('insufficient_balance', $result->get_error_code());
    }
    
    /**
     * Test vendor earnings calculation
     */
    public function test_vendor_earnings() {
        // Create vendor
        $user_id = $this->factory->user->create();
        $vendor_data = array(
            'store_name' => 'Earnings Test Store',
            'store_slug' => 'earnings-test',
            'commission_rate' => 25.00,
            'status' => 'active'
        );
        $vendor_id = vendor()->vendor_manager->create_vendor($user_id, $vendor_data);
        
        // Create multiple commissions
        global $wpdb;
        $commissions_table = Vendor_Database::get_commissions_table();
        
        // Approved commission
        $wpdb->insert($commissions_table, array(
            'vendor_id' => $vendor_id,
            'order_id' => 100,
            'product_id' => 200,
            'order_item_id' => 300,
            'gross_amount' => 80.00,
            'commission_amount' => 20.00,
            'commission_rate' => 25.00,
            'status' => 'approved'
        ));
        
        // Pending commission
        $wpdb->insert($commissions_table, array(
            'vendor_id' => $vendor_id,
            'order_id' => 101,
            'product_id' => 201,
            'order_item_id' => 301,
            'gross_amount' => 40.00,
            'commission_amount' => 10.00,
            'commission_rate' => 25.00,
            'status' => 'pending'
        ));
        
        // Test earnings summary
        $earnings = vendor()->commission->get_vendor_earnings_summary($vendor_id);
        $this->assertEquals(2, $earnings->total_orders);
        $this->assertEquals(120.00, $earnings->gross_sales);
        $this->assertEquals(30.00, $earnings->total_commission);
        $this->assertEquals(20.00, $earnings->approved_commission);
        $this->assertEquals(10.00, $earnings->pending_commission);
        
        // Test available balance
        $available_balance = vendor()->commission->get_vendor_available_balance($vendor_id);
        $this->assertEquals(20.00, $available_balance);
    }
    
    /**
     * Test REST API endpoints
     */
    public function test_rest_api_endpoints() {
        // Create vendor user
        $user_id = $this->factory->user->create();
        wp_set_current_user($user_id);
        
        $vendor_data = array(
            'store_name' => 'API Test Store',
            'store_slug' => 'api-test',
            'commission_rate' => 15.00,
            'status' => 'active'
        );
        vendor()->vendor_manager->create_vendor($user_id, $vendor_data);
        
        // Test dashboard stats endpoint
        $request = new WP_REST_Request('GET', '/vendor/v1/dashboard/stats');
        $response = rest_do_request($request);
        
        $this->assertEquals(200, $response->get_status());
        
        $data = $response->get_data();
        $this->assertArrayHasKey('earnings', $data);
        $this->assertArrayHasKey('orders', $data);
        $this->assertArrayHasKey('products', $data);
        $this->assertArrayHasKey('withdrawals', $data);
    }
    
    /**
     * Clean up after tests
     */
    public function tearDown(): void {
        parent::tearDown();
        
        // Clean up test data
        global $wpdb;
        
        $wpdb->query("DELETE FROM {$wpdb->prefix}vendor_vendors WHERE store_slug LIKE '%-test%'");
        $wpdb->query("DELETE FROM {$wpdb->prefix}vendor_commissions WHERE vendor_id NOT IN (SELECT id FROM {$wpdb->prefix}vendor_vendors)");
        $wpdb->query("DELETE FROM {$wpdb->prefix}vendor_withdrawals WHERE vendor_id NOT IN (SELECT id FROM {$wpdb->prefix}vendor_vendors)");
    }
}
